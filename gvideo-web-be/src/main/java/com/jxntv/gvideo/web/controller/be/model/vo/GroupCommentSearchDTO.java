package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.common.model.SearchDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR> Created on 2021/5/11.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupCommentSearchDTO extends SearchDTO {
    @ApiModelProperty("资源id")
    private Long mediaId;
    @ApiModelProperty("评论类型 0 主评论 1 回复评论")
    private Integer type;
    @ApiModelProperty("评论内容")
    private String commentContent;
    @ApiModelProperty("评论用户")
    private String commentUser;
    @ApiModelProperty("开始时间")
    private String start;
    @ApiModelProperty("结束时间")
    private String end;
    @ApiModelProperty(value = "圈子id", required = true)
    @NotNull(message = "社区ID不能为空")
    private Long groupId;
    @ApiModelProperty(value = "评论状态 1启用0禁用")
    private Integer status;
}
