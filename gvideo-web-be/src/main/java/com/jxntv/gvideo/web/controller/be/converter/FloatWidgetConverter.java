package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.OssImgMetaData;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupGatherActivityClient;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupTopicDTO;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.TvChannelClient;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvChannelDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.media.client.enums.BannerJumpMediaType;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetColorDTO;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetDTO;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetIconDTO;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetShareDTO;
import com.jxntv.gvideo.om.enums.widget.FloatWidgetDisplayType;
import com.jxntv.gvideo.om.enums.widget.FloatWidgetStatus;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.vo.floatwidget.*;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/5/16
 * Email: <EMAIL>
 */

@Slf4j
@Component
@RefreshScope
public class FloatWidgetConverter {

    @Resource
    private OssClient ossClient;
    @Resource
    private UserConsumer userConsumer;

    @Resource
    private MediaResourceClient mediaResourceClient;

    @Resource
    private GroupTopicClient groupTopicClient;

    @Resource
    private TvProgramClient tvProgramClient;

    @Resource
    private TvChannelClient tvChannelClient;

    @Resource
    private GroupGatherActivityClient groupGatherActivityClient;

    public FloatWidgetVO convert(FloatWidgetDTO dto) {
        String iconUrl = ossClient.getOssFile(dto.getIconId()).map(OssDTO::getUrl).orElse("");

        FloatWidgetVO vo = new FloatWidgetVO();
        vo.setId(dto.getId());
        vo.setCreateType(dto.getCreateType());
        vo.setMediaId(dto.getMediaId());
        vo.setType(dto.getType());
        vo.setLinkUrl(dto.getLinkUrl());
        vo.setExtraUrl(dto.getExtraUrl());
        vo.setJumpMediaId(dto.getJumpMediaId());
        vo.setJumpMediaType(dto.getJumpMediaType());
        vo.setTitle(dto.getTitle());
        vo.setHeight(dto.getHeight());
        vo.setWidth(dto.getWidth());
        vo.setIconId(dto.getIconId());
        vo.setIconUrl(iconUrl);
        vo.setColorId(dto.getColorId());
        vo.setColorStart(dto.getColorStart());
        vo.setColorEnd(dto.getColorEnd());
        vo.setStatus(dto.getStatus());
        vo.setStatusStr(FloatWidgetStatus.toStr(dto.getStatus()));
        vo.setCreateDate(dto.getCreateDate());
        vo.setCreateUserId(dto.getCreateUserId());
        vo.setUpdateDate(dto.getUpdateDate());
        vo.setUpdateUserId(dto.getUpdateUserId());
        vo.setJump(getJumpTitle(dto));

        if (Objects.nonNull(dto.getShareInfo())) {
            FloatWidgetShareVO shareInfo = this.convert(dto.getShareInfo());
            vo.setShareInfo(shareInfo);
        }



        return vo;
    }

    public FloatWidgetDTO convert(FloatWidgetAddVO vo) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);

        FloatWidgetDTO dto = new FloatWidgetDTO();
        dto.setId(vo.getId());
        dto.setMediaId(vo.getMediaId());
        dto.setCreateType(vo.getCreateType());
        dto.setDisplayType(convertCreateTypeToDisplayType(vo.getCreateType()));
        dto.setType(vo.getType());
        dto.setLinkUrl(vo.getLinkUrl());
        dto.setJumpMediaId(vo.getJumpMediaId());
        dto.setJumpMediaType(vo.getJumpMediaType());
        dto.setTitle(vo.getTitle());
        dto.setDescription(vo.getDescription());
        if (Objects.equals(vo.getCreateType(), 1)) {
            dto.setHeight(0);
            dto.setWidth(0);
        } else {
            OssImgMetaData metaData = ossClient.getOssImgMetaData(vo.getIconId()).orElse(null);
            if (Objects.nonNull(metaData)) {
                dto.setHeight(Objects.isNull(metaData.getHeight()) ? 0 : metaData.getHeight().intValue());
                dto.setWidth(Objects.isNull(metaData.getWidth()) ? 0 : metaData.getWidth().intValue());
            }
        }
        dto.setIconId(vo.getIconId());
        dto.setColorId(vo.getColorId());
        dto.setColorStart(vo.getColorStart());
        dto.setColorEnd(vo.getColorEnd());
        dto.setNeedLogin(vo.getNeedLogin());
        dto.setStatus(vo.getStatus());
        dto.setCreateDate(LocalDateTime.now());
        dto.setCreateUserId(userId);

        if (Objects.nonNull(vo.getShareInfo())) {
            FloatWidgetShareDTO shareInfo = this.convert(vo.getShareInfo());
            dto.setShareInfo(shareInfo);
        }

        return dto;
    }

    public FloatWidgetShareDTO convert(FloatWidgetShareVO vo) {
        FloatWidgetShareDTO dto = new FloatWidgetShareDTO();
        dto.setId(vo.getId());
        dto.setWidgetId(vo.getWidgetId());
        dto.setTitle(vo.getTitle());
        dto.setDescription(vo.getDescription());
        dto.setCoverId(vo.getCoverId());
        return dto;
    }

    public FloatWidgetShareVO convert(FloatWidgetShareDTO dto) {
        String coverUrl = ossClient.getOriOss(dto.getCoverId()).map(OssDTO::getUrl).orElse("");

        FloatWidgetShareVO vo = new FloatWidgetShareVO();
        vo.setId(dto.getId());
        vo.setWidgetId(dto.getWidgetId());
        vo.setTitle(dto.getTitle());
        vo.setDescription(dto.getDescription());
        vo.setCoverId(dto.getCoverId());
        vo.setCoverUrl(coverUrl);
        return vo;
    }

    public FloatWidgetDTO convert(FloatWidgetUpdateVO vo) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);

        FloatWidgetDTO dto = new FloatWidgetDTO();
        dto.setId(vo.getId());
        dto.setMediaId(vo.getMediaId());
        dto.setCreateType(vo.getCreateType());
        dto.setDisplayType(convertCreateTypeToDisplayType(vo.getCreateType()));
        dto.setType(vo.getType());
        dto.setLinkUrl(vo.getLinkUrl());
        dto.setExtraUrl(vo.getExtraUrl());
        dto.setJumpMediaId(vo.getJumpMediaId());
        dto.setJumpMediaType(vo.getJumpMediaType());

        dto.setTitle(vo.getTitle());
        if (Objects.equals(vo.getCreateType(), 1)) {
            dto.setHeight(0);
            dto.setWidth(0);
        } else {
            OssImgMetaData metaData = ossClient.getOssImgMetaData(vo.getIconId()).orElse(null);
            if (Objects.nonNull(metaData)) {
                dto.setHeight(Objects.isNull(metaData.getHeight()) ? 0 : metaData.getHeight().intValue());
                dto.setWidth(Objects.isNull(metaData.getWidth()) ? 0 : metaData.getWidth().intValue());
            }
        }
        dto.setIconId(vo.getIconId());
        dto.setColorId(vo.getColorId());
        dto.setColorStart(vo.getColorStart());
        dto.setColorEnd(vo.getColorEnd());
        dto.setStatus(vo.getStatus());
        dto.setNeedLogin(vo.getNeedLogin());
        dto.setUpdateDate(LocalDateTime.now());
        dto.setUpdateUserId(userId);

        if (Objects.nonNull(vo.getShareInfo())) {
            FloatWidgetShareDTO shareInfo = this.convert(vo.getShareInfo());
            dto.setShareInfo(shareInfo);
        }

        return dto;
    }

    public Integer convertCreateTypeToDisplayType(Integer createType) {
        return Objects.equals(createType, 1) ? FloatWidgetDisplayType.LABEL_BUTTON.getCode() : FloatWidgetDisplayType.IMAGE_BUTTON.getCode();
    }


    public FloatWidgetIconVO convert(FloatWidgetIconDTO dto) {
        String iconUrl = ossClient.getOssFile(dto.getIconId()).map(OssDTO::getUrl).orElse("");
        FloatWidgetIconVO floatWidgetIconVO = new FloatWidgetIconVO();
        floatWidgetIconVO.setIconId(dto.getIconId());
        floatWidgetIconVO.setIconUrl(iconUrl);
        return floatWidgetIconVO;

    }

    public FloatWidgetColorVO convert(FloatWidgetColorDTO dto) {
        FloatWidgetColorVO vo = new FloatWidgetColorVO();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setColorStart(dto.getColorStart());
        vo.setColorEnd(dto.getColorEnd());
        return vo;
    }

    public FloatWidgetIconDTO convert(FloatWidgetIconVO vo) {
        FloatWidgetIconDTO dto = new FloatWidgetIconDTO();
        dto.setName(vo.getName());
        dto.setIconId(vo.getIconId());

        return dto;
    }

    public FloatWidgetColorDTO convert(FloatWidgetColorVO vo) {
        FloatWidgetColorDTO dto = new FloatWidgetColorDTO();
        dto.setName(vo.getName());
        dto.setColorStart(vo.getColorStart());
        dto.setColorEnd(vo.getColorEnd());
        return dto;
    }

    private String getJumpTitle(FloatWidgetDTO dto){
        if (BannerJumpMediaType.NEWS.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> mediaResourceClient.get(e))
                    .map(Result::getResult)
                    .map(MediaResourceDTO::getShowName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.GROUP.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> groupTopicClient.getSimpleById(e))
                    .map(Result::getResult)
                    .map(GroupInfoDTO::getName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.TOPIC.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> groupTopicClient.topicDetail(e, null))
                    .map(Result::getResult)
                    .map(GroupTopicDTO::getContent)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.POSTS.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> mediaResourceClient.get(e))
                    .map(Result::getResult)
                    .map(MediaResourceDTO::getShowName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.LIVE_BROADCAST.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> mediaResourceClient.get(e))
                    .map(Result::getResult)
                    .map(MediaResourceDTO::getShowName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.COLUMN.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> tvProgramClient.getById(e))
                    .map(Result::getResult)
                    .map(TvProgramDTO::getProgramName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.CHANNEL.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> tvChannelClient.getById(e))
                    .map(Result::getResult)
                    .map(TvChannelDTO::getChannelName)
                    .orElse("");
            return title;
        }
        if (BannerJumpMediaType.ACTIVITY_GATHER.getCode().equals(dto.getJumpMediaType())) {
            String title = Optional.ofNullable(dto.getJumpMediaId())
                    .map(e -> groupGatherActivityClient.getActivityById(e))
                    .map(Result::getResult)
                    .map(GroupGatherActivityDTO::getTitle)
                    .orElse("");
            return title;
        }
        return "";
    }
}
