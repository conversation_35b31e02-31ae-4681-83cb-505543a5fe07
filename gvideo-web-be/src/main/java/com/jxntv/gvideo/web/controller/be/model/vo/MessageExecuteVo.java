package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-04-15
 */
@Data
@ApiModel(value="MessageExecuteVo", description="站内信下发时间对象")
public class MessageExecuteVo {

    @ApiModelProperty(value="通知ID")
    private Long id;
    @ApiModelProperty(value="下发类型")
    private Integer executeType;
    @ApiModelProperty(value="下发时间")
    private String executeDate;

}
