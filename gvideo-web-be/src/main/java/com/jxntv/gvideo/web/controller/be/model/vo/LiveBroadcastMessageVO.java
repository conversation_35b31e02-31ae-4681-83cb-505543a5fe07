package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Created on 2020/12/8.
 */
@Data
public class LiveBroadcastMessageVO {
    @ApiModelProperty(value = "留言id")
    private Long id;
    @ApiModelProperty(value = "留言名称")
    private String name;
    @ApiModelProperty(value = "留言用户id")
    private Long userId;
    @ApiModelProperty(value = "留言内容")
    private String message;
    @ApiModelProperty(value = "留言时间")
    private String createdDate;
    @ApiModelProperty(value = "留言状态 0 待审核 1 已通过 2 已删除")
    private Integer messageStatus;
    @ApiModelProperty(value = "用户是否被禁言 0 正常 1 禁言")
    private Integer userBan;
    @ApiModelProperty(value = "是否系统用户 0 前端用户 1 系统用户")
    private Integer sysUser;
    @ApiModelProperty(value = "图片json字符串")
    private List<Map<String, String>> images;
    @ApiModelProperty(value = "链接json字符串")
    private List<Map<String, String>> links;
    @ApiModelProperty(value = "机审状态")
    private Integer aiAuditState;

}
