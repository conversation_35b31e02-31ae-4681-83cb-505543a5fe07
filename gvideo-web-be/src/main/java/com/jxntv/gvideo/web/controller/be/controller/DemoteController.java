package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.Result;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/api/demote")
public class DemoteController {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * H5页面降级开关
     */
    private static final String H5_URL_DEMOTE_SWITCH = "h5::url::demote::switch";

    @PostMapping("/switch")
    public Result<Boolean> updateDemoteSwitch(@RequestParam Integer isOpen) {
        // 更新redis中的switch 1-开启 0-关闭
        stringRedisTemplate.opsForValue().set(H5_URL_DEMOTE_SWITCH, String.valueOf(isOpen));
        return Result.ok();
    }

    @GetMapping("/switch")
    public Result<Integer> getSwitch() {
        // 更新redis中的switch 1-开启 0-关闭
        String s = stringRedisTemplate.opsForValue().get(H5_URL_DEMOTE_SWITCH);
        if (StringUtils.isBlank(s)) {
            return Result.ok(0);
        }
        return Result.ok(Integer.parseInt(s));
    }
}
