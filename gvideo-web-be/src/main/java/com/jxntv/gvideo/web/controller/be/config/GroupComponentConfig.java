package com.jxntv.gvideo.web.controller.be.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022/5/25
 * Email: <EMAIL>
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "group.component")
public class GroupComponentConfig {
    /**
     * 520活动URL
     */
    private String love520Url;
    /**
     * 用户签到URL
     */
    private String userMarkUrl;

    /**
     * 签到领勋章URL
     */
    private String markMedalUrl;

}
