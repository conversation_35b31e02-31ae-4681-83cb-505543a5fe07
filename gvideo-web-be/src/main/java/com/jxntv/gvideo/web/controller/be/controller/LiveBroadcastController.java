package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.aliyun.sdk.ImClient;
import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.im.ImGroupAddDTO;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.BroadcastClient;
import com.jxntv.gvideo.media.client.MediaPageViewClient;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.om.client.AuditMediaClient;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysTenantDTO;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import com.jxntv.gvideo.user.client.enums.ConsumerCommandBusinessType;
import com.jxntv.gvideo.user.client.enums.ConsumerCommandSendType;
import com.jxntv.gvideo.user.client.enums.ConsumerCommandStatus;
import com.jxntv.gvideo.user.client.enums.ConsumerCommandToType;
import com.jxntv.gvideo.web.controller.be.client.*;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.LiveBroadcastConverter;
import com.jxntv.gvideo.web.controller.be.converter.MediaResourceConverter;
import com.jxntv.gvideo.web.controller.be.converter.TenantConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.login.thread.UserInfo;
import com.jxntv.gvideo.web.controller.be.model.vo.*;
import com.jxntv.gvideo.web.controller.be.model.vo.command.CommandEditVO;
import com.jxntv.gvideo.web.controller.be.service.CommandService;
import com.jxntv.gvideo.web.controller.be.utils.LocalDateTimeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2020/12/4.
 */
@RestController
@RefreshScope
@RequestMapping("/api/live/broadcast")
@Api(value = "活动直播管理api", tags = {"活动直播管理api"})
@Slf4j
public class LiveBroadcastController {
    private final String LIVE_RECORD = "live_record";
    @Autowired
    private BroadcastClient broadcastClient;
    @Resource
    private SysUserClient sysUserService;
    @Autowired
    private SysTenantService sysTenantService;
    @Autowired
    private MediaResourceClient resourceClient;
    @Autowired
    private SysMenuService sysMenuService;

    @Autowired
    private AuditMediaClient auditMediaClient;
    @Autowired
    private MediaResourceService mediaResourceService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private MediaResourceConverter resourceConverter;
    @Autowired
    private BroadcastLocationService broadcastLocationService;
    @Resource
    private LiveBroadcastConverter liveBroadcastConverter;
    @Resource
    private MediaPageViewClient mediaPageViewClient;
    @Resource
    private OssClient ossClient;
    @Resource
    private ImClient imClient;

    @Value("${broadcast.label}")
    public String broadcastLabelId;

    @Resource
    private CommandService commandService;

    @PostMapping("query")
    @ApiOperation("分页查询直播列表")
    @PreAuthorize("hasAuthority('sys:live:broadcast')")
    public Result<Page<BroadcastVO>> query(@RequestBody(required = false) BroadcastSearchDTO searchDTO) {
        if (Objects.isNull(searchDTO)) {
            searchDTO = new BroadcastSearchDTO();
        }
        SysUserDTO sysUser = getSysUserDTO();
        Long id = sysUser.getId();
        if (Objects.isNull(searchDTO.getTenantId())) {
            searchDTO.setTenantId(sysUser.getTenantId());
        }
        searchDTO.setUserId(id);
        Result<PageDTO<BroadcastDTO>> result = broadcastClient.page(searchDTO);
        PageDTO<BroadcastDTO> pageDTO = result.getResult();
        return Result.ok(Page.pageOf(pageDTO, liveBroadcastConverter::convert, BroadcastVO.class));
    }

    @PostMapping
    @ApiOperation("新建直播")
    @PreAuthorize("hasAuthority('sys:live:broadcast:add')")
    public Result<Void> save(@RequestBody BroadcastVO broadcastVO) {
        Long cerId = broadcastVO.getCertificationId();
        if (Objects.isNull(cerId)) {
            return Result.fail("认证号id不能为空");
        }
        //获取登录用户信息 转换dto进行rpc调用
        SysUserDTO sysUser = getSysUserDTO();
        BroadcastDTO dto = new BroadcastDTO();
        BeanCopier.create(BroadcastVO.class, BroadcastDTO.class, false).copy(broadcastVO, dto, null);
        Long id = sysUser.getId();
        String name = sysUser.getName();
        dto.setCreatedBy(id);
        dto.setCreatedName(name);
        dto.setId(null);
        dto.setStartTime(LocalDateTimeUtils.parse(broadcastVO.getStartTime()));
        dto.setEndTime(LocalDateTimeUtils.parse(broadcastVO.getEndTime()));
        dto.setNotice(broadcastVO.getNotice());
        //将直播归入media resource contentType为6
        MediaResourceDTO mediaResourceDTO = new MediaResourceDTO();
        mediaResourceDTO.setContentType(ContentType.LIVE_BROADCAST.getCode());
        mediaResourceDTO.setPlayStyle(broadcastVO.getType());
        String title = dto.getTitle();
        mediaResourceDTO.setShowName(title);
        mediaResourceDTO.setInternalName(title);
        mediaResourceDTO.setReleaseId(dto.getCertificationId());
        mediaResourceDTO.setReleaseType(0);
        mediaResourceDTO.setTenantId(Objects.isNull(sysUser.getTenantId()) ? -1 : sysUser.getTenantId());
        mediaResourceDTO.setCreateUserId(dto.getCreatedBy());
        mediaResourceDTO.setIsComment(true);
        mediaResourceDTO.setIsSearch(true);
        mediaResourceDTO.setStatus(MediaResourceStatus.AUDIT.getCode());
        mediaResourceDTO.setContentTypeLabel(Collections.singletonList(broadcastLabelId));
        mediaResourceDTO.setImages(broadcastVO.getThumbLst());
        mediaResourceDTO.setUpdateUserId(sysUser.getId());
        // 插入内容资源表
        Result<Long> res = resourceClient.create(mediaResourceDTO);
        if (res.callSuccess()) {
            Long mediaId = res.getResult();
            mediaResourceDTO.setId(mediaId);
            dto.setMediaId(mediaId);
            //创建群组
            String groupId = "live-broadcast:" + mediaId;

            ImGroupAddDTO imGroupAddDTO = new ImGroupAddDTO();
            imGroupAddDTO.setGroupId(groupId);
            imGroupAddDTO.setName(title);
            imGroupAddDTO.setType("ChatRoom");
            if (imClient.addGroup(imGroupAddDTO).callSuccess()) {
                log.info("创建群组成功:groupId:" + groupId);
            }
            //仅创建资源审核
            Result<Void> mediaRst = addAudit(mediaId);
            if (!mediaRst.callSuccess()) {
                return Result.fail(mediaRst.getMessage());
            }
        } else {
            return Result.fail(res.getMessage());
        }
        broadcastClient.saveOrUpdate(dto);
        return Result.ok();
    }

    @PutMapping
    @ApiOperation("编辑直播")
    @PreAuthorize("hasAuthority('sys:live:broadcast:modify')")
    public Result<Void> update(@RequestBody BroadcastVO broadcastVO) {
        Result<BroadcastDTO> entity = broadcastClient.findById(broadcastVO.getId());
        //获取登录用户信息 转换dto进行rpc调用
        SysUserDTO sysUser = getSysUserDTO();
        BroadcastDTO dto = new BroadcastDTO();
        BeanCopier.create(BroadcastVO.class, BroadcastDTO.class, false).copy(broadcastVO, dto, null);
        Long id = sysUser.getId();
        String name = sysUser.getName();
        dto.setUpdatedBy(id);
        dto.setUpdatedName(name);
        dto.setStartTime(LocalDateTimeUtils.parse(broadcastVO.getStartTime()));
        dto.setEndTime(LocalDateTimeUtils.parse(broadcastVO.getEndTime()));
        dto.setNotice(broadcastVO.getNotice());
        broadcastClient.saveOrUpdate(dto);
        if (entity.callSuccess()) {
            BroadcastDTO entityResult = entity.getResult();
            String title = broadcastVO.getTitle();
            Integer newType = broadcastVO.getType();
            Long newCertId = broadcastVO.getCertificationId();
            Long oldCertId = entityResult.getCertificationId();
            Integer oldType = entityResult.getType();
            boolean typeModify = !newType.equals(oldType);
            boolean certModify = !newCertId.equals(oldCertId);
            boolean titleModify = !entityResult.getTitle().equalsIgnoreCase(title);
            Integer status = entityResult.getStatus();
            boolean noEnd = status != 0 && status != 4;
            //标题更改时，且直播状态不是未开始或已下架需要同步至审核表进行审核
            Long mediaId = entityResult.getMediaId();
            Result<MediaResourceDTO> mediaDTO = mediaResourceService.get(mediaId);
            if (mediaDTO.callSuccess()) {
                MediaResourceDTO modify = mediaDTO.getResult();
                boolean canModify = false;
                //判断是否修改直播封面
                List<MediaResourceImageDTO> thumbList = broadcastVO.getThumbLst();
                if (!CollectionUtils.isEmpty(thumbList)) {
                    canModify = true;
                    modify.setImages(broadcastVO.getThumbLst());
                }
                //判断是否修改类型
                if (typeModify) {
                    canModify = true;
                    modify.setPlayStyle(dto.getType());
                }
                if (titleModify) {
                    canModify = true;
                    // 修改资源标题
                    modify.setInternalName(title);
                    modify.setShowName(title);
                    modify.setPlayStyle(dto.getType());
                    // 仅创建直播的时候需要审核
//                    modify.setStatus(MediaResourceStatus.AUDIT.getCode());
                    modify.setReleaseId(newCertId);
                    modify.setImages(broadcastVO.getThumbLst());
                    // 修改直播位标题
                    BroadcastLocationDTO broadcastLocationDTO = new BroadcastLocationDTO();
                    broadcastLocationDTO.setBroadcastId(broadcastVO.getId());
                    broadcastLocationDTO.setBroadcastType(1);
                    broadcastLocationDTO.setTitle(title);
                    broadcastLocationService.editTitle(broadcastLocationDTO);

                }
                if (certModify) {
                    canModify = true;
                    // 仅创建直播的时候需要审核
//                    modify.setStatus(5);
                    modify.setPlayStyle(dto.getType());
                    modify.setReleaseId(newCertId);
                    // 仅创建直播的时候需要审核
//                Result<Void> mediaRst = addAudit(mediaId);
//                if (!mediaRst.callSuccess()) {
//                    return Result.fail(mediaRst.getMessage());
//                }
                }
                if (canModify) {
                    mediaResourceService.update(mediaId, modify);
                }
            }
        }
        return Result.ok();
    }

    private Result<Void> addAudit(Long mediaId) {
        Result<Integer> auditStatusRst = auditMediaClient.removeUnaudit(mediaId);
        if (auditStatusRst.callSuccess()) {
            //该内容处于待审核状态，则不需要写入审核表 如果没有 则写入待审核表
            Result<MediaResourceDTO> mediaRst = mediaResourceService.get(mediaId);
            if (mediaRst.callSuccess()) {
                auditMediaClient.addAuditingMedia(resourceConverter.toAuditingDTO(mediaRst.getResult()));
            } else {
                return Result.fail(mediaRst.getMessage());
            }
        } else {
            return Result.fail(auditStatusRst.getMessage());
        }
        return Result.ok();
    }

    private void syncMediaResource(Result<BroadcastDTO> entity, MediaResourceStatus status) {
        if (entity.callSuccess()) {
            mediaResourceService.statusModify(entity.getResult().getMediaId(), status.getCode());
        }
    }

    @PostMapping("status")
    @ApiOperation("保存直播管理页面")
    @PreAuthorize("hasAuthority('sys:live:broadcast:manage')")
    public Result<Void> statusModify(@RequestBody BroadcastStatusVO broadcastStatusVO) {
        Result<BroadcastDTO> dtoResult = broadcastClient.findById(broadcastStatusVO.getId());
        if (!dtoResult.callSuccess()) {
            return Result.fail("直播不存在");
        }
        BroadcastDTO broadcastDTO = dtoResult.getResult();
        Integer newStatus = broadcastStatusVO.getStatus();
        Integer oldStatus = broadcastDTO.getStatus();
        //改成未开始和下架之外的所有状态 需要入审核表重新审核
        boolean statusModify = !oldStatus.equals(newStatus);
        //获取登录用户信息 转换dto进行rpc调用
        SysUserDTO sysUser = getSysUserDTO();
        BroadcastStatusDTO dto = new BroadcastStatusDTO();
        BeanCopier.create(BroadcastStatusVO.class, BroadcastStatusDTO.class, false).copy(broadcastStatusVO, dto, null);
        dto.setUpdatedBy(sysUser.getId());
        dto.setUpdatedName(sysUser.getName());
        if (broadcastStatusVO.endStatus()) {

            syncMediaResource(dtoResult, MediaResourceStatus.DELETE);
            //  下发直播下架命令
            CommandEditVO vo = new CommandEditVO();
            Map<String, Object> body = new HashMap<>();
            body.put("mediaId", dtoResult.getResult().getMediaId());
            vo.setBusinessType(ConsumerCommandBusinessType.LIVE_BROADCAST_OFF.getCode());
            vo.setSendType(ConsumerCommandSendType.IMMEDIATELY.getCode());
            vo.setToType(ConsumerCommandToType.ALL.getCode());
            vo.setBody(body);
            vo.setStartTime(LocalDateTime.now());
            vo.setEndTime(LocalDateTime.now().plusHours(1));
            vo.setStatus(ConsumerCommandStatus.ENABLE.getCode());
            commandService.addCommand(vo);
        } else {
            syncMediaResource(dtoResult, MediaResourceStatus.ENABLE);
        }
        //准备直播录制配置参数
        dto.setStatusModify(statusModify);
        dto.setOldStatus(broadcastDTO.getStatus());
        dto.setStreamType(broadcastDTO.getStreamType());
        dto.setRecordType(broadcastDTO.getRecordType());
        dto.setAppName(broadcastDTO.getAppName());
        dto.setStreamName(broadcastDTO.getStreamName());
        if (Objects.isNull(dto.getStartTime())) {
            dto.setStartTime(broadcastDTO.getStartTime());
        }
        if (Objects.isNull(dto.getEndTime())) {
            dto.setEndTime(broadcastDTO.getEndTime());
        }
        //判断是否可以删除录制配置，如果没有发生过直播记录，则可以直接删除配置
        if (redisTemplate.opsForHash().hasKey(LIVE_RECORD, dto.getStreamName())) {
            dto.setHasLiveStream(1);
        } else {
            dto.setHasLiveStream(0);
        }
        //  设置虚拟阅读数
        if (Objects.nonNull(broadcastStatusVO.getVirtualPv())) {
            mediaPageViewClient.setVirtual(broadcastDTO.getMediaId(), (long) broadcastStatusVO.getVirtualPv());
        }

        broadcastClient.statusModify(dto);
        return Result.ok();
    }

    @DeleteMapping("{id}")
    @PreAuthorize("hasAuthority('sys:live:broadcast:del')")
    @ApiOperation("删除直播")
    public Result<Void> delete(@PathVariable("id") Long id) {
        Result<BroadcastDTO> rst = broadcastClient.findById(id);
        if (rst.callSuccess()) {
            syncMediaResource(rst, MediaResourceStatus.DELETE);
            broadcastClient.del(id);
        } else {
            return Result.fail(rst.getMessage());
        }
        return Result.ok();
    }

    @GetMapping("{id}")
    @PreAuthorize("hasAuthority('sys:live:broadcast')")
    @ApiOperation("单条直播信息")
    public Result<BroadcastDetailVO> findById(@PathVariable("id") Long id) {

        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.isNull(userInfo)) {
            return Result.fail("用户未登录");
        }
        Result<SysUserDTO> sysUserDTOResult = sysUserService.getUserInfo(userInfo.getUsername());
        if (sysUserDTOResult.getCode() != CodeMessage.OK.getCode() || Objects.isNull(sysUserDTOResult.getResult())) {
            return Result.fail("获取用户信息失败");
        }


        BroadcastDTO result = broadcastClient.findById(id).orElseThrow();

        BroadcastDetailVO vo = new BroadcastDetailVO();
        BeanCopier.create(BroadcastDTO.class, BroadcastDetailVO.class, false).copy(result, vo, null);
        Long mediaId = result.getMediaId();
        vo.setResourceId(mediaId);

        MediaResourceDTO mediaResourceDTO = mediaResourceService.get(mediaId).orElseThrow();

        List<MediaResourceImageDTO> imageLst = mediaResourceDTO.getImages();
        if (!CollectionUtils.isEmpty(imageLst)) {
            List<MediaResourceImageVo> imgRstLst = imageLst.stream().map(item -> {
                MediaResourceImageVo imgVO = new MediaResourceImageVo();
                String ossId = item.getOssId();
                imgVO.setId(ossId);
                imgVO.setType(item.getType());
                ossClient.getOssFile(ossId).ifPresent(e -> imgVO.setUrl(e.getUrl()));
                return imgVO;
            }).collect(Collectors.toList());
            vo.setThumbLst(imgRstLst);
        }

        //  设置是否可以修改PV权限
        List<String> userPermissions = this.getUserPermissions(userInfo.getUsername());
        if (userPermissions.contains("sys:live:broadcast:virtualAdmin") || (userPermissions.contains("sys:live:broadcast:virtualSingle") && Objects.equals(result.getCreatedBy(), sysUserDTOResult.getResult().getId()))) {
            vo.setVirtualPvModifyFlag(Boolean.TRUE);
            //  设置虚拟阅读数
            Integer pv = mediaPageViewClient.get(result.getMediaId()).map(Long::intValue).orElse(0);
            Integer virtualPv = mediaPageViewClient.getVirtual(result.getMediaId()).map(Long::intValue).orElse(0);
            vo.setVirtualPv(virtualPv);
            vo.setPv(pv);
        } else {
            vo.setVirtualPvModifyFlag(Boolean.FALSE);
        }


        return Result.ok(vo);

    }

    @GetMapping("tenant")
    @ApiOperation("租户列表")
    @PreAuthorize("hasAuthority('sys:live:broadcast:all')")
    public Result<List<SimpleTenantVo>> tenantList() {
        List<SysTenantDTO> rst = sysTenantService.getAllTenants().getResult();
        if (CollectionUtils.isEmpty(rst)) {
            return Result.ok();
        }
        return Result.ok(rst.stream().map(new TenantConverter()::toSimpleVo).collect(Collectors.toList()));
    }

    private static final String CALL_BACK_KEY = "jspyyds";

    @RequestMapping("stop/callback")
    public void stopCallback(@RequestBody Map<String, Object> param) {
        Object type = param.get("event_type");
        if (type instanceof Integer) {
            int typeValue = Integer.parseInt(type.toString());
            if (typeValue == 0) {
                //处理断流事件
                String t = param.get("t").toString();
                String sign = param.get("sign").toString();
                String computeSign = DigestUtils.md5Hex(CALL_BACK_KEY + t);
                if (sign.equalsIgnoreCase(computeSign)) {
                    //秘钥签名核对
                    String streamName = param.get("stream_id").toString();
                    log.info("触发断流事件:" + streamName);
                }
            }
        }
    }

    @GetMapping("/record/video/page")
    @ApiOperation("直播录制视频列表")
    @PreAuthorize("hasAuthority('sys:live:broadcast:manage')")
    public Result<Page<LiveRecordVideoVO>> liveRecordVideoPage(@ApiParam(name = "id", value = "直播ID") @RequestParam Long id, @ApiParam(name = "pageNum", value = "页码") @RequestParam(required = false, defaultValue = "1") Integer pageNum, @ApiParam(name = "pageSize", value = "每页数量") @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Result<PageDTO<LiveRecordVideoDTO>> rst = broadcastClient.liveRecordVideoPage(id, pageNum, pageSize);
        if (!rst.callSuccess()) {
            return Result.ok(Page.pageOf(new ArrayList<LiveRecordVideoDTO>(2), 0, pageNum, pageNum, this::convert, LiveRecordVideoVO.class));
        } else {
            PageDTO<LiveRecordVideoDTO> result = rst.getResult();
            return Result.ok(Page.pageOf(result.getList(), result.getTotal(), result.getPageNum(), result.getPageSize(), this::convert, LiveRecordVideoVO.class));
        }
    }

    @GetMapping("/record/command")
    @ApiOperation("直播录制实时指令")
    @PreAuthorize("hasAuthority('sys:live:broadcast:manage')")
    public Result<Integer> getReplayUrl(@ApiParam(name = "id", value = "直播ID") @RequestParam Long id) {
        return broadcastClient.liveRecordCommand(id);
    }

    @GetMapping("/replayUrl")
    @ApiOperation("获取收录地址")
    public Result<String> liveRecordCommand(@ApiParam(name = "id", value = "直播ID") @RequestParam Long id) {
        return broadcastClient.getReplayUrl(id);
    }


    private LiveRecordVideoVO convert(LiveRecordVideoDTO dto) {
        LiveRecordVideoVO vo = new LiveRecordVideoVO();
        vo.setTitle(dto.getTitle());
        vo.setUrl(dto.getUrl());
        vo.setVideoId(dto.getVideoId());
        return vo;
    }

    /**
     * 获取系统用户对象
     *
     * @return
     */
    private SysUserDTO getSysUserDTO() {
        UserInfo userInfo = ThreadLocalCache.get();
        String username = userInfo.getUsername();
        SysUserDTO result = sysUserService.getUserInfo(username).getResult();
        Result<Long> tenantId = sysUserService.getPrimary(username);
        if (tenantId.callSuccess()) {
            result.setTenantId(tenantId.getResult());
        }
        return result;
    }


    private List<String> getUserPermissions(String username) {
        // 查询用户首选登录系统，使用首选租户，没有则报错
        Result<Long> primaryRes = sysUserService.getPrimary(username);
        if (primaryRes.getCode() != CodeMessage.OK.getCode()) {
            return Collections.emptyList();
        }
        Long primary = primaryRes.getResult();

        // 这里还要查到底登入哪个租户，因为首选租户也有可能被禁用
        Result<SysTenantDTO> res = sysTenantService.getTenantById(primary);
        if (res.getResult().getStatus().equals(0)) {
            // 如果首选租户被禁用，选择一个其他租户
            Result<List<SysTenantDTO>> tenantRes = sysTenantService.getLoginUserTenants(username);
            List<SysTenantDTO> list = tenantRes.getResult().stream().filter(s -> s.getStatus() > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return Collections.emptyList();
            }
            primary = list.get(0).getId();
        }

        // 查询用户权限
        return sysMenuService.getUserPermissions(primary, username).orElse(Collections.emptyList());
    }
}
