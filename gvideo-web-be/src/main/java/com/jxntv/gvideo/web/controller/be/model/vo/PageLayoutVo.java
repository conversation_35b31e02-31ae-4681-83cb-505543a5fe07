package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 页面布局设置
 * @date 2021/05/17 15:36
 */
@Data
@ApiModel(value="PageLayoutVo", description="PageLayoutVo")
public class PageLayoutVo {
    @ApiModelProperty(value = "页面ID", required = true)
    private Long id;
    @ApiModelProperty(value = "名称", required = true)
    private String name;
    @ApiModelProperty(value = "备注", required = true)
    private String remarks;
}
