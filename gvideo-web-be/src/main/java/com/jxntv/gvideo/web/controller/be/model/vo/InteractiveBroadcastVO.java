package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.InteractiveBroadcastClient;
import com.jxntv.gvideo.media.client.dto.InteractiveBroadcastDTO;
import com.jxntv.gvideo.media.client.enums.InteractiveBroadcastStatusEnum;
import com.jxntv.gvideo.web.controller.be.utils.SpringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> Created on 2020/12/31.
 */
@Data
@ApiModel(value = "互动直播页面对象", description = "互动直播页面对象")
public class InteractiveBroadcastVO {
    private Long id;
    @ApiModelProperty(value = "媒体ID")
    private Long mediaId;
    @ApiModelProperty(value = "直播状态")
    private String broadcastStatus;
    @ApiModelProperty(value = "直播状态码")
    private Integer status;
    @ApiModelProperty(value = "主播名称")
    private String title;
    @ApiModelProperty(value = "访问数据:点击总数/人次")
    private String viewStat;
    @ApiModelProperty(value = "评论数据:评论总数/人次")
    private String commentStat;
    @ApiModelProperty(value = "开播时间")
    private String startDate;
    @ApiModelProperty(value = "关播时间")
    private String endDate;
    @ApiModelProperty(value = "是否被ban")
    private Integer ban;
    @ApiModelProperty(value = "封面图")
    private String thumb;

    public static InteractiveBroadcastVO convert(InteractiveBroadcastDTO dto, RedisTemplate<String, Object> redisTemplate) {
        InteractiveBroadcastVO vo = new InteractiveBroadcastVO();
        vo.setTitle(dto.getTitle());
        vo.setBroadcastStatus(dto.getBroadcastStatus());
        vo.setId(dto.getId());
        vo.setMediaId(dto.getMediaId());
        vo.setStatus(dto.getStatus());
        OssClient ossClient = SpringUtils.getBean(OssClient.class);
        String thumb = dto.getThumb();
        Result<OssDTO> ossRst = ossClient.getOssFile(thumb);
        if (ossRst.callSuccess()) {
            vo.setThumb(ossRst.getResult().getUrl());
        }
        vo.setCommentStat(dto.getCommentStat());
        String begin = dto.getBeginDate();
        String end = dto.getEndDate();
        vo.setStartDate(begin);
        vo.setBan(dto.getBan());
        vo.setEndDate(end);
        //未结束直播取缓存数据
        if (!dto.getStatus().equals(InteractiveBroadcastStatusEnum.END.getCode())) {
            Long mediaId = dto.getMediaId();
            if (Objects.nonNull(mediaId)) {
                String key = InteractiveBroadcastClient.REDIS_PREFIX + "click:" + mediaId;
                List<Object> values = redisTemplate.opsForHash().values(key);
                int click = 0;
                for (Object value : values) {
                    if (Objects.nonNull(value)) {
                        click += Integer.parseInt(value.toString());
                    }
                }
                vo.setViewStat(click + "/" + values.size());
            }
        } else {
            vo.setViewStat(dto.getViewStat());
        }
        return vo;
    }
}
