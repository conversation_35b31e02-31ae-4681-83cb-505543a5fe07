package com.jxntv.gvideo.web.controller.be.model.vo.relevancy;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "MediaResourceRelevancyUpdateVO", description = "关联资源-修改")
public class MediaResourceRelevancyUpdateVO {

    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 排序序号
     */
    @ApiModelProperty(value = "序号")
    @NotNull(message = "序号不能为空")
    @Min(value = 0, message = "序号不能小于0")
    private Integer serialNo;

}
