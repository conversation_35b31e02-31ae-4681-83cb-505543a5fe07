package com.jxntv.gvideo.web.controller.be.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "wework")
@Data
@RefreshScope
public class WeWorkConfig {
    private List<String> webhooks;
}
