package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.web.controller.be.model.validate.SeoLinkGroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 推广外链
 */
@Data
public class SeoLinkVo {

    @ApiModelProperty(value = "展示图片ID")
    private String imageId;

    @ApiModelProperty(value = "展示图片")
    @NotEmpty(message = "推广外链展示图片不能为空", groups = SeoLinkGroup.class)
    private String image;

    @ApiModelProperty(value = "外链URL")
    @NotEmpty(message = "推广外链URL不能为空", groups = SeoLinkGroup.class)
    private String url;

    @ApiModelProperty(value = "副标题")
    @NotEmpty(message = "副标题不能为空", groups = SeoLinkGroup.class)
    private String subTitle;
}