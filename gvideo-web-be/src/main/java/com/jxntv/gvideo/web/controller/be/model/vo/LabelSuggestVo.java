package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.media.client.dto.LabelDTO;
import com.jxntv.gvideo.media.client.dto.LabelTypeDTO;
import com.jxntv.gvideo.media.client.dto.SuggestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "LabelSuggestVo", description = "标签自动补全")
public class LabelSuggestVo {
    @ApiModelProperty(value = "id", required = true)
    private String id;
    @ApiModelProperty(value = "value", required = true)
    private String value;

    public static LabelSuggestVo convert(SuggestDTO dto) {
        if (dto.getId().contains("T")) {
            return new LabelSuggestVo(dto.getId(), String.format("%s(类型)", dto.getSuggest()));
        } else {
            return new LabelSuggestVo(dto.getId(), dto.getSuggest());
        }
    }

    public static LabelSuggestVo convert(LabelDTO dto) {
        return new LabelSuggestVo(String.valueOf(dto.getId()), dto.getName());
    }

    public static LabelSuggestVo convert(LabelTypeDTO dto) {
        return new LabelSuggestVo(new StringBuilder("T").append(dto.getId()).toString(),
                new StringBuilder(dto.getName()).append("(类型)").toString());
    }
}
