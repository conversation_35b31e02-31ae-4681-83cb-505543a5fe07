package com.jxntv.gvideo.web.controller.be.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;


/**
 * <AUTHOR> Created on 2021/1/26.
 */
public class PrintUtils {

    public static String pretty(Object obj) {
        String str = new Gson().toJson(obj);
        JsonParser jsonParser = new JsonParser();
        JsonObject jsonObject = jsonParser.parse(str).getAsJsonObject();
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        return gson.toJson(jsonObject);
    }
}
