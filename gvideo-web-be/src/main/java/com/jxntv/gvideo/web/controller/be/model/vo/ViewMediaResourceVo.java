package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.media.client.dto.AlgorithmLabelDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceSpecialExtensionDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.mediaResource.LevelRespVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "ViewMediaResourceVo", description = "内容资源对象(回显使用)")
public class ViewMediaResourceVo {
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "关联文件ID", required = true)
    private Long fileId;
    @ApiModelProperty(value = "显示标题", required = true)
    private String showName;
    @ApiModelProperty(value = "内容类型", required = true)
    private Integer contentType;
    @ApiModelProperty(value = "播放样式", required = true)
    private Integer playStyle;
    @ApiModelProperty(value = "内部标题", required = true)
    private String internalName;
    @ApiModelProperty(value = "生效开始时间", required = true)
    private String effectStartDate;
    @ApiModelProperty(value = "生效结束时间", required = true)
    private String effectEndDate;
    @ApiModelProperty(value = "发布账号id（认证号）", required = true)
    private Long releaseId;
    @ApiModelProperty(value = "发布账号（认证号）")
    private String releaseStr;
    @ApiModelProperty(value = "栏目id", required = true)
    private Long columnId;
    @ApiModelProperty(value = "推荐内容id", required = true)
    private Long recommendId;
    @ApiModelProperty(value = "推荐内容标题", required = true)
    private String recommendTitle;
    @ApiModelProperty(value = "简介", required = true)
    private String introduction;
    @ApiModelProperty(value = "可评论", required = true)
    private Boolean isComment;
    @ApiModelProperty(value = "可搜索", required = true)
    private Boolean isSearch;
    @ApiModelProperty(value = "角标", required = true)
    private Integer cornerMark;
    @ApiModelProperty(value = "角标开始时间", required = true)
    private String cornerMarkStartDate;
    @ApiModelProperty(value = "角标结束时间", required = true)
    private String cornerMarkEndDate;
    @ApiModelProperty(value = "图片集合")
    private List<MediaResourceImageVo> images;
    @ApiModelProperty(value = "内容类型标签集合", required = true)
    private List<String> contentTypeLabels;
    @ApiModelProperty(value = "内容类型标签字符串")
    private String contentTypeLabelStr;
    @ApiModelProperty(value = "内部标签集合", required = true)
    private List<String> internalLabels;
    @ApiModelProperty(value = "内部标签字符串")
    private String internalLabelStr;
    @ApiModelProperty(value = "其他特征标签集合", required = true)
    private List<String> otherFeatureLabels;
    @ApiModelProperty(value = "其他特征标签字符串")
    private String otherFeatureLabelStr;
    @ApiModelProperty(value = "创建人id", required = true)
    private Long createUserId;
    @ApiModelProperty(value = "语音文字")
    private String soundContent;
    @ApiModelProperty(value = "媒体长度")
    private String length;
    @ApiModelProperty(value = "创建时间")
    private String createDate;
    @ApiModelProperty(value = "来源")
    private String source;
    @ApiModelProperty(value = "新闻封面、文字语音资源")
    private String cover;
    @ApiModelProperty(value = "外部分享标题")
    private String outShareTitle;
    @ApiModelProperty(value = "外部分享链接")
    private String outShareUrl;

    // 资源内容补充等级信息
    @ApiModelProperty(value = "内容等级信息")
    private LevelRespVo levelRespVo;

    @ApiModelProperty(value = "算法标签")
    private List<AlgorithmLabelDTO> algorithmLabel;

    @ApiModelProperty(value = "投票信息")
    private VoteVO vote;

    @ApiModelProperty(value = "专题扩展")
    private MediaResourceSpecialExtensionDTO specialExtension;
}
