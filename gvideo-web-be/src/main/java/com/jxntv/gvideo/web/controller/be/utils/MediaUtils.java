package com.jxntv.gvideo.web.controller.be.utils;

import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.PlayStyle;

public class MediaUtils {
    public static int convertMediaType(MediaResourceDTO dto) {
        //资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏
        //内容类型 1 视频自制内容 2 视频电影 3 视频节目 4 音频fm 5 视频剧集 6 活动直播 7 互动直播
        Integer contentType = dto.getContentType();
        //图文和语音一致
        if (contentType == ContentType.PIC_FONT.getCode() || contentType == ContentType.SOUND.getCode()) {
            return contentType;
        }
        //互动直播为竖屏
        if (contentType == ContentType.INTERACTIVE_BROADCAST.getCode()) {
            return 8;
        }
        //活动直播 1横2竖
        if (contentType == ContentType.LIVE_BROADCAST.getCode()) {
            return dto.getPlayStyle() == 1 ? 5 : 6;
        }
        //文章
        if (contentType == ContentType.NEWS.getCode()) {
            //文章：11 文章大图/三图 12 文章左文右图 13 外链
            //playstyle: 1-标题模式、2-左文右图模式、3-三图模式、4-大图模式、5-外链模式
            if (dto.getPlayStyle() == PlayStyle.SHORT.getCode()) {
                return 12;
            } else if (dto.getPlayStyle() == PlayStyle.OUTER_LINK_MODE.getCode()) {
                return 13;
            } else {
                return 11;
            }
        }
        //专题，都使用文章左文右图样式
        if (contentType == ContentType.SPECIAL.getCode()) {
            return 12;
        }
        boolean isVideo = ContentType.isVideo(contentType);
        boolean isLong = PlayStyle.isLong(dto.getPlayStyle());
        if (isVideo) {
            return isLong ? 1 : 2;
        } else {
            return isLong ? 3 : 4;
        }
    }
}
