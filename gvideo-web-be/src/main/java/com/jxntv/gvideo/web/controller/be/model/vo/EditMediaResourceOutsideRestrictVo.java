package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Data
public class EditMediaResourceOutsideRestrictVo {
    @ApiModelProperty("资源类型 1.视频 ")
    @NotNull(message = "资源类型不能为空")
    private Integer resourceType;

    @ApiModelProperty("站外观看限制类型 0-不限制 1-限制")
    @NotNull(message = "站外观看限制类型不能为空")
    @Min(value = 0, message = "站外观看限制类型不能小于0")
    @Max(value = 1, message = "站外观看限制类型不能大于1")
    private Integer restrictType;

    @ApiModelProperty("站外观看限制秒数")
    @Min(value = 0, message = "限制秒数不能小于0")
    private Integer restrictSeconds;
}
