package com.jxntv.gvideo.web.controller.be.client.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/6
 * Email: <EMAIL>
 */
@NoArgsConstructor
@Data
public class ShopGoodsDTO {

    @JsonProperty("goods_id")
    private Long goodsId;
    @JsonProperty("category_id")
    private String categoryId;
    @JsonProperty("goods_name")
    private String goodsName;
    @JsonProperty("sn")
    private String sn;
    @JsonProperty("price")
    private BigDecimal price;
    @JsonProperty("weight")
    private Integer weight;
    @JsonProperty("intro")
    private String intro;
    @JsonProperty("goods_transfee_charge")
    private Integer goodsTransfeeCharge;
    @JsonProperty("template_id")
    private String templateId;
    @JsonProperty("market_enable")
    private Integer marketEnable;
    @JsonProperty("disabled")
    private Integer disabled;
    @JsonProperty("is_auth")
    private Integer isAuth;
    @JsonProperty("enable_quantity")
    private Integer enableQuantity;
    @JsonProperty("quantity")
    private Integer quantity;
    @JsonProperty("seller_id")
    private String sellerId;
    @JsonProperty("seller_name")
    private String sellerName;
    @JsonProperty("sku_list")
    private Object skuList;
    @JsonProperty("thumbnail")
    private String thumbnail;
    @JsonProperty("last_modify")
    private String lastModify;
    @JsonProperty("comment_num")
    private Integer commentNum;
    @JsonProperty("grade")
    private Integer grade;
    @JsonProperty("mobile_intro")
    private String mobileIntro;
    @JsonProperty("goods_video")
    private String goodsVideo;
    @JsonProperty("category_name")
    private String categoryName;
    @JsonProperty("goods_off")
    private Integer goodsOff;
    @JsonProperty("param_list")
    private List<ParamListDTO> paramList;
    @JsonProperty("gallery_list")
    private List<GalleryListDTO> galleryList;
    @JsonProperty("page_title")
    private String pageTitle;
    @JsonProperty("meta_keywords")
    private String metaKeywords;
    @JsonProperty("meta_description")
    private String metaDescription;

    @NoArgsConstructor
    @Data
    public static class ParamListDTO {
        @JsonProperty("params")
        private List<ParamsDTO> params;
        @JsonProperty("group_name")
        private String groupName;
        @JsonProperty("group_id")
        private String groupId;

        @NoArgsConstructor
        @Data
        public static class ParamsDTO {
            @JsonProperty("id")
            private Object id;
            @JsonProperty("goods_id")
            private Object goodsId;
            @JsonProperty("param_id")
            private String paramId;
            @JsonProperty("param_name")
            private String paramName;
            @JsonProperty("param_value")
            private String paramValue;
            @JsonProperty("param_type")
            private Integer paramType;
            @JsonProperty("options")
            private String options;
            @JsonProperty("required")
            private Integer required;
            @JsonProperty("group_id")
            private String groupId;
            @JsonProperty("is_index")
            private Integer isIndex;
            @JsonProperty("option_list")
            private List<String> optionList;
        }
    }

    @NoArgsConstructor
    @Data
    public static class GalleryListDTO {
        @JsonProperty("img_id")
        private String imgId;
        @JsonProperty("goods_id")
        private String goodsId;
        @JsonProperty("thumbnail")
        private String thumbnail;
        @JsonProperty("small")
        private String small;
        @JsonProperty("big")
        private String big;
        @JsonProperty("original")
        private String original;
        @JsonProperty("tiny")
        private Object tiny;
        @JsonProperty("isdefault")
        private Integer isdefault;
        @JsonProperty("sort")
        private Object sort;
    }
}
