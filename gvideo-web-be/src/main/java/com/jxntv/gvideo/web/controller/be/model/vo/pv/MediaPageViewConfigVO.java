package com.jxntv.gvideo.web.controller.be.model.vo.pv;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("资源阅读数视图对象")
public class MediaPageViewConfigVO {

    @ApiModelProperty("配置ID")
    private Long id;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("类型：0-全局，1-规则匹配，2-单个配置")
    private Integer type;

    @ApiModelProperty("状态：0-禁用，1-启用")
    private Integer status;

    @ApiModelProperty("外显PV")
    private Integer pv;

    @ApiModelProperty("实际PV")
    private Integer realPv;

    @ApiModelProperty("算法设置")
    private List<MediaPageViewConfigFigureSettingVO> figureSettings;

    @ApiModelProperty("内容资源类型设置")
    private List<MediaPageViewConfigContentSettingVO> contentTypeSettings;

    @ApiModelProperty("内容发布账号设置")
    private List<MediaPageViewConfigContentSettingVO> contentReleaseSettings;

    @ApiModelProperty("单个内容设置")
    private List<MediaPageViewConfigContentSettingVO> singleContentSettings;


}
