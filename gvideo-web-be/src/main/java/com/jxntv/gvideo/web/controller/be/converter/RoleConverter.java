package com.jxntv.gvideo.web.controller.be.converter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jxntv.gvideo.web.controller.be.model.vo.RoleVo;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import com.jxntv.gvideo.user.client.SysRoleClient;
import com.jxntv.gvideo.user.client.dto.SysRoleDTO;

/**
 *
 * Created on 2020-02-14
 */
@Component
public class RoleConverter {

    @Autowired
    private SysRoleClient sysRoleClient;

    public RoleVo toVo(SysRoleDTO dto) {
        RoleVo vo = new RoleVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setDescribe(dto.getNote());
        vo.setTotalFuncNum(sysRoleClient.getFuncCountByRole(dto.getId()).getResult());
        vo.setTotalUserNum(sysRoleClient.getUserCountByRole(dto.getId()).getResult());
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        return vo;
    }

}
