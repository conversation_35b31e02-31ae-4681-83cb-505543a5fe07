package com.jxntv.gvideo.web.controller.be.model.vo.kingkong;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/25
 * Email: <EMAIL>
 */
@Data
@ApiModel("金刚位编辑对象")
public class KingKongPositionEditVO {

    @ApiModelProperty("金刚位标题")
    private String name;

    @ApiModelProperty("金刚位图片ID")
    private String iconId;

    @ApiModelProperty("金刚位类型:0-原生页面菜单，1-原生页面内容，2-H5页面 3-小程序")
    private Integer type;

    @ApiModelProperty("原生内容类型：0-文章，1-圈子，2-话题，3-动态，4-直播")
    private Integer contentType;

    @ApiModelProperty("跳转原生内容ID：type=0时菜单id，type=1内容id")
    private Long contentId;

    @ApiModelProperty("跳转原生内容名称")
    private String contentName;

    @ApiModelProperty("金刚位跳转链接")
    private String linkUrl;

    @ApiModelProperty("补充URL，兼容H5页面")
    private String extraUrl;


}
