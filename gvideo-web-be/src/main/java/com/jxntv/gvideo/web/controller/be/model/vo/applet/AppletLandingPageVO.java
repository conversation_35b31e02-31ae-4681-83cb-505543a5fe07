package com.jxntv.gvideo.web.controller.be.model.vo.applet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2022/12/15 10:46
 */
@Data
public class AppletLandingPageVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 落地页名称
     */
    @ApiModelProperty("落地页名称")
    private String landingPageName;

    /**
     * 课程图片
     */
    @ApiModelProperty("落地页图片")
    private List<CourseImageVO> landingPageImage;

    /**
     * 微信分享图片
     */
    @ApiModelProperty("落地页微信分享图片")
    private CourseImageVO weixinShareImage;

    /**
     * 跳转类型，1：导师 2：订单确认
     */
    @ApiModelProperty("跳转类型，1：导师 2：订单")
    private Integer jumpType;

    /**
     * 抖音页面分享ID
     */
    @ApiModelProperty("抖音分享ID")
    private String douyinShareId;

    /**
     * 导师JID，jump_type=2时存在
     */
    @ApiModelProperty("导师JID")
    private Long mentorJid;

    @ApiModelProperty("微信页面路径")
    private String weixinPath;

    @ApiModelProperty("抖音页面路径")
    private String douyinPath;

}
