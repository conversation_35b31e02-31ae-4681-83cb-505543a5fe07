package com.jxntv.gvideo.web.controller.be.utils;

import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR> Created on 2021/3/8.
 */
public class NumberUtils {
    public static String random(int size) {
        StringBuilder random = new StringBuilder();
        for (int i = 1; i <= size; i++) {
            random.append(ThreadLocalRandom.current().nextInt(10));
        }
        return random.toString();
    }
}
