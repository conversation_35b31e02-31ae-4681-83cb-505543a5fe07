package com.jxntv.gvideo.web.controller.be.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "MediaResourceVo", description = "内容资源列表")
public class MediaResourceVo {

    @ApiModelProperty(value = "ID")
    private Long id;
    @ApiModelProperty(value = "文件类型")
    private String fileType;
    @ApiModelProperty(value = "内部标题")
    private String internalName;
    @ApiModelProperty(value = "显示标题")
    private String showName;
    @ApiModelProperty(value = "权重")
    private Integer weight;
    @ApiModelProperty(value = "认证号")
    private String certification;
    @ApiModelProperty(value = "所属MCN")
    private String tenant;
    @ApiModelProperty(value = "处理人(备注)")
    private String updateUser;
    @ApiModelProperty(value = "创建人")
    private String createUser;
    @ApiModelProperty(value = "状态")
    private String status;
    @ApiModelProperty(value = "内容类型")
    private Integer contentType;
    @ApiModelProperty(value = "专题/合集 内容数据")
    private Integer contentCount;
    @ApiModelProperty(value = "内容类型文本")
    private String contentTypeStr;
    @ApiModelProperty(value = "资源页签")
    private Integer mediaTab;
    @ApiModelProperty(value = "发布类型")
    private Integer releaseType;
    @ApiModelProperty(value = "发布类型")
    private String releaseTypeStr;
    @ApiModelProperty(value = "发布作者名称")
    private String nickName;
    @ApiModelProperty(value = "内容标签")
    private String labelStr;
    @ApiModelProperty(value = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty("选择栏目id")
    private Long columnId;

    @ApiModelProperty("选择栏目名称")
    private String columnName;

    @ApiModelProperty("节目id")
    private Long programId;

    @ApiModelProperty("节目名称")
    private String programName;

    @ApiModelProperty("摘要")
    private String introduction;
}
