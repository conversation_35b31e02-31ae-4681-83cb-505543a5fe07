package com.jxntv.gvideo.web.controller.be.model.vo.recommend;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AppRecommendListResourceUpdateVO", description = "app推荐列表资源-修改")
public class AppRecommendListResourceUpdateVO implements Serializable {

    @ApiModelProperty(value = "ID")
    @NotNull(message = "ID不能为空")
    private Long id;
    /**
     * 资源ID
     */
    @ApiModelProperty(value = "资源ID")
    @NotNull(message = "资源ID不能为空")
    private Long resourceId;
    /**
     * 内容类型：0-H5、1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云、12-新闻、13-专题
     */
    @ApiModelProperty(value = "内容类型：0-H5、1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云、12-新闻、13-专题")
    private Integer contentType;
    /**
     * 资源标题
     */
    @ApiModelProperty(value = "资源标题")
    private String title;

    /**
     * icon, OSSID
     */
    @ApiModelProperty(value = "封面, OSSID")
    private String cover;

    /**
     * 顺序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "排序不能为空")
    @Min(value = 0, message = "排序不能小于0")
    private Integer sort;
}
