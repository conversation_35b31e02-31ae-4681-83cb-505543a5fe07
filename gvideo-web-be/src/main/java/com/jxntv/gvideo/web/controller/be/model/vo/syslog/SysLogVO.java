package com.jxntv.gvideo.web.controller.be.model.vo.syslog;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * <AUTHOR>
 */
@Data
@ApiModel("运营端操作日志对象")
public class SysLogVO {
    @ApiModelProperty("日志分组")
    private Long id;
    @ApiModelProperty("日志分组")
    private String logGroup;
    @ApiModelProperty("主题")
    private String subject;
    @ApiModelProperty("操作用户名称")
    private String createUserName;
    @ApiModelProperty("操作用户账号")
    private String createUser;
    @ApiModelProperty("api请求参数")
    private String methodParam;
    @ApiModelProperty("api返回结果")
    private String methodResult;
    @ApiModelProperty("操作时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

}
