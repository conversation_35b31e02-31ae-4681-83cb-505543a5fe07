package com.jxntv.gvideo.web.controller.be.sensors;

import com.jxntv.gvideo.web.controller.be.login.thread.UserInfo;
import lombok.Data;

import java.util.Map;

/**
 * 神策上报业务对象
 *
 * <AUTHOR>
 * @date 2022/1/21
 * Email: <EMAIL>
 */
@Data
public class SensorsReportBO {

    /**
     * 上报事件类型
     */
    private SensorsType type;
    /**
     * 当前用户信息
     */
    private UserInfo userInfo;
    /**
     * 请求参数
     */
    private Map<String, Object> params;
    /**
     * 返回结果
     */
    private Object retVal;

}
