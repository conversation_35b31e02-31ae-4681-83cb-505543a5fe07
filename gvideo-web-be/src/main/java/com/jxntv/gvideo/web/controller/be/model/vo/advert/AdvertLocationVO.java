package com.jxntv.gvideo.web.controller.be.model.vo.advert;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("投放位置")
public class AdvertLocationVO {

    @ApiModelProperty("投放位置id")
    private Long locationId;

    @ApiModelProperty("投放位置名称")
    private String locationName;

    @ApiModelProperty("投放位置类型：0-菜单页面，1-社区")
    private Integer locationType;


}
