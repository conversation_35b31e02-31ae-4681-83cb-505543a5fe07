package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.common.model.OptionsDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.group.sdk.client.GroupGatherActivityClient;
import com.jxntv.gvideo.group.sdk.dto.activity.*;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateUserDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupBlindDateEducationEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupBlindDateGenderEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupBlindDateMaritalEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupBlindDatePostEnum;
import com.jxntv.gvideo.web.controller.be.client.GroupBlindDateService;
import com.jxntv.gvideo.web.controller.be.model.vo.ImageVO;
import com.jxntv.gvideo.web.controller.be.model.vo.gather.activity.*;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupGatherActivityConverter {

    @Resource
    private OssClient ossClient;

    @Resource
    private GroupBlindDateService groupBlindDateService;

    @Resource
    private GroupGatherActivityClient groupGatherActivityClient;

    public GroupGatherActivityVO convertList(GroupGatherActivityDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherActivityVO vo = new GroupGatherActivityVO();
        vo.setId(dto.getId());
        vo.setTitle(dto.getTitle());
        vo.setStartDate(dto.getStartDate());
        vo.setEndDate(dto.getEndDate());
        vo.setJoinStartDate(dto.getJoinStartDate());
        vo.setJoinEndDate(dto.getJoinEndDate());
        vo.setStatus(dto.getStatus());
        vo.setWeight(dto.getWeight());
        vo.setCostFlag(dto.getCostFlag());
        vo.setType(dto.getType());
        return vo;
    }

    public GroupGatherActivityInfoVO convertInfo(GroupGatherActivityDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }

        GroupGatherActivityInfoVO vo = new GroupGatherActivityInfoVO();
        vo.setId(dto.getId());
        vo.setTitle(dto.getTitle());
        vo.setStartDate(dto.getStartDate());
        vo.setEndDate(dto.getEndDate());
        vo.setJoinStartDate(dto.getJoinStartDate());
        vo.setJoinEndDate(dto.getJoinEndDate());
        vo.setAddress(dto.getAddress());
        vo.setCostFlag(dto.getCostFlag());
        vo.setCost(dto.getCost());
        vo.setMemberFrom(dto.getMemberFrom());
        vo.setMemberTo(dto.getMemberTo());
        vo.setIntroduction(dto.getIntroduction());
        vo.setPushFlag(dto.getPushFlag());
        vo.setPushContent(dto.getPushContent());
        vo.setStatus(dto.getStatus());
        vo.setWeight(dto.getWeight());

        if (!CollectionUtils.isEmpty(dto.getImageList())) {
            Result<List<OssDTO>> result = ossClient.listResizeByIds(dto.getImageList());
            if (result.callSuccess() && !CollectionUtils.isEmpty(result.getResult())) {
                List<ImageVO> list = new ArrayList<>(result.getResult().size());
                result.getResult().stream().forEach(image -> {
                    list.add(new ImageVO(image.getUuid(), image.getUrl()));
                });
                vo.setImageList(list);
            }
        }
        vo.setRedBeansFlag(dto.getRedBeansFlag());
        vo.setRedBeansAmount(dto.getRedBeansAmount());
        vo.setTagId(dto.getTagId());
        vo.setLatitude(dto.getLatitude());
        vo.setLongitude(dto.getLongitude());
        vo.setType(dto.getType());

        if (Objects.nonNull(dto.getTagId())) {
            List<OptionsDTO> tagList = groupGatherActivityClient.queryTagList(dto.getGroupId(), dto.getGroupGatherId()).orElse(null);
            if (!CollectionUtils.isEmpty(tagList)){
                tagList.stream().filter(e -> Objects.equals(e.getId(),dto.getTagId())).findFirst().ifPresent(e -> vo.setTagName(e.getName()));
            }
        }
        return vo;
    }

    public GroupGatherActivityInsertDTO convertInsert(GroupGatherActivityInsertVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        GroupGatherActivityInsertDTO dto = new GroupGatherActivityInsertDTO();
        dto.setGroupId(vo.getGroupId());
        dto.setGroupGatherId(vo.getGroupGatherId());
        dto.setTitle(vo.getTitle());
        dto.setStartDate(vo.getStartDate());
        dto.setEndDate(vo.getEndDate());
        dto.setJoinStartDate(vo.getJoinStartDate());
        dto.setJoinEndDate(vo.getJoinEndDate());
        dto.setAddress(vo.getAddress());
        dto.setCostFlag(vo.getCostFlag());
        dto.setCost(this.formatCost(vo.getCostFlag(), vo.getCost()));
        dto.setMemberFrom(vo.getMemberFrom());
        dto.setMemberTo(vo.getMemberTo());
        dto.setIntroduction(vo.getIntroduction());
        dto.setPushFlag(vo.getPushFlag());
        dto.setPushContent(vo.getPushContent());
        dto.setWeight(vo.getWeight());
        dto.setImageList(vo.getImageList());
        dto.setRedBeansAmount(vo.getRedBeansAmount());
        dto.setRedBeansFlag(vo.getRedBeansFlag());
        dto.setType(vo.getType());
        dto.setLatitude(vo.getLatitude());
        dto.setLongitude(vo.getLongitude());
        dto.setTagId(vo.getTagId());
        dto.setTagName(vo.getTagName());
        return dto;
    }



    public GroupGatherActivityUpdateDTO convertUpdate(GroupGatherActivityUpdateVO vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        GroupGatherActivityUpdateDTO dto = new GroupGatherActivityUpdateDTO();
        dto.setId(vo.getId());
        dto.setTitle(vo.getTitle());
        dto.setStartDate(vo.getStartDate());
        dto.setEndDate(vo.getEndDate());
        dto.setJoinStartDate(vo.getJoinStartDate());
        dto.setJoinEndDate(vo.getJoinEndDate());
        dto.setAddress(vo.getAddress());
        dto.setCostFlag(vo.getCostFlag());
        dto.setCost(this.formatCost(vo.getCostFlag(), vo.getCost()));
        dto.setMemberFrom(vo.getMemberFrom());
        dto.setMemberTo(vo.getMemberTo());
        dto.setIntroduction(vo.getIntroduction());
        dto.setWeight(vo.getWeight());
        dto.setImageList(vo.getImageList());
        dto.setLatitude(vo.getLatitude());
        dto.setLongitude(vo.getLongitude());
        dto.setTagId(vo.getTagId());
        dto.setTagName(vo.getTagName());
        return dto;
    }

    public GroupGatherActivityMemberVO convertMemberList(GroupGatherActivityMemberDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherActivityMemberVO vo = new GroupGatherActivityMemberVO();
        vo.setId(dto.getId());
        vo.setEnrollDate(dto.getEnrollDate());
        vo.setAuditStatus(dto.getAuditStatus());
        vo.setAuditDate(dto.getAuditDate());
        vo.setPayState(dto.getPayState());
        vo.setRefundState(dto.getRefundState());
        vo.setAuditUserName(dto.getAuditUserName());
        vo.setRemarks(dto.getRemarks());
        vo.setMobile(dto.getMobile());
        vo.setName(dto.getName());
        vo.setGender(GroupBlindDateGenderEnum.toStr(dto.getGender()));
        vo.setMarital(GroupBlindDateMaritalEnum.toStr(dto.getMarital()));
        vo.setEducation(GroupBlindDateEducationEnum.toStr(dto.getEducation()));
        vo.setYear(Objects.nonNull(dto.getBirthday()) ? getYear(dto.getBirthday()) : "");
        vo.setHeight(dto.getHeight());
        vo.setPostType(GroupBlindDatePostEnum.toStr(dto.getPostType()));
        vo.setRegion(Objects.isNull(dto.getProvinceName()) && Objects.isNull(dto.getCityName()) ? "" : dto.getProvinceName() + dto.getCityName());
        vo.setCompany(dto.getCompanyName());

        if (StringUtils.hasText(dto.getPhotoOssId())) {
            Result<OssDTO> ossResult = ossClient.getOssResize(dto.getPhotoOssId());
            if (ossResult.callSuccess() && Objects.nonNull(ossResult.getResult()) && Objects.nonNull(ossResult.getResult().getUrl())) {
                vo.setPhotoUrl(ossResult.getResult().getUrl());
            }
        }
        GroupBlindDateUserDTO groupBlindDateUserDTO = groupBlindDateService.simpleUserInfoByJid(dto.getJid()).orElse(null);
        if (Objects.nonNull(groupBlindDateUserDTO)) {
            vo.setCompanyVerifyStatus(groupBlindDateUserDTO.getCompanyVerifyStatus());
            vo.setDailyPhotosAudit(groupBlindDateUserDTO.getDailyPhotosAudit());
            vo.setBlindDateUserId(groupBlindDateUserDTO.getId());
        }
        vo.setManifesto(dto.getManifesto());
        vo.setPairedJid(dto.getPairedJid());
        vo.setPairedFlag(dto.getPairedFlag());
        vo.setPairedTime(dto.getPairedTime());
        if (Objects.nonNull(dto.getPairedJid())){
            GroupBlindDateUserDTO pairedUserDTO = groupBlindDateService.simpleUserInfoByJid(dto.getPairedJid()).orElse(null);
            if (Objects.nonNull(pairedUserDTO)) {
                vo.setPairedNickname(pairedUserDTO.getNickname());
                vo.setPairedName(pairedUserDTO.getName());
            }
        }
        return vo;
    }

    String getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR) + "";
    }

    BigDecimal formatCost(int costFlag, BigDecimal cost) {
        if (costFlag == 0) {
            return new BigDecimal("0.00");
        } else {
            return cost.setScale(2, BigDecimal.ROUND_HALF_UP);
        }
    }


    public GroupGatherActivityEnrollMemberExportVO convertMemberExport(GroupGatherActivityEnrollMemberExportDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherActivityEnrollMemberExportVO vo = new GroupGatherActivityEnrollMemberExportVO();

        vo.setMobile(AESUtil.AESCBCDecode(dto.getMobile()));
        vo.setName(dto.getName());
        vo.setGender(GroupBlindDateGenderEnum.toStr(dto.getGender()));
        vo.setMarital(GroupBlindDateMaritalEnum.toStr(dto.getMarital()));
        vo.setEducation(GroupBlindDateEducationEnum.toStr(dto.getEducation()));
        vo.setYear(dto.getYear());
        vo.setHeight(String.valueOf(dto.getHeight()));
        vo.setPostType(GroupBlindDatePostEnum.toStr(dto.getPostType()));
        vo.setRegion(Objects.isNull(dto.getProvinceName()) && Objects.isNull(dto.getCityName()) ? "" : dto.getProvinceName() + dto.getCityName());
        vo.setCompanyName(dto.getCompanyName());
        vo.setDailyPhotosAudit(Integer.valueOf(0).equals(dto.getDailyPhotosAudit()) ? "审核中" : (Integer.valueOf(1).equals(dto.getDailyPhotosAudit()) ? "审核通过" : "审核不通过"));
        vo.setCompanyVerifyStatus(Integer.valueOf(0).equals(dto.getCompanyVerifyStatus()) ? "审核中" : (Integer.valueOf(1).equals(dto.getCompanyVerifyStatus()) ? "审核通过" : "审核不通过"));
        vo.setEnrollDate(DateUtils.format(dto.getEnrollDate()));
        vo.setAuditStatus(Integer.valueOf(0).equals(dto.getAuditStatus()) ? "待审核" : (Integer.valueOf(1).equals(dto.getAuditStatus()) ? "同意" : "已拒绝"));
        vo.setAuditDate(DateUtils.format(dto.getAuditDate()));

        return vo;
    }

}
