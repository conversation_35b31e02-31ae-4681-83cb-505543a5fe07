package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.group.sdk.dto.GroupPrivacyAgreementDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.GroupPrivacyAgreementInfoVO;
import com.jxntv.gvideo.web.controller.be.model.vo.GroupPrivacyAgreementVO;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public class GroupPrivacyAgreementConverter {

    public GroupPrivacyAgreementInfoVO convert(GroupPrivacyAgreementDTO dto) {
        if (Objects.isNull(dto)){
            return  null;
        }
        GroupPrivacyAgreementInfoVO vo = new GroupPrivacyAgreementInfoVO();
        vo.setTitle(dto.getTitle());
        vo.setContent(dto.getContent());
        return vo;
    }

    public GroupPrivacyAgreementDTO convert(GroupPrivacyAgreementVO vo) {
        GroupPrivacyAgreementDTO dto = new GroupPrivacyAgreementDTO();
        dto.setGroupId(vo.getGroupId());
        dto.setTitle(vo.getTitle());
        dto.setContent(vo.getContent());
        dto.setExtra(vo.getExtra());
        return dto;
    }

}
