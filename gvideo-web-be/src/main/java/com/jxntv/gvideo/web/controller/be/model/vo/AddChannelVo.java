package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class AddChannelVo extends ThreeLabelVo {
    @ApiModelProperty(value = "频道名称", required = true)
    private String name;
    @ApiModelProperty(value = "频道备注", required = true)
    private String remark;
    @ApiModelProperty(value = "频道类型", required = true)
    private Integer type;
    @ApiModelProperty(value = "资源类型", required = true)
    private List<String> resourceTypes;
    @ApiModelProperty(value = "是否H5页面类型", required = true)
    private boolean h5Flag;
    @ApiModelProperty(value = "H5页面地址", required = true)
    private String url;
}
