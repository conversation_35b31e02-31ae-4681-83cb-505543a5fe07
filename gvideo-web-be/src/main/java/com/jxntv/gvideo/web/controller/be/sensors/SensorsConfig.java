package com.jxntv.gvideo.web.controller.be.sensors;

import com.jxntv.gvideo.common.utils.JsonUtils;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.SuperPropertiesRecord;
import com.sensorsdata.analytics.javasdk.consumer.FastBatchConsumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.util.Objects;

/**
 * 神策初始化
 *
 * <AUTHOR> Created on 2021/7/14.
 */
@Slf4j
@RefreshScope
@Configuration
public class SensorsConfig {
    @Value("${sensors.serverUrl}")
    private String serverUrl;

    @Bean
    public SensorsAnalytics sensorsAnalytics() {
        log.info("初始化神策：serverUrl = {}", serverUrl);
        FastBatchConsumer fastBatchConsumer = new FastBatchConsumer(serverUrl, true, 50, 6000, 1, 5,
                failedData -> log.info("【埋点上报】fastBatchConsumer上报异常：{}", JsonUtils.toJson(failedData)));

        SensorsAnalytics sensorsAnalytics = new SensorsAnalytics(fastBatchConsumer);
        SuperPropertiesRecord superPropertiesRecord = propertiesRecord();
        if (Objects.nonNull(superPropertiesRecord)) {
            sensorsAnalytics.registerSuperProperties(superPropertiesRecord);
        }

        return sensorsAnalytics;
    }


    private SuperPropertiesRecord propertiesRecord() {
        SuperPropertiesRecord propertiesRecord = null;
        try {
            InetAddress address = InetAddress.getLocalHost();
            propertiesRecord = SuperPropertiesRecord.builder()
                    .addProperty("$ip", address.getHostAddress())
                    .addProperty("$receive_time", System.currentTimeMillis())
                    .addProperty("AppName", "今视频")
                    .build();
        } catch (Exception e) {
            log.error("神策配置失败", e);
        }
        return propertiesRecord;
    }

}
