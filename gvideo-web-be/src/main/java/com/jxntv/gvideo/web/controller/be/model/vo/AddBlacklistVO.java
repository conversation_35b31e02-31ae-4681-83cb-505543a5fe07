package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Created on 2021/5/11.
 */
@Data
@ApiModel("拉黑、禁言对象")
public class AddBlacklistVO {
    @ApiModelProperty("圈子id")
    @NotNull(message = "社区ID不能为空")
    private Long groupId;
    @ApiModelProperty("用户jid")
    @NotNull(message = "用户jid不能为空")
    private Long jid;
    @ApiModelProperty("类型1拉黑2禁言")
    @NotNull(message = "类型不能为空")
    private Integer type;
    @ApiModelProperty("手机号")
    @NotNull(message = "手机号不能为空")
    private String mobile;
    @ApiModelProperty("理由")
    private String reason;
}
