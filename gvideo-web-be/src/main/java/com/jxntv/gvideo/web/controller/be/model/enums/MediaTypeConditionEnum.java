package com.jxntv.gvideo.web.controller.be.model.enums;

/**
 * Created on 2020-04-01
 */
public enum MediaTypeConditionEnum {

    MEDIA_VIDEO(1, "视频-自制内容"),
    MEDIA_MOVIE(2, "视频-影视"),
    MEDIA_PROGRAM(3, "视频-节目"),
    MEDIA_FM(4, "音频-FM"),
    MEDIA_SERIES(5, "视频-剧集"),
    LIVE_BROADCAST(6, "活动直播"),
    INTERACTIVE_BROADCAST(7, "互动直播"),
    PIC_FONT(9, "图文资源"),
    SOUND(10, "语音资源"),
    GAN_YUN(11, "赣云-节目"),
    NEWS(12, "文章"),
    SPECIAL(13, "专题"),
    GAN_YUN_VIDEO(14, "赣云-视频"),
    LIVE_COLLECTION(15, "直播合辑"),
    NEW_SPECIAL(16, "新专题"),
    ;

    private Integer id;
    private String name;

    MediaTypeConditionEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}
