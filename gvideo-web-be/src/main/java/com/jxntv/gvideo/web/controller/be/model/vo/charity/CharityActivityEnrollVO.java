package com.jxntv.gvideo.web.controller.be.model.vo.charity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class CharityActivityEnrollVO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 今视频唯一JID
     */
    private Long jid;

    /**
     * 参赛作品名称
     */
    private String entryName;

    /**
     * 作品简介
     */
    private String entryIntro;

    /**
     * 单位
     */
    private String unit;

    /**
     * 姓名
     */
    private String name;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 证件号码
     */
    private String cardNo;

    /**
     * 性别
     */
    private String gender;

    /**
     * 邮寄地址
     */
    private String mailAddr;

    /**
     * 指导老师
     */
    private String instructor;

    /**
     * 封面图阿里云ID
     */
    private String coverOssId;

    /**
     * 封面图阿里云ID
     */
    private String coverOssUrl;

    /**
     * 视频阿里云ID
     */
    private String videoOssUrl;

    /**
     * 是否使用素材 0-未使用 1-使用
     */
    private Integer materialFlag;

    /**
     * 素材来源
     */
    private String materialSource;

    /**
     * 承诺人
     */
    private String accepter;

    /**
     * 报名时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime enrollDate;

    /**
     * 审核状态 0-未审核 1-通过 2-拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditDate;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人账号
     */
    private String auditUserName;

    /**
     * 审核备注（审核不通过原因）
     */
    private String remarks;

    /**
     * 证书ossID
     */
    private String certificateOssId;

    /**
     * 证书URL
     */
    private String certificateOssUrl;

}