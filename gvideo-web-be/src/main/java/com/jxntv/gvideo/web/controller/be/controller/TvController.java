package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.media.client.TvChannelClient;
import com.jxntv.gvideo.media.client.TvColumnClient;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.tv.*;
import com.jxntv.gvideo.media.client.enums.EnableEnum;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import com.jxntv.gvideo.web.controller.be.client.TvOptionLogService;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.TvConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.vo.tv.*;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequestMapping("/api/tv/")
@RestController
public class TvController {

    @Resource
    private TvConverter tvConverter;
    @Resource
    private TvChannelClient tvChannelClient;
    @Resource
    private TvProgramClient tvProgramClient;
    @Resource
    private TvColumnClient tvColumnClient;
    @Resource
    private SysUserClient sysUserService;
    @Autowired
    private TvOptionLogService tvOptionLogService;


    @PostMapping("/channel")
    @PreAuthorize("hasAuthority('sys:channel:create')")
    public Result<Long> addChannel(@RequestBody TvChannelAddVO addVO) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvChannelDTO dto = tvConverter.convert(addVO);
        dto.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvChannelClient.save(dto);
    }

    @GetMapping("/channel/{channelId}")
    @PreAuthorize("hasAuthority('sys:channel:detail')")
    public Result<TvChannelVO> getChannel(@PathVariable("channelId") Long channelId) {
        return tvChannelClient.getById(channelId).map(tvConverter::convert);
    }

    @PutMapping("/channel/{channelId}")
    @PreAuthorize("hasAuthority('sys:channel:modify')")
    public Result<Void> updateChannel(@PathVariable("channelId") Long channelId,
                                      @RequestBody TvChannelVO channelVo) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvChannelDTO dto = tvConverter.convert(channelVo);
        dto.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvChannelClient.update(channelId, dto);
    }

    @PutMapping("/channel/{channelId}/status")
    @PreAuthorize("hasAuthority('sys:channel:modify')")
    public Result<Void> updateChannelStatus(@PathVariable("channelId") Long channelId,
                                            @RequestParam Integer status) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        return tvChannelClient.modifyStatus(channelId, status, userInfo.getId(), userInfo.getName());
    }

    @GetMapping("/channel/page")
    @PreAuthorize("hasAuthority('sys:tv:manage')")
    public Result<Page<TvChannelVO>> channelPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                                 @RequestParam(defaultValue = "10") Integer pageSize,
                                                 @RequestParam(required = false) Long channelId,
                                                 @RequestParam(required = false) String channelNameLike,
                                                 @RequestParam(required = false) Integer type) {

        TvChannelSearchDTO searchDTO = new TvChannelSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setChannelNameLike(channelNameLike);
        if (Objects.nonNull(channelId)) {
            searchDTO.setIds(Collections.singletonList(channelId));
        }
        if (Objects.nonNull(type)) {
            searchDTO.setTypes(Arrays.asList(type));
        }

        Result<PageDTO<TvChannelDTO>> result = tvChannelClient.page(searchDTO);
        return result.map(page -> Page.pageOfTitle(page, tvConverter::convert, TvChannelVO.class));
    }

    @GetMapping("/channel/all")
    public Result<List<TvChannelVO>> allChannels() {
        return tvChannelClient.all().map(list -> list.stream().map(tvConverter::convert).collect(Collectors.toList()));
    }


    @PostMapping("/hot/program")
    @PreAuthorize("hasAuthority('sys:hot:column')")
    public Result<Long> addHotProgram(@RequestBody TvHotProgramAddVO addVO) {
        TvHotProgramDTO dto = tvConverter.convert(addVO);
        return tvProgramClient.saveHotProgram(dto);
    }

    @GetMapping("/hot/program/page")
    @PreAuthorize("hasAuthority('sys:hot:column')")
    public Result<Page<TvHotProgramVO>> programPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                                    @RequestParam(defaultValue = "10") Integer pageSize) {

        Result<PageDTO<TvHotProgramDTO>> result = tvProgramClient.pageHotProgram(pageNum, pageSize);
        return result.map(page -> Page.pageOfTitle(page, tvConverter::convert, TvHotProgramVO.class));
    }

    @GetMapping("/hot/program/all")
    @PreAuthorize("hasAuthority('sys:hot:column')")
    public Result<List<TvHotProgramVO>> allProgram() {

        Result<List<TvHotProgramDTO>> result = tvProgramClient.allProgram();
        return result.map(list -> list.stream().map(tvConverter::convert).collect(Collectors.toList()));
    }


    @DeleteMapping("/hot/program/{id}")
    @PreAuthorize("hasAuthority('sys:hot:column')")
    public Result<Void> deleteHotProgram(@PathVariable("id") Long id) {
        return tvProgramClient.deleteHotProgram(id);
    }

    @PostMapping("/channel/menu")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Long> addChannelMenu(@RequestBody TvChannelMenuAddVO menuAddVO) {
        TvChannelMenuDTO dto = tvConverter.convert(menuAddVO);
        return tvChannelClient.saveMenu(dto);
    }

    @ApiOperation(value = "批量导入栏目菜单", notes = "批量导入栏目菜单")
    @ApiImplicitParam(name = "file", value = "导入excel", required = true, dataType = "__file", paramType = "form")
    @PostMapping("/channel/{channelId}/menu/upload")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    Result<Void> batchUpload(@PathVariable("channelId") Long channelId,
                             @RequestParam MultipartFile file) {

        List<TvChannelMenuDTO> dtoList = tvConverter.convert(channelId, file);
        AssertUtil.notEmpty(dtoList, CodeMessage.BAD_REQUEST.getCode(), "导入信息为空");

        //  批量创建新的嘉宾信息
        tvChannelClient.batchSaveMenu(dtoList);

        return Result.ok();
    }


    @DeleteMapping("/channel/menu/{menuId}")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Void> deleteChannelMenu(@PathVariable("menuId") Long menuId) {
        return tvChannelClient.deleteChannelMenu(menuId);
    }

    @PostMapping("/channel/menu/del-bulk")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Void> deleteChannelMenu(@RequestBody List<Long> menuIds) {
        return tvChannelClient.deleteChannelMenu(menuIds);
    }

    @GetMapping("/channel/{channelId}/menu/list")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<List<TvChannelMenuVO>> getMenus(@PathVariable("channelId") Long channelId, @RequestParam String playDate) {
        Result<List<TvChannelMenuDTO>> result = tvChannelClient.listMenus(channelId, playDate);
        return result.map(list -> list.stream().map(e -> tvConverter.convert(e)).collect(Collectors.toList()));
    }

    @PostMapping("/channel/menu/config")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Long> addChannelMenuConfig(@RequestBody TvChannelMenuConfigVO configVO) {
        TvChannelMenuConfigDTO menuConfigDTO = tvConverter.convert(configVO);
        return tvChannelClient.saveMenuConfig(menuConfigDTO);
    }

    @PutMapping("/channel/menu/config/{configId}")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Void> updateChannelMenuConfig(@PathVariable("configId") Long configId, @RequestBody TvChannelMenuConfigVO configVO) {
        TvChannelMenuConfigDTO menuConfigDTO = tvConverter.convert(configVO);
        return tvChannelClient.updateMenuConfig(configId, menuConfigDTO);
    }

    @DeleteMapping("/channel/menu/config/{configId}")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<Void> deleteChanneMenuConfig(@PathVariable("configId") Long configId) {
        return tvChannelClient.deleteMenuConfig(configId);
    }

    @GetMapping("/channel/{channelId}/menu/config/list")
    @PreAuthorize("hasAuthority('sys:channel:manage')")
    public Result<List<TvChannelMenuConfigVO>> getChannelMenuConfigs(@PathVariable("channelId") Long channelId) {
        Result<List<TvChannelMenuConfigDTO>> result = tvChannelClient.listMenuConfigsByChannelId(channelId);
        return result.map(list -> list.stream().map(e -> tvConverter.convert(e)).collect(Collectors.toList()));
    }

    @PostMapping("/channel/column")
    @PreAuthorize("hasAuthority('sys:column:create')")
    public Result<Long> addColumn(@RequestBody TvColumnAddVO columnAddVO) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvColumnAddDTO columnAddDTO = tvConverter.convert(columnAddVO);
        columnAddDTO.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvColumnClient.save(columnAddDTO);
    }

    @GetMapping("/channel/column/{columnId}")
    @PreAuthorize("hasAuthority('sys:column:modify')")
    public Result<TvColumnVO> getColumn(@PathVariable("columnId") Long columnId) {
        return tvColumnClient.getById(columnId).map(tvConverter::convert);
    }

    @PutMapping("/channel/column/{columnId}")
    @PreAuthorize("hasAuthority('sys:column:modify')")
    public Result<Void> updateColumn(@PathVariable("columnId") Long columnId, @RequestBody TvColumnUpdateVO columnUpdateVO) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvColumnUpdateDTO columnUpdateDTO = tvConverter.convert(columnUpdateVO);
        columnUpdateDTO.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvColumnClient.update(columnId, columnUpdateDTO);
    }

    @PutMapping("/channel/column/{columnId}/status")
    @PreAuthorize("hasAuthority('sys:column:modify')")
    public Result<Void> modifyColumnStatus(@PathVariable("columnId") Long columnId, @RequestParam Integer status) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        return tvColumnClient.modifyStatus(columnId, status, userInfo.getId(), userInfo.getName());
    }

    @GetMapping("/channel/column/page")
    @PreAuthorize("hasAuthority('sys:tv:program:manage')")
    public Result<Page<TvColumnVO>> columnPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                               @RequestParam(defaultValue = "10") Integer pageSize,
                                               @RequestParam(required = false) Long id,
                                               @RequestParam(required = false) Long channelId,
                                               @RequestParam(required = false) String columnNameLike,
                                               @RequestParam(required = false) Integer status) {
        TvColumnSearchDTO searchDTO = new TvColumnSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setId(id);
        searchDTO.setChannelId(channelId);
        searchDTO.setColumnNameLike(columnNameLike);
        searchDTO.setStatus(status);
        Result<PageDTO<TvColumnDTO>> result = tvColumnClient.page(searchDTO);

        return result.map(page -> Page.pageOfTitle(page, e -> tvConverter.convert(e), TvColumnVO.class));
    }

    @GetMapping("/channel/column/all")
    public Result<List<TvColumnVO>> allColumns() {
        return tvColumnClient.all().map(list -> list.stream().filter(e -> Objects.equals(e.getStatus(), EnableEnum.ENABLE.getCode()))
                .map(e -> tvConverter.convert(e)).collect(Collectors.toList()));
    }

    @PostMapping("/channel/program")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<Long> addProgram(@RequestBody TvProgramEditVO programAddVO) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvProgramDTO programDTO = tvConverter.convert(programAddVO);
        programDTO.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvProgramClient.save(programDTO);
    }

    @GetMapping("/channel/program/{id}")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<TvProgramVO> getProgram(@PathVariable("id") Long id) {
        return tvProgramClient.getById(id).map(e -> tvConverter.convert(e));
    }

    @PutMapping("/channel/program/{id}")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<Void> updateProgram(@PathVariable("id") Long id, @RequestBody TvProgramEditVO programAddVO) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        TvProgramDTO programDTO = tvConverter.convert(programAddVO);
        programDTO.setLogDTO(TvOptionLogDTO.buildLogDTO(userInfo.getId(), userInfo.getName()));
        return tvProgramClient.update(id, programDTO);
    }

    @PutMapping("/channel/program/{id}/status")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<Void> modifyProgramStatus(@PathVariable("id") Long id, @RequestParam Integer status) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        return tvProgramClient.modifyStatus(id, status, userInfo.getId(), userInfo.getName());
    }

    @DeleteMapping("/channel/program/{id}")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<Void> deleteProgram(@PathVariable("id") Long id) {
        String username = ThreadLocalCache.get().getUsername();
        SysUserDTO userInfo = sysUserService.getUserInfo(username).getResult();
        if (Objects.isNull(userInfo)) {
            return Result.fail("未登录");
        }
        return tvProgramClient.deleteById(id, userInfo.getId(), userInfo.getName());
    }

    @GetMapping("/channel/program/page")
    @PreAuthorize("hasAuthority('sys:column:resource:manage')")
    public Result<Page<TvProgramVO>> programPage(@RequestParam Integer pageNum,
                                                 @RequestParam Integer pageSize,
                                                 @RequestParam(required = false) Long id,
                                                 @RequestParam(required = false) Long columnId,
                                                 @RequestParam(required = false) String programNameLike,
                                                 @RequestParam(required = false) String playDateStart,
                                                 @RequestParam(required = false) String playDateEnd,
                                                 @RequestParam(required = false) Integer season,
                                                 @RequestParam(required = false) Integer episode) {

        TvProgramSearchDTO searchDTO = new TvProgramSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        if (Objects.nonNull(id)) {
            searchDTO.setIds(Collections.singletonList(id));
        }
        searchDTO.setColumnId(columnId);
        searchDTO.setProgramNameLike(programNameLike);
        searchDTO.setPlayDateStart(playDateStart);
        searchDTO.setPlayDateEnd(playDateEnd);
        searchDTO.setSeason(season);
        searchDTO.setEpisode(episode);

        Result<PageDTO<TvProgramDTO>> result = tvProgramClient.page(searchDTO);
        return result.map(page -> Page.pageOfTitle(page, e -> tvConverter.convert(e), TvProgramVO.class));
    }

    @PostMapping("/option-log/page")
    public Result<Page<TvOptionLogVO>> optionLogPage(@RequestBody TvOptionLogSearchDTO searchDTO) {
        Result<PageDTO<TvOptionLogDTO>> rst = tvOptionLogService.page(searchDTO);
        if (!rst.callSuccess()) {
            return Result.ok(Page.pageOf(Collections.emptyList(), 0, searchDTO.getCurrent(), searchDTO.getSize(), tvConverter::toVO, TvOptionLogVO.class));
        }
        PageDTO<TvOptionLogDTO> dtoPage = rst.getResult();
        Page<TvOptionLogVO> page = Page.pageOf(dtoPage, tvConverter::toVO, TvOptionLogVO.class);
        tvConverter.completeLogInfo(page.getList());
        return Result.ok(page);
    }


}
