package com.jxntv.gvideo.web.controller.be.model.vo;

import java.util.List;
import java.util.Set;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-02-03
 */
@Data
@ApiModel(value="AuditFlowVo", description="审核流对象")
public class AddAuditFlowVo {

    @ApiModelProperty(value="主键ID")
    private Long id;
    @ApiModelProperty(value="流程名称", required = true)
    private String name;
    @ApiModelProperty(value="流程描述")
    private String note;
    @ApiModelProperty(value="步骤集合")
    private List<ProcedureVo> list;

    @Data
    public static class ProcedureVo{
        @ApiModelProperty(value="步骤ID")
        private Long stateId;
        @ApiModelProperty(value="角色ID集合")
        private Set<Long> roleIds;
        @ApiModelProperty(value="人员ID集合")
        private Set<Long> userIds;
    }

}
