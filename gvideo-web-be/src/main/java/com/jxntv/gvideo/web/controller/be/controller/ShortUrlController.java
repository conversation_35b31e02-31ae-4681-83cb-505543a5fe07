package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.aliyun.sdk.ShortUrlClient;
import com.jxntv.gvideo.aliyun.sdk.dto.shortUrl.GenerateDto;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.OutShareWhitelistClient;
import com.jxntv.gvideo.media.client.dto.OutShareWhitelistDTO;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.vo.GenerateShortUrlVO;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
@Slf4j
@RequestMapping("/api/short/url")
@Api(value = "短链接管理接口", tags = "短链接管理接口")
public class ShortUrlController {

    @Resource
    private ShortUrlClient shortUrlClient;

    @Resource
    private UserConsumer userConsumer;

    @Resource
    private OutShareWhitelistClient outShareWhitelistClient;

    @PostMapping()
    @ApiOperation(value = "生成短链")
    public Result<String> generate(@RequestBody @Validated GenerateShortUrlVO vo) {

        List<OutShareWhitelistDTO> outShareWhitelist = outShareWhitelistClient.all().orElse(null);
        if (CollectionUtils.isEmpty(outShareWhitelist)){
            return Result.fail("获取白名单失败，请稍后再试");
        }
        long count = outShareWhitelist.stream().filter(item -> vo.getOriginalUrl().contains(item.getPattern())).count();
        if (count <= 0){
            return Result.fail("无法转换，请先添加白名单");
        }

        Long userId = ThreadLocalCache.getUserId(userConsumer);
        GenerateDto  dto = new GenerateDto();
        dto.setOriginalUrl(vo.getOriginalUrl());
        dto.setExpireDate("2099-12-31");
        dto.setCreateBy(String.valueOf(userId));
        return shortUrlClient.generate(dto);
    }

}
