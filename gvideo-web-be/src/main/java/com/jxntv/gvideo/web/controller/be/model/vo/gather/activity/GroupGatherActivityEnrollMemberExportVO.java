package com.jxntv.gvideo.web.controller.be.model.vo.gather.activity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("活动报名列表列表")
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容居中
public class GroupGatherActivityEnrollMemberExportVO implements Serializable {

    @ExcelProperty(value = "姓名",index = 0)
    @ColumnWidth(15)
    private String name;

    @ExcelProperty(value = "手机号",index = 1)
    @ColumnWidth(15)
    private String mobile;

    @ExcelProperty(value = "性别",index = 2)
    @ColumnWidth(10)
    private String gender;

    @ExcelProperty(value = "出生年份",index = 3)
    @ColumnWidth(10)
    private String year;

    @ExcelProperty(value = "身高（cm）",index = 4)
    @ColumnWidth(10)
    private String height;

    @ExcelProperty(value = "学历",index = 5)
    @ColumnWidth(10)
    private String education;

    @ExcelProperty(value = "所在地",index = 6)
    @ColumnWidth(20)
    private String region;

    @ExcelProperty(value = "婚姻状况",index = 7)
    @ColumnWidth(10)
    private String marital;

    @ExcelProperty(value = "单位",index = 8)
    @ColumnWidth(30)
    private String companyName;

    @ExcelProperty(value = "生活照审核状态",index = 9)
    @ColumnWidth(20)
    private String dailyPhotosAudit;

    @ExcelProperty(value = "单位认证状态",index = 10)
    @ColumnWidth(20)
    private String companyVerifyStatus;

    @ExcelProperty(value = "职业",index = 11)
    @ColumnWidth(10)
    private String postType;

    @ExcelProperty(value = "报名时间",index = 12)
    @ColumnWidth(15)
    private String enrollDate;

    @ExcelProperty(value = "审核时间",index = 13)
    @ColumnWidth(15)
    private String auditDate;

    @ExcelProperty(value = "审核结果",index = 14)
    @ColumnWidth(10)
    private String auditStatus;
}
