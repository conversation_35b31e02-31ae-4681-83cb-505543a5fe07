package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-02-12
 */
@Data
@ApiModel(value = "CertificationColumnVo", description = "认证号栏目列表对象")
public class ColumnVo {
    @ApiModelProperty(value = "栏目ID", required = true)
    private Long id;
    @ApiModelProperty(value = "栏目名称", required = true)
    private String name;
    @ApiModelProperty(value = "有效资源", required = true)
    private Integer sources;
    @ApiModelProperty(value = "栏目介绍", required = true)
    private String introduce;
}
