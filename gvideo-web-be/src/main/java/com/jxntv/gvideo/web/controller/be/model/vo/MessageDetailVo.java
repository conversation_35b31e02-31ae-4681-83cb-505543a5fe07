package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-04-15
 */
@Data
@ApiModel(value="MessageDetailVo", description="站内信详情对象")
public class MessageDetailVo {

    @ApiModelProperty(value="ID")
    private Long id;
    @ApiModelProperty(value="通知名称")
    private String title;
    @ApiModelProperty(value="状态")
    private String status;
    @ApiModelProperty(value="下发时间")
    private String executeDate;
    @ApiModelProperty(value="发送方")
    private String from;
    @ApiModelProperty(value="接收方")
    private String to;
    @ApiModelProperty(value="最近一次操作人")
    private String adminUser;
}
