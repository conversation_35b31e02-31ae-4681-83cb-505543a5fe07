package com.jxntv.gvideo.web.controller.be.model.vo.om;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/27 10:54
 */
@Data
@ApiModel("渠道池响应结构体")
public class ChannelPoolRespVo implements Serializable {

    @ApiModelProperty("渠道池ID")
    private Long channelPoolId;

    @ApiModelProperty("渠道池名称")
    private String channelPoolName;

    @ApiModelProperty("渠道邀请码列表")
    private Map<String, String> inviteChannelCode;

    @ApiModelProperty("内容池ID")
    private Long contentPoolId;

    @ApiModelProperty("内容池名称")
    private String contentPoolName;

    @ApiModelProperty("渠道池状态")
    private Integer status;

    @ApiModelProperty("渠道池创建人")
    private String createName;

    @ApiModelProperty("渠道池更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;
}
