package com.jxntv.gvideo.web.controller.be.model.vo.gather.instruction;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupGatherActivityVO", description = "课堂教学组件列表")
public class GroupGatherInstructionVO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "课堂教学信息名称")
    private String name;

    @ApiModelProperty(value = "课堂教学信息标签")
    private String tag;

    @ApiModelProperty(value = "封面id")
    private String coverUrl;

    @ApiModelProperty(value = "上架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onLineDate;

    @ApiModelProperty(value = "下架时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date offLineDate;

    @ApiModelProperty(value = "状态，0-已创建 1-待上架 2-已上架 3-已下架")
    private Integer status;

}
