package com.jxntv.gvideo.web.controller.be.converter;

import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.aliyun.sdk.ShortUrlClient;
import com.jxntv.gvideo.aliyun.sdk.dto.shortUrl.ShortUrlMapDto;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.TvColumnClient;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.ContentListDTO;
import com.jxntv.gvideo.media.client.dto.ContentListResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceImageDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvColumnDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramSearchDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.EnableEnum;
import com.jxntv.gvideo.media.client.enums.MediaResourceImageType;
import com.jxntv.gvideo.media.client.enums.PlayStyle;
import com.jxntv.gvideo.web.controller.be.client.MediaResourceService;
import com.jxntv.gvideo.web.controller.be.model.vo.*;
import com.jxntv.gvideo.web.controller.be.rpc.OssConsumer;
import com.jxntv.gvideo.web.controller.be.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 内容列表转换
 * @date 2021/05/10 15:09
 */
@Slf4j
@Component
public class ContentListConvert {

    @Autowired
    private MediaResourceClient resourceClient;
    @Autowired
    private MediaResourceConverter resourceConverter;
    @Autowired
    private OssConsumer ossConsumer;
    @Autowired
    private ShortUrlClient shortUrlClient;

    @Resource
    private TvProgramClient tvProgramClient;

    @Resource
    private TvColumnClient tvColumnClient;

    public ContentListDTO toDTO(ContentListVo vo) {
        ContentListDTO dto = new ContentListDTO();
        BeanCopier.create(ContentListVo.class, ContentListDTO.class, false)
                .copy(vo, dto, null);
        ContentListVo.ContentListAutoVo autoVo = vo.getAutoRule();
        if (Objects.nonNull(autoVo)) {
            ContentListDTO.ContentListAutoDTO autoDTO = new ContentListDTO.ContentListAutoDTO();
            if (!CollectionUtils.isEmpty(autoVo.getContentType())) {
                autoDTO.setContentType(StringUtils.join(autoVo.getContentType(), ":"));
            } else {
                autoDTO.setContentType("");
            }
            if (!CollectionUtils.isEmpty(autoVo.getContentTypeLabels())) {
                autoDTO.setContentTypeLabel(StringUtils.join(autoVo.getContentTypeLabels(), ":"));
            } else {
                autoDTO.setContentTypeLabel("");
            }
            if (!CollectionUtils.isEmpty(autoVo.getInternalLabels())) {
                autoDTO.setInternalLabel(StringUtils.join(autoVo.getInternalLabels(), ":"));
            } else {
                autoDTO.setInternalLabel("");
            }
            if (!CollectionUtils.isEmpty(autoVo.getOtherFeatureLabels())) {
                autoDTO.setOtherLabel(StringUtils.join(autoVo.getOtherFeatureLabels(), ":"));
            } else {
                autoDTO.setOtherLabel("");
            }
            autoDTO.setShowName(autoVo.getShowName());
            autoDTO.setId(autoVo.getId());
            dto.setAutoRuleDTO(autoDTO);
        }
        return dto;
    }

    public ContentListResourceDTO toDTO(EditContentListResourceVo vo) {
        ContentListResourceDTO dto = new ContentListResourceDTO();
        BeanCopier.create(EditContentListResourceVo.class, ContentListResourceDTO.class, false)
                .copy(vo, dto, null);
        return dto;
    }

    public List<ContentListResourceDTO> toDTO(AddContentListResourceVo vo, Long userId) {
        return vo.getResourceList().stream().map(resourceVo -> {
            ContentListResourceDTO dto = new ContentListResourceDTO();
            dto.setResourceId(resourceVo.getId());
            dto.setContentType(resourceVo.getContentType());
            dto.setName(resourceVo.getShowName());
            dto.setListId(vo.getListId());
            dto.setCreateUserId(userId);
            dto.setUpdateUserId(userId);
            return dto;
        }).collect(Collectors.toList());
    }

    public ContentListResourceDTO toDTO(ContentListResourceVo vo) {
        ContentListResourceDTO dto = new ContentListResourceDTO();
        dto.setId(vo.getId());
        dto.setListId(vo.getListId());
        dto.setType(vo.getType());
        dto.setIndex(vo.getIndex());
        dto.setResourceId(vo.getResourceId());
        dto.setContentType(vo.getContentType());
        dto.setName(vo.getName());
        dto.setLinkUrl(vo.getLinkUrl());
        dto.setStickSort(vo.getStickSort());
        dto.setLockSort(vo.getLockSort());
        dto.setSort(vo.getSort());
        dto.setCreateUserId(vo.getCreateUserId());
        dto.setCreateDate(vo.getCreateDate());
        dto.setFlag(vo.getFlag());
        if (CollectionUtils.isEmpty(vo.getCoverList())) {
            if (StringUtils.isNotEmpty(vo.getCover())) {
                dto.setCoverList(Arrays.stream(vo.getCover().split(",")).collect(Collectors.toList()));
            }
        } else {
            List<String> coverList = vo.getCoverList().stream().map(OssFileVo::getId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            dto.setCoverList(coverList);
        }
        return dto;
    }

    public ContentListResourceVo toVo(ContentListResourceDTO dto) {
        ContentListResourceVo vo = new ContentListResourceVo();
        vo.setId(dto.getId());
        vo.setListId(dto.getListId());
        vo.setType(dto.getType());
        vo.setCreateDate(dto.getCreateDate());
        vo.setCreateUserId(dto.getCreateUserId());
        vo.setIsLock(Objects.nonNull(dto.getLockSort()) && dto.getLockSort() > 0);
        vo.setIsTop(Objects.nonNull(dto.getStickSort()) && dto.getStickSort() > 0);
        vo.setStickSort(Objects.nonNull(dto.getStickSort()) ? dto.getStickSort() : 0);
        vo.setSort(dto.getSort());
        vo.setShowType(dto.getShowType());
        if (Integer.valueOf(0).equals(dto.getType())) {
            Result<MediaResourceDTO> result = resourceClient.get(dto.getResourceId());
            if (result.callSuccess()) {
                MediaResourceVo resourceVo = resourceConverter.toVo(result.getResult());
                vo.setResourceId(resourceVo.getId());
                vo.setReleaseType(resourceVo.getReleaseType());
                vo.setContentType(resourceVo.getContentType());
                vo.setContentTypeStr(resourceVo.getContentTypeStr());
                vo.setMediaTab(ContentType.getMediaTab(resourceVo.getContentType()));
                vo.setName(StringUtils.isNotEmpty(dto.getName()) ? dto.getName() : resourceVo.getShowName());
                //构建封面图对象
                List<OssFileVo> imageList = getImageList(dto, result.getResult());
                if (!CollectionUtils.isEmpty(imageList)) {
                    vo.setCoverList(imageList);
                    vo.setCoverSize(imageList.size());
                }
            }
        } else if (Integer.valueOf(1).equals(dto.getType())){
            vo.setContentType(0);
            vo.setContentTypeStr("H5页面");
            vo.setName(dto.getName());
            vo.setLinkUrl(dto.getLinkUrl());
            if (StringUtils.isNotEmpty(dto.getLinkUrl())){
                ShortUrlMapDto shortUrlMapDto = shortUrlClient.getUrlMapBySurl(dto.getLinkUrl()).orElse(null);
                if (Objects.nonNull(shortUrlMapDto)){
                    vo.setOriginalUrl(shortUrlMapDto.getLurl());
                }
            }

            if (!CollectionUtils.isEmpty(dto.getCoverList())) {
                List<OssFileVo> imgList = new ArrayList<>(dto.getCoverList().size());
                for (int i = 0; i < dto.getCoverList().size(); i++) {
                    String ossId = dto.getCoverList().get(i);
                    //如果存在修改则对应的位置信息修改
                    if (StringUtils.isNotEmpty(ossId)) {
                        OssFileVo ossFileVo = new OssFileVo();
                        ossFileVo.setId(ossId);
                        ossFileVo.setUrl(ossConsumer.getImageUrl(ossId));
                        imgList.add(ossFileVo);
                    }
                }
                if (!CollectionUtils.isEmpty(imgList)) {
                    vo.setCoverList(imgList);
                    vo.setCoverSize(imgList.size());
                }
            }
        }else if (Integer.valueOf(2).equals(dto.getType())){

            TvColumnDTO tvColumnDTO = tvColumnClient.getById(dto.getResourceId()).orElse(null);
            if (Objects.isNull(tvColumnDTO)){
                return null;
            }

            TvProgramSearchDTO searchDTO = new TvProgramSearchDTO();
            searchDTO.setColumnId(dto.getResourceId());
            searchDTO.setCurrent(1);
            searchDTO.setSize(1);
            searchDTO.setStatus(EnableEnum.ENABLE.getCode());
            List<TvProgramDTO> list = tvProgramClient.page(searchDTO).map(PageDTO::getList).orElse(null);
            if (CollectionUtils.isEmpty(list)){
                return null;
            }
            TvProgramDTO tvProgramDTO = list.get(0);
            MediaResourceDTO mediaResourceDTO = resourceClient.get(tvProgramDTO.getMediaId()).orElse(null);
            if (Objects.isNull(mediaResourceDTO)){
                return null;
            }


            MediaResourceVo resourceVo = resourceConverter.toVo(mediaResourceDTO);
            vo.setResourceId(resourceVo.getId());
            vo.setReleaseType(resourceVo.getReleaseType());
            vo.setContentType(resourceVo.getContentType());
            vo.setContentTypeStr(resourceVo.getContentTypeStr());
            vo.setMediaTab(ContentType.getMediaTab(resourceVo.getContentType()));
            //构建封面图对象
            List<OssFileVo> imageList = getImageList(dto, mediaResourceDTO);
            if (!CollectionUtils.isEmpty(imageList)) {
                vo.setCoverList(imageList);
                vo.setCoverSize(imageList.size());
            }



            if (org.springframework.util.StringUtils.hasText(mediaResourceDTO.getIntroduction())){
                vo.setName( tvColumnDTO.getColumnName() +" | "+mediaResourceDTO.getIntroduction());
            }else{
                vo.setName(tvColumnDTO.getColumnName());
            }
            vo.setColumnId(tvColumnDTO.getId());
        }
        return vo;
    }

    //构建返回图片
    private List<OssFileVo> getImageList(ContentListResourceDTO dto, MediaResourceDTO mediaResourceDTO) {
        Integer contentType = mediaResourceDTO.getContentType();
        List<OssFileVo> imgList = new ArrayList<>(16);
        //1、先获取资源原本图片
        //2、对图片进行内容列表封面图覆盖
        if (contentType == ContentType.PIC_FONT.getCode()) {
            List<MediaResourceImageDTO> imageDTOS = SpringUtils.getBean(MediaResourceService.class).getImages(mediaResourceDTO.getId()).getResult();
            if (!CollectionUtils.isEmpty(imageDTOS)) {
                for (MediaResourceImageDTO item : imageDTOS) {
                    OssFileVo ossFileVo = new OssFileVo();
                    ossFileVo.setUrl(ossConsumer.getImageUrl(item.getOssId()));
                    imgList.add(ossFileVo);
                }
            }
        } else if (contentType == ContentType.GAN_YUN.getCode() || Integer.valueOf(ContentType.GAN_YUN_VIDEO.getCode()).equals(contentType)) {
            //赣云同步的视频放在pptvcover字段中
            Map<String, String> map = null;
            if (StringUtils.isNotEmpty(mediaResourceDTO.getPptvCover())) {
                map = JSON.parseObject(mediaResourceDTO.getPptvCover(), Map.class);
            }
            //封面图，如果后台修改了，则采用后台设置的数据
            String cover = getImage(mediaResourceDTO.getId());
            if (StringUtils.isNotEmpty(cover)) {
                OssFileVo ossFileVo = new OssFileVo();
                ossFileVo.setUrl(cover);
                imgList.add(ossFileVo);
            } else {
                if (Objects.nonNull(map) && map.containsKey("cover")) {
                    OssFileVo ossFileVo = new OssFileVo();
                    ossFileVo.setUrl(map.get("cover"));
                    imgList.add(ossFileVo);
                }
            }
        } else if (contentType == ContentType.NEWS.getCode()) {
            if (StringUtils.isNotEmpty(mediaResourceDTO.getPptvCover())) {
                Map<String, Object> tmp = JSON.parseObject(mediaResourceDTO.getPptvCover(), Map.class);
                if (!CollectionUtils.isEmpty(tmp) && tmp.containsKey("images")) {
                    List<String> tmpList = (List<String>) tmp.get("images");
                    for (String url : tmpList) {
                        OssFileVo ossFileVo = new OssFileVo();
                        ossFileVo.setUrl(url);
                        imgList.add(ossFileVo);
                    }
                }
            }
        } else {
            String cover = getImage(mediaResourceDTO.getId());
            if (StringUtils.isNotEmpty(cover)) {
                OssFileVo ossFileVo = new OssFileVo();
                ossFileVo.setUrl(cover);
                imgList.add(ossFileVo);
            }
        }
        //获取内容列表设置的封面
        if (!CollectionUtils.isEmpty(dto.getCoverList())) {
            for (int i = 0; i < dto.getCoverList().size(); i++) {
                String ossId = dto.getCoverList().get(i);
                //如果存在修改则对应的位置信息修改
                if (StringUtils.isNotEmpty(ossId) && i < imgList.size()) {
                    imgList.get(i).setId(ossId);
                    imgList.get(i).setUrl(ossConsumer.getImageUrl(ossId));
                }
            }
        }
        return imgList;
    }

    //获取资源图片
    public String getImage(Long mediaId) {
        String url = "";
        List<MediaResourceImageDTO> imageDTOS = SpringUtils.getBean(MediaResourceService.class).getImages(mediaId).getResult();
        if (!CollectionUtils.isEmpty(imageDTOS)) {
            Optional<MediaResourceImageDTO> feedImage = imageDTOS.stream().filter(img -> img.getType().equals(MediaResourceImageType.RECOMMENDED_FLOW.getCode())).findFirst();
            if (feedImage.isPresent()) {
                url = ossConsumer.getImageUrl(feedImage.get().getOssId());
            } else {
                url = ossConsumer.getImageUrl(imageDTOS.get(0).getOssId());
            }
        }
        return url;
    }

    public ContentListResourceVo toVo1(ContentListResourceDTO dto) {
        ContentListResourceVo vo = new ContentListResourceVo();
        vo.setId(dto.getId());
        vo.setListId(dto.getListId());
        vo.setType(dto.getType());
        vo.setCreateDate(dto.getCreateDate());
        vo.setCreateUserId(dto.getCreateUserId());
        vo.setIsLock(Objects.nonNull(dto.getLockSort()) && dto.getLockSort() > 0);
        vo.setIsTop(Objects.nonNull(dto.getStickSort()) && dto.getStickSort() > 0);
        vo.setStickSort(Objects.nonNull(dto.getStickSort()) ? dto.getStickSort() : 0);
        vo.setSort(dto.getSort());

        if (Integer.valueOf(0).equals(dto.getType())) {
            Result<MediaResourceDTO> result = resourceClient.get(dto.getResourceId());
            if (result.callSuccess()) {
                MediaResourceVo resourceVo = resourceConverter.toVo(result.getResult());
                vo.setResourceId(resourceVo.getId());
                vo.setReleaseType(resourceVo.getReleaseType());
                vo.setContentType(resourceVo.getContentType());
                vo.setContentTypeStr(resourceVo.getContentTypeStr());
                vo.setPublishDate(resourceVo.getCreateDate());
                //如果是新闻，增加显示模式
                if (result.getResult().getContentType().equals(ContentType.NEWS.getCode())) {
                    vo.setContentTypeStr(String.format("%s·%s", vo.getContentTypeStr(), PlayStyle.toStr(result.getResult().getContentType(), result.getResult().getPlayStyle())));
                }
                vo.setMediaTab(ContentType.getMediaTab(resourceVo.getContentType()));
                vo.setName(StringUtils.isNotEmpty(dto.getName()) ? dto.getName() : resourceVo.getShowName());
                //构建封面图对象
                List<OssFileVo> imageList = getImageList(dto, result.getResult());
                if (!CollectionUtils.isEmpty(imageList)) {
                    vo.setCoverList(imageList);
                    vo.setCoverSize(imageList.size());
                }
            }
        } else if (Integer.valueOf(1).equals(dto.getType())){
            vo.setContentType(0);
            vo.setContentTypeStr("H5页面");
            vo.setName(dto.getName());
            vo.setLinkUrl(dto.getLinkUrl());
            vo.setPublishDate(dto.getCreateDate());
            if (!CollectionUtils.isEmpty(dto.getCoverList())) {
                List<OssFileVo> imgList = new ArrayList<>(dto.getCoverList().size());
                for (int i = 0; i < dto.getCoverList().size(); i++) {
                    String ossId = dto.getCoverList().get(i);
                    //如果存在修改则对应的位置信息修改
                    if (StringUtils.isNotEmpty(ossId)) {
                        OssFileVo ossFileVo = new OssFileVo();
                        ossFileVo.setId(ossId);
                        ossFileVo.setUrl(ossConsumer.getImageUrl(ossId));
                        imgList.add(ossFileVo);
                    }
                }
                if (!CollectionUtils.isEmpty(imgList)) {
                    vo.setCoverList(imgList);
                    vo.setCoverSize(imgList.size());
                }
            }
        }else if (Integer.valueOf(2).equals(dto.getType())){

            TvColumnDTO tvColumnDTO = tvColumnClient.getById(dto.getResourceId()).orElse(null);
            if (Objects.isNull(tvColumnDTO)){
                return null;
            }

            TvProgramSearchDTO searchDTO = new TvProgramSearchDTO();
            searchDTO.setColumnId(dto.getResourceId());
            searchDTO.setCurrent(1);
            searchDTO.setSize(1);
            searchDTO.setStatus(EnableEnum.ENABLE.getCode());
            List<TvProgramDTO> list = tvProgramClient.page(searchDTO).map(PageDTO::getList).orElse(null);
            if (CollectionUtils.isEmpty(list)){
                return null;
            }
            TvProgramDTO tvProgramDTO = list.get(0);
            MediaResourceDTO mediaResourceDTO = resourceClient.get(tvProgramDTO.getMediaId()).orElse(null);
            if (Objects.isNull(mediaResourceDTO)){
                return null;
            }


            MediaResourceVo resourceVo = resourceConverter.toVo(mediaResourceDTO);
            vo.setResourceId(resourceVo.getId());
            vo.setReleaseType(resourceVo.getReleaseType());
            vo.setPublishDate(resourceVo.getCreateDate());
            //如果是新闻，增加显示模式
            if (mediaResourceDTO.getContentType().equals(ContentType.NEWS.getCode())) {
                vo.setContentTypeStr(String.format("%s·%s", vo.getContentTypeStr(), PlayStyle.toStr(mediaResourceDTO.getContentType(), mediaResourceDTO.getPlayStyle())));
            }
            vo.setMediaTab(ContentType.getMediaTab(resourceVo.getContentType()));

            if (org.springframework.util.StringUtils.hasText(mediaResourceDTO.getIntroduction())){
                vo.setName( tvColumnDTO.getColumnName() +" | "+mediaResourceDTO.getIntroduction());
            }else{
                vo.setName(tvColumnDTO.getColumnName());
            }
            //构建封面图对象
            List<OssFileVo> imageList = getImageList(dto, mediaResourceDTO);
            if (!CollectionUtils.isEmpty(imageList)) {
                vo.setCoverList(imageList);
                vo.setCoverSize(imageList.size());
            }
            //vo.setContentType(2);
            vo.setContentTypeStr("栏目");
            vo.setColumnId(tvColumnDTO.getId());
        }
        return vo;
    }

    public ContentListTreeVo toContentListVo(ContentListDTO dto) {
        ContentListTreeVo vo = new ContentListTreeVo();
        vo.setLabel(dto.getName());
        vo.setType(dto.getType());
        vo.setParent(dto.getParent());
        vo.setFolder(dto.getFolder());
        vo.setId(dto.getId());
        return vo;
    }

    public List<ContentListTreeVo> toContentListTree(List<ContentListDTO> contentListDTOS) {
        List<ContentListTreeVo> contentListTreeVoList = new ArrayList<>(contentListDTOS.size());
        for (ContentListDTO contentListDTO : contentListDTOS) {
            ContentListTreeVo vo = new ContentListTreeVo();
            vo.setId(contentListDTO.getId());
            vo.setType(contentListDTO.getType());
            vo.setUrl(contentListDTO.getUrl());
            vo.setLabel(contentListDTO.getName());
            vo.setFolder(contentListDTO.getFolder());
            vo.setParent(contentListDTO.getParent());
            contentListTreeVoList.add(vo);
        }
        //找到所有一级节点
        List<ContentListTreeVo> rootContentList = contentListTreeVoList.stream().filter(vo -> vo.getParent().equals(0L)).collect(Collectors.toList());
        // 遍历所有一级节点，每个节点都找到孩子节点
        for (ContentListTreeVo fatherVo : rootContentList) {
            // 找到所有孩子
            List<ContentListTreeVo> children = findChildren(fatherVo, contentListTreeVoList);
            // 赋值
            fatherVo.setChildren(children);
        }
        return rootContentList;
    }

    private List<ContentListTreeVo> findChildren(ContentListTreeVo fatherVo, List<ContentListTreeVo> allVoList) {
        List<ContentListTreeVo> children = new ArrayList<>();
        //添加子节点
        for (ContentListTreeVo vo : allVoList) {
            //添加自己的孩子
            if (vo.getParent().equals(fatherVo.getId())) {
                children.add(vo);
            }
        }
        //判断是否有子节点
        if (CollectionUtils.isEmpty(children)) {
            return children;
        }
        // 子节点找齐后，接着每一个子节点，找子节点是否还有下一级
        for (ContentListTreeVo nav : children) {
            nav.setChildren(findChildren(nav, allVoList));
        }
        return children;
    }

    public ContentListVo toVo(ContentListDTO dto) {
        ContentListVo vo = new ContentListVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        if (StringUtils.isNotEmpty(dto.getCover())) {
            vo.setOssId(dto.getCover());
            vo.setCover(ossConsumer.getImageUrl(dto.getCover()));
        }
        vo.setParent(dto.getParent());
        vo.setFolder(dto.getFolder());
        vo.setLimitNum(dto.getLimitNum());
        vo.setType(dto.getType());
        vo.setUrl(dto.getUrl());
        vo.setRemarks(dto.getRemarks());
        if (Objects.nonNull(dto.getAutoRuleDTO())) {
            ContentListVo.ContentListAutoVo autoVo = new ContentListVo.ContentListAutoVo();
            autoVo.setId(dto.getAutoRuleDTO().getId());
            autoVo.setShowName(dto.getAutoRuleDTO().getShowName());
            if (StringUtils.isNotEmpty(dto.getAutoRuleDTO().getContentType())) {
                autoVo.setContentType(Arrays.asList(dto.getAutoRuleDTO().getContentType().split(":")));
            }
            if (StringUtils.isNotEmpty(dto.getAutoRuleDTO().getContentTypeLabel())) {
                autoVo.setContentTypeLabels(Arrays.asList(dto.getAutoRuleDTO().getContentTypeLabel().split(":")));
            }
            if (StringUtils.isNotEmpty(dto.getAutoRuleDTO().getInternalLabel())) {
                autoVo.setInternalLabels(Arrays.asList(dto.getAutoRuleDTO().getInternalLabel().split(":")));
            }
            if (StringUtils.isNotEmpty(dto.getAutoRuleDTO().getOtherLabel())) {
                autoVo.setOtherFeatureLabels(Arrays.asList(dto.getAutoRuleDTO().getOtherLabel().split(":")));
            }
            vo.setAutoRule(autoVo);
        }
        return vo;
    }
}
