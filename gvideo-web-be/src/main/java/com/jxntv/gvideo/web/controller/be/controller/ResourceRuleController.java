package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.ResourceRuleClient;
import com.jxntv.gvideo.om.dto.ResourceRuleReqDTO;
import com.jxntv.gvideo.om.dto.ResourceRuleRespDTO;
import com.jxntv.gvideo.web.controller.be.client.ResourceRuleService;
import com.jxntv.gvideo.web.controller.be.converter.RuleConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.vo.om.ResourceRuleReqVo;
import com.jxntv.gvideo.web.controller.be.model.vo.om.ResourceRuleRespVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/17 17:00
 */
@RestController
@Slf4j
@RequestMapping(ResourceRuleClient.RESOURECE_RULE_PREFIX)
@Api(value = "动态规则接口", tags = "动态规则接口")
public class ResourceRuleController {

    @Autowired
    private ResourceRuleService resourceRuleService;

    @Autowired
    private RuleConverter ruleConverter;


    @PostMapping()
    @ApiOperation(value = "创建动态规则")
    public Result<Long> createResourceRule(@PathVariable("model_id") String modelId,
                                           @RequestBody ResourceRuleReqVo reqVo) {
        log.info("createResourceRule reqVo is {}", reqVo.toString());
        ResourceRuleReqDTO ruleReqDTO = new ResourceRuleReqDTO();
        BeanCopier.create(ResourceRuleReqVo.class, ResourceRuleReqDTO.class, false)
                .copy(reqVo, ruleReqDTO, null);
        String userName = ThreadLocalCache.get().getUsername();
        ruleReqDTO.setCreateName(userName);

        Result<Long> result = resourceRuleService.createResourceRule(modelId, ruleReqDTO);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            return Result.fail(result.getCode(), result.getMessage());
        }

        return Result.ok(result.getResult());
    }


    @GetMapping("/{rule_id}")
    @ApiOperation(value = "查询规则模型下的动态规则")
    public Result<ResourceRuleRespVo> queryResourceRuleById(@PathVariable("model_id") String modelId,
                                                            @PathVariable("rule_id") String ruleId) {

        Result<ResourceRuleRespDTO> result = resourceRuleService.queryResourceRuleById(modelId, ruleId);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            return Result.fail(result.getCode(), result.getMessage());
        }

        return Result.ok(ruleConverter.convert(result.getResult()));
    }


    @GetMapping()
    @ApiOperation(value = "查询规则模型下的规则列表")
    public Result<List<ResourceRuleRespVo>> queryResourceRuleList(@PathVariable("model_id") String modelId) {

        Result<List<ResourceRuleRespDTO>> result = resourceRuleService.queryResourceRuleList(modelId);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            return Result.fail(result.getCode(), result.getMessage());
        }
        List<ResourceRuleRespVo> respVoList = new ArrayList<>();
        for (ResourceRuleRespDTO respDTO : result.getResult()) {
            respVoList.add(ruleConverter.convert(respDTO));
        }
        return Result.ok(respVoList);
    }


    @PostMapping("/{rule_id}")
    @ApiOperation(value = "修改动态规则")
    public Result<ResourceRuleRespVo> modifyResourceRule(@PathVariable("model_id") String modelId,
                                                         @PathVariable("rule_id") String ruleId,
                                                         @RequestBody ResourceRuleReqVo reqVo) {

        ResourceRuleReqDTO ruleReqDTO = new ResourceRuleReqDTO();
        BeanCopier.create(ResourceRuleReqVo.class, ResourceRuleReqDTO.class, false)
                .copy(reqVo, ruleReqDTO, null);
        String userName = ThreadLocalCache.get().getUsername();
        ruleReqDTO.setCreateName(userName);

        Result<ResourceRuleRespDTO> result = resourceRuleService.modifyResourceRule(modelId, ruleId, ruleReqDTO);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            return Result.fail(result.getCode(), result.getMessage());
        }

        return Result.ok(ruleConverter.convert(result.getResult()));
    }
}
