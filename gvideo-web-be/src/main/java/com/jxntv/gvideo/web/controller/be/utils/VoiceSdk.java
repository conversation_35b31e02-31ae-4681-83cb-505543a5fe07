package com.jxntv.gvideo.web.controller.be.utils;

import com.google.gson.Gson;
import com.jxntv.gvideo.aliyun.sdk.dto.NlpDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.SoundDTO;
import com.jxntv.gvideo.web.controller.be.model.dto.VoiceToContentDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2021/08/10 11:42
 */
@Slf4j
public class VoiceSdk {
    private static final String URL_FORMAT = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer?appkey=%s&token=%s&format=wav&sample_rate=16000";
    private static final String STATUS_SUCCESS = "20000000";

    private static String getResponseWithTimeout(Request q) {
        String ret = null;
        OkHttpClient client = new OkHttpClient().newBuilder().build();
        try {
            Response s = client.newCall(q).execute();
            if (s.isSuccessful()) {
                ret = s.body().string();
                s.close();
            }
        } catch (SocketTimeoutException e) {
            ret = null;
            log.error("语音转文字请求超时: {}", e.getLocalizedMessage());
        } catch (IOException e) {
            log.error("语音转文字异常：{}", e.getLocalizedMessage());
        }
        return ret;
    }

    private static SoundDTO handleResult(String response) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        Gson gson = new Gson();
        VoiceToContentDTO dto = gson.fromJson(response, VoiceToContentDTO.class);
        if (Objects.isNull(dto) || !STATUS_SUCCESS.equals(dto.getStatus())) {
            return null;
        }
        String content = "";
        String length = null;
        if (Objects.nonNull(dto.getFlash_result()) && !CollectionUtils.isEmpty(dto.getFlash_result().getSentences())) {
            List<String> textList = dto.getFlash_result().getSentences().stream().map(VoiceToContentDTO.Sentence::getText).collect(Collectors.toList());
            content = StringUtils.join(textList, "");
            int size = dto.getFlash_result().getSentences().size();
            VoiceToContentDTO.Sentence sentence = dto.getFlash_result().getSentences().get(size - 1);
            Integer endTime = Objects.nonNull(sentence.getEnd_time()) ? sentence.getEnd_time() / 1000 : 0;
            length = String.valueOf(endTime);
        }
        SoundDTO soundDTO = new SoundDTO();
        soundDTO.setContent(content);
        soundDTO.setLength(length);
        return soundDTO;
    }

    public static SoundDTO sendPostData(NlpDTO nlpDTO, HashMap<String, String> headers, byte[] data) {
        RequestBody body;
        if (data.length == 0) {
            log.info("语音转文字请求数据为空");
            return null;
        } else {
            body = RequestBody.create(MediaType.parse("application/octet-stream"), data);
        }
        Headers.Builder hb = new Headers.Builder();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                hb.add(entry.getKey(), entry.getValue());
            }
        }
        Request request = new Request.Builder()
                .url(String.format(URL_FORMAT, nlpDTO.getAppKey(), nlpDTO.getToken()))
                .headers(hb.build())
                .post(body)
                .build();
        String response = getResponseWithTimeout(request);
        return handleResult(response);
    }
}
