package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2020-02-13
 */
@Data
@ApiModel(value = "UserPassLoginVo", description = "账号密码登录对象")
public class UserPassLoginVo {

    @ApiModelProperty(value = "手机号", required = true)
    private String username;
    @ApiModelProperty(value = "密码", required = true)
    private String password;
    @ApiModelProperty(value = "图片验证码")
    private String imageCode;
    @ApiModelProperty(value = "标识用户唯一设备ID", required = true)
    private String uuid;

}
