package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.FeedbackClient;
import com.jxntv.gvideo.interact.client.dto.FeedbackDTO;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.FeedbackConverter;
import com.jxntv.gvideo.web.controller.be.model.vo.FeedbackVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;

/**
 * <AUTHOR>
 * @description 用户反馈接口
 * @date 2021/08/11 15:19
 */
@RequestMapping(FeedbackClient.PREFIX)
@Api(value = "用户反馈接口", tags = {"用户反馈接口"})
@RestController
public class FeedbackController {
    @Autowired
    private FeedbackConverter feedbackConverter;
    @Autowired
    private FeedbackClient feedbackClient;

    @GetMapping("/page")
    @ApiOperation(value = "用户反馈列表")
    public Result<Page<FeedbackVO>> page(@ApiParam(name = "keyword", value = "关键字") @RequestParam(name = "keyword", defaultValue = "", required = false) String keyword,
                                         @ApiParam(name = "current", value = "页码") @RequestParam(value = "current", required = false, defaultValue = "1") Integer current,
                                         @ApiParam(name = "size", value = "每页数量") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        Result<PageDTO<FeedbackDTO>> result = feedbackClient.page(keyword, current, size);
        if (!result.callSuccess()) {
            return Result.ok(Page.pageOf(Collections.emptyList(), 0, current, size, feedbackConverter::toVo, FeedbackVO.class));
        }
        PageDTO<FeedbackDTO> page = result.getResult();
        return Result.ok(Page.pageOf(page.getList(), page.getTotal(), page.getPageNum(), page.getPageSize(), feedbackConverter::toVo, FeedbackVO.class));
    }
}
