package com.jxntv.gvideo.web.controller.be.job;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.ImManageClient;
import com.jxntv.gvideo.media.client.dto.ImOfficialMessageDTO;
import com.jxntv.gvideo.media.client.enums.ImMessageStatusEnum;
import com.jxntv.gvideo.web.controller.be.service.ImService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Component
public class ImOfficialMessageJob {
    @Autowired
    private ImService imService;
    @Autowired
    private ImManageClient imManageClient;

    @XxlJob("imOfficialMessageJob")
    public ReturnT<String> execute() {
        log.info("扫描IM官方私信消息...");
        List<ImOfficialMessageDTO> messageDTOList = imManageClient.listAllMessage(ImMessageStatusEnum.NOT_SEND.getCode()).getResult();
        if (CollectionUtils.isEmpty(messageDTOList)) {
            log.info("暂无IM官方私信消息...");
        }
        for (ImOfficialMessageDTO dto : messageDTOList) {
            ImMessageStatusEnum statusEnum = imService.sendMessage(dto).orElse(ImMessageStatusEnum.SEND_FAIL);
            dto.setStatus(statusEnum.getCode());
            Result rst = imManageClient.updateOfficialMessageStatus(dto);
            if (!rst.callSuccess()) {
                log.info("IM官方私信消息状态修改失败, id = {}", dto.getId());
            }
        }
        return ReturnT.SUCCESS;
    }
}
