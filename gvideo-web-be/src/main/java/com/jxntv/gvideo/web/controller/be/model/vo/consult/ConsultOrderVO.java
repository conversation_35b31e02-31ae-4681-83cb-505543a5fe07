package com.jxntv.gvideo.web.controller.be.model.vo.consult;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode
public class ConsultOrderVO implements Serializable {


    /**
     * 主键
     */
    private Long id;

    /**
     * 订单编号
     */
    @ApiModelProperty("订单编号")
    private String orderNo;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 昵称
     */
    @ApiModelProperty("用户昵称")
    private String userNickName;

    /**
     * 头像地址
     */
    @ApiModelProperty("用户头像")
    private String userAvatarUrl;

    /**
     * 用户手机号
     */
    @ApiModelProperty("用户手机号")
    private String userMobile;

    /**
     * 订单状态:10:待支付、20:待咨询、30:咨询中,40:已完成、50:已取消
     */
    @ApiModelProperty("订单状态")
    private Integer state;

    /**
     * 订单金额
     */
    @ApiModelProperty("订单金额")
    private String amount;

    /**
     * 咨询师id
     */
    @ApiModelProperty("导师id")
    private Long counselorId;

    /**
     * 咨询师名称
     */
    @ApiModelProperty("导师名称")
    private String counselorName;

    /**
     * 咨询师头像
     */
    @ApiModelProperty("导师头像")
    private String counselorAvatar;

    /**
     * 支付状态:1:未支付,2:一支付
     */
    @ApiModelProperty("支付状态")
    private Integer payState;

    /**
     * 支付金额
     */
    @ApiModelProperty("支付金额")
    private String payMoney;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("支付时间")
    private Date payTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型，1：咨询，2：课程")
    private Integer orderType;

    /**
     * 渠道来源
     */
    @ApiModelProperty("渠道来源，1：抖音，2：微信")
    private Integer platform;
}
