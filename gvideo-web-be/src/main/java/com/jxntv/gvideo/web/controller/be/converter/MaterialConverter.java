package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupGatherActivityClient;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupTopicDTO;
import com.jxntv.gvideo.media.client.MaterialClient;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.TvChannelClient;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.dto.tv.TvChannelDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.media.client.enums.BannerJumpMediaType;
import com.jxntv.gvideo.media.client.enums.BannerMenuEnum;
import com.jxntv.gvideo.media.client.enums.Jump;
import com.jxntv.gvideo.media.client.enums.MaterialType;
import com.jxntv.gvideo.web.controller.be.model.vo.*;
import com.jxntv.gvideo.web.controller.be.rpc.OssConsumer;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import com.jxntv.gvideo.web.controller.be.utils.StatusUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Optional;

@Component
public class MaterialConverter {

    @Resource
    private OssConsumer ossConsumer;
    @Resource
    private GroupTopicClient groupTopicClient;
    @Resource
    private MediaResourceClient mediaResourceClient;
    @Resource
    private UserConsumer userConsumer;
    @Autowired
    private TvChannelClient tvChannelClient;
    @Autowired
    private TvProgramClient tvProgramClient;
    @Autowired
    private MaterialClient materialClient;
    @Resource
    private GroupGatherActivityClient groupGatherActivityClient;

    public MaterialVo convert(MaterialDTO dto) {
        MaterialVo vo = new MaterialVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setType(MaterialType.toStr(dto.getType()));
        vo.setResourceCount(materialClient.resourceCount(dto.getId()).getResult());
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setUpdateUser(userConsumer.nameWithRemark(dto.getCreateUserId()));
        vo.setStatus(StatusUtils.toStr(dto.getStatus()));
        vo.setPeriod(DateUtils.join(dto.getPeriodStart(), dto.getPeriodEnd()));
        return vo;
    }

    public MaterialDTO convert(AddMaterialVo addMaterialVo) {
        MaterialDTO dto = new MaterialDTO();
        dto.setType(addMaterialVo.getType());
        if (addMaterialVo.getAdvertising() != null) {
            dto.setAdvertising(BooleanUtils.toBoolean(addMaterialVo.getAdvertising()));
        }
        dto.setName(addMaterialVo.getName());
        dto.setTitle(addMaterialVo.getTitle());
        dto.setRemark(addMaterialVo.getRemark());
        dto.setPeriodStart(DateUtils.parse(addMaterialVo.getPeriodStart()));
        dto.setPeriodEnd(DateUtils.parse(addMaterialVo.getPeriodEnd()));
        dto.setCornerMark(addMaterialVo.getCornerMark());
        dto.setCornerMarkStartDate(DateUtils.parse(addMaterialVo.getCornerMarkStartDate()));
        dto.setCornerMarkEndDate(DateUtils.parse(addMaterialVo.getCornerMarkEndDate()));

        if (addMaterialVo.getType() == null) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "素材类型不能为空");
        }
        if (addMaterialVo.getType().equals(MaterialType.SEO_LINK.getCode())) {
            SeoLinkDTO seoLinkDTO = new SeoLinkDTO();
            seoLinkDTO.setImage(addMaterialVo.getSeoLink().getImage());
            seoLinkDTO.setUrl(addMaterialVo.getSeoLink().getUrl());
            seoLinkDTO.setSubTitle(addMaterialVo.getSeoLink().getSubTitle());
            dto.setSeoLinkDTO(seoLinkDTO);
        }
        if (addMaterialVo.getType().equals(MaterialType.OPERATION_LINK.getCode())) {
            OperationLinkDTO operationLinkDTO = new OperationLinkDTO();
            operationLinkDTO.setImage(addMaterialVo.getOperationLink().getImage());
            operationLinkDTO.setUrl(addMaterialVo.getOperationLink().getUrl());
            operationLinkDTO.setPlatform(addMaterialVo.getOperationLink().getPlatform());
            dto.setOperationLinkDTO(operationLinkDTO);
        }
        if (addMaterialVo.getType().equals(MaterialType.MAP_LOCATION.getCode())) {
            MapLocationDTO mapLocationDTO = new MapLocationDTO();
            mapLocationDTO.setImage(addMaterialVo.getMapLocation().getImage());
            mapLocationDTO.setUrl(addMaterialVo.getMapLocation().getUrl());
            mapLocationDTO.setAddress(addMaterialVo.getMapLocation().getAddress());
            dto.setMapLocationDTO(mapLocationDTO);
        }
        if (addMaterialVo.getType().equals(MaterialType.OPEN_ADVERT.getCode())) {
            OpenAdvertDTO openAdvertDTO = new OpenAdvertDTO();
            openAdvertDTO.setAdvertType(addMaterialVo.getOpenAdvert().getAdvertType());
            openAdvertDTO.setContentForm(addMaterialVo.getOpenAdvert().getContentForm());
            openAdvertDTO.setJump(addMaterialVo.getOpenAdvert().getJump());
            openAdvertDTO.setJumpUrl(addMaterialVo.getOpenAdvert().getJumpUrl());
            openAdvertDTO.setMediaFile(addMaterialVo.getOpenAdvert().getMediaFile());
            dto.setOpenAdvertDTO(openAdvertDTO);
        }
        if (addMaterialVo.getType().equals(MaterialType.ACTIVITY_REGISTRATION.getCode())) {
            ActivityRegistrationDTO activityRegistrationDTO = new ActivityRegistrationDTO();
            activityRegistrationDTO.setDeadLine(DateUtils.parse(addMaterialVo.getActivityRegistration().getDeadLine()));
            activityRegistrationDTO.setImage(addMaterialVo.getActivityRegistration().getImage());
            activityRegistrationDTO.setUrl(addMaterialVo.getActivityRegistration().getUrl());
            dto.setActivityRegistrationDTO(activityRegistrationDTO);
        }
        return dto;
    }

    public MaterialDTO convert(EditMaterialVo editMaterialVo) {
        MaterialDTO dto = new MaterialDTO();
        dto.setType(editMaterialVo.getType());
        dto.setAdvertising(editMaterialVo.getAdvertising());
        dto.setName(editMaterialVo.getName());
        dto.setTitle(editMaterialVo.getTitle());
        dto.setRemark(editMaterialVo.getRemark());
        dto.setPeriodStart(DateUtils.parse(editMaterialVo.getPeriodStart()));
        dto.setPeriodEnd(DateUtils.parse(editMaterialVo.getPeriodEnd()));
        dto.setCornerMark(editMaterialVo.getCornerMark() == null ? -1 : editMaterialVo.getCornerMark());
        dto.setCornerMarkStartDate(DateUtils.parse(editMaterialVo.getCornerMarkStartDate()));
        dto.setCornerMarkEndDate(DateUtils.parse(editMaterialVo.getCornerMarkEndDate()));
        dto.setStatus(editMaterialVo.getStatus());

        if (editMaterialVo.getType().equals(MaterialType.SEO_LINK.getCode())) {
            SeoLinkDTO seoLinkDTO = new SeoLinkDTO();
            seoLinkDTO.setImage(editMaterialVo.getSeoLink().getImage());
            seoLinkDTO.setUrl(editMaterialVo.getSeoLink().getUrl());
            seoLinkDTO.setSubTitle(editMaterialVo.getSeoLink().getSubTitle());
            dto.setSeoLinkDTO(seoLinkDTO);
        }
        if (editMaterialVo.getType().equals(MaterialType.OPERATION_LINK.getCode())) {
            OperationLinkDTO operationLinkDTO = new OperationLinkDTO();
            operationLinkDTO.setImage(editMaterialVo.getOperationLink().getImage());
            operationLinkDTO.setUrl(editMaterialVo.getOperationLink().getUrl());
            operationLinkDTO.setPlatform(editMaterialVo.getOperationLink().getPlatform());
            dto.setOperationLinkDTO(operationLinkDTO);
        }
        if (editMaterialVo.getType().equals(MaterialType.MAP_LOCATION.getCode())) {
            MapLocationDTO mapLocationDTO = new MapLocationDTO();
            mapLocationDTO.setImage(editMaterialVo.getMapLocation().getImage());
            mapLocationDTO.setUrl(editMaterialVo.getMapLocation().getUrl());
            mapLocationDTO.setAddress(editMaterialVo.getMapLocation().getAddress());
            dto.setMapLocationDTO(mapLocationDTO);
        }
        if (editMaterialVo.getType().equals(MaterialType.OPEN_ADVERT.getCode())) {
            OpenAdvertDTO openAdvertDTO = new OpenAdvertDTO();
            openAdvertDTO.setAdvertType(editMaterialVo.getOpenAdvert().getAdvertType());
            openAdvertDTO.setContentForm(editMaterialVo.getOpenAdvert().getContentForm());
            openAdvertDTO.setJump(editMaterialVo.getOpenAdvert().getJump());
            openAdvertDTO.setJumpUrl(editMaterialVo.getOpenAdvert().getJumpUrl());
            openAdvertDTO.setMediaFile(editMaterialVo.getOpenAdvert().getMediaFile());
            dto.setOpenAdvertDTO(openAdvertDTO);
        }
        if (editMaterialVo.getType().equals(MaterialType.ACTIVITY_REGISTRATION.getCode())) {
            ActivityRegistrationDTO activityRegistrationDTO = new ActivityRegistrationDTO();
            activityRegistrationDTO.setDeadLine(DateUtils.parse(editMaterialVo.getActivityRegistration().getDeadLine()));
            activityRegistrationDTO.setImage(editMaterialVo.getActivityRegistration().getImage());
            activityRegistrationDTO.setUrl(editMaterialVo.getActivityRegistration().getUrl());
            dto.setActivityRegistrationDTO(activityRegistrationDTO);
        }
        return dto;
    }

    public AddMaterialVo convertAdd(MaterialDTO materialDTO) {
        AddMaterialVo addMaterialVo = new AddMaterialVo();
        addMaterialVo.setType(materialDTO.getType());
        addMaterialVo.setAdvertising(materialDTO.getAdvertising() ? 1 : 0);
        addMaterialVo.setName(materialDTO.getName());
        addMaterialVo.setTitle(materialDTO.getTitle());
        addMaterialVo.setRemark(materialDTO.getRemark());
        addMaterialVo.setPeriodStart(DateUtils.format(materialDTO.getPeriodStart()));
        addMaterialVo.setPeriodEnd(DateUtils.format(materialDTO.getPeriodEnd()));
        addMaterialVo.setCornerMark(Objects.equals(materialDTO.getCornerMark(), -1) ? null : materialDTO.getCornerMark());
        addMaterialVo.setCornerMarkStartDate(DateUtils.format(materialDTO.getCornerMarkStartDate()));
        addMaterialVo.setCornerMarkEndDate(DateUtils.format(materialDTO.getCornerMarkEndDate()));

        if (materialDTO.getType().equals(MaterialType.SEO_LINK.getCode())) {
            SeoLinkVo vo = new SeoLinkVo();
            vo.setImageId(materialDTO.getSeoLinkDTO().getImage());
            vo.setImage(ossConsumer.getImageUrl(materialDTO.getSeoLinkDTO().getImage()));
            vo.setUrl(materialDTO.getSeoLinkDTO().getUrl());
            vo.setSubTitle(materialDTO.getSeoLinkDTO().getSubTitle());
            addMaterialVo.setSeoLink(vo);
        } else {
            addMaterialVo.setSeoLink(new SeoLinkVo());
        }
        if (materialDTO.getType().equals(MaterialType.OPERATION_LINK.getCode())) {
            OperationLinkVo vo = new OperationLinkVo();
            vo.setImage(materialDTO.getOperationLinkDTO().getImage());
            vo.setImage(ossConsumer.getImageUrl(materialDTO.getOperationLinkDTO().getImage()));
            vo.setUrl(materialDTO.getOperationLinkDTO().getUrl());
            vo.setPlatform(materialDTO.getOperationLinkDTO().getPlatform());
            addMaterialVo.setOperationLink(vo);
        } else {
            addMaterialVo.setOperationLink(new OperationLinkVo());
        }
        if (materialDTO.getType().equals(MaterialType.MAP_LOCATION.getCode())) {
            MapLocationVo vo = new MapLocationVo();
            vo.setImageId(materialDTO.getMapLocationDTO().getImage());
            vo.setImage(ossConsumer.getImageUrl(materialDTO.getMapLocationDTO().getImage()));
            vo.setUrl(materialDTO.getMapLocationDTO().getUrl());
            vo.setAddress(materialDTO.getMapLocationDTO().getAddress());
            addMaterialVo.setMapLocation(vo);
        } else {
            addMaterialVo.setMapLocation(new MapLocationVo());
        }
        if (materialDTO.getType().equals(MaterialType.OPEN_ADVERT.getCode())) {
            OpenAdvertVo vo = new OpenAdvertVo();
            vo.setAdvertType(materialDTO.getOpenAdvertDTO().getAdvertType());
            vo.setContentForm(materialDTO.getOpenAdvertDTO().getContentForm());
            vo.setJump(materialDTO.getOpenAdvertDTO().getJump());
            vo.setJumpUrl(materialDTO.getOpenAdvertDTO().getJumpUrl());
            vo.setJumpTitle(this.getJumpTitle(materialDTO.getOpenAdvertDTO()));

            String uuid = materialDTO.getOpenAdvertDTO().getMediaFile();
            vo.setMediaFileId(uuid);
            vo.setMediaFile(ossConsumer.getImageUrl(uuid));
            addMaterialVo.setOpenAdvert(vo);
        } else {
            addMaterialVo.setOpenAdvert(new OpenAdvertVo());
        }
        if (materialDTO.getType().equals(MaterialType.ACTIVITY_REGISTRATION.getCode())) {
            ActivityRegistrationVo vo = new ActivityRegistrationVo();
            vo.setDeadLine(DateUtils.format(materialDTO.getActivityRegistrationDTO().getDeadLine()));
            vo.setImageId(materialDTO.getActivityRegistrationDTO().getImage());
            vo.setImage(ossConsumer.getImageUrl(materialDTO.getActivityRegistrationDTO().getImage()));
            vo.setUrl(materialDTO.getActivityRegistrationDTO().getUrl());
            addMaterialVo.setActivityRegistration(vo);
        } else {
            addMaterialVo.setActivityRegistration(new ActivityRegistrationVo());
        }
        return addMaterialVo;
    }

    /**
     * 获取列表展示的跳转地址
     *
     * @param dto
     * @return
     */
    private String getJumpTitle(OpenAdvertDTO dto) {

        if (Objects.isNull(dto)) {
            return "";
        }

        //  外部跳转直接返回jumpUrl
        if (Objects.equals(Jump.OUT.getCode(), dto.getJump())) {
            return dto.getJumpUrl();
        }

        if (StringUtils.isEmpty(dto.getJumpUrl())) {
            return "";
        }
        String[] jumps = dto.getJumpUrl().split("-");
        Integer jumpMediaType = jumps.length >= 1 ? Integer.parseInt(jumps[0]) : -1;
        Long jumpMediaId = jumps.length == 2 ? Long.parseLong(jumps[1]) : 0L;
        //  内部跳转判断跳转类型
        if (Objects.equals(Jump.LOCATION.getCode(), dto.getJump())) {
            BannerMenuEnum menu = BannerMenuEnum.parse(jumpMediaType);
            if (Objects.nonNull(menu)) {
                return menu.getDesc();
            }
        }
        if (Objects.equals(Jump.CONTENT.getCode(), dto.getJump())) {

            if (BannerJumpMediaType.NEWS.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> mediaResourceClient.get(e))
                        .map(Result::getResult)
                        .map(MediaResourceDTO::getShowName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.GROUP.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> groupTopicClient.getSimpleById(e))
                        .map(Result::getResult)
                        .map(GroupInfoDTO::getName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.TOPIC.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> groupTopicClient.topicDetail(e, null))
                        .map(Result::getResult)
                        .map(GroupTopicDTO::getContent)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.POSTS.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> mediaResourceClient.get(e))
                        .map(Result::getResult)
                        .map(MediaResourceDTO::getShowName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.LIVE_BROADCAST.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> mediaResourceClient.get(e))
                        .map(Result::getResult)
                        .map(MediaResourceDTO::getShowName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.COLUMN.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> tvProgramClient.getById(e))
                        .map(Result::getResult)
                        .map(TvProgramDTO::getProgramName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.CHANNEL.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> tvChannelClient.getById(e))
                        .map(Result::getResult)
                        .map(TvChannelDTO::getChannelName)
                        .orElse("");
                return title;
            }
            if (BannerJumpMediaType.ACTIVITY_GATHER.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> groupGatherActivityClient.getActivityById(e))
                        .map(Result::getResult)
                        .map(GroupGatherActivityDTO::getTitle)
                        .orElse("");
                return title;
            }

            if (BannerJumpMediaType.GAN_YUN_VIDEO.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> mediaResourceClient.get(e))
                        .map(Result::getResult)
                        .map(MediaResourceDTO::getShowName)
                        .orElse("");
                return title;
            }

            if (BannerJumpMediaType.SPECIAL.getCode().equals(jumpMediaType)) {
                String title = Optional.ofNullable(jumpMediaId)
                        .map(e -> mediaResourceClient.get(e))
                        .map(Result::getResult)
                        .map(MediaResourceDTO::getShowName)
                        .orElse("");
                return title;
            }
        }

        return null;
    }
}
