package com.jxntv.gvideo.web.controller.be.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description PGC内容资源
 * @date 2021/04/30 17:04
 */
@Data
@NoArgsConstructor
@ApiModel(value = "AddMediaResourceExternalVO", description = "外源数据表单")
public class AddMediaResourceExternalVO {

    @ApiModelProperty(value = "作者名称")
    @JsonProperty("author")
    private String author;
    @ApiModelProperty(value = "作者ID")
    @JsonProperty("author_id")
    private Long authorId;
    @ApiModelProperty(value = "内容标题")
    @JsonProperty("title")
    private String title;
    @ApiModelProperty(value = "内容正文")
    @JsonProperty("content")
    private String content;
    @ApiModelProperty(value = "发布平台")
    @JsonProperty("platform")
    private String platform;
    @ApiModelProperty(value = "平台内容ID")
    @JsonProperty("content_id")
    private String contentId;

    @ApiModelProperty(value = "租户ID")
    @JsonProperty("tenant_id")
    private Long tenantId;

    @ApiModelProperty(value = "是否可以评论")
    @JsonProperty("can_comment")
    private Boolean canComment;

    @ApiModelProperty(value = "是否可以搜索")
    @JsonProperty("can_search")
    private Boolean canSearch;

    @ApiModelProperty(value = "内容创建时间")
    @JsonProperty("create_time")
    private String createTime;
}
