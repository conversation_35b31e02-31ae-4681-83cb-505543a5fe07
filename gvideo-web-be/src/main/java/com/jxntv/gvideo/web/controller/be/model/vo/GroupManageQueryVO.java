package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> Created on 2021/5/11.
 */
@Data
@ApiModel("GroupManageQueryVO")
public class GroupManageQueryVO {
    @ApiModelProperty("圈子id")
    @NotNull(message = "社区ID不能为空")
    private Long groupId;

    @ApiModelProperty("手机号列表")
    @NotNull(message = "手机号列表不能为空")
    private List<String> mobileList;

}
