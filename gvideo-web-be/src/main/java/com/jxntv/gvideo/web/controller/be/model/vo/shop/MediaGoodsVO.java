package com.jxntv.gvideo.web.controller.be.model.vo.shop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/6/1
 * Email: <EMAIL>
 */

@Data
@ApiModel("直播间商品信息")
public class MediaGoodsVO {

    @ApiModelProperty("ID")
    private Long id;


    @ApiModelProperty("商品名称")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品图片URL")
    private String iconUrl;

    @ApiModelProperty("商品价格")
    private BigDecimal price;

    @ApiModelProperty("是否正在展示")
    private Boolean isShow;

}
