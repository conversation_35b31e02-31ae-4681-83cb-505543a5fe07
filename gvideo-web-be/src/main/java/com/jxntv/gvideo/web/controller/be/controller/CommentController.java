package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.CommentClient;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.interact.client.dto.CommentPrimaryDTO;
import com.jxntv.gvideo.interact.client.dto.CommentReplyDTO;
import com.jxntv.gvideo.interact.client.dto.CommentTreeDTO;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.param.QueryCommentParam;
import com.jxntv.gvideo.track.sdk.log.SysLogGroup;
import com.jxntv.gvideo.track.sdk.log.Syslog;
import com.jxntv.gvideo.web.controller.be.client.AnswerService;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.CommentConverter;
import com.jxntv.gvideo.web.controller.be.model.vo.CommentTreeVo;
import com.jxntv.gvideo.web.controller.be.model.vo.CommentVo;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created on 2020-02-03
 */
@RestController
@RequestMapping("/api/comment")
@Api(value = "评论相关接口", tags = {"CommentController"})
public class CommentController {

    @Autowired
    private CommentClient commentClient;
    @Autowired
    private CommentConverter converter;
    @Autowired
    private AnswerService answerService;

    @GetMapping("/list")
    @ApiOperation(value = "评论管理列表", notes = "评论管理列表")
    public Result<Page<CommentVo>> getCommentList(
            @ApiParam(name = "type", value = "评论类型0主评论1回复评论2全部") @RequestParam(value = "type", required = false)
                    Integer type,
            @ApiParam(name = "status", value = "评论状态1启用0禁用2全部") @RequestParam(value = "status", required = false)
                    Integer status,
            @ApiParam(name = "createStartDate", value = "提交审核开始时间") @RequestParam(value = "createStartDate", required = false)
                    String createStartDate,
            @ApiParam(name = "createEndDate", value = "提交审核结束时间") @RequestParam(value = "createEndDate", required = false)
                    String createEndDate,
            @ApiParam(name = "consumerUser", value = "C端用户名或ID") @RequestParam(value = "consumerUser", required = false)
                    String consumerUser,
            @ApiParam(name = "mediaId", value = "资源ID") @RequestParam(value = "mediaId", required = false) Long mediaId,
            @ApiParam(name = "commentId", value = "评论ID") @RequestParam(value = "commentId", required = false)
                    Long commentId,
            @ApiParam(name = "pageNum", value = "页码", required = true) @RequestParam("pageNum") Integer pageNum,
            @ApiParam(name = "pageSize", value = "每页数量", required = true) @RequestParam("pageSize") Integer pageSize,
            @ApiParam(name = "aiAuditState", value = "机审状态", required = true) @RequestParam("aiAuditState") Integer aiAuditState,
            @RequestParam(value = "keyword") String keyword) {
        QueryCommentParam param = new QueryCommentParam();
        param.setType(type);
        param.setStatus(status);
        param.setCreateStartDate(DateUtils.parse(createStartDate));
        param.setCreateEndDate(DateUtils.parse(createEndDate));
        param.setConsumerUser(consumerUser);
        param.setCommentId(commentId);
        param.setCurrent(pageNum);
        param.setSize(pageSize);
        param.setAiAuditState(aiAuditState);
        param.setKeyword(keyword);
        Result<PageDTO<CommentDTO>> result = commentClient.queryCommentList(param);
        return Result.ok(Page.pageOf(result.getResult().getList(),
                (int) result.getResult().getTotal(),
                (int) result.getResult().getPageNum(), (int) result.getResult().getPageSize(),
                converter::toVo, CommentVo.class));
    }

    @PutMapping("/status")
    @ApiOperation(value = "评论启用隐藏接口", notes = "对评论进行启用和隐藏操作")
    @Syslog(type= SysLogGroup.COMMENT_AUDIT,subject = "评论启用/隐藏")
    public Result changeStatus(
            @ApiParam(name = "id", value = "评论或回复ID", required = true) @RequestParam("id") Long id,
            @ApiParam(name = "type", value = "评论类型", required = true) @RequestParam("type") Integer type,
            @ApiParam(name = "status", value = "启用隐藏状态，1启用0隐藏", required = true) @RequestParam("status") Integer status) {
        Result result = commentClient.changeStatus(id, type, status);
        //主评论
        if (type == 0) {
            //修改问答记录表状态
            answerService.status(status, Collections.singletonList(id), 2);
        }
        return result;
    }

    @PutMapping("/batch/status")
    @ApiOperation(value = "评论启用隐藏批量接口", notes = "对评论进行批量启用和隐藏操作")
    @Syslog(type= SysLogGroup.COMMENT_AUDIT,subject = "批量-评论启用/隐藏")
    public Result changeBatchStatus(@RequestBody Map<String, Object> map) {
        List<Map<String, Integer>> list = (List<Map<String, Integer>>) map.get("comments");
        for (Map<String, Integer> m : list) {
            Long id = (long) m.get("id");
            Integer type = m.get("type");
            Integer status = m.get("status");
            commentClient.changeStatus(id, type, status);

            //主评论
            if (type == 0) {
                //修改问答记录表状态
                answerService.status(status, Collections.singletonList(id), 2);
            }
        }
        return Result.ok();
    }

    @GetMapping("/tree")
    @ApiOperation(value = "某个评论及回复树", notes = "获取某个评论或某个回复所在的整个回复集合")
    public Result<CommentTreeVo> getReplyTree(
            @ApiParam(name = "id", value = "评论或回复ID", required = true) @RequestParam("id") Long id,
            @ApiParam(name = "type", value = "评论类型", required = true) @RequestParam("type") Integer type) {
        Result<CommentTreeDTO> result = commentClient.getReplyTree(id, type, null, null);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            return Result.fail("获取失败");
        }
        return Result.ok(converter.toTreeVO(result.getResult()));
    }

    @GetMapping
    @ApiOperation(value = "获取评论及回复内容", notes = "获取评论及回复内容")
    public Result<CommentVo> getComment(
            @ApiParam(name = "id", value = "评论或回复ID", required = true) @RequestParam("id") Long id,
            @ApiParam(name = "type", value = "评论类型: 0-主评论、1-回复", required = true) @RequestParam("type") Integer type) {
        // 0-主评论、1-回复
        if (type.equals(0)) {
            CommentPrimaryDTO commentPrimaryDTO = commentClient.getPrimary(id).getResult();
            if (Objects.isNull(commentPrimaryDTO)) {
                return Result.fail("获取失败");
            }
            return Result.ok(converter.toVo(commentPrimaryDTO));
        } else {
            CommentReplyDTO commentReplyDTO = commentClient.getReply(id).getResult();
            if (Objects.isNull(commentReplyDTO)) {
                return Result.fail("获取失败");
            }
            return Result.ok(converter.toVo(commentReplyDTO));
        }
    }

}
