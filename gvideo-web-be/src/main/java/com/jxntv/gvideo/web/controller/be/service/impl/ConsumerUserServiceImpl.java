package com.jxntv.gvideo.web.controller.be.service.impl;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.group.sdk.client.*;
import com.jxntv.gvideo.interact.client.CommentClient;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.interact.client.MessageClient;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.om.client.AuditMediaClient;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.web.controller.be.client.ConsultMentorService;
import com.jxntv.gvideo.web.controller.be.service.ConsumerUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/14
 */
@Slf4j
@Service
public class ConsumerUserServiceImpl implements ConsumerUserService {

    @Autowired
    private ConsumerUserClient consumerUserClient;
    @Autowired
    private InteractClient interactClient;
    @Resource
    private MessageClient messageClient;
    @Autowired
    private MediaResourceClient mediaResourceClient;
    @Autowired
    private CommentClient commentClient;
    @Resource
    private AuditMediaClient auditMediaClient;
    @Resource
    private GroupManageClient groupManageClient;
    @Resource
    private GroupBlindDateUserClient groupBlindDateUserClient;
    @Resource
    private MentorInfoClient mentorInfoClient;
    @Autowired
    private ConsultMentorService consultMentorService;
    @Resource
    private GroupRedNameClient groupRedNameClient;
    @Resource
    private GroupMedalGrantClient groupMedalGrantClient;

    @Override
    public void clearUserInfo(Long jid) {
        if (Objects.isNull(jid)){
            return;
        }
        //  废弃所有的该用户发布的数据
        removeMedia(jid);
        //  删除用户互动数据（关注，喜欢，消息）
        removeInteract(jid);
        //  删除用户评论相关数据
        removeComment(jid);
        //  删除社区相关数据
        removeGroup(jid);
        //  删除用户都市放心爱的相亲相关信息
        removeBlindDateInfo(jid);
        //  删除用户相关信息
        removeAllUserInfo(jid);
        //删除导师相关信息
        removeMentorInfo(jid);
        //勋章红名相关信息
        removeMedalAndRedNameInfo(jid);
    }

    /**
     * 移除用户在社区相关数据
     *
     * @param jid 用户id
     */
    private void removeGroup(Long jid) {
        if (!groupManageClient.exitAllByJid(jid).callSuccess()) {
            log.info("移除用户加入社区错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }

    private void removeAllUserInfo(Long jid) {
        // 用户的资料要删除，包括审核中的
        if (!consumerUserClient.deleteAllUserInfo(jid).callSuccess()) {
            log.info("删除用户的所有相关审核中资料出错,账户" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

    }

    /**
     * 删除用户的评论、回复、点赞、评论审核数据
     *
     * @param jid 用户id
     */
    private void removeComment(Long jid) {
        //  清理主评论
        if (!commentClient.deletePrimaryByFromJid(jid).callSuccess()) {
            log.info("清理主评论错误,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
        //  清理评论回复
        if (!commentClient.deleteReplyByFromJid(jid).callSuccess()) {
            log.info("清理评论回复错误,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
        //  清理评审核中的评论
        if (!commentClient.deleteAuditCommentByFromJid(jid).callSuccess()) {
            log.info("清理评论审核数据错误,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
        //  清理点赞数据
        if (!commentClient.deletePraiseByFromJid(jid).callSuccess()) {
            log.info("清理点赞数据错误,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }

    /**
     * 移除用户互动数据
     * 1、关注
     * 2、喜欢
     * 3、用户消息
     *
     * @param jid 用户id
     */
    private void removeInteract(Long jid) {
        //  清理用户的关注
        if (!interactClient.deleteUserFollows(jid).callSuccess()) {
            log.info("清理用户关注错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        //  清理用户被关注
        if (!interactClient.deleteAuthorFollows(jid).callSuccess()) {
            log.info("清理用户被关注错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        //  清理我喜欢的内容
        if (!interactClient.deleteUserFavorites(jid).callSuccess()) {
            log.info("清理我喜欢的内容错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        //  清理掉因我发布的内容产生的被喜欢
        PageDTO<MediaResourceDTO> ownMediaPage;
        int current = 1;
        int size = 100;
        do {
            ownMediaPage = mediaResourceClient.getOwnMedias(jid, current++, size).orElseThrow(() -> {
                log.info("查询用户发布内容错误，账号" + jid);
                return new CodeMessageException(CodeMessage.ERROR);
            });

            List<Long> mediaIds = ownMediaPage.getList().stream().map(MediaResourceDTO::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(mediaIds)) {
                log.info("用户发布视频ID为：" + StringUtils.join(mediaIds.toArray()));
                if (!interactClient.updateFavoriteAuthorStatus(mediaIds, false).callSuccess()) {
                    log.info("同步收藏作者状态为禁用出错");
                    throw new CodeMessageException(CodeMessage.ERROR);
                }
            }

        } while (ownMediaPage.getPageNum() * ownMediaPage.getPageSize() < ownMediaPage.getTotal());

        //  清理用户的消息
        if (!messageClient.deleteUserMessage(jid).callSuccess()) {
            log.info("清理用户消息错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

    }

    /**
     * 移除用户资源数据
     * 1、发布的资源
     * 2、在审核资源
     * 3、问答广场资源
     *
     * @param jid 用户id
     */
    private void removeMedia(Long jid) {
        if (!mediaResourceClient.discardUgc(jid).callSuccess()) {
            log.error("废弃用户发布媒体资源数据异常,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        if (!auditMediaClient.deleteAuditMediaByAuthor(1, jid).callSuccess()) {
            log.error("删除用户审核资源数据出错,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }

    /**
     * 删除用户的相亲信息
     *
     * @param jid 用户id
     */
    private void removeBlindDateInfo(Long jid) {
        //  清理相亲数据
        if (!groupBlindDateUserClient.removeUserInfoByJid(jid).callSuccess()) {
            log.info("清理都市放心爱的用户相亲数据出错,账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }
    /**
     * 删除用户的导师信息
     *
     * @param jid 用户id
     */
    private void removeMentorInfo(Long jid) {

        if (!mentorInfoClient.deleteMentorByJid(jid).callSuccess()) {
            log.info("删除用户的导师资料出错,账户" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        if (!mentorInfoClient.deleteFreeMentorByJid(jid).callSuccess()) {
            log.info("删除用户的免费导师资料出错,账户" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

        if (!consultMentorService.deleteMentorByJid(jid).callSuccess()) {
            log.info("删除付费咨询的导师资料出错,账户" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }

    }

    /**
     * 勋章红名信息
     *
     * @param jid 用户id
     */
    private void removeMedalAndRedNameInfo(Long jid) {
        if (!groupMedalGrantClient.deleteGrantByJid(jid).callSuccess()) {
            log.info("移除用户勋章错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
        if (!groupRedNameClient.deleteRedNameByJid(jid).callSuccess()) {
            log.info("移除用户红名错误，账号" + jid);
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }
}
