package com.jxntv.gvideo.web.controller.be.model.vo.gather.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupGatherActivityVO", description = "活动组件列表")
public class GroupGatherActivityVO {

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "活动主题")
    private String title;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @ApiModelProperty(value = "活动报名开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date joinStartDate;

    @ApiModelProperty(value = "活动报名结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date joinEndDate;

    @ApiModelProperty(value = "活动状态：0-未开始、1-进行中、2-已结束")
    private Integer status;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    private Integer costFlag;

    @ApiModelProperty(value = "活动类型  0-普通活动 1-约会计划")
    private Integer type;

}
