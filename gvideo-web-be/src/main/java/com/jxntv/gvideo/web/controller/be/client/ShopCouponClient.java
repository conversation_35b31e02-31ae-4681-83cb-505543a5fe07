package com.jxntv.gvideo.web.controller.be.client;

import com.jxntv.gvideo.web.controller.be.client.dto.ShopCouponDTO;
import com.jxntv.gvideo.web.controller.be.client.dto.ShopListDTO;
import com.jxntv.gvideo.web.controller.be.client.dto.WebPageDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2022/6/2
 * Email: <EMAIL>
 */
@FeignClient(name = "shop-service",url = "${shop.base-api-url}", contextId = "coupon")
public interface ShopCouponClient {

    @GetMapping("/shops/list")
    WebPageDTO<ShopListDTO> listShop(@RequestParam Long page_no, @RequestParam Long page_size);


    @GetMapping("/promotions/coupons/all")
    WebPageDTO<ShopCouponDTO> listCoupon(@RequestParam Long page_no,@RequestParam Long page_size,@RequestParam Long seller_id);

}
