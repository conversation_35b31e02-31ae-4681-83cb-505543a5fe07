package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.OutShareUserClient;
import com.jxntv.gvideo.media.client.OutShareWhitelistClient;
import com.jxntv.gvideo.media.client.dto.OutShareUserDTO;
import com.jxntv.gvideo.media.client.dto.OutShareUserSearchDTO;
import com.jxntv.gvideo.media.client.dto.OutShareWhitelistSearchDTO;
import com.jxntv.gvideo.media.client.dto.OutShareWhitelistDTO;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.OutShareUserConverter;
import com.jxntv.gvideo.web.controller.be.converter.OutShareWhitelistConverter;
import com.jxntv.gvideo.web.controller.be.model.vo.outshare.OutShareUserVO;
import com.jxntv.gvideo.web.controller.be.model.vo.outshare.OutShareWhitelistVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/out-share")
@Api(value = "外部分享管理", tags = {"OutShareController"})
public class OutShareController {

    @Resource
    private OutShareWhitelistClient outShareWhitelistClient;
    @Resource
    private OutShareWhitelistConverter outShareWhitelistConverter;
    @Resource
    private OutShareUserClient outShareUserClient;
    @Resource
    private OutShareUserConverter outShareUserConverter;

    @PostMapping("/whitelist")
    public Result<Long> create(@RequestBody OutShareWhitelistVO vo) {
        OutShareWhitelistDTO dto = outShareWhitelistConverter.toDTO(vo);
        LocalDateTime now = LocalDateTime.now();
        dto.setCreateDate(now);
        dto.setUpdateDate(now);
        return outShareWhitelistClient.create(dto);
    }

    @PutMapping("/whitelist/{id}")
    public Result<Void> update(@PathVariable("id") Long id, @RequestBody OutShareWhitelistVO vo) {
        OutShareWhitelistDTO dto = outShareWhitelistConverter.toDTO(vo);
        dto.setUpdateDate(LocalDateTime.now());
        return outShareWhitelistClient.update(id, dto);
    }

    @GetMapping("/whitelist/{id}")
    public Result<OutShareWhitelistVO> getById(@PathVariable("id") Long id) {
        return outShareWhitelistClient.getById(id).map(outShareWhitelistConverter::toVO);
    }

    @GetMapping("/whitelist/page")
    public Result<Page<OutShareWhitelistVO>> page(@RequestParam Integer pageNum,
                                                  @RequestParam Integer pageSize,
                                                  @RequestParam(required = false) String nameLike,
                                                  @RequestParam(required = false) Boolean asc,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updateDateStart,
                                                  @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime updateDateEnd) {
        OutShareWhitelistSearchDTO searchDTO = new OutShareWhitelistSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setNameLike(nameLike);
        searchDTO.setAsc(asc);
        searchDTO.setUpdateDateStart(updateDateStart);
        searchDTO.setUpdateDateEnd(updateDateEnd);

        Result<PageDTO<OutShareWhitelistDTO>> result = outShareWhitelistClient.page(searchDTO);
        return result.map(page -> Page.pageOfTitle(page, outShareWhitelistConverter::toVO, OutShareWhitelistVO.class));
    }

    @GetMapping("/whitelist/all")
    public Result<List<OutShareWhitelistVO>> all() {
        return outShareWhitelistClient.all().map(list -> list.stream().map(outShareWhitelistConverter::toVO).collect(Collectors.toList()));
    }

    @DeleteMapping("/whitelist/{id}")
    public Result<Void> deleteById(@PathVariable("id") Long id) {
        return outShareWhitelistClient.deleteById(id);
    }


    @GetMapping("/whitelist/analysis")
    @ApiOperation(value = "解析外部链接内容")
    public Result<Map<String, Object>> analysis(@RequestParam String url) {
        try {
            Document document = Jsoup.parse(new URL(url), 5000);
            if (Objects.isNull(document)) {
                return Result.ok();
            }
            Elements elements = document.getElementsByTag("title");
            if (CollectionUtils.isEmpty(elements)) {
                return Result.ok();
            }
            Map<String, Object> result = new HashMap<>(2);
            result.put("title", elements.get(0).text());
            return Result.ok(result);
        } catch (IOException e) {
            log.error("【网址jsoup解析】解析异常：{}", e.getLocalizedMessage());
            return Result.fail("外部链接解析失败，请检查外部链接");
        }
    }

}
