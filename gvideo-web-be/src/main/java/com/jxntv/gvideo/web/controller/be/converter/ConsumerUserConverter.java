package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AuditClient;
import com.jxntv.gvideo.om.dto.AuditProcedureDTO;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.SysRoleClient;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.*;
import com.jxntv.gvideo.web.controller.be.client.MediaResourceService;
import com.jxntv.gvideo.web.controller.be.model.vo.*;
import com.jxntv.gvideo.web.controller.be.model.vo.audit.CurrentAdminUserVo;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import com.jxntv.gvideo.web.controller.be.utils.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created on 2020-02-14
 */
@Slf4j
@Component
public class ConsumerUserConverter {

    @Resource
    private OssClient ossClient;
    @Resource
    private ConsumerUserClient userClient;

    @Resource
    private AuditClient auditClient;
    @Resource
    private SysUserClient sysUserClient;
    @Resource
    private SysRoleClient sysRoleClient;

    public ConsumerUserVo toVo(ConsumerUserDTO dto) {
        ConsumerUserVo vo = new ConsumerUserVo();
        Long jid = dto.getJid();
        vo.setJid(jid);
        vo.setNickname(dto.getNickname());
        vo.setMobile(dto.getMobile());
        vo.setType(dto.getType() == 0 ? "普通用户" : "实名认证用户");
        vo.setStatus(dto.getStatus() > 0 ? "启用" : "禁用");
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        MediaResourceService mediaResourceService = SpringUtils.getBean(MediaResourceService.class);
        Result<Integer> sumRst = mediaResourceService.countByJid(jid);
        if (sumRst.callSuccess()) {
            vo.setResourceSum(sumRst.getResult());
        }
        return vo;
    }

    public static AuditingNameVo toVo(ConsumerNicknameDTO dto) {
        AuditingNameVo vo = new AuditingNameVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        vo.setOldNickname(dto.getNicknameOld());
        String memo = dto.getMemo();
        String newNickName = dto.getNickname();
        if (!StringUtils.isEmpty(memo)) {
            String[] memos = memo.split(";");
            String highlightPrefix = "<font color=\"red\">";
            String highlightSuffix = "</font>";
            for (String str : memos) {
                newNickName = newNickName.replace(str, highlightPrefix + str + highlightSuffix);
            }
            vo.setNewNickname(newNickName);
        } else {
            vo.setNewNickname(dto.getNickname());
        }
        vo.setMobile(dto.getMobile());
        vo.setStatus(dto.getStatus() > 0 ? "启用" : "禁用");
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAiAuditState(dto.getAiAuditState());
        return vo;
    }

    public ConsumerAuditVO toVO(ConsumerAuditDTO dto) {
        ConsumerAuditVO vo = new ConsumerAuditVO();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        vo.setMobile(dto.getMobile());
        vo.setCurNickname(dto.getCurNickname());
        vo.setNicknameId(dto.getNicknameId());
        vo.setNickname(dto.getNickname());
        vo.setNicknameOld(dto.getNicknameOld());
        vo.setNicknameCreateDate(dto.getNicknameCreateDate());
        vo.setNicknameCheckStatus(dto.getNicknameCheckStatus());
        vo.setAvatarId(dto.getAvatarId());
        vo.setAvatar(dto.getAvatar());
        vo.setAvatarOld(dto.getAvatarOld());
        vo.setAvatarCreateDate(dto.getAvatarCreateDate());
        vo.setAvatarCheckStatus(dto.getAvatarCheckStatus());
        vo.setInfoId(dto.getInfoId());
        vo.setInfo(dto.getInfo());
        vo.setInfoOld(dto.getInfoOld());
        vo.setInfoCreateDate(dto.getInfoCreateDate());
        vo.setInfoCheckStatus(dto.getInfoCheckStatus());
        vo.setNicknameAiAuditState(dto.getNicknameAiAuditState());
        vo.setAvatarAiAuditState(dto.getAvatarAiAuditState());
        vo.setIntroAiAuditState(dto.getIntroAiAuditState());


        //  设置昵称审核信息
        if (Objects.equals(dto.getNicknamePhase(), 2)) {
            vo.setNicknameCurrentProcedure("完成");
        } else {
            if (Objects.equals(dto.getNicknameCheckStatus(), 0) && Objects.nonNull(dto.getNicknameProcedureId())) {
                Long procedureId = dto.getNicknameProcedureId();
                //  当前审核节点名
                auditClient.getAuditProcedureStateName(procedureId).ifPresent(vo::setNicknameCurrentProcedure);
                //  设置当前审核人信息
                auditClient.getAuditUsers(procedureId).ifPresent(auditUsers -> {
                    List<CurrentAdminUserVo> adminUserVos = auditUsers.stream().map(e -> {
                        CurrentAdminUserVo adminUserVo = new CurrentAdminUserVo();
                        adminUserVo.setUsername(e.getUsername());
                        adminUserVo.setName(e.getName());

                        return adminUserVo;
                    }).collect(Collectors.toList());
                    //  当前可以处理人数
                    vo.setNicknameCurrentAdmin(adminUserVos.size());
                    //  当前可以人信息
                    vo.setNicknameCurrentAdminDetail(adminUserVos);
                });
            } else {
                vo.setNicknameCurrentProcedure("待流转");
            }
        }

        //  设置头像审核信息
        if (Objects.equals(dto.getAvatarPhase(), 2)) {
            vo.setAvatarCurrentProcedure("完成");
        } else {
            if (Objects.equals(dto.getAvatarCheckStatus(), 0) && Objects.nonNull(dto.getAvatarProcedureId())) {
                Long procedureId = dto.getAvatarProcedureId();
                //  当前审核节点名
                auditClient.getAuditProcedureStateName(procedureId).ifPresent(vo::setAvatarCurrentProcedure);
                //  设置当前审核人信息
                auditClient.getAuditUsers(procedureId).ifPresent(auditUsers -> {
                    List<CurrentAdminUserVo> adminUserVos = auditUsers.stream().map(e -> {
                        CurrentAdminUserVo adminUserVo = new CurrentAdminUserVo();
                        adminUserVo.setUsername(e.getUsername());
                        adminUserVo.setName(e.getName());

                        return adminUserVo;
                    }).collect(Collectors.toList());
                    //  当前可以处理人数
                    vo.setAvatarCurrentAdmin(adminUserVos.size());
                    //  当前可以人信息
                    vo.setAvatarCurrentAdminDetail(adminUserVos);
                });
            } else {
                vo.setAvatarCurrentProcedure("待流转");
            }
        }

        //  设置简介审核信息
        if (Objects.equals(dto.getInfoPhase(), 2)) {
            vo.setInfoCurrentProcedure("完成");
        } else {
            if (Objects.equals(dto.getInfoCheckStatus(), 0) && Objects.nonNull(dto.getInfoProcedureId())) {
                Long procedureId = dto.getInfoProcedureId();
                //  当前审核节点名
                auditClient.getAuditProcedureStateName(procedureId).ifPresent(vo::setInfoCurrentProcedure);
                //  设置当前审核人信息
                auditClient.getAuditUsers(procedureId).ifPresent(auditUsers -> {
                    List<CurrentAdminUserVo> adminUserVos = auditUsers.stream().map(e -> {
                        CurrentAdminUserVo adminUserVo = new CurrentAdminUserVo();
                        adminUserVo.setUsername(e.getUsername());
                        adminUserVo.setName(e.getName());

                        return adminUserVo;
                    }).collect(Collectors.toList());
                    //  当前可以处理人数
                    vo.setInfoCurrentAdmin(adminUserVos.size());
                    //  当前可以人信息
                    vo.setInfoCurrentAdminDetail(adminUserVos);
                });
            } else {
                vo.setInfoCurrentProcedure("待流转");
            }
        }

        return vo;
    }


    public void completeConsumerAuditVo(List<ConsumerAuditVO> consumerAuditVOList) {
        if (CollectionUtils.isEmpty(consumerAuditVOList)) {
            return;
        }
        List<String> ossIdList = new ArrayList<>(consumerAuditVOList.size() * 2);
        List<String> avatarList = consumerAuditVOList.stream().map(ConsumerAuditVO::getAvatar).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> oldAvatarList = consumerAuditVOList.stream().map(ConsumerAuditVO::getAvatarOld).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        ossIdList.addAll(avatarList);
        ossIdList.addAll(oldAvatarList);
        List<OssDTO> ossDTOList = CollectionUtils.isEmpty(ossIdList) ? Collections.emptyList() : ossClient.listOriFileByIds(ossIdList).orElse(Collections.emptyList());
        Map<String, OssDTO> ossDTOMap = ossDTOList.stream().collect(Collectors.toMap(OssDTO::getUuid, e -> e, (a, b) -> a));
        for (ConsumerAuditVO vo : consumerAuditVOList) {
            OssDTO ossDTO = ossDTOMap.get(vo.getAvatar());
            if (Objects.nonNull(ossDTO)) {
                vo.setAvatarUrl(ossDTO.getUrl());
            }
            OssDTO oldOssDTO = ossDTOMap.get(vo.getAvatarOld());
            if (Objects.nonNull(oldOssDTO)) {
                vo.setAvatarOldUrl(oldOssDTO.getUrl());
            }
        }
    }

    public static AuditingNameLogVo toVo(AuditlogNicknameDTO dto) {
        AuditingNameLogVo vo = new AuditingNameLogVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        vo.setOldNickname(dto.getNicknameOld());
        String memo = dto.getMemo();
        String newNickName = dto.getNickname();
        if (!StringUtils.isEmpty(memo)) {
            String[] memos = memo.split(";");
            String highlightPrefix = "<font color=\"red\">";
            String highlightSuffix = "</font>";
            for (String str : memos) {
                newNickName = newNickName.replace(str, highlightPrefix + str + highlightSuffix);
            }
            vo.setNewNickname(newNickName);
        } else {
            vo.setNewNickname(dto.getNickname());
        }
        vo.setMobile(dto.getMobile());
        vo.setCheckStatus(dto.getCheckstatus() > 0 ? "通过" : "驳回");
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAuditDate(DateUtils.format(dto.getAuditDate()));
        vo.setAiAuditState(dto.getAiAuditState());
        vo.setLinkId(dto.getLinkId());
        return vo;
    }

    public AuditingIdentityVo toVo(ConsumerIdentityDTO dto) {
        AuditingIdentityVo vo = new AuditingIdentityVo();
        vo.setId(dto.getId());
        vo.setIdentity(dto.getIdentity());
        vo.setFrontImage(ossClient.getOssFile(dto.getFront()).getResult().getUrl());
        vo.setBackImage(ossClient.getOssFile(dto.getBack()).getResult().getUrl());
        vo.setHandImage(ossClient.getOssFile(dto.getHand()).getResult().getUrl());
        vo.setNickname(dto.getNickname());
        vo.setStatus(dto.getStatus() > 0 ? "启用" : "禁用");
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        return vo;
    }

    public AuditingIdentityLogVo toVo(AuditlogIdentityDTO dto) {
        AuditingIdentityLogVo vo = new AuditingIdentityLogVo();
        vo.setId(dto.getId());
        vo.setIdentity(dto.getIdentity());
        vo.setFrontImage(ossClient.getOssFile(dto.getFront()).getResult().getUrl());
        vo.setBackImage(ossClient.getOssFile(dto.getBack()).getResult().getUrl());
        vo.setHandImage(ossClient.getOssFile(dto.getHand()).getResult().getUrl());
        vo.setNickname(dto.getNickname());
        vo.setCheckStatus(dto.getCheckstatus() > 0 ? "通过" : "驳回");
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAuditDate(DateUtils.format(dto.getAuditDate()));
        return vo;
    }

    public SimpleIdNameVo toFuzzyVo(ConsumerUserDTO dto) {
        SimpleIdNameVo vo = new SimpleIdNameVo();
        vo.setId(dto.getJid());
        vo.setName(dto.getMobile());
        return vo;
    }

    public AuditingInfoVo toVo(ConsumerInfoDTO dto) {
        AuditingInfoVo vo = new AuditingInfoVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        Result<ConsumerInfoDTO> info = userClient.getInfoByJid(dto.getJid());
        if (info.callSuccess()) {
            vo.setNickname(info.getResult().getNickname());
            vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
            vo.setNewInfo(dto.getInfo());
            vo.setOldInfo(dto.getInfoOld());
        }
        vo.setAiAuditState(dto.getAiAuditState());
        return vo;
    }

    public AuditingInfoLogVo toVo(AuditlogInfoDTO dto) {
        AuditingInfoLogVo vo = new AuditingInfoLogVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        vo.setNewInfo(dto.getInfo());
        vo.setOldInfo(dto.getInfoOld());
        String nickname = userClient.getInfoByJid(dto.getJid()).map(ConsumerInfoDTO::getNickname).orElse("");
        vo.setNickname(nickname);
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAuditDate(DateUtils.format(dto.getAuditDate()));
        vo.setAuditUsername(dto.getAuditUserId());
        vo.setCheckStatus(dto.getCheckstatus() > 0 ? "审核通过" : "审核驳回");
        vo.setAiAuditState(dto.getAiAuditState());
        vo.setLinkId(dto.getLinkId());
        return vo;
    }

    public AuditingAvatarVo toVo(ConsumerAvatarDTO dto) {
        AuditingAvatarVo vo = new AuditingAvatarVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        Result<ConsumerUserDTO> result = userClient.getUserByJid(dto.getJid());
        if (result.callSuccess()) {
            vo.setNickname(result.getResult().getNickname());
        }
        if (StringUtils.isNotEmpty(dto.getAvatar())) {
            ossClient.getOssFile(dto.getAvatar()).ifPresent(ossDTO -> vo.setNewAvatar(ossDTO.getUrl()));
        }
        if (StringUtils.isNotEmpty(dto.getAvatarOld())) {
            ossClient.getOssFile(dto.getAvatarOld()).ifPresent(ossDTO -> vo.setOldAvatar(ossDTO.getUrl()));
        }
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAiAuditState(dto.getAiAuditState());
        return vo;
    }

    public AuditingAvatarLogVo toVo(AuditlogAvatarDTO dto) {
        AuditingAvatarLogVo vo = new AuditingAvatarLogVo();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        if (StringUtils.isNotEmpty(dto.getAvatar())) {
            ossClient.getOssFile(dto.getAvatar()).ifPresent(oss -> vo.setNewAvatar(oss.getUrl()));
        }
        if (StringUtils.isNotEmpty(dto.getAvatarOld())) {
            ossClient.getOssFile(dto.getAvatarOld()).ifPresent(oss -> vo.setOldAvatar(oss.getUrl()));
        }
        Result<ConsumerUserDTO> result = userClient.getUserByJid(dto.getJid());
        if (result.isPresent()) {
            vo.setNickname(result.getResult().getNickname());
        } else {
            vo.setNickname(String.valueOf(dto.getJid()));
        }
        vo.setCreateDate(DateUtils.format(dto.getCreateDate()));
        vo.setAuditDate(DateUtils.format(dto.getAuditDate()));
        vo.setAuditUsername(dto.getAuditUserId());
        vo.setCheckStatus(dto.getCheckstatus() > 0 ? "审核通过" : "审核驳回");
        vo.setAiAuditState(dto.getAiAuditState());
        vo.setLinkId(dto.getLinkId());
        return vo;
    }

    public AuthenticationUserVo toVo(ConsumerAuthenticationDTO dto) {
        AuthenticationUserVo vo = new AuthenticationUserVo();
        vo.setId(dto.getId());
        vo.setCreateDate(dto.getCreateDate());
        vo.setId(dto.getId());
        vo.setIntroduction(dto.getIntroduction());
        vo.setJid(dto.getJid());
        vo.setMobile(dto.getMobile());
        vo.setNickname(dto.getNickname());
        return vo;
    }

    public ConsumerUserInfoVo toVO(ConsumerUserDTO dto) {
        ConsumerUserInfoVo consumerUserInfoVo = new ConsumerUserInfoVo();
        consumerUserInfoVo.setId(dto.getId());
        consumerUserInfoVo.setJid(dto.getJid());
        consumerUserInfoVo.setStatus(dto.getStatus());
        consumerUserInfoVo.setMobile(dto.getMobile());
        consumerUserInfoVo.setNickname(dto.getNickname());
        consumerUserInfoVo.setInfo(dto.getInfo());
        consumerUserInfoVo.setAvatar(dto.getAvatar());
        if (StringUtils.isNotBlank(dto.getAvatar())) {
            ossClient.getOssFile(dto.getAvatar()).ifPresent(ossDTO -> consumerUserInfoVo.setAvatarUrl(ossDTO.getUrl()));
        }
        consumerUserInfoVo.setGender(dto.getGender());
        consumerUserInfoVo.setCreateDate(dto.getCreateDate());
        return consumerUserInfoVo;
    }

    public ConsumerUserInfoVo toVO1(ConsumerUserDTO dto) {
        ConsumerUserInfoVo consumerUserInfoVo = new ConsumerUserInfoVo();
        consumerUserInfoVo.setId(dto.getId());
        consumerUserInfoVo.setJid(dto.getJid());
        consumerUserInfoVo.setStatus(dto.getStatus());
        consumerUserInfoVo.setMobile(dto.getMobile());
        consumerUserInfoVo.setNickname(dto.getNickname());
        consumerUserInfoVo.setInfo(dto.getInfo());
        consumerUserInfoVo.setAvatar(dto.getAvatar());
        consumerUserInfoVo.setGender(dto.getGender());
        consumerUserInfoVo.setCreateDate(dto.getCreateDate());
        return consumerUserInfoVo;
    }
}
