package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.media.client.dto.AdvertSearchMarketingDTO;
import com.jxntv.gvideo.media.client.dto.BannerDTO;
import com.jxntv.gvideo.media.client.enums.AdvertSearchMarketingStatus;
import com.jxntv.gvideo.web.controller.be.model.vo.AdvertSearchMarketingVO;
import com.jxntv.gvideo.web.controller.be.utils.ImageUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/3/6 15:30
 */
@Component
public class AdvertSearchMarketingConvert {

    @Resource
    private OssClient ossClient;
    @Resource
    private BannerConverter bannerConverter;


    public AdvertSearchMarketingVO toVO(AdvertSearchMarketingDTO dto) {
        AdvertSearchMarketingVO advertSearchMarketingVO = new AdvertSearchMarketingVO();
        advertSearchMarketingVO.setId(dto.getId());
        advertSearchMarketingVO.setName(dto.getName());
        advertSearchMarketingVO.setStartDate(dto.getStartDate());
        advertSearchMarketingVO.setEndDate(dto.getEndDate());
        advertSearchMarketingVO.setStatus(dto.getStatus());
        AdvertSearchMarketingStatus status = AdvertSearchMarketingStatus.parse(dto.getStatus());
        if (Objects.nonNull(status)) {
            advertSearchMarketingVO.setStatusStr(status.getDesc());
        }
        advertSearchMarketingVO.setCreateUserId(dto.getCreateUserId());
        advertSearchMarketingVO.setCreateDate(dto.getCreateDate());
        advertSearchMarketingVO.setUpdateUserId(dto.getUpdateUserId());
        advertSearchMarketingVO.setUpdateDate(dto.getUpdateDate());
        advertSearchMarketingVO.setTriggerKeyword(dto.getTriggerKeyword());
        advertSearchMarketingVO.setJumpType(dto.getJumpType());
        advertSearchMarketingVO.setJumpUrl(dto.getJumpUrl());
        advertSearchMarketingVO.setExtraUrl(dto.getExtraUrl());
        advertSearchMarketingVO.setInnerJumpType(dto.getInnerJumpType());
        advertSearchMarketingVO.setJumpMenu(dto.getJumpMenu());
        advertSearchMarketingVO.setJumpMediaId(dto.getJumpMediaId());
        advertSearchMarketingVO.setJumpMediaType(dto.getJumpMediaType());
        advertSearchMarketingVO.setBanner(ImageUtils.buildImageVO(dto.getBannerOssId()));
        advertSearchMarketingVO.setBannerOssId(dto.getBannerOssId());
        BannerDTO bannerDTO = new BannerDTO();
        bannerDTO.setJumpType(dto.getJumpType());
        bannerDTO.setJumpUrl(dto.getJumpUrl());
        bannerDTO.setInnerJumpType(dto.getInnerJumpType());
        bannerDTO.setJumpMenu(dto.getJumpMenu());
        bannerDTO.setJumpMediaId(dto.getJumpMediaId());
        bannerDTO.setJumpMediaType(dto.getJumpMediaType());
        advertSearchMarketingVO.setJump(bannerConverter.getJumpTitle(bannerDTO));

        return advertSearchMarketingVO;
    }


    public AdvertSearchMarketingDTO toDTO(AdvertSearchMarketingVO vo) {
        AdvertSearchMarketingDTO advertSearchMarketingDTO = new AdvertSearchMarketingDTO();
        advertSearchMarketingDTO.setId(vo.getId());
        advertSearchMarketingDTO.setName(vo.getName());
        advertSearchMarketingDTO.setStartDate(vo.getStartDate());
        advertSearchMarketingDTO.setEndDate(vo.getEndDate());
        advertSearchMarketingDTO.setStatus(vo.getStatus());
        advertSearchMarketingDTO.setCreateUserId(vo.getCreateUserId());
        advertSearchMarketingDTO.setCreateDate(vo.getCreateDate());
        advertSearchMarketingDTO.setUpdateUserId(vo.getUpdateUserId());
        advertSearchMarketingDTO.setUpdateDate(vo.getUpdateDate());
        advertSearchMarketingDTO.setTriggerKeyword(vo.getTriggerKeyword());
        advertSearchMarketingDTO.setBannerOssId(vo.getBannerOssId());
        advertSearchMarketingDTO.setJumpType(vo.getJumpType());
        advertSearchMarketingDTO.setJumpUrl(vo.getJumpUrl());
        advertSearchMarketingDTO.setExtraUrl(vo.getExtraUrl());
        advertSearchMarketingDTO.setInnerJumpType(vo.getInnerJumpType());
        advertSearchMarketingDTO.setJumpMenu(vo.getJumpMenu());
        advertSearchMarketingDTO.setJumpMediaId(vo.getJumpMediaId());
        advertSearchMarketingDTO.setJumpMediaType(vo.getJumpMediaType());
        return advertSearchMarketingDTO;
    }
}
