package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.ChannelTabClient;
import com.jxntv.gvideo.media.client.dto.ChannelTabDTO;
import com.jxntv.gvideo.om.client.KingKongPositionChannelStrategyClient;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategyDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategySearchDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionSearchDTO;
import com.jxntv.gvideo.track.sdk.log.SysLogGroup;
import com.jxntv.gvideo.track.sdk.log.Syslog;
import com.jxntv.gvideo.user.client.ChannelClient;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelTabDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelTabSearchDTO;
import com.jxntv.gvideo.web.controller.be.client.KingKongPositionChannelStrategyService;
import com.jxntv.gvideo.web.controller.be.client.KingKongPositionService;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.KingKongPositionConverter;
import com.jxntv.gvideo.web.controller.be.converter.LinkUrlConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.dto.LinkDto;
import com.jxntv.gvideo.web.controller.be.model.vo.kingkong.*;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/25
 * Email: <EMAIL>
 */

@Slf4j
@RestController
@RequestMapping("/api/king-kong/position")
@Api(value = "金刚位管理", tags = {"KingKongPositionController"})
public class KingKongPositionController {

    @Resource
    private KingKongPositionService kingKongPositionService;
    @Resource
    private KingKongPositionChannelStrategyService kingKongPositionChannelStrategyService;
    @Resource
    private UserConsumer userConsumer;
    @Resource
    private KingKongPositionConverter kingKongPositionConverter;
    @Resource
    private ChannelClient channelClient;
    @Resource
    private ChannelTabClient channelTabClient;
    @Resource
    private LinkUrlConverter linkUrlConverter;


    @PostMapping
    @ApiOperation(value = "添加金刚位", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "添加金刚位")
    public Result<Long> addPosition(@RequestBody KingKongPositionEditVO editVO) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);
        KingKongPositionDTO dto = new KingKongPositionDTO();
        dto.setName(editVO.getName());
        dto.setIconId(editVO.getIconId());
        dto.setType(editVO.getType());
        dto.setContentId(editVO.getContentId());
        dto.setContentType(editVO.getContentType());
        dto.setContentName(editVO.getContentName());
        dto.setLinkUrl(convertLinkUrl(editVO));
        dto.setCreateDate(LocalDateTime.now());
        dto.setCreateUserId(userId);

        return kingKongPositionService.add(dto);
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "修改金刚位", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "修改金刚位")
    public Result<Boolean> updatePositionById(@PathVariable("id") Long id, @RequestBody KingKongPositionEditVO editVO) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);
        KingKongPositionDTO dto = new KingKongPositionDTO();
        dto.setIconId(editVO.getIconId());
        dto.setName(editVO.getName());
        dto.setType(editVO.getType());
        dto.setContentId(editVO.getContentId());
        dto.setContentType(editVO.getContentType());
        dto.setContentName(editVO.getContentName());
        dto.setLinkUrl(convertLinkUrl(editVO));
        dto.setExtraUrl(editVO.getExtraUrl());
        dto.setUpdateDate(LocalDateTime.now());
        dto.setUpdateUserId(userId);

        return kingKongPositionService.updateById(id, dto);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除金刚位", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "删除金刚位")
    public Result<Boolean> deletePositionById(@PathVariable("id") Long id) {
        return kingKongPositionService.deleteById(id);
    }

    @GetMapping("/page")
    @ApiOperation(value = "金刚位分页列表", notes = "")
    public Result<Page<KingKongPositionVO>> pageOfPosition(@RequestParam(defaultValue = "0") Integer pageNum,
                                                           @RequestParam(defaultValue = "10") Integer pageSize,
                                                           @RequestParam(required = false) String nameLike,
                                                           @RequestParam(required = false) Long id) {
        KingKongPositionSearchDTO searchDTO = new KingKongPositionSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setNameLike(nameLike);
        searchDTO.setId(id);
        PageDTO<KingKongPositionDTO> pageDTO = kingKongPositionService.page(searchDTO).orElseThrow();
        return Result.ok(Page.pageOfTitle(pageDTO, kingKongPositionConverter::convert, KingKongPositionVO.class));
    }

    @PostMapping("/channel/strategy")
    @ApiOperation(value = "添加金刚位渠道策略", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "添加金刚位渠道策略")
    public Result<Long> addStrategy(@RequestBody KingKongPositionChannelStrategyEditVO editVO) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);
        KingKongPositionChannelStrategyDTO dto = new KingKongPositionChannelStrategyDTO();
        dto.setCreateDate(LocalDateTime.now());
        dto.setCreateUserId(userId);
        dto.setChannels(editVO.getChannels());
        dto.setIsJuniorMode(editVO.getIsJuniorMode());
        if (!CollectionUtils.isEmpty(editVO.getPositions())) {
            List<KingKongPositionChannelStrategyEditPositionVO> positions = editVO.getPositions();
            List<KingKongPositionDTO> positionDTOList = new ArrayList<>();
            for (KingKongPositionChannelStrategyEditPositionVO vo : positions) {
                KingKongPositionDTO positionDTO = new KingKongPositionDTO();
                positionDTO.setId(vo.getId());
                positionDTO.setBubbleWords(vo.getBubbleWords());

                positionDTOList.add(positionDTO);
            }
            dto.setPositions(positionDTOList);
        }

        return kingKongPositionChannelStrategyService.add(dto);
    }

    @PutMapping("/channel/strategy/{id}")
    @ApiOperation(value = "修改金刚位渠道策略", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "修改金刚位渠道策略")
    public Result<Boolean> updateStrategy(@PathVariable("id") Long id, @RequestBody KingKongPositionChannelStrategyEditVO editVO) {
        Long userId = ThreadLocalCache.getUserId(userConsumer);
        KingKongPositionChannelStrategyDTO dto = new KingKongPositionChannelStrategyDTO();
        dto.setChannelId(editVO.getChannelId());
        dto.setChannelName(editVO.getChannelName());
        dto.setChannels(editVO.getChannels());
        dto.setUpdateDate(LocalDateTime.now());
        dto.setUpdateUserId(userId);
        if (!CollectionUtils.isEmpty(editVO.getPositions())) {
            List<KingKongPositionChannelStrategyEditPositionVO> positions = editVO.getPositions();
            List<KingKongPositionDTO> positionDTOList = new ArrayList<>();
            for (KingKongPositionChannelStrategyEditPositionVO vo : positions) {
                KingKongPositionDTO positionDTO = new KingKongPositionDTO();
                positionDTO.setId(vo.getId());
                positionDTO.setBubbleWords(vo.getBubbleWords());

                positionDTOList.add(positionDTO);
            }
            dto.setPositions(positionDTOList);
        }

        return kingKongPositionChannelStrategyService.updateById(id, dto);
    }

    @DeleteMapping("/channel/strategy/{id}")
    @ApiOperation(value = "删除金刚位渠道策略", notes = "")
    @Syslog(type = SysLogGroup.KING_KONG_POSITION, subject = "删除金刚位渠道策略")
    public Result<Boolean> deleteStrategy(@PathVariable("id") Long id) {
        return kingKongPositionChannelStrategyService.deleteById(id);
    }

    @GetMapping("/channel/strategy/page")
    @ApiOperation(value = "金刚位渠道策略分页列表", notes = "")
    public Result<Page<KingKongPositionChannelStrategyVO>> pageOfStrategy(@RequestParam(defaultValue = "0") Integer pageNum,
                                                                          @RequestParam(defaultValue = "10") Integer pageSize,
                                                                          @RequestParam(required = false) String nameLike,
                                                                          @RequestParam(required = false) Long id,
                                                                          @RequestParam(required = false, defaultValue = "0") Integer isJuniorMode) {
        KingKongPositionChannelStrategySearchDTO searchDTO = new KingKongPositionChannelStrategySearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setNameLike(nameLike);
        searchDTO.setId(id);
        searchDTO.setIsJuniorMode(isJuniorMode);
        PageDTO<KingKongPositionChannelStrategyDTO> pageDTO = kingKongPositionChannelStrategyService.page(searchDTO).orElseThrow();
        return Result.ok(Page.pageOfTitle(pageDTO, kingKongPositionConverter::convert, KingKongPositionChannelStrategyVO.class));
    }


    @GetMapping("/channel/strategy/menus")
    @ApiOperation(value = "金刚位原生菜单列表", notes = "下拉选项")
    public Result<List<KingKongPositionMenuVO>> getMenus() {

        List<ChannelTabDTO> dtoList = channelTabClient.listAll().orElse(Collections.emptyList());
        //  去重
        Map<Long, ChannelTabDTO> map = dtoList.stream().collect(Collectors.toMap(ChannelTabDTO::getChannelId, e -> e, (e1, e2) -> e2));
        dtoList = new ArrayList<>(map.values());
        //  按照id排序
        dtoList = dtoList.stream().sorted(Comparator.comparing(ChannelTabDTO::getId)).collect(Collectors.toList());
        //  目前支持home和live类型
        dtoList = dtoList.stream().filter(e -> "home".equals(e.getType()) || "live".equals(e.getType())).collect(Collectors.toList());

        List<KingKongPositionMenuVO> collect = dtoList.stream().map(e -> new KingKongPositionMenuVO(e.getId(), e.getName(), e.getType())).collect(Collectors.toList());

        return Result.ok(collect);
    }

    @GetMapping("/channel/strategy/channels")
    @ApiOperation(value = "金刚位渠道分页列表", notes = "下拉选项")
    public Result<Page<Map<String, Object>>> pageOfChannels(@RequestParam(defaultValue = "0") Integer pageNum,
                                                            @RequestParam(defaultValue = "1000") Integer pageSize,
                                                            @RequestParam(required = false) String nameLike) {

        ConsumerChannelTabSearchDTO searchDTO = new ConsumerChannelTabSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setNameLike(nameLike);
        PageDTO<ConsumerChannelTabDTO> pageDTO = channelClient.getConsumerChannelTabPage(searchDTO).orElseThrow();

        Page<Map<String, Object>> page = Page.pageWithoutTitle(pageDTO, e -> {
            Map<String, Object> map = new HashMap<>();
            map.put("id", e.getOpenInstallId());
            map.put("name", e.getName());

            return map;
        });
        //  固定添加一个默认渠道
        Map<String, Object> defaultChannel = new HashMap<>();
        defaultChannel.put("id", KingKongPositionChannelStrategyClient.DEFAULT_CHANNEL_ID);
        defaultChannel.put("name", "默认渠道");

        //  固定添加一个青少年默认渠道
        Map<String, Object> defaultJuniorChannel = new HashMap<>();
        defaultJuniorChannel.put("id", KingKongPositionChannelStrategyClient.DEFAULT_JUNIOR_CHANNEL_ID);
        defaultJuniorChannel.put("name", "青少年默认渠道");

        List<Map<String, Object>> list = page.getList();
        list.add(0, defaultChannel);
        list.add(1, defaultJuniorChannel);

        return Result.ok(page);
    }

    private String convertLinkUrl(KingKongPositionEditVO editVO) {
        return linkUrlConverter.convert(LinkDto.builder()
                .type(editVO.getType())
                .contentType(editVO.getContentType())
                .channelTabId(editVO.getContentId())
                .linkUrl(editVO.getLinkUrl())
                .contentId(editVO.getContentId()).build());

    }

}
