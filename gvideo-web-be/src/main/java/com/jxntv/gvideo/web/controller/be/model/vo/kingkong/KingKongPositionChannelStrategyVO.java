package com.jxntv.gvideo.web.controller.be.model.vo.kingkong;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/25
 * Email: <EMAIL>
 */
@Data
@ApiModel("金刚位渠道策略对象")
public class KingKongPositionChannelStrategyVO {

    @ApiModelProperty(value = "渠道策略ID")
    private Long id;

    @ApiModelProperty(value = "渠道ID")
    private String channelId;

    @ApiModelProperty(value = "渠道ID")
    private String channelName;

    @ApiModelProperty(value = "金刚位列表")
    private List<KingKongPositionVO> positions;


}
