package com.jxntv.gvideo.web.controller.be.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxntv.gvideo.web.controller.be.model.vo.audit.CurrentAdminUserVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/04 14:41
 */
@Data
public class ConsumerAuditVO implements Serializable {
    private Long id;
    private Long jid;
    private String mobile;
    private String curNickname;
    private Long nicknameId;
    private String nickname;
    private String nicknameOld;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date nicknameCreateDate;
    private Integer nicknameCheckStatus;
    @ApiModelProperty(value = "当前审核流程")
    private String nicknameCurrentProcedure;
    @ApiModelProperty(value = "当前可处理人")
    private Integer nicknameCurrentAdmin;
    @ApiModelProperty(value = "当前可处理人详情")
    private List<CurrentAdminUserVo> nicknameCurrentAdminDetail;


    private Long avatarId;
    private String avatar;
    private String avatarUrl;
    private String avatarOld;
    private String avatarOldUrl;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date avatarCreateDate;
    private Integer avatarCheckStatus;
    @ApiModelProperty(value = "当前审核流程")
    private String avatarCurrentProcedure;
    @ApiModelProperty(value = "当前可处理人")
    private Integer avatarCurrentAdmin;
    @ApiModelProperty(value = "当前可处理人详情")
    private List<CurrentAdminUserVo> avatarCurrentAdminDetail;


    private Long infoId;
    private String info;
    private String infoOld;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date infoCreateDate;
    private Integer infoCheckStatus;
    @ApiModelProperty(value = "当前审核流程")
    private String infoCurrentProcedure;
    @ApiModelProperty(value = "当前可处理人")
    private Integer infoCurrentAdmin;
    @ApiModelProperty(value = "当前可处理人详情")
    private List<CurrentAdminUserVo> infoCurrentAdminDetail;


    private Integer nicknameAiAuditState;
    private Integer avatarAiAuditState;
    private Integer introAiAuditState;


}
