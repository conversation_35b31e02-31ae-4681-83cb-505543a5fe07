package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.user.client.dto.CertificationColumnDTO;
import com.jxntv.gvideo.user.client.dto.CertificationDTO;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "AutoCompleteVo", description = "下拉提示")
public class AutoCompleteVo {

    @ApiModelProperty(value = "ID", required = true)
    private Long id;
    @ApiModelProperty(value = "value", required = true)
    private String value;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static AutoCompleteVo convert(MediaFileDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getFileName());
        return vo;
    }

    public static AutoCompleteVo convert(MediaResourceDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getInternalName());
        return vo;
    }

    public static AutoCompleteVo convertShowName(MediaResourceDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getShowName());
        return vo;
    }

    public static AutoCompleteVo convert(MaterialDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getName());
        return vo;
    }

    public static AutoCompleteVo convert(CertificationDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getName());
        return vo;
    }

    public static AutoCompleteVo convert(CertificationColumnDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getName());
        return vo;
    }

    public static AutoCompleteVo convert(LabelDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getName());
        return vo;
    }

    public static AutoCompleteVo convert(SysUserDTO dto) {
        AutoCompleteVo vo = new AutoCompleteVo();
        vo.setId(dto.getId());
        vo.setValue(dto.getName());
        return vo;
    }
}
