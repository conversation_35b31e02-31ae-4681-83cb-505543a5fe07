package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.media.client.DramaProgramClient;
import com.jxntv.gvideo.media.client.dto.DramaColumnDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.DramaColumnVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Author: niedamin
 * @Date: 2023/06/30 11:15
 */
@Component
public class DramaColumnConverter {

    @Resource
    private OssClient ossClient;

    @Resource
    private DramaProgramClient dramaProgramClient;

    public DramaColumnVO convert(DramaColumnDTO dto) {
        DramaColumnVO vo = new DramaColumnVO();
        vo.setColumnName(dto.getColumnName());
        vo.setId(dto.getId());
        vo.setCategory(dto.getCategory());
        vo.setDramaTotalNum(dto.getDramaTotalNum());
        vo.setCoverUrl(ossClient.getOssFile(dto.getCoverId()).map(OssDTO::getUrl).orElse(""));
        vo.setCurrentTotalNum(dramaProgramClient.getProgramNumByColumnId(dto.getId()).getResult());
        vo.setStatus(dto.getStatus());
        vo.setStyle(dto.getStyle());
        vo.setCharge(dramaProgramClient.columnCharge(dto.getId()).getResult());
        vo.setPoint(dto.getPoint());
        vo.setCoverId(dto.getCoverId());
        vo.setJspBean(dto.getJspBean());
        vo.setBeian(dto.getBeian());
        vo.setWeight(dto.getWeight());
        return vo;
    }
}
