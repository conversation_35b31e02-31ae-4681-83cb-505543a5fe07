package com.jxntv.gvideo.web.controller.be.model.vo.pendant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PendantUpdateVO", description = "挂件-修改")
public class PendantUpdateVO {

    @ApiModelProperty(value = "挂件ID")
    @NotNull(message = "挂件ID不能为空")
    private Long id;

    @ApiModelProperty(value = "挂件名称")
    @Length(max = 10, message = "挂件名称不能超过10字")
    private String title;

/*    @ApiModelProperty(value = "挂件类型 1-头像 2-皮肤")
    @Min(value = 1, message = "挂件类型在1-2之间")
    @Max(value = 2, message = "挂件类型在1-2之间")
    private Integer type;*/

    @ApiModelProperty(value = "挂件图片id")
    private String ossId;

    @ApiModelProperty(value = "描述信息")
    @Length(max = 20, message = "描述信息不能超过20字")
    private String description;

    @ApiModelProperty(value = "挂件状态 1-上架 2-下架")
    @Min(value = 1, message = "挂件状态在1-2之间")
    @Max(value = 2, message = "挂件状态在1-2之间")
    private Integer status;
}
