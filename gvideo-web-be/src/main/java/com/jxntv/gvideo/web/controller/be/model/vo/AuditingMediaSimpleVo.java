package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.web.controller.be.model.vo.audit.CurrentAdminUserVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 *
 * Created on 2020-04-07
 */
@Data
@ApiModel(value="AuditingMediaSimpleVo", description="待审核简单对象")
public class AuditingMediaSimpleVo {

    @ApiModelProperty(value="待审核记录ID")
    private Long id;
    @ApiModelProperty(value = "内容类型")
    private String contentType;
    @ApiModelProperty(value = "数据类型")
    private String dataTypeStr;
    @ApiModelProperty(value = "资源类型")
    private Integer mediaType;
    @ApiModelProperty(value = "资源标题")
    private String showName;
    @ApiModelProperty(value = "认证号")
    private String certification;
    @ApiModelProperty(value = "当前审核流程")
    private String currentProcedure;
    @ApiModelProperty(value = "当前可处理人")
    private Integer currentAdmin;
    @ApiModelProperty(value = "当前可处理人详情")
    private List<CurrentAdminUserVo> currentAdminDetail;
    @ApiModelProperty(value = "提交审核时间")
    private String createDate;

    /**
     * 1:机审中，2：审核正常，3：审核风险
     */
    @ApiModelProperty(value = "AI审核状态")
    private Integer aiAuditState;

    /**
     * 机审结果
     */
    @ApiModelProperty(value = "AI审核结果")
    private String aiAuditResult;

    @ApiModelProperty(value = "数据类型")
    private Integer dataType;
}
