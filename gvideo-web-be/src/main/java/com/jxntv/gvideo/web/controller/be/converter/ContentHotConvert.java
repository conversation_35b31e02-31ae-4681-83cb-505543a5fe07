package com.jxntv.gvideo.web.controller.be.converter;


import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerInfoDTO;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.SoundDTO;
import com.jxntv.gvideo.om.dto.AuditContentHotDTO;
import com.jxntv.gvideo.om.dto.AuditContentHotRuleDTO;
import com.jxntv.gvideo.om.dto.ContentHotQueryDTO;
import com.jxntv.gvideo.om.enums.ContentTypeEnum;
import com.jxntv.gvideo.om.enums.HotStatusEnum;
import com.jxntv.gvideo.om.enums.HotTypeEnum;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 内容热度转换
 * @date 2021/07/14 15:51
 */
@Component
public class ContentHotConvert {
    @Autowired
    private SysUserClient sysUserClient;
    @Autowired
    private ConsumerUserClient consumerUserClient;
    @Autowired
    private MediaResourceClient mediaResourceClient;
    @Resource
    private OssClient ossClient;

    public AuditContentHotRuleDTO toDTO(AddContentHotRuleVO vo) {
        AuditContentHotRuleDTO dto = new AuditContentHotRuleDTO();
        BeanCopier.create(AddContentHotRuleVO.class, AuditContentHotRuleDTO.class, false)
                .copy(vo, dto, null);
        return dto;
    }

    public ContentHotQueryDTO toDTO(ContentHotQueryVO vo) {
        ContentHotQueryDTO dto = new ContentHotQueryDTO();
        BeanCopier.create(ContentHotQueryVO.class, ContentHotQueryDTO.class, false)
                .copy(vo, dto, null);
        return dto;
    }

    public AuditContentHotDTO toDTO(ContentHotStatusVO vo) {
        AuditContentHotDTO dto = new AuditContentHotDTO();
        BeanCopier.create(ContentHotStatusVO.class, AuditContentHotDTO.class, false)
                .copy(vo, dto, null);
        return dto;
    }

    public AuditContentHotVO toVO(AuditContentHotDTO dto) {
        AuditContentHotVO vo = new AuditContentHotVO();
        BeanCopier.create(AuditContentHotDTO.class, AuditContentHotVO.class, false)
                .copy(dto, vo, null);
        vo.setTypeStr(HotTypeEnum.getNameByCode(dto.getType()).getName());
        vo.setContentTypeStr(ContentTypeEnum.getNameByCode(dto.getContentType()).getName());
        if (ContentTypeEnum.getNameByCode(dto.getContentType()).equals(ContentTypeEnum.RESOURCE)) {
            vo.setMediaTypeStr(ContentType.toStr(dto.getMediaType()));
        } else {
            // 1-纯图片、2-纯语音、3-纯文字、4-文字+语音、5-文字+图片
            if (dto.getMediaType().equals(1)) {
                vo.setMediaTypeStr("纯图片");
            } else if (dto.getMediaType().equals(2)) {
                vo.setMediaTypeStr("纯语音");
            } else if (dto.getMediaType().equals(4)) {
                vo.setMediaTypeStr("文字+语音");
            } else if (dto.getMediaType().equals(5)) {
                vo.setMediaTypeStr("文字+图片");
            } else {
                vo.setMediaTypeStr("纯文字");
            }
            //语音
            if(StringUtils.isNotEmpty(dto.getSoundOssId())){
                SoundDTO soundDTO = ossClient.getContentBySoundId(dto.getSoundOssId()).orElse(null);
                if(Objects.nonNull(soundDTO)){
                    vo.setSoundUrl(soundDTO.getUrl());
                    vo.setSoundContent(soundDTO.getContent());
                }
            }

            //图片
            if(StringUtils.isNotEmpty(dto.getImage())){
                List<OssDTO> ossDTOList = ossClient.listByIds(Arrays.asList(dto.getImage().split(":"))).orElse(Collections.emptyList());
                vo.setImageList(ossDTOList.stream().map(ossDTO -> {
                    ImageVO imageVO = new ImageVO();
                    imageVO.setOssId(ossDTO.getUuid());
                    imageVO.setUrl(ossDTO.getUrl());
                    return imageVO;
                }).collect(Collectors.toList()));
            }
        }
        vo.setStatusStr(HotStatusEnum.getNameByCode(dto.getStatus()).getName());
        if (HotTypeEnum.HOT.equals(HotTypeEnum.getNameByCode(dto.getType()))) {
            SysUserDTO sysUserDTO = sysUserClient.getUserInfoById(dto.getCreateUserId()).orElse(null);
            if (Objects.nonNull(sysUserDTO)) {
                vo.setCreateUserName(sysUserDTO.getName());
            }
        } else {
            ConsumerInfoDTO consumerInfoDTO = consumerUserClient.getInfoByJid(dto.getCreateUserId()).orElse(null);
            if (Objects.nonNull(consumerInfoDTO)) {
                vo.setCreateUserName(consumerInfoDTO.getNickname());
            }
        }
        return vo;
    }
}
