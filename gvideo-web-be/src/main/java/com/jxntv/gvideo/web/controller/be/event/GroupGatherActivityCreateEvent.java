package com.jxntv.gvideo.web.controller.be.event;

import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityInsertDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 社区活动创建事件
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupGatherActivityCreateEvent implements Serializable {


    /**
     * 新建活动ID
     */
    private Long activityId;

    /**
     * 活动
     */
    private GroupGatherActivityInsertDTO info;




}
