package com.jxntv.gvideo.web.controller.be.model.vo.blind.date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupBlindDateUserImageVO", description = "相亲-个人照片")
@AllArgsConstructor
@NoArgsConstructor
public class GroupBlindDateUserImageVO {


    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "阿里云uuid")
    private String ossId;

    @ApiModelProperty(value = "图片地址")
    private String url;


}
