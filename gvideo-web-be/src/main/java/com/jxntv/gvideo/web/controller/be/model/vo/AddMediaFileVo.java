package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "AddMediaFileVo", description = "创建文件对象")
public class AddMediaFileVo {

    @ApiModelProperty(value = "文件名", required = true)
    private String name;

    @ApiModelProperty(value = "阿里云ID", required = true)
    private String ossId;

    @ApiModelProperty(value = "文件类型", required = true)
    private Integer type;

    @ApiModelProperty(value = "状态 1.待关联 2.已关联 3.已删除")
    private Integer status;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    @ApiModelProperty(value = "租户id")
    private Long tenantId;

}
