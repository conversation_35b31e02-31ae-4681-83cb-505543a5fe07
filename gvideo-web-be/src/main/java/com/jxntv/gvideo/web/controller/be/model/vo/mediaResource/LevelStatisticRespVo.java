package com.jxntv.gvideo.web.controller.be.model.vo.mediaResource;

import com.jxntv.gvideo.media.client.dto.MediaResourceStatisticRespDTO;
import com.jxntv.gvideo.media.client.dto.level.LevelRespDTO;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/30 16:55
 */
@Data
@ApiModel("等级统计响应")
public class LevelStatisticRespVo {

    @ApiModelProperty("S等级资源占比")
    private String proportionForS;
    @ApiModelProperty("S等级资源数量")
    private Long numOfLevelS;
    @ApiModelProperty("A等级资源占比")
    private String proportionForA;
    @ApiModelProperty("A等级资源数量")
    private Long numOfLevelA;
    @ApiModelProperty("B等级资源占比")
    private String proportionForB;
    @ApiModelProperty("B等级资源数量")
    private Long numOfLevelB;
    @ApiModelProperty("C等级资源占比")
    private String proportionForC;
    @ApiModelProperty("C等级资源数量")
    private Long numOfLevelC;

    public LevelStatisticRespVo(MediaResourceStatisticRespDTO dto) {
        this.proportionForS = dto.getProportionForS();
        this.numOfLevelS = dto.getNumOfLevelS();
        this.proportionForA = dto.getProportionForA();
        this.numOfLevelA = dto.getNumOfLevelA();
        this.proportionForB = dto.getProportionForB();
        this.numOfLevelB = dto.getNumOfLevelB();
        this.proportionForC = dto.getProportionForC();
        this.numOfLevelC = dto.getNumOfLevelC();
    }
}
