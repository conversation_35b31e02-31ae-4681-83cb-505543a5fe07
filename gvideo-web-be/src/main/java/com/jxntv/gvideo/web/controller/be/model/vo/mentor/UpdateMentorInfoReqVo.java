package com.jxntv.gvideo.web.controller.be.model.vo.mentor;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel(value = "UpdateMentorInfoReqVo", description = "导师成员信息修改")
public class UpdateMentorInfoReqVo {

    @NotNull(message = "权重不能为空")
    @Range(min = 1, max = 100, message = "1-100之间的自然数")
    @ApiModelProperty(value = "权重", required = true)
    private Integer weights;

    @NotNull(message = "被提问次数不能为空")
    @Range(min = 1, max = 100, message = "1-100之间的自然数")
    @ApiModelProperty(value = "被提问次数", required = true)
    private Integer effectiveTimes;

    @NotEmpty(message = "擅长领域不能为空")
    @ApiModelProperty(value = "擅长领域", required = true)
    private List<String> areasOfExpertise;
}
