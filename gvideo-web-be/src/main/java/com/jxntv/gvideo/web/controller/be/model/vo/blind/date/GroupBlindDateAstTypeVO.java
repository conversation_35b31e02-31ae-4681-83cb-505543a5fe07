package com.jxntv.gvideo.web.controller.be.model.vo.blind.date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="GroupBlindDateCompanyVerifyVO", description="都市放心爱-科协标识信息")
public class GroupBlindDateAstTypeVO {

    @ApiModelProperty(value="ID")
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value="科协标识  1-通过  0-重置为未认证")
    @NotNull(message = "科协标识不能为空")
    @Min(value=0,message = "科协标识在0-1之间")
    @Max(value=1,message = "科协标识在0-1之间")
    private Integer astType;

}
