package com.jxntv.gvideo.web.controller.be.event.listener;

import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AuditClient;
import com.jxntv.gvideo.om.dto.AuditBizDTO;
import com.jxntv.gvideo.om.dto.AuditConditionDTO;
import com.jxntv.gvideo.om.dto.AuditProcedureDTO;
import com.jxntv.gvideo.user.client.ConsumerAuditingClient;
import com.jxntv.gvideo.user.client.dto.ConsumerAvatarDTO;
import com.jxntv.gvideo.web.controller.be.event.ConsumerAvatarAuditEvent;
import com.jxntv.gvideo.web.controller.be.service.ImService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ConsumerAvatarAuditEventListener {

    @Resource
    private AuditClient auditClient;
    @Resource
    private ConsumerAuditingClient consumerAuditingClient;
    @Resource
    private ImService imService;

    /**
     * 默认流程节点id
     */
    private static final Long DEFAULT_PROCEDURE_ID = 0L;

    @Async
    @EventListener
    public void onEvent(ConsumerAvatarAuditEvent event) {
        ConsumerAvatarDTO consumerAvatar = event.getConsumerAvatar();
        //  未设置流程节点
        if (Objects.equals(consumerAvatar.getCheckstatus(), 0) && Objects.isNull(consumerAvatar.getProcedureId())) {
            handleProcedureNull(consumerAvatar);
        }
        //  审核拒绝
        if (Objects.equals(consumerAvatar.getCheckstatus(), -1)) {
            handleReject(consumerAvatar);
        }

        //  审核通过
        if (Objects.equals(consumerAvatar.getCheckstatus(), 1)) {
            handlePass(consumerAvatar);
        }

    }

    private void handleProcedureNull(ConsumerAvatarDTO consumerAvatar) {
        //  设置流程节点
        Long procedureId = getProcedureId(consumerAvatar);
        consumerAuditingClient.updateAuditConsumerAvatarProcedureId(consumerAvatar.getId(), procedureId);
    }

    private void handleReject(ConsumerAvatarDTO consumerAvatar) {
        consumerAuditingClient.effectConsumerAvatar(consumerAvatar.getId());
    }

    private void handlePass(ConsumerAvatarDTO consumerAvatar) {
        //  如果是终审流程节点，生效审核效果
        if (isFinalProcedure(consumerAvatar.getProcedureId())) {
            consumerAuditingClient.effectConsumerAvatar(consumerAvatar.getId()).ifPresent(e -> {
                //  更新IM头像
                imService.updateUserProfile(consumerAvatar.getJid());
            });

        } else {
            //  重置审核状态为0（未审核）并将procedureId设置为下一个审核节点
            Long nextProcedureId = getProcedureId(consumerAvatar);
            consumerAuditingClient.updateAuditConsumerAvatarProcedureId(consumerAvatar.getId(), nextProcedureId);
        }
    }

    /**
     * 获取下一个审核节点id
     * 1、没有审核节点匹配初始审核节点
     * 2、返回下一个审核节点
     *
     * @param consumerAvatarDTO 头像审核数据
     * @return 审核流程节点id
     */
    private Long getProcedureId(ConsumerAvatarDTO consumerAvatarDTO) {
        Long currentProcedureId = consumerAvatarDTO.getProcedureId();
        if (Objects.isNull(currentProcedureId)) {
            return getStartProcedureId(consumerAvatarDTO);
        } else {
            return getNextProcedureId(currentProcedureId);
        }
    }

    private Long getStartProcedureId(ConsumerAvatarDTO consumerAvatarDTO) {
        List<AuditBizDTO> auditBizList = auditClient.getAuditBizList(null, null).orElse(Collections.emptyList());
        for (AuditBizDTO auditBizDTO : auditBizList) {
            //  获取业务类型关联的筛选条件列表
            List<AuditConditionDTO> auditConditionList = auditClient.getAuditCondition(auditBizDTO.getId()).orElse(Collections.emptyList());
            for (AuditConditionDTO auditConditionDTO : auditConditionList) {
                //  目前是固定匹配关联流程configId=19的业务类型
                if (Objects.equals(auditConditionDTO.getConfigId(), 19L) && Objects.nonNull(auditBizDTO.getFlowId())) {
                    return auditClient.getAuditProcedureMin(auditBizDTO.getFlowId()).map(AuditProcedureDTO::getId).orElseThrow();
                }
            }

        }

        return DEFAULT_PROCEDURE_ID;
    }

    private Long getNextProcedureId(Long currentProcedureId) {
        return auditClient.getAuditProcedureNext(currentProcedureId).map(AuditProcedureDTO::getId).orElseThrow();
    }


    /**
     * 判断当前节点是否为终审节点
     * 1、当前没有分配节点id，返回false
     * 2、当前节点id为默认节点，返回true
     * 3、其他，判断是否拥有下一个节点
     *
     * @param currentProcedureId 当前审核节点
     * @return 是否为终审节点
     */
    private boolean isFinalProcedure(Long currentProcedureId) {
        if (Objects.isNull(currentProcedureId)) {
            return false;
        }

        if (Objects.equals(currentProcedureId, DEFAULT_PROCEDURE_ID)) {
            return true;
        }

        //  是否拥有下一个审核节点
        Result<AuditProcedureDTO> result = auditClient.getAuditProcedureNext(currentProcedureId);
        if (!result.callSuccess()) {
            throw new CodeMessageException(result.getCode(), result.getMessage());
        }

        return Objects.isNull(result.getResult());
    }

}
