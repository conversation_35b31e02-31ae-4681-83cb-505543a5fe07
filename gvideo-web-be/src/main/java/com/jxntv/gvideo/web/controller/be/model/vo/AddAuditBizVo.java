package com.jxntv.gvideo.web.controller.be.model.vo;

import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-02-03
 */
@Data
@ApiModel(value="AddAuditBizVo", description="审核业务对象")
public class AddAuditBizVo {

    @ApiModelProperty(value="主键ID")
    private Long id;
    @ApiModelProperty(value="业务名称", required = true)
    private String name;
    @ApiModelProperty(value="业务描述")
    private String note;
    @ApiModelProperty(value="步骤集合")
    private List<AuditConditionVo> list;

    @Data
    public static class AuditConditionVo{
        @ApiModelProperty(value="条件配置ID")
        private Long configId;
        @ApiModelProperty(value="条件名称")
        private String configName;
        @ApiModelProperty(value="条件逻辑符")
        private String configLogic;
        @ApiModelProperty(value="条件内容样式")
        private String contentStyle;
        @ApiModelProperty(value="条件内容字符串")
        private List<String> value;
        @ApiModelProperty(value="条件内容ID")
        private List<Long> content;
    }

}
