package com.jxntv.gvideo.web.controller.be.model.vo.home.card;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupHomeCardOperateVO", description = "首页卡片管理")
public class GroupHomeCardOperateVO {

    @ApiModelProperty(value = "关联圈子ID")
    @NotNull(message = "关联圈子ID不能为空")
    private Long groupId;

    @ApiModelProperty(value = "关联组件ID")
    @NotNull(message = "关联组件ID不能为空")
    private Long gatherId;

    @ApiModelProperty(value = "首页卡片ID")
    private Long id;

    @ApiModelProperty(value = "序号")
    @NotNull(message = "首页卡片序号不能为空")
    @Min(value=1,message = "首页卡片序号在1-2之间")
    @Max(value=2,message = "首页卡片序号在1-2之间")
    private Integer serialNo;

    @ApiModelProperty(value = "是否展示 0-不展示 1-展示")
    @NotNull(message = "是否展示不能为空")
    @Min(value=0,message = "是否展示在0-1之间")
    @Max(value=1,message = "是否展示在0-1之间")
    private Integer showFlag;

    @ApiModelProperty(value = "卡片标题")
    @Length(max = 6,message = "卡片标题不能超过6字")
    private String title;

    @ApiModelProperty(value = "卡片内容")
    @NotBlank(message = "卡片内容不能为空")
    @Length(max = 20,message = "卡片内容不能超过20字")
    private String content;

    @ApiModelProperty(value = "卡片ossId")
    @NotBlank(message = "卡片ossId不能为空")
    private String ossId;

    @ApiModelProperty(value = "跳转类型，0-应用内跳转，1-链接")
    private Integer jumpType;

    @ApiModelProperty(value = "外链接跳转URL")
    private String jumpUrl;

    @ApiModelProperty(value = "内链接跳转类型，0-跳菜单，1-跳内容")
    private Integer innerJumpType;

    @ApiModelProperty(value = "跳转菜单类型，0-新闻首页，1-推荐首页，2-我的圈子，3-发现圈子")
    private Integer jumpMenu;

    @ApiModelProperty(value = "跳转内容id")
    private Long jumpMediaId;

    @ApiModelProperty(value = "跳转内容类型 0-新闻，1-圈子，2-话题，3-动态，4-直播")
    private Integer jumpMediaType;

}
