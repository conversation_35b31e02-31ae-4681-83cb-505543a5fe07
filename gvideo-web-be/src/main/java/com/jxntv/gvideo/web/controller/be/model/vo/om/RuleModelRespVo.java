package com.jxntv.gvideo.web.controller.be.model.vo.om;

import com.jxntv.gvideo.om.dto.AddTypeEnum;
import com.jxntv.gvideo.om.dto.ModelTypeEnum;
import com.jxntv.gvideo.om.dto.RuleModelStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/20 9:29
 */
@Data
@ApiModel("规则模型请求结构体")
public class RuleModelRespVo implements Serializable {

    @ApiModelProperty("模型ID")
    private Long modelId;

    @ApiModelProperty("模型名称")
    private String modelName;

    @ApiModelProperty("添加类型")
    private AddTypeEnum addType;

    @ApiModelProperty("模型类型")
    private ModelTypeEnum modelType;

    @ApiModelProperty("内容数量")
    private Integer nums;

    @ApiModelProperty("创建人名称")
    private String createName;

    @ApiModelProperty("模型状态")
    private RuleModelStatus status;

    @ApiModelProperty("模型创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @ApiModelProperty("模型更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;
}
