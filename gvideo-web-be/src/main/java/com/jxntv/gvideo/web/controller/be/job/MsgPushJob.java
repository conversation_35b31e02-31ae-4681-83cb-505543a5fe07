package com.jxntv.gvideo.web.controller.be.job;

import com.jxntv.gvideo.common.utils.FileUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * <AUTHOR> Created on 2021/1/26.
 */

@Slf4j
@Component
public class MsgPushJob {

    private final String DIR_PATH = "/home/<USER>/file/push";

    /**
     * 定时删除推送产生文件
     */
    @XxlJob("deleteMsgPushFile")
    private void deleteMsgPushFile() {
        try {
            log.info("【消息推送】开始删除推送产生图片文件");
            FileUtil.deleteFile(new File(DIR_PATH));
        } catch (Exception e) {
            log.info("【消息推送】开始删除推送产生图片文件异常：{}", e.getLocalizedMessage());
        }
    }

}
