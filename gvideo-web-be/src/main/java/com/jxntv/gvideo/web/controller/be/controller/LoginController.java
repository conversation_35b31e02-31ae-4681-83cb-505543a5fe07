package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.SysMenuClient;
import com.jxntv.gvideo.web.controller.be.login.security.jwt.JwtUtils;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.login.thread.UserInfo;
import com.jxntv.gvideo.web.controller.be.model.vo.UserPassLoginVo;
import com.jxntv.gvideo.web.controller.be.model.vo.UserSmsLoginVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Created on 2020-01-11
 */
@RestController
@RequestMapping("/api/auth")
@Api(value = "登录接口", tags = {"LoginController"})
public class LoginController {

    @Autowired
    private SysMenuClient sysMenuClient;

    @PostMapping("/pass")
    @ApiOperation(value="密码登录接口", notes="手机号+密码登录接口")
    public Result passLogin(@RequestBody UserPassLoginVo userPassLoginVo){
        return Result.ok();
    }

    @PostMapping("/sms")
    @ApiOperation(value="短信验证码登录接口", notes="短信验证码登录接口")
    public Result smsLogin(@RequestBody UserSmsLoginVo userSmsLoginVo){
        return Result.ok();
    }

    @PostMapping("/switch")
    @ApiOperation(value="用户切换系统自动登录接口", notes="系统右上角有用户相关系统下拉框，用户可以选择系统自动切换")
    public Result<String> switchLogin(
            @ApiParam(name = "tenantId", value = "要进入的系统", required = true) @RequestParam("tenantId") Long tenantId){
        UserInfo userInfo = ThreadLocalCache.get();
        String loginUser = userInfo.getUsername();
        // 这里判断用户是否在目标租户中有授予权限，有权限才返回token，否则返回空
        Result<List<String>> permRes = sysMenuClient.getUserPermissions(tenantId, loginUser);
        if (permRes.getCode() != CodeMessage.OK.getCode()){
            return Result.fail("用户没有跳转系统权限，请联系系统管理员");
        }
        String username = loginUser + "|" + tenantId;
        List<SimpleGrantedAuthority> authorities = new ArrayList<>();
        User userDetail = new User(username, "", authorities);
        // 生成新租户token
        String newToken = JwtUtils.generateToken(userDetail);
        // Redis中清除旧token，目前暂不实现
        return Result.ok(newToken);
    }

    @PostMapping("/logout")
    @ApiOperation(value="用户注销登录接口", notes="用户注销登录接口")
    public Result logout(){
        // Redis中清除旧token，目前暂不实现
        return Result.ok();
    }

}
