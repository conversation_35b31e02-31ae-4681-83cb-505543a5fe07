package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "EditCertificationVo", description = "编辑认证号对象")
public class EditCertificationVo {

    @ApiModelProperty(value = "认证号名称")
    private String name;
    @ApiModelProperty(value = "认证号介绍")
    private String introduce;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "背景图")
    private String backgroundImage;
    @ApiModelProperty(value = "状态")
    private int status;
    @ApiModelProperty(value = "圈子列表", required = false)
    private List<Long> groupIds;
}
