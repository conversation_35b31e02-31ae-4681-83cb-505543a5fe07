package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AiLabelDTO;
import com.jxntv.gvideo.media.client.dto.AlgorithmLabelDTO;
import com.jxntv.gvideo.web.controller.be.client.AiLabelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2022/10/19 15:52
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/label")
@Api(value = "算法标签接口", tags = {"AiLabelController"})
public class AiLabelController {

    @Resource
    private AiLabelService aiLabelService;

    @GetMapping("all")
    @ApiOperation(value = "整体标签预览")
    public Result<List<AlgorithmLabelDTO>> preview() {
        return aiLabelService.preview();
    }

    @GetMapping("/{id}/children")
    @ApiOperation(value = "查询子标签")
    public Result<List<AiLabelDTO>> getChildren(@PathVariable("id") Long id) {
        return aiLabelService.getChildren(id);
    }
}
