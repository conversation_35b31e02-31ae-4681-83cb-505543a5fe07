package com.jxntv.gvideo.web.controller.be.event;

import com.jxntv.gvideo.web.controller.be.sensors.PostContentDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/09/30 10:03
 */
@Data
public class PostContentEvent implements Serializable {
    /**
     * 注册账号ID
     */
    private Long account;
    /**
     * 发布账号类型：0-PGC、1-UGC
     */
    private Integer releaseType;
    /**
     * 注册账号名称
     */
    private String accountName;

    /**
     * 账号所属租户ID
     */
    private List<String> authorTenantId;
    /**
     * 账号所属租户名称
     */
    private List<String> authorTenantName;
    /**
     * 圈子所属租户ID
     */
    private List<String> tenantId;
    /**
     * 圈子所属租户名称
     */
    private List<String> tenantName;
    /**
     * 圈子ID
     */
    private List<String> communityId;
    /**
     * 圈子名称
     */
    private List<String> communityName;
    /**
     * 话题ID
     */
    private List<String> conversationId;
    /**
     * 话题名称
     */
    private List<String> conversationName;
    /**
     * 内容ID
     */
    private Long contentId;
    /**
     * 内容名称
     */
    private String contentName;
    /**
     * 内容类型:
     * 1：长视频、横视频
     * 2:短视频、竖视频
     * 3:长音频、横音频
     * 4:短音频、竖音频
     * 5:横活动直播
     * 6:竖活动直播
     * 7:互动横屏直播;
     * 8:互动竖屏直播
     * 9:图文展示
     * 10:语音文字
     * 11:新闻大图或三图
     * 12:新闻左文右图
     */
    private String contentType;
    /**
     * 动态类型:
     * 1、普通帖子
     * 2、提问
     */
    private String postType;
    /**
     * 发布结果
     */
    private String publishResult;
    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 失败原因
     */
    private String failReason;
    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 定位名称
     */
    private String contentPositioning;
    /**
     * 是不是匿名帖
     */
    private Boolean isAnonymous;


    public PostContentDTO toDTO() {
        PostContentDTO dto = new PostContentDTO();
        dto.setAccount(String.valueOf(this.getAccount()));
        dto.setAccountName(this.getAccountName());
        dto.setAuthorTenantId(this.getAuthorTenantId());
        dto.setAuthorTenantName(this.getAuthorTenantName());
        dto.setTenantId(this.getTenantId());
        dto.setTenantName(this.getTenantName());
        dto.setCommunityId(this.getCommunityId());
        dto.setCommunityName(this.getCommunityName());
        dto.setConversationId(this.getConversationId());
        dto.setConversationName(this.getConversationName());
        dto.setContentId(String.valueOf(this.getContentId()));
        dto.setContentName(this.getContentName());
        dto.setContentType(this.getContentType());
        dto.setPostType(this.getPostType());
        dto.setPublishResult(this.getPublishResult());
        dto.setSuccess(this.getSuccess());
        dto.setFailReason(this.getFailReason());
        dto.setPublishTime(this.getPublishTime());
        dto.setContentPositioning(this.getContentPositioning());
        dto.setIsAnonymous(this.getIsAnonymous());
        return dto;
    }
}
