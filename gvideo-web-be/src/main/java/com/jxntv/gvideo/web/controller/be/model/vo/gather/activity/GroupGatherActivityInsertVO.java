package com.jxntv.gvideo.web.controller.be.model.vo.gather.activity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupGatherActivityInsertVO", description = "活动组件-新增活动")
public class GroupGatherActivityInsertVO {

    @ApiModelProperty(value = "活动关联圈子ID")
    @NotNull(message = "活动关联圈子ID不能为空")
    private Long groupId;

    @ApiModelProperty(value = "活动关联圈子组件ID")
    @NotNull(message = "活动关联圈子组件ID不能为空")
    private Long groupGatherId;

    @ApiModelProperty(value = "活动主题")
    @NotBlank(message = "活动主题不能为空")
    @Length(max = 30, message = "活动主题不能超过30字")
    private String title;

    @ApiModelProperty(value = "活动开始时间")
    @NotBlank(message = "活动开始时间不能为空")
    private String startDate;

    @ApiModelProperty(value = "活动结束时间")
    @NotBlank(message = "活动结束时间不能为空")
    private String endDate;

    @ApiModelProperty(value = "活动报名开始时间")
    @NotBlank(message = "活动报名开始时间不能为空")
    private String joinStartDate;

    @ApiModelProperty(value = "活动报名结束时间")
    @NotBlank(message = "活动报名结束时间不能为空")
    private String joinEndDate;

    @ApiModelProperty(value = "活动地址")
    @NotBlank(message = "活动地址不能为空")
    @Length(max = 40, message = "活动地址不能超过40字")
    private String address;

    @ApiModelProperty(value = "活动参与费用标识 0-免费 1-收费 2-押金")
    @NotNull(message = "活动参与费用不能为空")
    @Min(value = 0, message = "活动参与费用标识在0-2之间")
    @Max(value = 2, message = "活动参与费用标识在0-2之间")
    private Integer costFlag;

    @ApiModelProperty(value = "活动参与费用")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @DecimalMin(value = "0.00", message = "活动参与费用在0.01-99999.99之间")
    @DecimalMax(value = "99999.99", message = "活动参与费用在0.01-99999.99之间")
    private BigDecimal cost;

    @ApiModelProperty(value = "活动参与人数起始")
    @Min(value = 1, message = "活动参与人数在1-1000之间")
    @Max(value = 1000, message = "活动参与人数在1-1000之间")
    private Integer memberFrom;

    @ApiModelProperty(value = "活动参与人数截止")
    @Min(value = 1, message = "活动参与人数在1-1000之间")
    @Max(value = 1000, message = "活动参与人数在1-1000之间")
    private Integer memberTo;

    @ApiModelProperty(value = "活动简介")
    //@NotBlank(message = "活动简介不能为空")
    private String introduction;

    @ApiModelProperty(value = "推送标记：0-不推送，1-推送 默认为1")
//    @NotNull(message = "是否推送标记不能为空")
//    @Min(value = 0, message = "推送标记在0-1之间")
//    @Max(value = 1, message = "推送标记在0-1之间")
    private Integer pushFlag;

    @ApiModelProperty(value = "推送内容")
    private String pushContent;


    @ApiModelProperty(value = "活动海报照片")
    @NotNull(message = "活动海报照片不能为空")
    @Size(min = 1, max = 5, message = "活动海报照片在1-5张之间")
    private List<String> imageList;

    @ApiModelProperty(value = "权重")
    private Integer weight;

    @ApiModelProperty(value = "活动参与消耗红豆标识 0-免费 1-收费")
    private Integer redBeansFlag;

    @ApiModelProperty(value = "活动参与耗红豆数")
    private Integer redBeansAmount;

    @ApiModelProperty(value = "活动标签id 有标签选择项目则传值")
    private Long tagId;

    @ApiModelProperty(value = "活动标签名称 无标签选择项目则传手动输入的")
    @Length(max = 4, message = "活动标签名称不能超过40字")
    private String tagName;

    @ApiModelProperty(value = "活动类型  0-普通活动 1-约会计划")
    @NotNull(message = "活动类型不能为空")
    @Min(value = 0, message = "活动类型在0-1之间")
    @Max(value = 1, message = "活动类型在0-1之间")
    private Integer type;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

}
