package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.interact.client.dto.FeedbackDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.FeedbackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2021/08/11 15:49
 */
@Slf4j
@Component
public class FeedbackConverter {
    public FeedbackVO toVo(FeedbackDTO dto) {
        FeedbackVO vo = new FeedbackVO();
        BeanCopier.create(FeedbackDTO.class, FeedbackVO.class, false)
                .copy(dto, vo, null);
        return vo;
    }
}
