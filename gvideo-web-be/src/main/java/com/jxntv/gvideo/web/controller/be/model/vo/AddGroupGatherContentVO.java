package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/06/11 9:36
 */
@Data
@ApiModel(value = "AddGroupGatherContentVo", description = "圈子组件关联内容")
public class AddGroupGatherContentVO {
    @ApiModelProperty("圈子ID")
    @NotNull(message = "社区ID不能为空")
    private Long groupId;

    @ApiModelProperty("组件ID")
    @NotNull(message = "组件ID不能为空")
    private Long gatherId;

    @ApiModelProperty("内容Id集合")
    @NotNull(message = "内容不能为空")
    @Size(min = 1, message = "内容不能为空")
    private List<Long> contentIdList;
}
