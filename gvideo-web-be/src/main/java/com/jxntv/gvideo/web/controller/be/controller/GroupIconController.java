package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupIconClient;
import com.jxntv.gvideo.group.sdk.dto.GroupIconDTO;
import com.jxntv.gvideo.group.sdk.enums.group.icon.GroupIconButtonWordsScene;
import com.jxntv.gvideo.group.sdk.params.icon.GroupIconEditParam;
import com.jxntv.gvideo.group.sdk.params.icon.GroupIconSearchParam;
import com.jxntv.gvideo.media.client.enums.BannerMenuEnum;
import com.jxntv.gvideo.web.controller.be.common.vo.Page;
import com.jxntv.gvideo.web.controller.be.converter.LinkUrlConverter;
import com.jxntv.gvideo.web.controller.be.converter.group.GroupIconConverter;
import com.jxntv.gvideo.web.controller.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.web.controller.be.model.dto.LinkDto;
import com.jxntv.gvideo.web.controller.be.model.vo.BannerMediaMenuVO;
import com.jxntv.gvideo.web.controller.be.model.vo.group.icon.GroupIconEditVO;
import com.jxntv.gvideo.web.controller.be.model.vo.group.icon.GroupIconVO;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import com.jxntv.gvideo.web.controller.be.service.BannerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: niedamin
 * @Date: 2022/12/05 15:08
 */
@RestController
@Slf4j
@RequestMapping("/api/group/icon")
@Api(value = "圈子icon接口", tags = "圈子icon接口")
public class GroupIconController {

    @Resource
    private GroupIconClient groupIconClient;

    @Resource
    private UserConsumer userConsumer;

    @Resource
    private LinkUrlConverter linkUrlConverter;
    @Resource
    private GroupIconConverter groupIconConverter;
    @Resource
    private BannerService bannerService;

    @PostMapping
    public Result<Long> add(@RequestBody GroupIconEditVO editVO) {
        GroupIconEditParam param = groupIconConverter.convert(editVO);
        Long userId = ThreadLocalCache.getUserId(userConsumer);


        param.setUpdateUserId(userId);
        param.setCreateUserId(userId);
        param.setLinkUrl(convertLinkUrl(param));
        return groupIconClient.add(param);
    }

    @PutMapping("/{id}")
    public Result<Long> modify(@PathVariable Long id, @RequestBody GroupIconEditVO editVO) {
        GroupIconEditParam param = groupIconConverter.convert(editVO);
        Long userId = ThreadLocalCache.getUserId(userConsumer);
        param.setUpdateUserId(userId);
        param.setLinkUrl(convertLinkUrl(param));
        return groupIconClient.modify(id, param);
    }

    @GetMapping("/{id}")
    public Result<GroupIconDTO> get(@PathVariable Long id) {
        return groupIconClient.get(id);
    }

    @GetMapping("/page")
    public Result<Page<GroupIconVO>> page(@ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                          @ApiParam(value = "分页大小") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                          @ApiParam(value = "组件id") @RequestParam Long componentId) {

        GroupIconSearchParam searchParam = new GroupIconSearchParam();
        searchParam.setCurrent(pageNum);
        searchParam.setSize(pageSize);
        searchParam.setComponentId(componentId);
        Result<PageDTO<GroupIconDTO>> result = groupIconClient.page(searchParam);
        return result.map(page -> Page.pageOf(page, e -> groupIconConverter.convert(e), GroupIconVO.class));
    }

    @PostMapping("/delete")
    public Result<Void> delete(@RequestBody List<Long> ids) {
        if (!CollectionUtils.isEmpty(ids)) {
            ids.forEach(id -> groupIconClient.delete(id));
        }
        return Result.ok();
    }

    @PostMapping("/publish")
    public Result<Void> publish(@RequestBody List<Long> ids) {
        groupIconClient.publish(ids);
        return Result.ok();
    }

    @GetMapping("/scenes")
    public Result<List<String>> getScenes() {
        GroupIconButtonWordsScene[] values = GroupIconButtonWordsScene.values();
        List<String> collect = Arrays.stream(values).map(GroupIconButtonWordsScene::getScene).collect(Collectors.toList());
        return Result.ok(collect);

    }


    @GetMapping("/menus")
    public Result<List<BannerMediaMenuVO>> getMenus() {
        List<BannerMenuEnum> jumpMenus = new ArrayList<>();
        jumpMenus.add(BannerMenuEnum.answer_square);
        jumpMenus.add(BannerMenuEnum.qa_my_question);
        jumpMenus.add(BannerMenuEnum.qa_choose_teacher);

        List<BannerMediaMenuVO> list = jumpMenus
                .stream()
                .map(BannerMediaMenuVO::valueOf)
                .collect(Collectors.toList());
        return Result.ok(list);

    }


    private String convertLinkUrl(GroupIconEditParam icon) {
        return linkUrlConverter.convert(LinkDto.builder()
                .type(icon.getType())
                .groupId(icon.getGroupId())
                .contentType(icon.getContentType())
                .linkUrl(icon.getLinkUrl())
                .contentId(icon.getContentId()).build());
    }
}
