package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Created on 2021/5/11.
 */
@Data
@ApiModel("添加圈子成员")
public class GroupUserAddVO {
    @ApiModelProperty("用户id")
    @NotNull(message = "用户id不能为空")
    private Long userId;
    @ApiModelProperty("角色类型")
    @NotNull(message = "角色类型不能为空")
    private Integer roleType;
    @ApiModelProperty("圈子id")
    @NotNull(message = "社区ID不能为空")
    private Long groupId;
    @ApiModelProperty("租户id")
    private Long tenantId;
}
