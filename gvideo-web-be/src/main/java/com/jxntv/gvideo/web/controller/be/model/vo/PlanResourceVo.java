package com.jxntv.gvideo.web.controller.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "PlanResourceVo", description = "投放资源")
public class PlanResourceVo {

    @ApiModelProperty(value = "ID", required = true)
    private Long id;
    @ApiModelProperty(value = "资源类型", required = true)
    private String resourceType;
    @ApiModelProperty(value = "资源ID", required = true)
    private Long resourceId;
    @ApiModelProperty(value = "内部标题", required = true)
    private String title;
    @ApiModelProperty(value = "曝光权重", required = true)
    private Integer weight;
    @ApiModelProperty(value = "实际投放开始时间", required = true)
    private String startDate;
    @ApiModelProperty(value = "实际投放结束时间", required = true)
    private String endDate;
    @ApiModelProperty(value = "资源添加时间", required = true)
    private String addDate;
    @ApiModelProperty(value = "处理人(备注)")
    private String user;
    @ApiModelProperty(value = "媒体页签")
    private Integer mediaTab;
//    @ApiModelProperty(value = "作品类型0pgc1ugc")
    private Integer releaseType;
}
