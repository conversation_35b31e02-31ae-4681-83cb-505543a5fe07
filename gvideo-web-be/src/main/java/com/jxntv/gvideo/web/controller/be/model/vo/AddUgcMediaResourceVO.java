package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.media.client.dto.MediaResourceLocationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "AddUgcMediaResourceVO", description = "UGC添加资源对象")
public class AddUgcMediaResourceVO {

    @ApiModelProperty(value = "音视频UUID")
    private String videoId;
    @ApiModelProperty(value = "音视频fileId")
    private Long fileId;
    @ApiModelProperty(value = "文件名")
    private String fileName;
    @ApiModelProperty(value = "1视频2音频3图文4语音")
    private Integer fileType;
    @ApiModelProperty(value = "显示标题", required = true)
    private String showName;
    @ApiModelProperty(value = "内部标题", required = true)
    private String internalName;
    @ApiModelProperty(value = "简介")
    private String introduction;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "图片集合")
    private List<MediaResourceImageVo> images;
    @ApiModelProperty(value = "内容类型标签集合")
    private List<String> contentTypeLabels;
    @ApiModelProperty(value = "内部标签集合")
    private List<String> internalLabels;
    @ApiModelProperty(value = "其他特征标签集合")
    private List<String> otherFeatureLabels;
    @ApiModelProperty(value = "音视频长度")
    private String length;
    @ApiModelProperty(value = "jid")
    private Long releaseId;
    @ApiModelProperty(value = "播放样式")
    private Integer playStyle;
    @ApiModelProperty(value = "语音文字")
    private String soundContent;
    @ApiModelProperty(value = "圈子id")
    private Long groupId;
    @ApiModelProperty(value = "话题id")
    private Long topicId;
    @ApiModelProperty(value = "组件id")
    private Long gatherId;
    @ApiModelProperty(value = "外部分享标题")
    private String outShareTitle;
    @ApiModelProperty(value = "外部分享链接")
    private String outShareUrl;
    @ApiModelProperty(value = "定位信息")
    private MediaResourceLocationDTO location;
    @ApiModelProperty(value = "投票信息")
    private VoteVO vote;
    @ApiModelProperty(value = "数据类型")
    private Integer dataType;
}
