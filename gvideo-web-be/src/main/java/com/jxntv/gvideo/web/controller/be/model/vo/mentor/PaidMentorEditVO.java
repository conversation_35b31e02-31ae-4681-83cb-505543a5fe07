package com.jxntv.gvideo.web.controller.be.model.vo.mentor;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/7/4
 * Email: <EMAIL>
 */
@Data
@ApiModel("付费导师编辑对象")
public class PaidMentorEditVO {


    /**
     * 导师jid
     */
    private Long jid;

    /**
     * 回答次数
     */
    private Integer answerTimes;
    /**
     * 已经回答的次数
     */
    private Integer usedAnswerTimes;
}
