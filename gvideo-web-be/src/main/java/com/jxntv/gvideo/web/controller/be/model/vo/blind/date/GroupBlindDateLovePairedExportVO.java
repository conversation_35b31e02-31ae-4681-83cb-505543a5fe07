package com.jxntv.gvideo.web.controller.be.model.vo.blind.date;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("相亲活动配对列表")
@HeadStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//表头居中
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)//内容居中
public class GroupBlindDateLovePairedExportVO implements Serializable {
    /**
     * 配对序号
     */
    @ColumnWidth(15)
    @ExcelProperty(value = "配对序号",index = 0)
    private Integer pairedSerialNo;

    /**
     * 嘉宾编号
     */
    @ExcelProperty(value = "嘉宾编号",index = 1)
    @ColumnWidth(15)
    private Integer guestSerialNo;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别",index = 2)
    @ColumnWidth(10)
    private String gender;

    /**
     * 昵称
     */
    @ExcelProperty(value = "昵称",index = 3)
    @ColumnWidth(15)
    private String nickname;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名",index = 4)
    @ColumnWidth(15)
    private String name;

    /**
     * 电话号码
     */
    @ExcelProperty(value = "电话号码",index = 5)
    @ColumnWidth(15)
    private String mobile;

    /**
     * 配对成功的
     */
    @ExcelProperty(value = "配对成功",index = 6)
    @ColumnWidth(30)
    private String paired;

    /**
     * 一选
     */
    @ExcelProperty(value = "一选",index = 7)
    @ColumnWidth(45)
    private String primaries;

}
