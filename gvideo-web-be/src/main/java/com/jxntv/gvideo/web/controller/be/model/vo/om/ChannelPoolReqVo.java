package com.jxntv.gvideo.web.controller.be.model.vo.om;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/27 10:54
 */
@Data
@ApiModel("渠道池请求结构体")
public class ChannelPoolReqVo implements Serializable {

    @ApiModelProperty("渠道池名称")
    private String channelPoolName;

    @ApiModelProperty("渠道邀请码")
    private List<String> inviteChannelCode;

    @ApiModelProperty("内容池ID")
    private Long contentPoolId;
}
