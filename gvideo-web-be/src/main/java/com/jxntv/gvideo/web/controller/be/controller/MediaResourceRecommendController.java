package com.jxntv.gvideo.web.controller.be.controller;

import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MaterialClient;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.MediaResourceRecommendClient;
import com.jxntv.gvideo.media.client.dto.MaterialDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceRecommendDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.MediaResourceRecommendType;
import com.jxntv.gvideo.media.client.enums.ResourceType;
import com.jxntv.gvideo.web.controller.be.common.vo.ListVo;
import com.jxntv.gvideo.web.controller.be.converter.MediaResourceConverter;
import com.jxntv.gvideo.web.controller.be.model.vo.AutoCompleteVo;
import com.jxntv.gvideo.web.controller.be.model.vo.EditMediaResourceRecommendVo;
import com.jxntv.gvideo.web.controller.be.model.vo.MediaResourceRecommendVo;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.jxntv.gvideo.common.exception.CodeMessage.ERROR;
import static com.jxntv.gvideo.common.exception.CodeMessage.OK;


/**
 * Created on 2020-02-24
 */
@RestController
@RequestMapping("/api/media-resource-recommends")
@Api(value = "资源相关推荐", tags = "MediaResourceRecommendController")
public class MediaResourceRecommendController {

    @Autowired
    private MediaResourceRecommendClient mediaResourceRecommendClient;
    @Autowired
    private MediaResourceClient mediaResourceClient;
    @Autowired
    private MaterialClient materialClient;

    @GetMapping
    @ApiOperation(value = "资源相关推荐列表")
    public Result<ListVo<MediaResourceRecommendVo>> list(@ApiParam(name = "resourceId", value = "资源ID", required = true)
                                                         @RequestParam Long resourceId) {
        Result<List<MediaResourceRecommendDTO>> result = mediaResourceRecommendClient.list(resourceId);
        if (result.getCode() != OK.getCode()) {
            return Result.fail(result.getMessage());
        }
        List<MediaResourceDTO> resources = bulkResource(result.getResult());
        List<MaterialDTO> materials = bulkMaterial(result.getResult());
        return Result.ok(ListVo.listOf(result.getResult(), dto -> MediaResourceConverter
                .toRecommendVo(dto, resources, materials), MediaResourceRecommendVo.class));
    }

    @PutMapping("/sort")
    @ApiOperation(value = "更新排序")
    public Result sort(@RequestBody Long[] ids) {
        return mediaResourceRecommendClient.sort(ids);
    }

    private List<MediaResourceDTO> bulkResource(List<MediaResourceRecommendDTO> recommends) {
        List<Long> contentIds = recommends.stream().filter(r -> r.getType() != null && r.getType()
                .equals(MediaResourceRecommendType.RESOURCE.getCode()))
                .map(MediaResourceRecommendDTO::getContentId).collect(Collectors.toList());
        Result<List<MediaResourceDTO>> result = mediaResourceClient.bulk(contentIds);
        if (result.getCode() != OK.getCode()) {
            throw new CodeMessageException(ERROR, result.getMessage());
        }
        return result.getResult();
    }

    private List<MaterialDTO> bulkMaterial(List<MediaResourceRecommendDTO> recommends) {
        List<Long> contentIds = recommends.stream().filter(r -> r.getType() != null && r.getType()
                .equals(MediaResourceRecommendType.MATERIAL.getCode()))
                .map(MediaResourceRecommendDTO::getContentId).collect(Collectors.toList());
        Result<List<MaterialDTO>> result = materialClient.bulk(contentIds);
        if (result.getCode() != OK.getCode()) {
            throw new CodeMessageException(ERROR, result.getMessage());
        }
        return result.getResult();
    }

    @ApiOperation(value = "编辑推荐内容")
    @PutMapping("/{id}")
    public Result update(@PathVariable Long id, @RequestBody
            EditMediaResourceRecommendVo editMediaResourceRecommendVo) {
        MediaResourceRecommendDTO dto = new MediaResourceRecommendDTO();
        dto.setContentId(editMediaResourceRecommendVo.getResourceId());
        dto.setValidityStartDate(DateUtils.parse(editMediaResourceRecommendVo.getStart()));
        dto.setValidityEndDate(DateUtils.parse(editMediaResourceRecommendVo.getEnd()));
        Integer type = editMediaResourceRecommendVo.getType();
        if (type != null) {
            if (type.equals(ResourceType.VIDEO.getCode()) || type.equals(ResourceType.AUDIO.getCode())
                    || type.equals(ResourceType.PPTV.getCode()) || type.equals(ResourceType.LIVE_BROADCAST.getCode())
                    || type.equals(ResourceType.NEWS.getCode()) || type.equals(ResourceType.SPECIAL.getCode())) {
                dto.setType(MediaResourceRecommendType.RESOURCE.getCode());
            } else if (type.equals(ResourceType.MATERIAL.getCode())) {
                dto.setType(MediaResourceRecommendType.MATERIAL.getCode());
            } else {
                return Result.fail("资源类型错误");
            }
        }
        return mediaResourceRecommendClient.update(id, dto);
    }

    @GetMapping("/autocomplete")
    @ApiOperation(value = "根据名称推荐")
    public Result<List<AutoCompleteVo>> autocomplete(@ApiParam(name = "type", value = "1视频 2音频 3运营素材 4第三方资源 5直播 6图文 7语音 8文章 9专题", required = true)
                                                     @RequestParam Integer type,
                                                     @ApiParam(name = "prefix", value = "搜索前缀", required = true)
                                                     @RequestParam String prefix) {

        ResourceType resourceType = ResourceType.parse(type);
        if (resourceType.equals(ResourceType.VIDEO)) {
            List<Integer> contentTypeList = Arrays.asList(
                    ContentType.VIDEO_FILM.getCode(),
                    ContentType.VIDEO_HOMEMADE.getCode(),
                    ContentType.VIDEO_COLUMN.getCode(),
                    ContentType.VIDEO_PROGRAM.getCode());

            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzy(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.AUDIO)) {
            List<Integer> contentTypeList = Collections.singletonList(ContentType.AUDIO_FM.getCode());

            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzy(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.MATERIAL)) {
            Result<List<MaterialDTO>> result = materialClient.fuzzy(prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.PPTV)) {
            List<Integer> contentTypeList = Arrays.asList(
                    ContentType.VIDEO_FILM.getCode(),
                    ContentType.VIDEO_COLUMN.getCode(),
                    ContentType.VIDEO_PROGRAM.getCode());
            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzyPptv(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convertShowName).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.LIVE_BROADCAST)) {
            List<Integer> contentTypeList = Arrays.asList(
                    ContentType.LIVE_BROADCAST.getCode(),
                    ContentType.INTERACTIVE_BROADCAST.getCode()
            );
            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzy(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.NEWS)) {
            List<Integer> contentTypeList = Collections.singletonList(ContentType.NEWS.getCode());

            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzy(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else if (resourceType.equals(ResourceType.SPECIAL)) {
            List<Integer> contentTypeList = Collections.singletonList(ContentType.SPECIAL.getCode());

            Result<List<MediaResourceDTO>> result = mediaResourceClient.fuzzy(contentTypeList, prefix);
            if (result.getCode() != OK.getCode()) {
                return Result.fail(result.getMessage());
            }
            return Result.ok(result.getResult().stream().map(AutoCompleteVo::convert).collect(Collectors.toList()));
        } else {
            return Result.fail("查询类型错误");
        }
    }
}
