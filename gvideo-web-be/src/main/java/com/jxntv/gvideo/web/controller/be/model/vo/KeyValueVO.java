package com.jxntv.gvideo.web.controller.be.model.vo;

import com.jxntv.gvideo.media.client.enums.BroadcastDisplayType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> Created on 2021/5/14.
 */
@Data
@ApiModel("键值对页面对象")
public class KeyValueVO {
    @ApiModelProperty("id")
    private String key;
    @ApiModelProperty("值")
    private String value;

    public static KeyValueVO valueOf(String key, String value) {
        KeyValueVO valueVO = new KeyValueVO();
        valueVO.setValue(value);
        valueVO.setKey(key);
        return valueVO;
    }


    public static KeyValueVO valueOf(BroadcastDisplayType displayType) {
        KeyValueVO valueVO = new KeyValueVO();
        valueVO.setKey(String.valueOf(displayType.getCode()));
        valueVO.setValue(displayType.getDesc());
        return valueVO;
    }
}
