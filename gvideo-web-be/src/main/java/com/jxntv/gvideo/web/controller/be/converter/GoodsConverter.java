package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.media.client.dto.shop.MediaGoodsDTO;
import com.jxntv.gvideo.web.controller.be.client.ShopGoodsClient;
import com.jxntv.gvideo.web.controller.be.client.dto.ShopGoodsDTO;
import com.jxntv.gvideo.web.controller.be.client.dto.ShopGoodsPageDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.shop.GoodsVO;
import com.jxntv.gvideo.web.controller.be.model.vo.shop.MediaGoodsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/1
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class GoodsConverter {


    @Resource
    private ShopGoodsClient shopGoodsClient;


    public MediaGoodsVO convert(MediaGoodsDTO dto){
        MediaGoodsVO goodsVO = new MediaGoodsVO();
        goodsVO.setId(dto.getId());
        goodsVO.setGoodsId(dto.getGoodsId());
        goodsVO.setIsShow(dto.getIsShow());

        ShopGoodsDTO shopGoodsDTO = shopGoodsClient.getGoodsById(dto.getGoodsId());
        if (Objects.nonNull(shopGoodsDTO)) {
            goodsVO.setName(shopGoodsDTO.getGoodsName());
            goodsVO.setIconUrl(shopGoodsDTO.getThumbnail());
            goodsVO.setPrice(shopGoodsDTO.getPrice());
        }

        return goodsVO;
    }

    public GoodsVO convert(ShopGoodsPageDTO.GoodsDataDTO.DataDTO dto){
        GoodsVO goodsVO = new GoodsVO();
        goodsVO.setId(dto.getGoodsId());
        goodsVO.setName(dto.getName());
        goodsVO.setPrice(dto.getPrice());
        goodsVO.setIconUrl(dto.getThumbnail());
        goodsVO.setIsShow(false);

        return goodsVO;
    }
}
