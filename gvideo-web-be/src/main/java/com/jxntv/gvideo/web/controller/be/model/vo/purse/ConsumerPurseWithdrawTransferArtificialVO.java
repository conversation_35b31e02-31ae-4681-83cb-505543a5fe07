package com.jxntv.gvideo.web.controller.be.model.vo.purse;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="ConsumerPurseWithdrawTransferArtificialVO", description="人工转账信息")
public class ConsumerPurseWithdrawTransferArtificialVO {

    @ApiModelProperty(value="ID")
    @NotNull(message = "ID不能为空")
    private Long id;

    @ApiModelProperty(value="人工转账备注")
    @NotBlank(message = "备注不能为空")
    @Length(max = 20,message = "备注字数不能超过20字")
    private String remarks;
}
