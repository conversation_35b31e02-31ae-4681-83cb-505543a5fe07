package com.jxntv.gvideo.web.controller.be.job;

import com.jxntv.gvideo.web.controller.be.client.BroadcastLocationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Created on 2021/1/26.
 */
@Slf4j
@Component
public class BroadcastLocationJob  {
    @Autowired
    private BroadcastLocationService broadcastLocationService;
//
//    @Override
//    public String getCron() {
//        return "0 0/1 * * * ?";
//    }
//
//    @Override
//    public String getName() {
//        return "BroadcastLocationJob";
//    }
//
//    @Override
//    public Class getJobClass() {
//        return BroadcastLocationJob.class;
//    }
//
//    @Override
//    @Bean("BroadcastLocationJobTrigger")
//    public Trigger getTrigger() throws ParseException {
//        return getDefaultTrigger();
//    }
//
//    @Override
//    @Bean("BroadcastLocationJobDetail")
//    public JobDetail getJobDetail() {
//        return getDefaultJobDetail();
//    }

    @XxlJob("broadcastLocationJob")
    public ReturnT<String> execute() {
        log.info("扫描直播投放计划状态");
        broadcastLocationService.scanStatus();
        return ReturnT.SUCCESS;
    }
}
