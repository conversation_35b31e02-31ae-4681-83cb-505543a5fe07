package com.jxntv.gvideo.web.controller.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.SoundDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodAudioPlayDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodVideoPlayDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.web.controller.be.rpc.OssConsumer;
import com.jxntv.gvideo.web.controller.be.rpc.TenantConsumer;
import com.jxntv.gvideo.web.controller.be.rpc.UserConsumer;
import com.jxntv.gvideo.web.controller.be.utils.DateUtils;
import com.jxntv.gvideo.media.client.MediaFileClient;
import com.jxntv.gvideo.media.client.dto.MediaFileDTO;
import com.jxntv.gvideo.media.client.enums.MediaFileStatus;
import com.jxntv.gvideo.media.client.enums.MediaFileType;
import com.jxntv.gvideo.web.controller.be.model.vo.MediaFileVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class MediaFileConverter {

    @Resource
    private UserConsumer userConsumer;
    @Resource
    private TenantConsumer tenantConsumer;
    @Resource
    private MediaFileClient mediaFileClient;
    @Resource
    private OssClient ossClient;


    public MediaFileVo convert(MediaFileDTO dto) {
        MediaFileVo vo = new MediaFileVo();
        vo.setId(dto.getId());
        vo.setName(dto.getFileName());
        vo.setSourceId(dto.getResourceId().equals(-1L) ? null : dto.getResourceId());
        vo.setStatus(MediaFileStatus.toStr(dto.getStatus()));
        if (!Objects.equals(MediaFileStatus.DELETED.getCode(), dto.getStatus())) {
            vo.setFileURL(getFileURL(dto));
        }
        vo.setTime(DateUtils.format(dto.getCreateDate()));
        vo.setType(MediaFileType.toStr(dto.getFileType()));
        vo.setUp(userConsumer.name(dto.getCreateUserId()));
        if (dto.getLimitView()) {
            List<Long> ids = mediaFileClient.getVisibleTenantIds(dto.getId()).getResult();
            vo.setTenant(tenantConsumer.namesSeparatedByCommas(ids));
        } else {
            vo.setTenant("全租户");
        }
        return vo;
    }

    private String getFileURL(MediaFileDTO file) {
        String result = "";

        String ossId = file.getOssId();
        Integer fileType = file.getFileType();
        if (fileType.equals(MediaFileType.VIDEO.getCode())) {
            result = ossClient.getVideoFileUrl(ossId).map(VodVideoPlayDTO::getUrl).orElse("");
        } else if (fileType.equals(MediaFileType.AUDIO.getCode()) || fileType == MediaFileType.SOUND.getCode()) {
            result = ossClient.getAudioFileUrl(ossId).map(VodAudioPlayDTO::getUrl).orElse("");
        }

        return result;
    }
}
