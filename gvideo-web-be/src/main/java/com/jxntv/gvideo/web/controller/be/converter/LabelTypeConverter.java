package com.jxntv.gvideo.web.controller.be.converter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.jxntv.gvideo.web.controller.be.model.vo.LabelTypeVo;
import com.jxntv.gvideo.media.client.LabelClient;
import com.jxntv.gvideo.media.client.dto.LabelTypeDTO;
import com.jxntv.gvideo.web.controller.be.model.vo.SimpleLabelTypeVo;
import com.jxntv.gvideo.common.model.Result;

/**
 *
 * Created on 2020-02-14
 */
@Component
public class LabelTypeConverter {

    @Autowired
    private LabelClient labelClient;

    public LabelTypeVo labelType(LabelTypeDTO dto) {
        LabelTypeVo vo = new LabelTypeVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setIsSystem(dto.getIsSystem() ? "系统类型" : "自建类型");
        Result<Integer> res = labelClient.count(dto.getId());
        vo.setTotal(res.getResult());
        return vo;
    }

    public SimpleLabelTypeVo simpleLabelType(LabelTypeDTO dto) {
        SimpleLabelTypeVo vo = new SimpleLabelTypeVo();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        return vo;
    }

}
