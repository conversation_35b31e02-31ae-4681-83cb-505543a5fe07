package com.jxntv.gvideo.web.controller.be.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxntv.gvideo.web.controller.be.config.SwaggerConfig;
import com.jxntv.gvideo.web.controller.be.model.vo.AddChannelPlanVo;
import com.jxntv.gvideo.web.controller.be.utils.LocalDateTimeUtils;
import com.jxntv.gvideo.web.controller.be.utils.Restful;
import com.jxntv.gvideo.common.model.Result;
import org.apache.commons.lang.RandomStringUtils;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJsonTesters;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.json.JacksonTester;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

@SpringBootTest
@AutoConfigureMockMvc
@AutoConfigureJsonTesters
public class ChannelPlanControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;

    private Restful restful;

    @Autowired
    private JacksonTester<Result<Long>> longJacksonTester;

    @Test
    public void create() throws Exception {
        AddChannelPlanVo plan = new AddChannelPlanVo();
        plan.setName(RandomStringUtils.random(9));
        plan.setChannelId(1L);
        plan.setPlanStartDate(LocalDateTimeUtils.format(LocalDateTime.now().plus(1, ChronoUnit.DAYS)));
        plan.setPlanEndDate(LocalDateTimeUtils.format(LocalDateTime.now().plus(1, ChronoUnit.WEEKS)));
        plan.setPos(1);
        String content = restful.post("/api/plans", plan);
        Long id = longJacksonTester.parseObject(content).getResult();
        Assertions.assertThat(id).isNotNull();
    }

    @BeforeEach
    public void beforeEach() {
        if (restful == null) {
            restful = new Restful(SwaggerConfig.Authorization, mockMvc, objectMapper);
        }
    }
}