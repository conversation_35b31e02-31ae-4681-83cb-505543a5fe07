create table if not exists `gvideo-uat`.activity_registration
(
    id        int(10) auto_increment comment 'ID'
        primary key,
    image     varchar(32) not null comment '展示图片',
    dead_line datetime    not null comment '截止时间',
    url       varchar(32) not null comment '外链URL'
)
    comment '活动报名' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.app_protocol
(
    id               bigint auto_increment comment '主键ID'
        primary key,
    cid              varchar(128)                        null comment '设备编号',
    jid              bigint(16)                          null comment '今视频ID',
    protocol_type    tinyint(1)                          null comment '协议类型',
    protocol_version varchar(10)                         null comment '协议版本号',
    create_date      timestamp default CURRENT_TIMESTAMP null comment '用户同意时间'
)
    comment 'APP用户同意协议表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_biz
(
    id          int(10) auto_increment comment '自增主键'
        primary key,
    name        varchar(32)                          null comment '业务类型名称',
    note        varchar(64)                          null comment '业务类型描述',
    flow_id     int(10)                              null comment '关联流程ID',
    level       tinyint(1)                           null comment '等级',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核状态表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_condition
(
    id          int(10) auto_increment comment '自增主键'
        primary key,
    config_id   int(10)                              null comment '条件ID',
    content     varchar(2000)                        null comment '规则内容',
    biz_id      int(10)                              null comment '业务类型ID',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核规则表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_condition_config
(
    id          int(10) auto_increment comment '自增主键'
        primary key,
    name        varchar(32)                          null comment '规则名称',
    logic       varchar(32)                          null comment '逻辑关系',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核规则配置表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_flow
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    name        varchar(32)                          null comment '工作流名称',
    note        varchar(64)                          null comment '工作流描述描述',
    version     smallint   default 1                 null comment '版本号',
    uuid        varchar(64)                          null comment '工作流历史唯一ID',
    is_filed    tinyint(1) default 0                 null comment '是否已归档的历史版本',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核工作流表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_log_media
(
    id               int(10) auto_increment comment '自增ID'
        primary key,
    media_id         int(10)                              null comment '音视频ID',
    content_type     tinyint(1)                           null comment '内容类型',
    media_type       tinyint(1)                           null comment '1视频或2音频',
    author_id        bigint                               null comment '作者ID',
    author_name      varchar(64)                          null comment '作者名字（筛选用）',
    media_title      varchar(255)                         null comment '资源名称',
    procedure_id     int(10)                              null comment '当前工作流及节点位置',
    admin_user_id    int(10)                              null comment '当前处理人ID',
    admin_user_name  varchar(32)                          null comment '当前处理人名',
    checkstatus      tinyint(1) default 0                 null comment '审核状态：0待审1通过-1退修-2废弃',
    memo             varchar(255)                         null comment '机审命中信息（预留）',
    refuse_code      int(10)                              null comment '人工拒绝原因码（预留）',
    refuse_note      varchar(255)                         null comment '人工拒绝备注（预留）',
    is_final         tinyint(1) default 0                 null comment '是否终审',
    is_flow_refuse   tinyint(1) default 0                 null comment '是否由于审核流配置原因退修',
    is_last_final    tinyint(1) default 0                 null comment '是否一个物料最终的终审',
    create_date      timestamp  default CURRENT_TIMESTAMP null comment '资源创建时间',
    enter_audit_date timestamp  default CURRENT_TIMESTAMP null comment '资源进入审核时间',
    update_date      timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    audit_date       timestamp                            null comment '审核完成时间',
    index idx_media (media_id),
    index idx_author (author_id),
    index idx_procedure (procedure_id)
)
    comment '审核音视频日志表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_procedure
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    flow_id     int(10)                              null comment '工作流ID',
    state_id    int(10)                              null comment '状态ID',
    sort        tinyint(1)                           null comment '排序',
    role_ids    varchar(1000)                        null comment '关联角色ID集合，逗号分割',
    user_ids    varchar(1000)                        null comment '关联用户ID集合，逗号分割',
    is_filed    tinyint(1) default 0                 null comment '是否已归档',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核步骤表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.audit_state
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    name        varchar(32)                          null comment '步骤名称',
    note        varchar(64)                          null comment '步骤描述',
    is_system   tinyint(1) default 0                 null comment '是否系统预设',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '审核状态节点表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditing_comment
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    comment_id      bigint                                  null comment '评论或回复唯一ID',
    platform        int(8)        default 0                 not null comment '平台类型：0-今视频、1-赣云',
    category_id     bigint        default -1                not null comment '栏目ID',
    media_id        bigint                                  null comment '资源ID',
    show_name       varchar(128)  default ''                not null comment '显示标题',
    type            tinyint(3)    default 0                 null comment '评论类型0主评论1回复评论',
    content         varchar(255)                            null comment '内容',
    from_jid        bigint                                  null comment '评论人JID',
    from_name       varchar(32)                             null comment '评论人昵称',
    phase           tinyint(3)    default 1                 null comment '当前阶段0待机审1待人审2审核完',
    checkstatus     tinyint(3)    default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                            null comment '命中敏感词',
    audit_user_id   varchar(32)                             null comment '审核人用户名',
    audit_user_name varchar(32)                             null comment '审核人名',
    create_date     timestamp     default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                               null comment '审核完成时间',
    image           varchar(2000) default ''                null comment '图片oss id集合 分好隔开',
    sound_oss_id    varchar(128)  default ''                null comment '语音oss id',
    index idx_media (media_id)
)
    comment '评论待审核表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditing_media
(
    id               int(10) auto_increment comment '自增ID'
        primary key,
    media_id         int(10)                                 null comment '音视频ID',
    content_type     tinyint(1)                              null comment '内容类型',
    media_type       tinyint(1)                              null comment '1-视频、2-音频、3-运营素材、4-第三方资源、5-直播、6-图文、7-语音、8-文章、9-专题',
    author_id        bigint                                  null comment '作者ID',
    author_name      varchar(64)                             null comment '作者名字（筛选用）',
    author_type      tinyint(1)    default 0                 null comment '作者类型0PGC1UGC',
    media_title      varchar(255)                            null comment '资源名称',
    procedure_id     int(10)       default 0                 null comment '当前工作流及节点位置',
    admin_user_id    int(10)       default 0                 null comment '当前处理人ID',
    admin_user_name  varchar(32)   default '0'               null comment '当前处理人名',
    checkstatus      tinyint(1)    default 0                 null comment '审核状态：0待审1通过-1拒绝',
    memo             varchar(255)                            null comment '机审命中信息（预留）',
    refuse_code      int(10)                                 null comment '人工拒绝原因码（预留）',
    refuse_note      varchar(255)                            null comment '人工拒绝备注（预留）',
    create_date      timestamp     default CURRENT_TIMESTAMP null comment '资源创建时间',
    enter_audit_date timestamp     default CURRENT_TIMESTAMP null comment '资源进入审核时间',
    update_date      timestamp     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    audit_date       timestamp                               null comment '审核完成时间',
    del_flag         tinyint(1)    default 0                 null comment '逻辑删除标记，0正常1删除',
    content          varchar(2000) default ''                null,
    index idx_media (media_id)
)
    comment '音视频待审核表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditlog_avatar
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    avatar          varchar(255)                         null comment '头像文件ID',
    avatar_old      varchar(255)                         null comment '原头像文件ID',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '审核完成时间',
    index idx_jid (jid)
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditlog_comment
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    comment_id      bigint                                  null comment '评论或回复唯一ID',
    platform        int(8)        default 0                 not null comment '平台类型：0-今视频、1-赣云',
    category_id     bigint        default -1                not null comment '栏目ID',
    media_id        bigint                                  null comment '资源ID',
    show_name       varchar(128)  default ''                not null comment '显示标题',
    type            tinyint(3)    default 0                 null comment '评论类型0主评论1回复评论',
    content         varchar(255)                            null comment '内容',
    from_jid        bigint                                  null comment '评论人JID',
    from_name       varchar(32)                             null comment '评论人昵称',
    checkstatus     tinyint(3)    default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                            null comment '命中敏感词',
    audit_user_id   varchar(32)                             null comment '审核人用户名',
    audit_user_name varchar(32)                             null comment '审核人名',
    create_date     timestamp     default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                               null comment '审核完成时间',
    image           varchar(2000) default ''                null comment '图片oss id集合 分好隔开',
    sound_oss_id    varchar(128)  default ''                null comment '语音oss id',
    index idx_media (media_id)
)
    comment '评论审核日志表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditlog_identity
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    identity        varchar(32)                          null comment '身份证号',
    front           varchar(255)                         null comment '身份证正面',
    back            varchar(255)                         null comment '身份证反面',
    hand            varchar(255)                         null comment '身份证手持',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                            null comment '审核完成时间',
    index idx_jid (jid)
)
    comment '身份证审核日志表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditlog_info
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    info            varchar(255)                         null comment '新简介',
    info_old        varchar(255)                         null comment '原简介',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                         null comment '命中敏感词',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(255)                         null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '审核完成时间',
    index idx_jid (jid)
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.auditlog_nickname
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    nickname        varchar(255)                         null comment '新昵称',
    nickname_old    varchar(255)                         null comment '原昵称',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                         null comment '命中敏感词',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '审核完成时间',
    index idx_jid (jid)
)
    comment '昵称审核日志表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.broadcast_consumer_statistics
(
    id          bigint auto_increment
        primary key,
    live_id     bigint  default 0 not null,
    type        tinyint default 1 not null comment '1 活动直播 2 互动直播',
    jid         bigint  default 0 not null,
    like_sum    int     default 0 not null,
    click_sum   int     default 0 not null,
    share_sum   int     default 0 not null,
    comment_sum int     default 0 not null
)
    comment '直播用户行为统计表';

create table if not exists `gvideo-uat`.certification
(
    id               int auto_increment comment '认证号ID'
        primary key,
    name             varchar(10) collate utf8mb4_bin       not null comment '认证号名称',
    introduce        varchar(100) collate utf8mb4_bin      not null comment '认证号介绍',
    avatar           varchar(32) collate utf8mb4_bin       not null comment '头像',
    background_image varchar(32) collate utf8mb4_bin       not null comment '背景图',
    status           tinyint(1)  default 1                 not null comment '状态',
    create_date      timestamp   default CURRENT_TIMESTAMP null comment '创建时间',
    update_date      timestamp   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag         smallint(3) default 0                 null comment '逻辑删除标记(0--正常 1--删除)'
)
    comment '认证号表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.certification_column
(
    id               int auto_increment comment '栏目ID'
        primary key,
    certification_id int                                   not null comment '认证号ID',
    name             varchar(10) collate utf8mb4_bin       not null comment '栏目名称',
    introduce        varchar(100) collate utf8mb4_bin      not null comment '栏目介绍',
    status           tinyint(1)  default 0                 null comment '状态:0启用-1禁用',
    create_date      timestamp   default CURRENT_TIMESTAMP null comment '创建时间',
    update_date      timestamp   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag         smallint(3) default 0                 null comment '逻辑删除标记(0--正常 1--删除)'
)
    comment '认证号栏目表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.certification_tenant
(
    id                 int auto_increment comment '关系ID'
        primary key,
    certification_id   int                                 not null comment '认证号ID',
    tenant_id          int                                 not null comment '租户ID',
    tenant_create_date timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '租户创建时间',
    create_date        timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date        timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '认证号租户关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.certification_user
(
    id               int auto_increment comment '关系ID'
        primary key,
    certification_id int                                 not null comment '认证号ID',
    tenant_id        int                                 not null comment '租户ID',
    user_id          int                                 not null comment '账号ID',
    user_create_date timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '账号创建时间',
    create_date      timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date      timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '认证号账号关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.channel
(
    id                 int(20) auto_increment comment '自增ID'
        primary key,
    type               int                                     not null comment '频道类型',
    name               varchar(30)                             not null comment '频道名称',
    remark             varchar(30)                             not null comment '备注',
    status             tinyint                      default 1  not null comment '状态 1 启用 0 禁用',
    operator_id        int                                     not null comment '操作人id',
    sort               int                          default 1  not null comment '排序',
    channel_res_type   char(100) charset utf8mb4    default '' not null comment '频道资源类型',
    disable_date       datetime                                null comment '禁用时间',
    content_type_label varchar(100) charset utf8mb4 default '' not null comment '内容类型标签',
    internal_label     varchar(100) charset utf8mb4 default '' not null comment '内部标签',
    other_label        varchar(100) charset utf8mb4 default '' not null comment '其他类型标签',
    other_label_type   varchar(100) charset utf8mb4 default '' not null comment '其他标签类型',
    url                varchar(256)                 default '' not null comment 'H5页面地址'
)
    comment '频道' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.channel_label
(
    id         int(20) auto_increment comment '自增ID'
        primary key,
    channel_id int not null comment '频道ID',
    label_id   int not null comment '标签ID'
)
    comment '频道的标签' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.channel_plan
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    channel_id  int                                 not null comment '频道ID',
    name        varchar(30)                         not null comment '计划名称',
    pos         int       default -1                not null comment '投放位置',
    start_date  datetime                            not null comment '开始时间',
    end_date    datetime                            not null comment '结束时间',
    operator_id int                                 not null comment '操作人ID',
    create_date timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    delete_date timestamp                           null comment '删除时间',
    status      int                                 not null comment '状态 结束 删除'
)
    comment '投放计划' collate = utf8mb4_unicode_ci;

create index idx_channel
    on `gvideo-uat`.channel_plan (channel_id);

create table if not exists `gvideo-uat`.channel_resource_type
(
    id           int(20) auto_increment comment '自增ID'
        primary key,
    channel_id   int not null comment '频道ID',
    content_type int null comment '内容类型',
    play_style   int null comment '播放样式'
)
    comment '频道资源类型' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.channel_tenant
(
    id                 int auto_increment comment '关系ID'
        primary key,
    channel_id         int                                 not null comment '频道ID',
    tenant_id          int                                 not null comment '租户ID',
    tenant_create_date timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '租户创建时间',
    create_date        timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date        timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '频道租户关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.channel_user
(
    id               int auto_increment comment '关系ID'
        primary key,
    channel_id       int                                 not null comment '认证号ID',
    tenant_id        int                                 not null comment '租户ID',
    user_id          int                                 not null comment '用户ID',
    user_create_date timestamp default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '账号创建时间',
    create_date      timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date      timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '频道用户关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.comment_primary
(
    id           bigint auto_increment comment '自增ID'
        primary key,
    content      varchar(255)                            null comment '评论内容',
    platform     int(8)        default 0                 not null comment '平台类型：0-今视频、1-赣云',
    category_id  bigint        default -1                not null comment '栏目ID',
    media_id     bigint                                  null comment '评论资源ID',
    show_name    varchar(128)  default ''                not null comment '显示标题',
    from_jid     bigint                                  null comment '评论人JID',
    from_name    varchar(32)                             null comment '评论人昵称',
    status       tinyint(3)    default 1                 null comment '1启用0禁用',
    checkstatus  tinyint(3)    default 0                 null comment '审核结果1通过-1拒绝',
    create_date  timestamp     default CURRENT_TIMESTAMP null comment '创建时间',
    update_date  timestamp     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    audit_date   datetime                                null comment '审核时间',
    del_flag     tinyint(1)    default 0                 null comment '逻辑删除标记',
    image        varchar(2000) default ''                null comment '图片oss id集合 分好隔开',
    sound_oss_id varchar(128)  default ''                null comment '语音oss id',
    index idx_media (media_id)
)
    comment '主评论表' collate = utf8mb4_unicode_ci;

create index idx_media_id
    on `gvideo-uat`.comment_primary (media_id);

create table if not exists `gvideo-uat`.comment_reply
(
    id           bigint auto_increment comment '自增ID'
        primary key,
    content      varchar(255)                            null comment '回复内容',
    platform     int(8)        default 0                 not null comment '平台类型：0-今视频、1-赣云',
    category_id  bigint        default -1                not null comment '栏目ID',
    media_id     bigint                                  null comment '资源ID',
    show_name    varchar(128)  default ''                not null comment '显示标题',
    primary_id   bigint                                  null comment '主评论ID',
    from_jid     bigint                                  null comment '回复人JID',
    from_name    varchar(32)                             null comment '回复人昵称',
    to_jid       bigint                                  null comment '回复给谁JID',
    to_name      varchar(32)                             null comment '回复给谁人名',
    status       tinyint(3)    default 1                 null comment '1启用0禁用',
    checkstatus  tinyint(3)    default 0                 null comment '审核结果1通过-1拒绝',
    create_date  timestamp     default CURRENT_TIMESTAMP null comment '创建时间',
    update_date  timestamp     default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    audit_date   timestamp                               null comment '审核时间',
    del_flag     tinyint(1)    default 0                 null comment '逻辑删除标记',
    image        varchar(2000) default ''                null comment '图片oss id集合 分好隔开',
    sound_oss_id varchar(128)  default ''                null comment '语音oss id',
    index idx_media (media_id)
)
    comment '回复评论表' collate = utf8mb4_unicode_ci;

create index idx_primary
    on `gvideo-uat`.comment_reply (primary_id);

create table if not exists `gvideo-uat`.consumer_avatar
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    avatar          varchar(255)                         null comment '头像文件ID',
    avatar_old      varchar(255)                         null comment '原头像文件ID',
    phase           tinyint(3) default 1                 null comment '当前阶段0待机审1待人审2审核完',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                            null comment '审核完成时间',
    index idx_jid (jid)
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_channel
(
    id           bigint auto_increment comment '主键id'
        primary key,
    device_id    varchar(256) default '' not null comment '设备id',
    channel_id   int          default 0  not null comment '渠道id',
    channel_name varchar(128)            null comment '渠道名'
)
    comment 'c端渠道表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_delete_task
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    jid         bigint                               null comment 'JID',
    is_sync     tinyint(1) default 0                 null comment '同步标记1已同步0未同步',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    index idx_jid (jid)
)
    comment '用户注销账户处理数据任务' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_identity
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    identity        varchar(32)                          null comment '身份证号',
    front           varchar(255)                         null comment '身份证正面',
    back            varchar(255)                         null comment '身份证反面',
    hand            varchar(255)                         null comment '身份证手持',
    phase           tinyint(1) default 1                 null comment '当前阶段0待机审1待人审2审核完',
    checkstatus     tinyint(1) default 0                 null comment '审核结果1通过-1拒绝',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                            null comment '审核完成时间',
    index idx_jid (jid)
)
    comment '用户身份证待审核表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_info
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    info            varchar(255)                         null comment '新简介',
    info_old        varchar(255)                         null comment '原简介',
    phase           tinyint(3) default 1                 null comment '当前阶段0待机审1待人审2审核完',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                         null comment '命中敏感词',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                            null comment '审核完成时间',
    index idx_jid (jid)
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_nickname
(
    id              bigint auto_increment comment '自增ID'
        primary key,
    jid             bigint                               null comment '今视频ID',
    nickname        varchar(255)                         null comment '新昵称',
    nickname_old    varchar(255)                         null comment '原昵称',
    phase           tinyint(3) default 1                 null comment '当前阶段0待机审1待人审2审核完',
    checkstatus     tinyint(3) default 0                 null comment '审核结果1通过-1拒绝',
    memo            varchar(255)                         null comment '命中敏感词',
    audit_user_id   varchar(32)                          null comment '审核人用户名',
    audit_user_name varchar(32)                          null comment '审核人名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '进入审核时间',
    audit_date      timestamp                            null comment '审核完成时间',
    index idx_jid (jid)
)
    comment '用户昵称待审核表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_user
(
    id               bigint auto_increment comment '自增ID'
        primary key,
    jid              bigint                               not null comment '今视频唯一ID',
    device           varchar(255)                         null comment '设备号',
    mobile           varchar(32)                          null comment '手机号',
    country_code     varchar(10)                          null comment '国家区号',
    nickname         varchar(20)                          null comment '昵称',
    gender           tinyint(1)                           null comment '性别',
    gender_visible   tinyint(1) default 1                 null comment '性别是否可见',
    birthday         datetime                             null comment '出生日期',
    birthday_visible tinyint(1) default 1                 null comment '出生日期是否可见',
    avatar           varchar(255)                         null comment '头像地址',
    info             varchar(255)                         null comment '个人简介',
    type             tinyint(3) default 0                 null comment '用户类型0普通1实名',
    status           tinyint(3) default 1                 null comment '正常/冻结状态',
    create_date      timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date      timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag         tinyint(1) default 0                 null comment '逻辑删除标记',
    index idx_jid (jid)
)
    comment 'C端用户表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_user_push_config
(
    id        bigint unsigned auto_increment comment '主键id'
        primary key,
    jid       bigint unsigned  default 0  not null comment '今视频用户id',
    token     varchar(256)     default '' not null comment '第三方服务token 记录最近一次登录token',
    device_id varchar(64)      default '' not null comment '设备id',
    type      tinyint unsigned default 0  not null comment '1 ios 2 华为 3 oppo 4 小米 5 vivo',
    rec_msg   int unsigned     default 0  not null comment '用户堆积消息'
)
    comment '用户推送配置表' collate = utf8mb4_unicode_ci;

create index idx_jid_token_type
    on `gvideo-uat`.consumer_user_push_config (jid, token, type);

create table if not exists `gvideo-uat`.consumer_user_push_msg
(
    id          bigint unsigned auto_increment comment '主键id'
        primary key,
    region      varchar(16)      default ''                not null comment '1 ios 2 华为 3 oppo 4 小米 5 vivo',
    title       varchar(64)      default ''                not null comment '推送标题',
    content     varchar(64)      default ''                not null comment '推送内容',
    content_id  bigint           default 0                 not null comment '资源id',
    type        tinyint unsigned default 0                 not null comment '0 立即发送 1 定时发送',
    push_time   datetime         default CURRENT_TIMESTAMP not null comment '消息发送时间',
    status      tinyint          default 0                 not null comment '0 待发送 1 已发送',
    link_url    varchar(64)      default ''                not null,
    username    varchar(64)                                null,
    user_id     int                                        null,
    create_time datetime                                   null
)
    comment '消息推送表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.consumer_user_push_record
(
    id         bigint unsigned auto_increment comment '主键id'
        primary key,
    msg_id     bigint unsigned  default 0                 not null comment '消息id',
    jid        bigint unsigned  default 0                 not null comment '今视频用户id',
    token      varchar(64)      default ''                not null comment '推送token',
    device_id  varchar(64)      default ''                not null comment '设备id',
    request_id varchar(64)      default ''                not null comment '请求id',
    status     tinyint unsigned default 1                 not null comment '推送状态 0 失败 1 成功',
    push_time  datetime         default CURRENT_TIMESTAMP not null comment '推送日期'
)
    comment '消息推送日志表' collate = utf8mb4_unicode_ci;

create index idx_push_time
    on `gvideo-uat`.consumer_user_push_record (push_time);

create table if not exists `gvideo-uat`.content_list
(
    id             bigint auto_increment comment '主键id'
        primary key,
    name           varchar(128)                                not null comment '名称',
    parent         bigint            default 0                 not null comment '父级ID',
    folder         tinyint(2)        default 0                 not null comment '是否目录：0-否、1-是',
    limit_num      smallint unsigned default 0                 not null comment '限制条数',
    type           tinyint(2)        default 0                 not null comment '类型：0-手动、1-自动',
    sort           int unsigned      default 0                 not null comment '排序',
    remarks        varchar(256)      default ''                not null comment '备注',
    tenant_id      int                                         not null comment '租户id',
    create_user_id int                                         not null comment '创建人',
    update_user_id int                                         null comment '更新人',
    create_date    timestamp         default CURRENT_TIMESTAMP null comment '创建时间',
    update_date    timestamp         default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    status         tinyint(2)        default 1                 not null comment '状态：0-禁用、1-启用',
    del_flag       tinyint(3)        default 0                 null comment '逻辑删除标记(0--正常 1--删除)'
)
    comment '内容列表' collate = utf8mb4_unicode_ci;

create index idx_parent
    on `gvideo-uat`.content_list (parent);

create index idx_tenant_id
    on `gvideo-uat`.content_list (tenant_id);

create table if not exists `gvideo-uat`.content_list_auto
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    list_id            bigint                  not null comment '内容列表ID',
    content_type       varchar(128) default '' not null comment '资源类型, 参考前端频道管理',
    show_name          varchar(128) default '' not null comment '显示标题',
    content_type_label varchar(128) default '' not null comment '内容类型标签',
    internal_label     varchar(128) default '' not null comment '内部标签',
    other_label        varchar(128) default '' not null comment '其他类型标签',
    constraint idx_list_id
        unique (list_id)
)
    comment '内容列表自动规则表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.content_list_resource
(
    id             bigint auto_increment comment '主键id'
        primary key,
    list_id        bigint                                 not null comment '内容列表ID',
    resource_id    int(20)                                not null comment '资源ID',
    content_type   int                                    not null comment '内容类型：1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云、12-文章、13-专题',
    name           varchar(128) default ''                not null comment '资源标题',
    cover          varchar(64)  default ''                not null comment '缩略图',
    stick_sort     int unsigned default 0                 not null comment '置顶顺序',
    lock_sort      int unsigned default 0                 not null comment '锁定顺序',
    sort           int unsigned default 0                 not null comment '顺序',
    create_user_id int                                    not null comment '创建人',
    update_user_id int                                    null comment '更新人',
    create_date    timestamp    default CURRENT_TIMESTAMP null comment '创建时间',
    update_date    timestamp    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '内容列表资源关联表' collate = utf8mb4_unicode_ci;

create index idx_list_id
    on `gvideo-uat`.content_list_resource (list_id);

create index idx_lock
    on `gvideo-uat`.content_list_resource (lock_sort);

create index idx_resource_id
    on `gvideo-uat`.content_list_resource (resource_id);

create index idx_sort
    on `gvideo-uat`.content_list_resource (sort);

create index idx_stick
    on `gvideo-uat`.content_list_resource (stick_sort);

create table if not exists `gvideo-uat`.feedback_question
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    title       varchar(256)                         null comment '问题标题',
    father      int(10)                              null comment '父ID',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '反馈帮助问题表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.feedback_question_detail
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    question_id int(10)                              null comment '问题ID',
    type        tinyint(1) default 1                 null comment '1文字类型或2图片类型',
    content     varchar(8000)                        null comment '内容',
    is_bold     tinyint(1) default 0                 null comment '1文字加粗0文字不加粗',
    height      smallint(3)                          null comment '图片的高度',
    sort        tinyint(1)                           null comment '排序',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '反馈帮助问题详情表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.flyway_schema_history
(
    installed_rank int                                 not null
        primary key,
    version        varchar(50)                         null,
    description    varchar(200)                        not null,
    type           varchar(20)                         not null,
    script         varchar(1000)                       not null,
    checksum       int                                 null,
    installed_by   varchar(100)                        not null,
    installed_on   timestamp default CURRENT_TIMESTAMP not null,
    execution_time int                                 not null,
    success        tinyint(1)                          not null
);

create index flyway_schema_history_s_idx
    on `gvideo-uat`.flyway_schema_history (success);

create table if not exists `gvideo-uat`.group_apply_audit
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    group_id           bigint                                 not null comment '圈子id',
    mobile             varchar(11)                            not null comment '申请人手机',
    jid                bigint                                 not null comment 'jid',
    nickname           varchar(255) default ''                not null comment '申请人昵称',
    create_date        datetime     default CURRENT_TIMESTAMP null comment '申请时间',
    audit_date         datetime                               null comment '审核时间',
    group_audit_status tinyint      default 1                 null comment '审核状态 1 待审核 2 通过 3 拒绝',
    audit_user_id      int                                    null comment '审核人id',
    apply_reason       varchar(256)                           null comment '申请理由'
)
    comment '圈子审核表' collate = utf8mb4_unicode_ci;

create index idx_group_id
    on `gvideo-uat`.group_apply_audit (group_id);

create table if not exists `gvideo-uat`.group_blacklist
(
    id            bigint auto_increment comment '主键id'
        primary key,
    group_id      bigint                                 not null comment '圈子id',
    jid           bigint                                 not null comment '用户jid',
    type          tinyint      default 1                 not null comment '1拉黑2禁言',
    mute_end_date datetime                               null comment '禁言结束时间',
    mobile        varchar(11)  default ''                not null comment '联系电话',
    create_date   datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    reason        varchar(256) default ''                not null
)
    comment '圈子黑名单表' collate = utf8mb4_unicode_ci;

create index idx_group_id
    on `gvideo-uat`.group_blacklist (group_id);

create table if not exists `gvideo-uat`.group_info
(
    id           bigint auto_increment comment '主键id'
        primary key,
    cover        varchar(64)      default '' not null comment '封面',
    introduction varchar(512)     default '' not null comment '简介',
    owner_mobile varchar(11)      default '' not null comment '圈主手机',
    status       tinyint unsigned default 1  null comment '圈子状态 1启用2禁用',
    tenant_id    int              default 0  not null comment '管理租户id',
    tenant_name  varchar(64)      default '' not null comment '管理租户名称',
    weight       tinyint unsigned default 0  not null comment '权重',
    name         varchar(32)      default '' not null comment '圈子名称',
    code         varchar(32)                 null comment '编码',
    apply_mode   tinyint          default 1  not null comment '加入条件 1 无需审核 2 审核通过可加入'
)
    comment '圈子基本信息表' collate = utf8mb4_unicode_ci;

create index idx_tenant_id
    on `gvideo-uat`.group_info (tenant_id);

create table if not exists `gvideo-uat`.group_manage
(
    id          bigint auto_increment comment '主键id'
        primary key,
    group_id    bigint                                not null comment '圈子id',
    name        varchar(255)                          null comment '管理员名称',
    user_id     bigint                                null comment '用户jid或当role_type=4时为sys_user_id',
    mobile      varchar(11) default ''                not null comment '联系电话',
    role_type   tinyint     default 1                 not null comment '角色类型 1 普通用户 2 管理员 3 圈主 4 超级管理员',
    create_date datetime    default CURRENT_TIMESTAMP null comment '创建时间',
    tenant_id   int         default 0                 not null,
    constraint idx_group_id
        unique (group_id, user_id)
)
    comment '圈子管理员关联表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.group_option_log
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    log_content_type   tinyint unsigned default 0                 not null comment '变更内容类型 1 基本信息 2 话题管理 3 内容管理 4 评论管理 5 圈子人员管理 6 入圈审核管理',
    change_field       tinyint unsigned default 0                 not null comment '变更字段',
    group_id           bigint           default 0                 not null comment '圈子id',
    before_value       varchar(512)                               null comment '变更前',
    after_value        varchar(512)                               null comment '变更后',
    create_date        datetime         default CURRENT_TIMESTAMP null comment '操作时间',
    create_user_id     bigint                                     not null comment '操作人id',
    create_user_name   varchar(32)                                not null comment '操作人',
    role_type          tinyint                                    not null comment '角色类型 1 普通用户 2 管理员 3 圈主 4 超级管理员',
    create_tenant_name varchar(32)                                not null comment '租户名称',
    subject            varchar(64)      default ''                not null comment '主体'
)
    comment '圈子操作日志' collate = utf8mb4_unicode_ci;

create index idx_group_id
    on `gvideo-uat`.group_option_log (group_id);

create table if not exists `gvideo-uat`.group_topic
(
    id               bigint auto_increment comment '主键id'
        primary key,
    group_id         bigint                                 not null comment '圈子id',
    code             varchar(32)                            null comment '编码',
    content          varchar(512) default ''                not null comment '内容',
    weight           tinyint      default 0                 null comment '权重',
    status           tinyint      default 1                 null comment '状态1启用2禁用',
    create_date      datetime     default CURRENT_TIMESTAMP null comment '创建时间',
    create_user_id   bigint                                 not null comment '创建人id、jid',
    create_user_name varchar(64)                            null comment '创建人昵称',
    admin_create     tinyint(1)   default 1                 null comment '后台用户创建',
    constraint uniq_code
        unique (code)
)
    comment '话题表' collate = utf8mb4_unicode_ci;

create index idx_group_id
    on `gvideo-uat`.group_topic (group_id);

create table if not exists `gvideo-uat`.group_topic_content
(
    id                 bigint auto_increment comment '主键id'
        primary key,
    group_id           bigint            not null comment '圈子id',
    topic_id           bigint  default 0 not null comment '话题id',
    media_id           bigint            not null comment '内容id',
    topic_media_status tinyint default 1 not null comment '内容状态 1 启用 2 禁用 3 置顶 4 精华'
)
    comment '圈子话题动态关联表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.group_topic_label
(
    id       bigint auto_increment comment '主键id'
        primary key,
    group_id bigint default 0 not null comment '圈子id',
    label    int              not null comment '标签id',
    topic_id bigint default 0 not null comment '话题id'
)
    comment '圈子标签关联表' collate = utf8mb4_unicode_ci;

create index idx_group_id
    on `gvideo-uat`.group_topic_label (group_id);

create index idx_label_id
    on `gvideo-uat`.group_topic_label (label);

create index idx_topic_id
    on `gvideo-uat`.group_topic_label (topic_id);

create table if not exists `gvideo-uat`.interact_favorite
(
    id            bigint auto_increment comment '自增主键'
        primary key,
    jid           bigint     default -1                not null comment '用户id',
    media_id      bigint                               not null comment '媒体id',
    media_type    tinyint(1) default -1                not null comment '媒体类型',
    media_status  tinyint(1) default 1                 null comment '资源状态1可用0不可用',
    author_status tinyint(1) default 1                 null comment '作者状态1启用0禁用',
    create_date   timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    update_date   timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag      tinyint(1) default 0                 not null comment '逻辑删除标记'
)
    comment '我的收藏表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.interact_follow
(
    id            bigint auto_increment comment '关注表id'
        primary key,
    jid           bigint     default -1                not null comment '用户id',
    author_id     bigint                               not null comment '作者ID',
    type          tinyint(1) default 0                 null comment '作者类型，0pgc1ugc',
    author_status tinyint(1) default 1                 null comment '作者状态1启用0禁用',
    create_date   timestamp  default CURRENT_TIMESTAMP not null comment '创建时间',
    update_date   timestamp  default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag      tinyint(1) default 0                 not null comment '逻辑删除标记'
)
    comment '我的关注表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.interact_follow_news
(
    jid         bigint    default -1                not null comment '用户id'
        primary key,
    newest_date timestamp default CURRENT_TIMESTAMP not null comment '最新时间'
)
    comment '我的动态更新时间记录表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.interactive_broadcast
(
    id                   bigint unsigned auto_increment comment '主键id'
        primary key,
    status               tinyint unsigned default 0                 not null comment '0 未开始 1 正在直播 2 结束 3 下架',
    jid                  bigint unsigned                            not null comment '开播用户jid',
    sys_user_id          bigint unsigned                            not null comment '所属用户id',
    tenant_id            bigint unsigned                            not null comment '所属租户id',
    title                varchar(64)                                null comment '直播标题',
    description          varchar(200)                               null comment '直播描述',
    thumb                varchar(200)                               null comment '直播封面',
    click                int unsigned     default 0                 not null comment '点击次数',
    view                 int unsigned     default 0                 not null comment '访问次数',
    comment_count        int unsigned     default 0                 not null comment '评论数量',
    comment_people_count int unsigned     default 0                 not null comment '评论人数',
    created_date         datetime         default CURRENT_TIMESTAMP not null comment '创建时间',
    end_date             datetime                                   null comment '结束时间',
    certification_id     int                                        null comment '认证号id',
    media_id             bigint           default 0                 not null,
    live_url             varchar(256)     default ''                null,
    digg                 bigint           default 0                 not null,
    del_flag             tinyint          default 0                 not null comment '删除标记'
)
    comment '互动直播表' collate = utf8mb4_unicode_ci;

create index idx_create_date
    on `gvideo-uat`.interactive_broadcast (created_date);

create index idx_jid
    on `gvideo-uat`.interactive_broadcast (jid);

create index idx_sys_user_id
    on `gvideo-uat`.interactive_broadcast (sys_user_id);

create index idx_tenant_id
    on `gvideo-uat`.interactive_broadcast (tenant_id);

create table if not exists `gvideo-uat`.interactive_broadcast_ban
(
    id          bigint unsigned auto_increment comment '主键id'
        primary key,
    sys_user_id bigint unsigned                    not null comment '用户id',
    create_time datetime default CURRENT_TIMESTAMP not null
)
    comment '直播黑名单表' collate = utf8mb4_unicode_ci;

create index idx_broadcast_id
    on `gvideo-uat`.interactive_broadcast_ban (sys_user_id);

create table if not exists `gvideo-uat`.interactive_broadcast_goods
(
    id           bigint unsigned auto_increment comment '主键id'
        primary key,
    goods_id     int unsigned    not null comment '商品id',
    broadcast_id bigint unsigned not null comment '直播id'
)
    comment '直播商品表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.interactive_broadcast_grant
(
    id          bigint unsigned auto_increment comment '主键id'
        primary key,
    tenant_id   bigint unsigned  default 0                 null comment '租户id',
    shop_grant  tinyint(3)       default 0                 null comment '商铺权限 0 无权限 1 有权限',
    del_flag    tinyint unsigned default 0                 not null comment '0 正常 1 删除',
    create_date datetime         default CURRENT_TIMESTAMP not null
)
    comment '直播授权表' collate = utf8mb4_unicode_ci;

create index idx_tenant_id
    on `gvideo-uat`.interactive_broadcast_grant (tenant_id);

create table if not exists `gvideo-uat`.interactive_broadcast_tenant_eshop
(
    id        bigint unsigned auto_increment comment '主键id'
        primary key,
    tenant_id bigint unsigned default 0 null comment '租户id',
    eshop_id  bigint unsigned default 0 null comment '电商商铺id'
)
    comment '直播租户电商关联表' collate = utf8mb4_unicode_ci;

create index idx_eshop_id
    on `gvideo-uat`.interactive_broadcast_tenant_eshop (eshop_id);

create index idx_tenant_id
    on `gvideo-uat`.interactive_broadcast_tenant_eshop (tenant_id);

create table if not exists `gvideo-uat`.label
(
    id          int(10) auto_increment comment '标签自增ID'
        primary key,
    name        varchar(32)                          null comment '标签名称',
    type_id     int(10)                              null comment '标签类型ID',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(3) default 0                 null comment '删除标记'
)
    comment '标签表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.label_type
(
    id          int(10) auto_increment comment '标签类型自增ID'
        primary key,
    name        varchar(32)                          null comment '标签类型名称',
    is_system   int(10)    default 0                 null comment '系统预置1还是用户新建0',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(3) default 0                 null comment '删除标记'
)
    comment '标签类型表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.live_broadcast
(
    id               bigint unsigned auto_increment comment '主键id'
        primary key,
    title            varchar(50)      default '' not null comment '标题',
    status           tinyint unsigned default 0  not null comment '直播状态 0 未开始 1 预告 2 直播中 3 回放 4 下架',
    type             tinyint unsigned default 0  not null comment '直播类型 1 横屏 2 竖屏',
    thumb            varchar(255)     default '' not null comment '封面图',
    recommend_status tinyint unsigned default 0  not null comment '推荐状态 0 默认 1 推荐 2 置顶',
    live_url         varchar(255)     default '' not null comment '直播流地址',
    live_url_bak     varchar(255)     default '' not null comment '直播流备用地址',
    live_url_status  tinyint unsigned default 0  not null comment '0 启用直播流地址 1 启用备用地址',
    created_date     datetime                    null comment '创建时间',
    updated_date     datetime                    null comment '更新时间',
    created_by       int                         null comment '创建人',
    created_name     varchar(255)                null comment '创建人名称',
    updated_by       int                         null comment '修改人',
    del_flag         tinyint          default 0  not null comment '删除标记 0 正常 1 删除',
    description      varchar(2000)               null comment '直播简介',
    updated_name     varchar(255)     default '' not null comment '修改人名称',
    certification_id int              default 0  not null,
    media_id         bigint                      null comment 'media_resource 主键',
    click            int              default 0  not null,
    share            int              default 0  not null,
    comment          varchar(16)      default '' not null,
    like_sum         int              default 0  not null
)
    comment '直播表' collate = utf8mb4_unicode_ci;

create index idx_created_by
    on `gvideo-uat`.live_broadcast (created_by);

create index idx_status
    on `gvideo-uat`.live_broadcast (status);

create table if not exists `gvideo-uat`.live_broadcast_ban
(
    id                bigint unsigned auto_increment comment '主键id'
        primary key,
    live_broadcast_id bigint unsigned            not null comment '直播id',
    consumer_user     bigint unsigned            not null comment '被禁言用户',
    sys_user          tinyint unsigned default 0 not null comment '0 普通用户 1 系统用户',
    constraint unq_broadcast_user
        unique (live_broadcast_id, consumer_user, sys_user)
)
    comment '直播禁言名单' collate = utf8mb4_unicode_ci;

create index idx_consumer_user
    on `gvideo-uat`.live_broadcast_ban (consumer_user);

create table if not exists `gvideo-uat`.live_broadcast_message
(
    id                bigint unsigned auto_increment comment '主键id'
        primary key,
    live_broadcast_id bigint unsigned                            not null comment '直播id',
    user_id           bigint unsigned  default 0                 not null comment '用户id',
    user_name         varchar(255)     default ''                not null comment '用户名称',
    created_date      datetime         default CURRENT_TIMESTAMP not null comment '留言时间',
    message           varchar(255)     default ''                not null comment '留言内容',
    status            tinyint unsigned default 0                 not null comment '留言状态 0 待审核 1 已通过 2 已删除',
    top               tinyint unsigned default 0                 not null comment '是否置顶 0 正常 1 置顶',
    publish_time      datetime                                   null,
    images            varchar(255)     default ''                not null,
    `system`          tinyint          default 1                 null,
    links             varchar(255)     default ''                not null
)
    comment '直播留言板' collate = utf8mb4_unicode_ci;

create index idx_consumer_user
    on `gvideo-uat`.live_broadcast_message (user_id);

create index idx_live_broadcast_id
    on `gvideo-uat`.live_broadcast_message (live_broadcast_id);

create index idx_status
    on `gvideo-uat`.live_broadcast_message (status);

create table if not exists `gvideo-uat`.main_data
(
    id             int(20) auto_increment comment '自增ID'
        primary key,
    program_type   varchar(10) default '' not null comment '资源类型',
    free           int                    not null comment '付费状态',
    name           varchar(128)           not null comment '资源名称',
    description    varchar(1000)          not null comment '资源简介',
    actor          varchar(100)           not null comment '主演',
    director       varchar(30)            not null comment '导演',
    country        varchar(10)            not null comment '地区',
    release_year   int         default -1 not null comment '上映年份',
    language       varchar(10)            not null comment '语言',
    series_count   int                    not null comment '总集数',
    score          decimal                not null comment '评分',
    copyright_date datetime               null comment '版权有效期',
    cover          varchar(100)           null comment '封面',
    hor_cover      varchar(100)           null comment '海报',
    program_type2  varchar(100)           null comment 'pptv标签',
    in_labels      varchar(100)           null comment '内部标签',
    out_labels     varchar(100)           null comment '外部标签',
    series_code    varchar(32)            not null comment '主数据pptv编号'
)
    comment '主数据' charset = utf8mb4;

create table if not exists `gvideo-uat`.map_location
(
    id      int(10) auto_increment comment 'ID'
        primary key,
    image   varchar(32) not null comment '展示图片',
    address varchar(32) not null comment '展示地址',
    url     varchar(32) not null comment '外链URL'
)
    comment '地图定位' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.material
(
    id                     int auto_increment comment '运营素材id'
        primary key,
    type                   int                                                        not null comment '运营素材类型',
    advertising            tinyint                                                    not null comment '广告标识',
    name                   varchar(30) collate utf8mb4_bin  default ''                not null comment '运营素材名称',
    title                  varchar(30) collate utf8mb4_bin  default ''                not null comment '展示标题',
    remark                 varchar(100) collate utf8mb4_bin default ''                not null comment '备注',
    child_id               int                                                        not null comment '子表id',
    corner_mark            int                              default -1                not null comment '角标',
    corner_mark_start_date datetime                                                   null comment '角标开始时间',
    corner_mark_end_date   datetime                                                   null comment '角标结束时间',
    period_start           datetime                                                   null comment '默认周期开始时间',
    period_end             datetime                                                   null comment '默认周期结束时间',
    create_user_id         int                                                        not null comment '创建人id',
    update_user_id         int                                                        null comment '更新人id',
    create_date            timestamp                        default CURRENT_TIMESTAMP not null comment '创建时间',
    status                 tinyint                          default 1                 not null comment '状态'
)
    comment '运营素材表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_audit_sync
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    media_id    int(10)                              null comment '资源ID',
    is_sync     tinyint(1) default 0                 null comment '同步标记1已同步0未同步',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '待审核同步的兜底表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_file
(
    id             int(20) auto_increment comment 'ID'
        primary key,
    file_type      int                                    not null comment '文件类型 1.视频 2.音频',
    limit_view     tinyint                                not null comment '是否限制租户查看',
    file_name      varchar(255) default ''                not null comment '文件名',
    create_user_id int                                    not null comment '上传人id',
    resource_id    int          default -1                not null comment '关联资源id',
    oss_id         varchar(45)                            null comment '阿里云ID',
    status         int          default 1                 not null comment '状态 1.待关联 2.已关联 3.已删除',
    create_date    timestamp    default CURRENT_TIMESTAMP not null comment '上传时间'
)
    comment '影音文件表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_full_text
(
    media_id bigint auto_increment
        primary key,
    content  text null comment '内容'
)
    comment '语音文字表';

create table if not exists `gvideo-uat`.media_resource
(
    id                     int(20) auto_increment comment '自增ID'
        primary key,
    file_id                int(20)                                null comment '关联文件ID',
    content_type           int                                    not null comment '内容类型：1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云、12-文章、13-专题',
    play_style             int          default 2                 not null comment '播放样式',
    show_name              varchar(256) default ''                not null comment '显示标题',
    internal_name          varchar(256)                           null comment '内部标题',
    introduction           varchar(1000)                          null comment '简介',
    effect_start_date      timestamp                              null comment '生效开始时间',
    effect_end_date        timestamp                              null comment '生效结束时间',
    release_id             bigint                                 not null comment '发布账号id（认证号）',
    release_type           tinyint(1)   default 0                 null comment '发布账号类型0pgc1ugc2pptv',
    column_id              int                                    null comment '栏目id',
    source_id              int(10)      default 0                 not null comment '来源ID',
    copyright              tinyint(2)   default 0                 not null comment '是否原创：0-否、1-是',
    virtual_pv             int(10)      default 0                 not null comment '虚拟点击数',
    pv                     int(10)      default 0                 not null comment '点击数',
    weight                 smallint(3)  default 0                 not null comment '权重',
    tenant_id              int                                    not null comment '租户id',
    create_user_id         int                                    not null comment '创建人id',
    update_user_id         int                                    null comment '更新人id',
    can_comment            tinyint                                not null comment '可评论',
    can_search             tinyint                                not null comment '可搜索',
    can_update             tinyint      default 0                 not null comment '是否有更新',
    corner_mark            int                                    null comment '角标',
    corner_mark_start_date datetime                               null comment '角标开始时间',
    corner_mark_end_date   datetime                               null comment '角标结束时间',
    status                 int          default 1                 not null comment '媒体资源状态 1 待修改 2 待提交 3 启用 4 禁用 5 审核中 6 已废弃',
    create_date            timestamp    default CURRENT_TIMESTAMP not null comment '创建时间',
    update_date            timestamp                              null comment '更新时间',
    discard_date           timestamp                              null comment '废弃时间',
    submit_date            timestamp                              null comment '提交审核时间',
    pass_date              timestamp                              null comment '审核通过时间',
    repair_date            timestamp                              null comment '审核退修时间',
    enable_date            timestamp                              null comment '启用时间',
    disable_date           timestamp                              null comment '禁用时间',
    content_type_label     varchar(100) default ''                not null comment '内容类型标签',
    internal_label         varchar(100) default ''                not null comment '内部标签',
    other_label            varchar(100) default ''                not null comment '其他类型标签',
    other_label_type       varchar(100) default ''                not null comment '其他标签类型',
    length                 varchar(10)                            null comment '音视频长度/文章字数',
    pptv_main_data_id      bigint                                 null comment 'pptv主数据ID',
    pptv_program_code      bigint                                 null comment 'pptvID',
    pptv_series_num        bigint                                 null comment 'pptv节目集数',
    pptv_cover             varchar(2000)                          null comment 'pptv封面/文章缩略图',
    content                mediumtext                             null comment '内容'
)
    comment '音视频资源表' collate = utf8mb4_unicode_ci;

create index idx_release
    on `gvideo-uat`.media_resource (release_id);

create table if not exists `gvideo-uat`.media_resource_image
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    type        int                     not null comment '图片类型 -1 全局通用 1 搜索、收藏 2 推荐流 3 推荐位 4 顶图',
    resource_id int                     not null comment '资源ID',
    oss_id      varchar(100) default '' not null comment '阿里云图片ID'
)
    comment '音视频封面图表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_resource_label
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    resource_id int(20) not null comment '资源ID',
    type        int     not null comment '标签类型',
    label_id    int     not null comment '标签ID'
)
    comment '音视频标签表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_resource_pendant
(
    id                int auto_increment comment '挂件id'
        primary key,
    resource_id       int      not null comment '所属资源id',
    trigger_condition int      not null comment '触发条件',
    resource_type     int      not null comment '内容类型 1.视频 2.音频 3.素材',
    content_id        int      not null comment '关联内容id',
    start_date        datetime null comment '开始时间',
    end_date          datetime null comment '结束时间'
)
    comment '资源浮层挂件表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_resource_recommend
(
    id                  int auto_increment comment '推荐id'
        primary key,
    resource_id         int      not null comment '所属资源id',
    location            int      not null comment '推荐位置',
    type                int      null comment '推荐内容类型 1.资源 2.素材',
    content_id          int      null comment '推荐内容id',
    validity_start_date datetime null comment '有效开始时间',
    validity_end_date   datetime null comment '有效结束时间'
)
    comment '资源推荐表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_status_task
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    content_id  int(10)                              null comment '资源ID或PGCID',
    status      tinyint(1)                           null comment '目标状态',
    type        tinyint(1)                           null comment '类型1资源2PGC',
    is_sync     tinyint(1) default 0                 null comment '同步标记1已同步0未同步',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '资源关联用户认证号任务' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.media_voice_content
(
    uuid    varchar(64)              not null primary key,
    content varchar(2000) default '' not null comment '文字内容',
    length  varchar(10)   default '' not null comment '长度',
    constraint media_voice_content_id_uindex
        unique (uuid)
)
    comment '语音文字表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.message
(
    id              bigint auto_increment comment '消息主键ID'
        primary key,
    title           varchar(64)                          null comment '站内信标题',
    intro           varchar(255)                         null comment '站内信简介',
    detail          varchar(512)                         null comment '站内信详情',
    status          tinyint(1) default 0                 null comment '5种状态，0未发布1已发布2待发布-1已撤回-2已删除',
    type            tinyint(1)                           null comment '站内信模版类型，1文本2按钮3图片',
    extra           varchar(32)                          null comment '按钮文字或图片OSSID',
    outer_url       varchar(255)                         null comment '内外链跳转地址',
    from_type       tinyint(1)                           null comment '发送人类型，0系统消息1认证号消息',
    from_id         bigint     default 0                 null comment '发送人ID，认证号用',
    from_name       varchar(32)                          null comment '发送人名称',
    from_status     tinyint(1) default 1                 null comment '作者状态1启用0禁用',
    to_type         tinyint(1)                           null comment '接收人类型，0全体1粉丝2指定用户',
    to_ids          varchar(8000)                        null comment '接收人ID，指定用户时使用',
    admin_user_id   int(10)                              null comment '管理员ID',
    admin_user_name varchar(32)                          null comment '管理员用户名',
    create_date     timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date     timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    execute_type    tinyint(1)                           null comment '执行类型，1立即2定时',
    execute_date    timestamp                            null comment '执行时间',
    del_flag        tinyint(1) default 0                 null comment '逻辑删除标记，1删除0正常'
)
    comment '站内信表' collate = utf8mb4_unicode_ci;

create index IDX_CRT
    on `gvideo-uat`.message (create_date)
    comment '创建时间索引';

create index IDX_EXT
    on `gvideo-uat`.message (execute_date)
    comment '执行时间索引';

create table if not exists `gvideo-uat`.message_user
(
    id          bigint auto_increment comment '自增ID'
        primary key,
    jid         bigint                               null comment '用户今视频ID',
    msg_id      bigint                               null comment '消息主键ID',
    from_id     bigint                               null comment '冗余字段方便查询',
    from_status tinyint(1) default 1                 null comment '作者状态1启用0禁用',
    is_read     tinyint(1) default 0                 null comment '是否已读，1已读0未读',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记，1删除0正常',
    constraint IDX_JID_MSGID
        unique (jid, msg_id) comment '用户ID和消息ID唯一索引'
)
    comment '站内信用户关系表' collate = utf8mb4_unicode_ci;

create index IDX_JID_CRD
    on `gvideo-uat`.message_user (jid, create_date)
    comment '用户ID和创建时间联合索引';

create table if not exists `gvideo-uat`.open_advert
(
    id           int(10) auto_increment comment 'ID'
        primary key,
    advert_type  int                    not null comment '广告类型',
    content_form int                    not null comment '内容形式',
    media_file   varchar(50) default '' not null comment '媒体文件',
    jump         int         default 0  null comment '是否跳转',
    jump_url     varchar(32) default '' not null comment '跳转URL'
)
    comment '开屏广告' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.operation_link
(
    id       int(10) auto_increment comment 'ID'
        primary key,
    image    varchar(32) not null comment '展示图片',
    platform int         not null comment '三方平台',
    url      varchar(32) not null comment '外链URL'
)
    comment '运营外链' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.oss_file
(
    uuid        varchar(32)          not null comment '文件编码UUID'
        primary key,
    name        varchar(255)         null comment 'oss-name',
    bucket      varchar(255)         null comment 'oss-bucket',
    endpoint    varchar(255)         null comment 'oss-region',
    biz_type    smallint             null comment '业务类型',
    upload      tinyint(1) default 0 null comment '是否已经上传',
    expire_date timestamp            null comment '过期时间',
    del_flag    tinyint(1) default 0 null comment '逻辑删除标记'
)
    comment '静态存储文件表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.plan_resource
(
    id                     int(20) auto_increment comment '自增ID'
        primary key,
    plan_id                int                                    not null comment '计划ID',
    resource_id            int                                    not null comment '资源ID',
    resource_internal_name varchar(30) charset utf8mb4 default '' not null comment '资源内部标题',
    resource_content_type  int                                    not null comment '资源内容类型',
    resource_play_style    int                                    not null comment '资源播放类型',
    resource_status        int                                    not null comment '资源状态',
    current_weight         int                         default 0  not null comment '当前权重',
    weight                 int                                    not null comment '权重',
    user_id                int                                    not null comment '处理人id',
    add_date               datetime                               not null comment '添加时间',
    start_date             datetime                               null comment '实际开始时间',
    end_date               datetime                               null comment '实际结束时间',
    status                 int                         default 1  not null comment '状态 进行中1 已结束2'
)
    comment '投放计划资源' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_banner
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    plan_id     int(10)                              null comment '关联计划ID',
    title       varchar(32)                          null comment '计划名称',
    url         varchar(64)                          null comment '关联资源链接/路径',
    corner      varchar(32)                          null comment '角标',
    cover       varchar(32)                          null comment '封面',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_banner_log
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    plan_id     int(10)                             not null comment 'planID',
    content     varchar(100)                        not null comment '操作纪录',
    operator_id int(10)                             not null comment '操作人ID',
    create_date timestamp default CURRENT_TIMESTAMP not null comment '操作时间'
)
    comment '横幅计划操作日志' charset = utf8mb4;

create table if not exists `gvideo-uat`.pptv_banner_plan
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    name        varchar(32)                         null comment '计划名称',
    status      tinyint(1)                          null comment '计划状态',
    start_date  datetime                            null comment '生效起始时间',
    end_date    datetime                            null comment '生效结束时间',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_group_count
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    group_id    int(10)                             not null comment '板块ID',
    count       int(10)                             null comment '板块资源数量',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_group_log
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    plan_id     int(10)                             not null comment 'planID',
    content     varchar(100)                        not null comment '操作纪录',
    operator_id int(10)                             not null comment '操作人ID',
    create_date timestamp default CURRENT_TIMESTAMP not null comment '操作时间'
)
    comment '板块计划操作日志' charset = utf8mb4;

create table if not exists `gvideo-uat`.pptv_group_plan
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    name        varchar(32)                         null comment '板块名称',
    status      tinyint(1)                          null comment '板块状态',
    weight      tinyint(1)                          null comment '板块权重',
    type        tinyint(1)                          null comment '策略类型:1条件筛选2人工筛选',
    max         int(10)                             null comment '条件筛选最大值',
    content     varchar(255)                        null comment '策略内容',
    start_date  datetime                            null comment '生效起始时间',
    end_date    datetime                            null comment '生效结束时间',
    create_date timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_label_log
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    plan_id     int(10)                             not null comment 'planID',
    content     varchar(100)                        not null comment '操作纪录',
    operator_id int(10)                             not null comment '操作人ID',
    create_date timestamp default CURRENT_TIMESTAMP not null comment '操作时间'
)
    comment '标签计划操作日志' charset = utf8mb4;

create table if not exists `gvideo-uat`.pptv_label_plan
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    name        varchar(32)                         null comment '计划名称',
    status      tinyint(1)                          null comment '计划状态',
    weight      tinyint(1)                          null comment '计划权重',
    content     varchar(255)                        null comment '策略内容',
    start_date  datetime                            null comment '生效起始时间',
    end_date    datetime                            null comment '生效结束时间',
    create_date timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_program
(
    id                  int(20) auto_increment comment '自增ID'
        primary key,
    serial_id           int(20)                 not null comment '所属剧集ID',
    program_code        char(9)      default '' not null comment '影片唯一Id ，节目ID，一个Series 可以包含1 个或多个Program。',
    content_type        int          default -1 not null comment '内容类型，0：正片、1：预告、2：花絮、3：资讯、4：看点',
    program_set_tiltle  varchar(50)  default '' not null comment '影片集数标题',
    sub_hor_cover       varchar(100) default '' not null comment '剧集每个分集对应横版截图',
    series_num          int          default -1 not null comment '集数序号，标识第几集',
    content_online_time datetime                not null comment '节目更新时间。比如电视剧单集的更新时间',
    duration            char(10)     default '' not null comment '时长'
)
    comment 'pptv影片' charset = utf8mb4;

create table if not exists `gvideo-uat`.pptv_query_config
(
    id          int(10) auto_increment comment '自增主键ID'
        primary key,
    value       varchar(32)                         null comment '规则条件名称',
    query       varchar(32)                         null comment '规则检索字段',
    create_date timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.pptv_serial_info
(
    id            int(20) auto_increment comment '自增ID'
        primary key,
    name          varchar(50)   default '' not null comment '影片名称',
    cover         varchar(100)  default '' not null comment '影片海报（纵版），格式jpg，分辨率423*564',
    hor_cover     varchar(100)  default '' not null comment '影片截图（横版），格式jpg，分辨率720*540',
    program_type  varchar(20)   default '' not null comment '影片大类型，比如说：电影、电视剧、综艺、动漫等。媒资一级分类',
    program_type2 varchar(20)   default '' not null comment '影片类型，比如说：爱情、喜剧、言情等。媒资二级分类',
    actor         varchar(100)  default '' not null comment '演员，如遇到多个，用/分开',
    director      varchar(100)  default '' not null comment '导演，如遇到多个，用/分开',
    country       varchar(20)   default '' not null comment '地区',
    release_year  int                      not null comment '上映年份',
    score         decimal                  not null comment '评分。媒资豆瓣评分',
    series_count  int                      not null comment '总集数，单部电影则为1，如果>1 则为Series（电视剧或音乐专辑等形式)',
    current_num   int                      not null comment '该节目集更新集数。单集，字段为1',
    language      char(5)       default '' not null comment '影片语言',
    update_time   datetime                 not null comment '媒资内容更新时间',
    description   varchar(1000) default '' not null,
    free          int                      not null comment '默认全部收费 0 免费；1 付费',
    work_state    int                      not null comment '内容的更新状态 0 更新； 1 已完结'
)
    comment 'pptv原始表' charset = utf8mb4;

create table if not exists `gvideo-uat`.preset_avatar
(
    id          varchar(32)                          not null comment '头像文件ID'
        primary key,
    url         varchar(255)                         null comment '头像地址',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '预设头像表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.seo_link
(
    id        int(10) auto_increment comment 'ID'
        primary key,
    image     varchar(32)                            not null comment '展示图片',
    url       varchar(64)                            not null comment '外链URL',
    sub_title varchar(15) charset utf8mb4 default '' not null comment '副标题'
)
    comment '推广外链' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.source
(
    id             int(10) auto_increment comment '主键id'
        primary key,
    name           varchar(128)                         not null comment '来源名称',
    create_user_id int                                  not null comment '创建人',
    update_user_id int                                  null comment '更新人',
    create_date    timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date    timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag       tinyint(3) default 0                 null comment '逻辑删除标记(0--正常 1--删除)'
)
    comment '来源表' collate = utf8mb4_unicode_ci;

create index idx_name
    on `gvideo-uat`.source (name);

create table if not exists `gvideo-uat`.special_tag
(
    id             bigint auto_increment comment '主键id'
        primary key,
    resource_id    int(20)                             not null comment '专题ID',
    name           varchar(128)                        not null comment '栏目名称',
    create_user_id int                                 not null comment '创建人',
    update_user_id int                                 null comment '更新人',
    create_date    timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date    timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '专题栏目表' collate = utf8mb4_unicode_ci;

create index idx_resource_id
    on `gvideo-uat`.special_tag (resource_id);

create table if not exists `gvideo-uat`.special_tag_resource
(
    id             bigint auto_increment comment '主键id'
        primary key,
    tag_id         bigint                                 not null comment '栏目ID',
    resource_id    int(20)                                not null comment '资源ID',
    sort           int unsigned default 0                 not null comment '顺序',
    create_user_id int                                    not null comment '创建人',
    update_user_id int                                    null comment '更新人',
    create_date    timestamp    default CURRENT_TIMESTAMP null comment '创建时间',
    update_date    timestamp    default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '专题栏目资源关联表' collate = utf8mb4_unicode_ci;

create index idx_resource_id
    on `gvideo-uat`.special_tag_resource (resource_id);

create index idx_sort
    on `gvideo-uat`.special_tag_resource (sort);

create index idx_tag_id
    on `gvideo-uat`.special_tag_resource (tag_id);

create table if not exists `gvideo-uat`.spread
(
    id          int(20) auto_increment comment '自增ID'
        primary key,
    throw_type  int                                 not null comment '投放类型',
    material_id int                                 not null comment '素材ID',
    start_date  datetime                            null comment '开始时间',
    end_date    datetime                            null comment '结束时间',
    status      int                                 not null comment '状态',
    exposure    int                                 not null comment '曝光总长',
    skip        int                                 not null comment '跳过时长',
    operator_id int                                 not null comment '操作人ID',
    create_date timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    delete_date timestamp                           null comment '删除时间'
)
    comment '开屏' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.spread_log
(
    id              int(20) auto_increment comment '自增ID'
        primary key,
    spread_id       int                                 not null comment '开屏计划ID',
    content         varchar(100) charset utf8mb4        not null comment '操作内容',
    operator_id     int                                 not null comment '操作人ID',
    login_tenant_id int                                 not null comment '登录租户ID',
    operate_date    timestamp default CURRENT_TIMESTAMP not null comment '操作时间'
)
    comment '开屏计划操作日志' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.statistic_app_log
(
    id           bigint auto_increment comment '主键ID'
        primary key,
    type         varchar(6)  null comment '动作',
    pid          varchar(20) null comment '页面位置',
    session_id   varchar(36) null comment 'session-UUID',
    timestamp    timestamp   null comment '事件时间',
    ev           varchar(20) null comment '事件名称',
    ds           json        null comment '具体统计信息',
    uid          bigint      null comment '用户ID',
    network_type varchar(6)  null comment '网络类型',
    carrier      varchar(10) null comment '载体',
    cid          varchar(64) null comment '设备号',
    app_name     varchar(25) null comment 'app名称',
    app_version  varchar(20) null comment 'app版本号',
    channel      varchar(20) null comment '频道',
    manufacturer varchar(20) null comment '制造商',
    model        varchar(64) null comment 'model',
    os           varchar(10) null comment '操作系统',
    os_version   varchar(10) null comment '操作系统版本'
)
    comment '埋点日志记录表' collate = utf8mb4_unicode_ci;

create index IDX_CID
    on `gvideo-uat`.statistic_app_log (cid)
    comment '设备ID索引';

create index IDX_UID
    on `gvideo-uat`.statistic_app_log (uid)
    comment '用户ID索引';

create table if not exists `gvideo-uat`.statistic_business
(
    id             int(10) auto_increment comment '主键ID'
        primary key,
    statistic_date timestamp                           null comment '日期-精确到天',
    clock          tinyint(1)                          null comment '小时0-23',
    type           tinyint(1)                          null comment '类型，1推荐曝光2推荐点击3开屏曝光4开屏点击',
    sum            int(10)                             null comment '数量',
    create_date    timestamp default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '商业化统计表' collate = utf8mb4_unicode_ci;

create index IDX_DATE_TYPE
    on `gvideo-uat`.statistic_business (statistic_date, type)
    comment '统计时间和类型联合索引';

create table if not exists `gvideo-uat`.statistic_finish
(
    id             bigint auto_increment comment '自增ID'
        primary key,
    statistic_date timestamp                           null comment '统计日期',
    type           tinyint(1)                          null comment '统计编号',
    is_finish      tinyint(1)                          null comment '是否完成',
    finish_date    timestamp default CURRENT_TIMESTAMP null comment '完成时间'
)
    comment '埋点统计状态表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.statistic_media
(
    id             int(10) auto_increment comment '主键ID'
        primary key,
    statistic_date timestamp                           null comment '日期-精确到天',
    clock          tinyint(1)                          null comment '小时0-23',
    media_id       int(10)                             null comment '音视频ID',
    content_type   tinyint(1)                          null comment '内容类型',
    type           tinyint(1)                          null comment '类型，1播放2收藏3转发4评论',
    sum            int(10)                             null comment '数量',
    create_date    timestamp default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '内容统计表' collate = utf8mb4_unicode_ci;

create index IDX_DATE_TYPE
    on `gvideo-uat`.statistic_media (statistic_date, type)
    comment '统计时间和类型联合索引';

create table if not exists `gvideo-uat`.statistic_produce
(
    id             int(10) auto_increment comment '主键ID'
        primary key,
    statistic_date timestamp                           null comment '日期-精确到天',
    clock          tinyint(1)                          null comment '小时0-23',
    pgc_id         int(10)                             null comment 'PGCID',
    media_type     tinyint(1)                          null comment '媒体类型1视频2音频5直播6图文7语音',
    type           tinyint(1)                          null comment '类型，1播放2收藏3转发4评论5曝光6生产',
    sum            int(10)                             null comment '数量',
    create_date    timestamp default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '生产者统计表' collate = utf8mb4_unicode_ci;

create index IDX_DATE_TYPE
    on `gvideo-uat`.statistic_produce (statistic_date, type)
    comment '统计时间和类型联合索引';

create table if not exists `gvideo-uat`.statistic_user
(
    id             int(10) auto_increment comment '主键ID'
        primary key,
    statistic_date timestamp                           null comment '日期-精确到天',
    clock          tinyint(1)                          null comment '小时0-23',
    type           tinyint(1)                          null comment '类型，1新增2新增注册3活跃4活跃注册',
    sum            int(10)                             null comment '数量',
    create_date    timestamp default CURRENT_TIMESTAMP null comment '创建时间'
)
    comment '用户统计表' collate = utf8mb4_unicode_ci;

create index IDX_DATE_TYPE
    on `gvideo-uat`.statistic_user (statistic_date, type)
    comment '统计时间和类型联合索引';

create table if not exists `gvideo-uat`.sys_config
(
    id    int(10) auto_increment comment '配置自增ID'
        primary key,
    name  varchar(32) not null comment '配置名称',
    code  int         not null comment '配置编号',
    intro varchar(32) not null comment '配置描述'
)
    comment '音视频配置表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_menu
(
    id          int                                   not null comment '菜单ID'
        primary key,
    name        varchar(32) collate utf8mb4_bin       not null comment '菜单名称',
    component   varchar(32) collate utf8mb4_bin       null comment '前端组件名称',
    permission  varchar(32) collate utf8mb4_bin       null comment '菜单权限标识',
    is_common   smallint(3) default 1                 null comment '公用标记，1是公用，0是父租户用',
    path        varchar(128) collate utf8mb4_bin      null comment '前端跳转URL',
    parent_id   int                                   not null comment '父菜单ID',
    sort        int         default 1                 null comment '排序',
    type        smallint(3)                           not null comment '菜单类型 （类型  -1：按钮、0：目录、1：菜单（页面）、2：按钮+页面）',
    create_date timestamp   default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp   default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    smallint(3) default 0                 null comment '逻辑删除标记(0--正常 1--删除)'
)
    comment '菜单权限表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_role
(
    id          int(10) auto_increment comment '自增id'
        primary key,
    tenant_id   int(10)                              null comment '自租户id',
    name        varchar(255)                         null comment '角色名称',
    note        varchar(255)                         null comment '角色描述',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    del_flag    tinyint(1) default 0                 null comment '逻辑删除标记'
)
    comment '系统角色表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_role_menu
(
    role_id int(10) not null comment '角色id',
    menu_id int(10) not null comment '资源id',
    constraint IDX_ROLE_MENU
        unique (role_id, menu_id)
)
    comment '系统角色资源表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_tenant
(
    id          int(10) auto_increment comment '自增id'
        primary key,
    name        varchar(255)                         null comment '租户名称',
    note        varchar(255)                         null comment '租户描述',
    status      tinyint(1) default 1                 null comment '租户状态:1启用-1禁用',
    is_system   tinyint(1) default 0                 not null comment '是否系统租户',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建日期',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新日期'
)
    comment '系统租户表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_tenant_user
(
    id          int(10) auto_increment comment '自增ID'
        primary key,
    user_id     int(10)                              not null comment '用户ID',
    tenant_id   int(10)                              not null comment '租户ID',
    is_primary  int(10)    default 0                 null comment '是否用户的首选租户',
    status      tinyint(1) default 1                 null comment '用户在对应租户的状态',
    del_flag    tinyint(1) default 0                 null comment '用户在租户下的删除标记',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '用户在租户下创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '用户信息更新时间'
)
    comment '租户用户关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_user
(
    id          int(10) auto_increment comment '自增id'
        primary key,
    username    varchar(20)                         null comment '手机号',
    password    varchar(256)                        null comment '密码',
    name        varchar(255)                        null comment '姓名',
    note        varchar(255)                        null comment '简介',
    create_date timestamp default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    constraint IDX_USERNAME
        unique (username)
)
    comment '系统用户表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.sys_user_role
(
    user_id int(10) not null comment '用户id',
    role_id int(10) not null comment '角色id',
    constraint IDX_USR_ROLE
        unique (user_id, role_id)
)
    comment '用户角色表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.t_user
(
    id          int auto_increment comment '??'
        primary key,
    user_name   varchar(255) default ''                not null comment '???',
    gender      tinyint      default 0                 not null comment '????',
    country_id  int          default 0                 not null comment '??id',
    birthday    date                                   null comment '??????',
    create_time timestamp    default CURRENT_TIMESTAMP null comment '??????'
)
    charset = utf8mb4;

create table if not exists `gvideo-uat`.tenant_media_file
(
    id            int(20) auto_increment comment '自增ID'
        primary key,
    tenant_id     int not null comment '租户id',
    media_file_id int not null comment '影音文件id'
)
    comment '租户音视频关系表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.test
(
    id   int auto_increment
        primary key,
    name varchar(11) null
);

create table if not exists `gvideo-uat`.third_log
(
    id           int(20) auto_increment comment '自增ID'
        primary key,
    third_id     int                                 not null comment '三方资源ID',
    content      varchar(100)                        not null comment '操作纪录',
    operator_id  int                                 not null comment '操作人ID',
    operate_date timestamp default CURRENT_TIMESTAMP not null comment '操作时间'
)
    comment '第三方资源操作日志' charset = utf8mb4;

create table if not exists `gvideo-uat`.vest
(
    id       bigint unsigned auto_increment comment '主键id'
        primary key,
    name     varchar(64)      default '' not null comment '马甲名称',
    enable   tinyint unsigned default 1  not null comment '马甲状态 0 禁用 1 启用',
    del_flag tinyint unsigned default 0  not null comment '删除标记 0 正常 1 删除',
    constraint uniq__name
        unique (name)
)
    comment '马甲管理' collate = utf8mb4_unicode_ci;

create index idx_del_flag
    on `gvideo-uat`.vest (del_flag);

create index idx_enable
    on `gvideo-uat`.vest (enable);

create table if not exists `gvideo-uat`.vod_file
(
    uuid      varchar(32)          not null comment '视频UUID'
        primary key,
    file_name varchar(128)         not null comment '文件名带后缀',
    upload    tinyint(1) default 0 null comment '上传状态，1成功0未上传，初始为0'
)
    comment 'VOD文件表' collate = utf8mb4_unicode_ci;

create table if not exists `gvideo-uat`.vod_ugc_file
(
    uuid        varchar(32)                          not null comment '用户视频UUID'
        primary key,
    file_name   varchar(128)                         not null comment '文件名带后缀',
    upload      tinyint(1) default 0                 null comment '上传状态，1成功0未上传，初始为0',
    create_date timestamp  default CURRENT_TIMESTAMP null comment '创建时间',
    update_date timestamp  default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment 'UGC的VOD文件表' collate = utf8mb4_unicode_ci;

