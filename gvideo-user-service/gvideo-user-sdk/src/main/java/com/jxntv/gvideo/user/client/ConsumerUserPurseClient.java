package com.jxntv.gvideo.user.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.dto.*;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc C端用户钱包
 */
public interface ConsumerUserPurseClient {


    /**
     * 查询C端用户钱包余额(app端)
     *
     * @param jid
     * @return
     */
    @GetMapping("/api/consumer/purse")
    Result<BigDecimal> getPurseByJid(@RequestParam(value = "jid", required = true) Long jid);

    /**
     * 查询C端用户钱包状态(app端)
     *
     * @param jid
     * @return
     */
    @GetMapping("/api/consumer/purse/status")
    Result<Boolean> getPurseStatusByJid(@RequestParam(value = "jid", required = true) Long jid);


    /**
     * 查询C端用户钱包明细(app端)
     *
     * @param jid
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/consumer/purse/record/list")
    Result<PageDTO<ConsumerUserPurseRecordDTO>> getPurseRecordList(
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 查询C端用户钱包信息(app端)
     * 合并钱包余额及钱包明细接口
     *
     * @param jid
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/consumer/purse/info")
    Result<ConsumerUserPurseInfoDTO> getPurseInfo(
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 查询C端用户钱包明细日志(app端)
     *
     * @param id
     * @return
     */
    @GetMapping("/api/consumer/purse/record/log/{id}")
    Result<ConsumerUserPurseRecordInfoDTO> getPurseRecordLog(@PathVariable(value = "id", required = false) Long id);

    /**
     * 查询C端用户钱包 提现已绑定账号(app端)
     *
     * @param jid
     * @return
     */
    @GetMapping("/api/consumer/purse/withdraw/account")
    Result<ConsumerUserPurseWithdrawAccountDTO> getWithdrawAccount(@RequestParam(value = "jid", required = false) Long jid);

    /**
     * 查询C端用户钱包 解绑提现账号(app端)
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw/account/add")
    Result<Boolean> saveWithdrawAccount(@RequestBody ConsumerPurseWithdrawAccountDTO dto);

    /**
     * 查询C端用户钱包 解绑提现账号(app端)
     *
     * @param jid
     * @return
     */
    @DeleteMapping("/api/consumer/purse/withdraw/account/remove")
    Result<Boolean> removeWithdrawAccount(@RequestParam(value = "jid", required = false) Long jid);


    /**
     * 给C端用户发放奖励
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/giveReward")
    Result<Boolean> giveReward(@RequestBody ConsumerGiveRewardDTO dto);

    /**
     * 保存C端用户提现申请信息（App端）
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw")
    Result<Boolean> saveWithdrawInfo(@RequestBody ConsumerPurseWithdrawDTO dto);


    /**
     * 查询C端用户提现申请列表(运营端)
     *
     * @param jid
     * @param nickname
     * @param mobile
     * @param minAmount
     * @param maxAmount
     * @param startDate
     * @param endDate
     * @param auditStatus
     * @param paymentStatus
     * @param partnerTradeNo
     * @param paymentNo
     * @param asc
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/consumer/purse/withdraw/page")
    Result<PageDTO<ConsumerUserPurseWithdrawDTO>> queryPurseWithdrawList(@RequestParam(value = "id", required = false) Long jid,
                                                                         @RequestParam(value = "nickname", required = false) String nickname,
                                                                         @RequestParam(value = "mobile", required = false) String mobile,
                                                                         @RequestParam(value = "minAmount", required = false) BigDecimal minAmount,
                                                                         @RequestParam(value = "maxAmount", required = false) BigDecimal maxAmount,
                                                                         @RequestParam(value = "startDate", required = false) Date startDate,
                                                                         @RequestParam(value = "endDate", required = false) Date endDate,
                                                                         @RequestParam(value = "auditStatus", required = false) Integer auditStatus,
                                                                         @RequestParam(value = "paymentStatus", required = false) Integer paymentStatus,
                                                                         @RequestParam(value = "partnerTradeNo", required = false) String partnerTradeNo,
                                                                         @RequestParam(value = "paymentNo", required = false) String paymentNo,
                                                                         @RequestParam(value = "orderBy", required = false) String orderBy,
                                                                         @RequestParam(value = "asc", required = false) boolean asc,
                                                                         @RequestParam(required = false, defaultValue = "1") long current,
                                                                         @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 保存提现申请审核信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw/saveAudit")
    Result<Boolean> saveWithdrawAuditInfo(@RequestBody ConsumerPurseWithdrawAuditDTO dto);

    /**
     * 保存人工转账信息
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw/saveTransferArtificial")
    Result<Boolean> saveTransferArtificial(@RequestBody ConsumerPurseTransferArtificialDTO dto);

    /**
     * 转账失败重试
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw/tryTransfer")
    Result<Boolean> tryTransfer(@RequestBody ConsumerPurseTransferTryDTO dto);

    /**
     * 查询C端用户钱包余额列表(运营端)
     *
     * @param jid
     * @param nickname
     * @param mobile
     * @param minBalance
     * @param maxBalance
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/consumer/purse/page")
    Result<PageDTO<ConsumerUserPurseDTO>> queryPurseList(@RequestParam(value = "jid", required = false) Long jid,
                                                         @RequestParam(value = "nickname", required = false) String nickname,
                                                         @RequestParam(value = "mobile", required = false) String mobile,
                                                         @RequestParam(value = "minBalance", required = false) BigDecimal minBalance,
                                                         @RequestParam(value = "maxBalance", required = false) BigDecimal maxBalance,
                                                         @RequestParam(required = false, defaultValue = "1") long current,
                                                         @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 询C端用户钱包余额变化明细(运营端)
     *
     * @param jid
     * @param minBalance
     * @param maxBalance
     * @param adjustStartDate
     * @param adjustEndDate
     * @param adjustMinAmount
     * @param adjustMaxAmount
     * @param adjustType
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/consumer/purse/record/page")
    Result<PageDTO<ConsumerUserPurseRecordOperationDTO>> queryPurseRecordList(
            @RequestParam(value = "jid", required = true) Long jid,
            @RequestParam(value = "minBalance", required = false) BigDecimal minBalance,
            @RequestParam(value = "maxBalance", required = false) BigDecimal maxBalance,
            @RequestParam(value = "adjustStartDate", required = false) Date adjustStartDate,
            @RequestParam(value = "adjustEndDate", required = false) Date adjustEndDate,
            @RequestParam(value = "adjustMinAmount", required = false) BigDecimal adjustMinAmount,
            @RequestParam(value = "adjustMaxAmount", required = false) BigDecimal adjustMaxAmount,
            @RequestParam(value = "adjustType", required = false) Integer adjustType,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 获取钱包记录爆料组件信息
     *
     * @param purseRecordId
     * @return
     */
    @GetMapping("/api/consumer/purse/record/brokeName")
    Result<ConsumerPurseBrokeDTO> getBrokeNameByPurseRecordId(@RequestParam Long purseRecordId);

    /**
     * 查询钱包提现金额
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/consumer/purse/withdraw/amount")
    Result<BigDecimal> queryWithdrawAmount(@RequestBody ConsumerPurseWithdrawAmountStatisticsDTO dto);
}
