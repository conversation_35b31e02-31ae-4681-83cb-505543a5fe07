package com.jxntv.gvideo.user.client.enums;

/**
 *  C端用户钱包明细变动状态
 * <AUTHOR>
 */

public enum ConsumerUserPurseRecordStatus {

    ANSWER(10, "回答提问","回答"),
    GIVE_REWARD(11, "发放奖励","回答"),
    WITHDRAW_APPLY(21, "已申请提现","回答"),
    WITHDRAW_AUDIT_PASS(22, "提现申请通过","已同意申请"),
    WITHDRAW_AUDIT_REFUSE(23, "提现申请被拒绝","已拒绝申请"),
    WITHDRAW_TRANSFER_SUCCESS(24, "提现已到账","已到账"),
    WITHDRAW_TRANSFER_FAILED(25, "提现到账失败","已同意申请"),
    WITHDRAW_TRANSFER_ARTIFICIAL(26, "提现已到账","已到账"),
    BROKE_THE_NEWS(30, "爆料","爆料"),
    BROKE_THE_NEWS_REWARD(31, "发放奖励","爆料发放奖励"),
    BROKE_THE_NEWS_SERIES_REWARD(32, "发放奖励","爆料追加奖励"),
    WITHDRAW_WAIT_PAY(27, "提现转账待确认","已同意申请"),
    NEW_YEAR_LOTTERY(40, "发放奖励","新春红包"),

    ;

    private int code;
    private String desc;
    private String sign;

    ConsumerUserPurseRecordStatus(int code, String desc, String sign) {
        this.code = code;
        this.desc = desc;
        this.sign = sign;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (ConsumerUserPurseRecordStatus value : values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }

    public static String sign(Integer code) {
        if (code == null) {
            return "";
        }
        for (ConsumerUserPurseRecordStatus value : values()) {
            if (value.getCode() == code) {
                return value.getSign();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }
}
