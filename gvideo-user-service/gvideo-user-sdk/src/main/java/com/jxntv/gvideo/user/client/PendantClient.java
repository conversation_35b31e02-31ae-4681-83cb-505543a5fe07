package com.jxntv.gvideo.user.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.dto.pendant.*;
import com.jxntv.gvideo.user.client.fallback.PendantClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 圈子-都是放心爱-访客Client
 *
 * <AUTHOR>
 * @date 2022-06-08 11:39:48
 */
@FeignClient(name = "user-service", contextId = "user-pendant", fallbackFactory = PendantClientFallback.class)
public interface PendantClient {

    String PREFIX = "/api/user/pendant";

    /**
     * 查询用户佩戴的挂件信息
     *
     * @param dto
     * @return
     */
    @GetMapping(PREFIX + "/wear/info")
    Result<PendantWearInfoDTO> queryWearInfo(@RequestParam(required =false) Long jid, @RequestParam(required = false) Integer type);


    /**
     * 插件列表
     *
     * @param searchDTO
     * @return
     */
    @PostMapping(PREFIX + "/page")
    Result<PageDTO<PendantDTO>> page(@RequestBody PendantSearchDTO searchDTO);


    /**
     * 插件详情
     *
     * @param id
     * @return
     */
    @GetMapping(PREFIX + "/info/{id}")
    Result<PendantDTO> getInfoById(@PathVariable Long id);

    /**
     * 新增插件信息
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "/info")
    Result<Long> addPendant(@RequestBody PendantInsertDTO dto);

    /**
     * 修改插件信息
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX + "/info")
    Result<Boolean> updatePendant(@RequestBody PendantUpdateDTO dto);


    /**
     * 分页查询发放列表
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "/grant/page")
    Result<PageDTO<PendantGrantDTO>> pageGrant(@RequestBody PendantGrantSearchDTO searchDTO);

    /**
     * 新增发放信息
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "/grant/info")
    Result<Boolean> addGrantInfo(@RequestBody PendantGrantInsertDTO dto);

    /**
     * 修改发放信息
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX + "/grant/info")
    Result<Boolean> updateGrantInfo(@RequestBody PendantGrantUpdateDTO dto);

    /**
     * 用户佩戴挂件
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "/wear")
    Result<Boolean> userWearPendant(@RequestBody UserPendantWearDTO dto);
}
