package com.jxntv.gvideo.user.client.dto.pendant;

import lombok.Data;

import java.io.Serializable;

/**
 * 插件信息-新增
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class PendantInsertDTO implements Serializable {

    /**
     * 挂件名称
     */
    private String title;
    /**
     * 类型 1-头像 2-皮肤
     */
    private Integer type;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 挂件ossid
     */
    private String ossId;
    /**
     * 创建用户id
     */
    private Long createUserId;
    /**
     * 创建用户账号
     */
    private String createUserName;


}
