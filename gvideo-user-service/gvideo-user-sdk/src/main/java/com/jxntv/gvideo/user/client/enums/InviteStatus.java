package com.jxntv.gvideo.user.client.enums;

public enum InviteStatus {

    PENDING(0, "未核销"),

    WRITE_OFF(1, "已核销"),

    INVALID(2, "已过期"),
    ;

    private Integer code;

    private String desc;

    InviteStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static InviteStatus parse(Integer code) {
        for (InviteStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }

        return null;
    }

}
