package com.jxntv.gvideo.user.client.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum PushMsgContentTypeEnum {
    // //内容类型：0-资源、1-社区、2-话题、3-<PERSON>GC、4-<PERSON><PERSON>、5-原生页面、6-H5页面、7-评论、8-回复、9-粉丝关注、10-置顶/精华、11-发布资源
    RESOURCE(0, "资源"),
    GROUP(1, "社区"),
    TOPIC(2, "话题"),
    UGC(3, "UGC"),
    PGC(4, "PGC"),
    NATIVA_PAGE(5, "原生页面"),
    H5_PAGE(6, "H5页面"),
    COMMENT(7, "评论"),
    REPLY(8, "回复"),
    FOLLOW(9, "关注"),
    GROUP_TOP(10, "置顶/精华"),
    RESOURCE_CREATE(11, "发布资源"),
    QUESTION(12, "提问"),
    ANSWER(13, "回答"),
    REPLY_ANSWER(14, "回复回答"),
    ACTIVITY(15, "活动"),
    ACCOST_SEND(16, "招呼-发出"),
    ACCOST_RECEIVE(17, "招呼-接收"),
    BLIND_DATE_AUDIT_FAILURE(18, "都市放心爱-审核失败"),
    BLIND_DATE_AUDIT_PASS(19, "都市放心爱-审核通过"),
    IM_CHAT_NOTIFICATION(20, "IM聊天通知"),
    TODAY_TALK_COUPON(24,"今日聊优惠券推送"),
    LIVE_BROADCAST_SUBSCRIBE(25,"活动直播订阅消息推送"),
    VISITOR_STATISTICS(26, "都市放心爱-访客统计"),
    TV_LIVE_SUBSCRIBE(27,"电视直播订阅消息推送"),

    ;

    private Integer code;
    private String desc;

    PushMsgContentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PushMsgContentTypeEnum parse(Integer code) {
        for (PushMsgContentTypeEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String toStr(Integer code) {
        PushMsgContentTypeEnum typeEnum = parse(code);
        return Objects.isNull(typeEnum) ? "" : typeEnum.getDesc();
    }
}
