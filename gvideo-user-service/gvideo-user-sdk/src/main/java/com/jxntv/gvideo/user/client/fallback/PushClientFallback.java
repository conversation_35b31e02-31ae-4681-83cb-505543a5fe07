package com.jxntv.gvideo.user.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.PushClient;
import com.jxntv.gvideo.user.client.dto.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Created on 2021/1/19.
 */
@Slf4j
@Component
public class PushClientFallback implements FallbackFactory<PushClient> {
    @Override
    public PushClient create(Throwable throwable) {
        return new PushClient() {
            @Override
            public Result<Object> token(PushTokenDTO dto) {
                log.error("PushClient.token() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> bindToken(PushTokenBindDTO dto) {
                log.error("PushClient.bindToken() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


            @Override
            public Result<MsgRspDTO> pushMsg(PushMsgDTO pushMsgDTO) {
                log.error("PushClient.pushMsg() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> batchPushMsg(List<PushMsgDTO> pushMsgDTOList) {
                log.error("PushClient.batchPushMsg() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> hasPush(Long mediaId, Integer contentType) {
                log.error("PushClient.hasPush() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> hasFollowPush(Long jid) {
                log.error("PushClient.hasFollowPush() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateRecMsg(Long configId, int recMsg) {
                log.error("PushClient.updateRecMsg() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


            @Override
            public Result<MsgRspDTO> getMsgById(Long id) {
                log.error("PushClient.getMsgById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteMsgById(Long id) {
                log.error("PushClient.deleteMsgById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public PageDTO<MsgRspDTO> page(PushMsgSearchDTO searchDTO) {
                log.error("PushClient.page() fallback", throwable);
                return PageDTO.empty(searchDTO.getCurrent(), searchDTO.getSize());
            }

            @Override
            public Result<PageDTO<ConsumerUserPushRecordDTO>> recordPage(ConsumerUserPushRecordSearchDTO searchDTO) {
                log.error("PushClient.recordPage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<ConsumerUserPushRecordDTO>> queryPushMsgRecord(Integer limit) {
                log.error("PushClient.queryPushMsgRecord() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
