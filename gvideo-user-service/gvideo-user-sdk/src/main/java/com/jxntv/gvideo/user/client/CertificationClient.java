package com.jxntv.gvideo.user.client;


import com.jxntv.gvideo.common.model.PageDTO;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.dto.*;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 认证号管理
 * <p>
 * <p>
 * Created on 2020-02-10
 */
public interface CertificationClient {

    @GetMapping("/api/certifications/live")
    Result<List<CertificationDTO>> queryLiveCertification();

    @GetMapping("/api/certifications")
    Result<List<CertificationDTO>> list();

    @PostMapping("/api/certifications/page")
    Result<PageDTO<CertificationDTO>> page(@RequestBody CertificationSearchDTO searchDTO);

    @GetMapping("/api/certifications/{id}")
    Result<CertificationDTO> get(@PathVariable Long id);

    @PostMapping("/api/certifications")
    Result<Long> create(@RequestBody CertificationDTO dto);

    @PutMapping("/api/certifications/{id}")
    Result update(@PathVariable Long id, @RequestBody CertificationDTO dto);

    @PutMapping("/api/certifications/{id}/status")
    Result updateStatus(@PathVariable Long id, @RequestBody CertificationDTO dto);

    @DeleteMapping("/api/certifications/{id}")
    Result delete(@PathVariable Long id);

    @GetMapping("/api/certifications/{id}/tenants")
    Result<PageDTO<CertificationTenantDTO>> tenants(@PathVariable Long id,
                                                    @RequestParam(required = false, defaultValue = "1") long current,
                                                    @RequestParam(required = false, defaultValue = "10") long size);

    @GetMapping("/api/certifications/{id}/all-tenants")
    Result<List<CertificationTenantDTO>> allTenants(@PathVariable Long id);

    @PostMapping("/api/certifications/all-tenants")
    Result<List<CertificationTenantDTO>> allTenants(@RequestBody List<Long> ids);

    @PostMapping("/api/certifications/{id}/tenants")
    Result addTenant(@PathVariable Long id, @RequestParam Long tenantId);

    @DeleteMapping("/api/certifications/{id}/tenants/{tenant-id}")
    Result removeTenant(@PathVariable Long id, @PathVariable("tenant-id") Long tenantId);

    @GetMapping("/api/certifications/{id}/tenants/{tenant-id}/user-count")
    Result<Integer> userCount(@PathVariable Long id, @PathVariable("tenant-id") Long tenantId);

    @GetMapping("/api/certifications/{id}/tenants/{tenant-id}/users")
    Result<PageDTO<CertificationUserDTO>> users(@PathVariable Long id,
                                                @PathVariable("tenant-id") Long tenantId,
                                                @RequestParam(required = false, defaultValue = "1") long current,
                                                @RequestParam(required = false, defaultValue = "10") long size);

    @PostMapping("/api/certifications/{id}/tenants/{tenant-id}/users")
    Result addUser(@PathVariable Long id,
                   @PathVariable("tenant-id") Long tenantId,
                   @RequestParam Long userId);

    @DeleteMapping("/api/certifications/{id}/tenants/{tenant-id}/users/{user-id}")
    Result removeUser(@PathVariable Long id,
                      @PathVariable("tenant-id") Long tenantId,
                      @PathVariable("user-id") Long userId);

    @GetMapping("/api/certifications/fuzzy")
    Result<List<CertificationDTO>> fuzzy(@RequestParam String name, @RequestParam Long tenantId, @RequestParam Long userId);

    @PostMapping("/api/certifications/getIdsByTenantIds")
    Result<List<Long>> getIdsByTenantIds(@RequestBody List<Long> tenantIds);

    @PostMapping("/api/certifications/bulk")
    Result<List<CertificationDTO>> bulk(@RequestBody List<Long> ids);

    /**
     * 查询该租户下所有认证号
     *
     * @param tenantId
     * @return
     */
    @GetMapping("/api/certifications/list/tenant")
    Result<List<CertificationDTO>> listTenant(@RequestParam Long tenantId);

    /**
     * 查询认证号
     *
     * @param username
     * @return
     */
    @GetMapping("/api/certifications/list/username")
    Result<List<CertificationDTO>> queryByUsername(@RequestParam String username);

    /**
     * 查询认证号关联圈子
     *
     * @param certificationId
     * @return
     */
    @GetMapping("/api/certifications/group/list")
    Result<List<CertificationGroupDTO>> listGroupById(@RequestParam Long certificationId, @RequestParam(required = false) Integer status);
}
