package com.jxntv.gvideo.user.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.dto.CommandMarkDetailDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerCommandDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerCommandMarkDataDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerCommandSearchDTO;
import com.jxntv.gvideo.user.client.param.CommandMarkDetailParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface ConsumerCommandClient {

    @PostMapping("/api/commands")
    Result<Long> addCommand(@RequestBody ConsumerCommandDTO dto);

    @PutMapping("/api/commands/{commandId}")
    Result<Boolean> updateCommand(@PathVariable Long commandId, @RequestBody ConsumerCommandDTO dto);

    @GetMapping("/api/commands")
    Result<List<ConsumerCommandDTO>> listCommandByDeviceId(@RequestParam String deviceId);

    @PostMapping("/api/commands/{commandId}/{deviceId}/mark")
    Result<Boolean> mark(@PathVariable Long commandId, @PathVariable String deviceId);

    /**
     * 标记扩展数据
     * @param dataDTO
     * @return
     */
    @PostMapping("/api/commands/markData")
    Result<Boolean> markData(@RequestBody ConsumerCommandMarkDataDTO dataDTO);

    @PostMapping("/api/commands/page")
    Result<PageDTO<ConsumerCommandDTO>> page(@RequestBody ConsumerCommandSearchDTO searchDTO);

    /**
     * 命令执行详情
     * @param param
     * @return
     */
    @PostMapping("/api/commands/markDetail")
    Result<CommandMarkDetailDTO> detail(@RequestBody CommandMarkDetailParam param);
}
