package com.jxntv.gvideo.user.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.dto.SysMenuDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * Created on 2020-02-18
 */
public interface SysMenuClient {

    @GetMapping("/api/menu/router")
    Result<List<SysMenuDTO>> getAllMenus();

    @GetMapping("/api/menu/router/common")
    Result<List<SysMenuDTO>> getCommonMenus();

    @GetMapping("/api/menu/router/user")
    Result<List<SysMenuDTO>> getUserMenus(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("username") String username);

    @GetMapping("/api/menu/permission")
    Result<List<String>> getUserPermissions(
            @RequestParam("tenantId") Long tenantId,
            @RequestParam("username") String username);

    /**
     * 查询c端用户是否有开播权限
     *
     * @param username
     * @return
     */
    @GetMapping("/api/menu/right/by/username")
    Result<Void> getRightByUsername(@RequestParam String username);

    /**
     * 查询用户是否有圈子超管权限
     *
     * @param userId
     * @return
     */
    @GetMapping("/api/menu/find/group/super")
    Result<Boolean> queryPermissionByUserId(@RequestParam Long userId, @RequestParam String permission, @RequestParam Long tenantId);
}
