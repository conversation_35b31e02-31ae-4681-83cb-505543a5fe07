package com.jxntv.gvideo.user.client.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ConsumerNicknameAuditSearchDTO extends SearchDTO {

    /**
     * 审核状态：0-未审核，1-审核通过，-1-审核未通过
     */
    private Integer checkstatus;
    /**
     * 审核流程节点是否为null
     */
    private Boolean procedureIdIsNull;
    /**
     * 审核阶段：0-机审，1-人审，2-审核完成
     */
    private Integer phase;


}
