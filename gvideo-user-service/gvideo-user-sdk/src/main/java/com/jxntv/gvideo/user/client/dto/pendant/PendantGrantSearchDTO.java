package com.jxntv.gvideo.user.client.dto.pendant;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

/**
 * 插件信息 查询
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class PendantGrantSearchDTO extends SearchDTO {

    /**
     * 挂件id
     */
    private Long pendantId;
    /**
     * 用户ID
     */
    private Long jid;

    /**
     * 状态 0-未上架 1-已上架 2-已下架
     */
    private Integer pendantStatus;

    /**
     * 状态 0-已发放 1-已收回
     */
    private Integer grantStatus;

    /**
     * 佩戴标识 0-未佩戴 1-已佩戴
     */
    private Integer wearFlag;

    /**
     * 类型 1-头像 2-皮肤
     */
    private Integer type;

}
