package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * Created on 2020-02-07
 */
@Data
public class ConsumerNickname {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 今视频号
     */
    private Long jid;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 原昵称
     */
    private String nicknameOld;
    /**
     * 手机号
     */
    @TableField(exist = false)
    private String mobile;
    /**
     * 当前处于阶段：0待机身1待人审
     */
    private Integer phase;
    /**
     * 审核状态
     */
    private Integer checkstatus;
    /**
     * 用户状态
     */
    @TableField(exist = false)
    private Integer status;
    /**
     * 审核命中信息
     */
    private String memo;
    /**
     * 审核人ID
     */
    private String auditUserId;
    /**
     * 审核人名
     */
    private String auditUserName;
    /**
     * 用户创建时间
     */
    private Date createDate;
    /**
     * 用户更新时间
     */
    private Date auditDate;
    /**
     * 机审状态
     */
    private Integer aiAuditState;
    /**
     * 流程节点id
     */
    private Long procedureId;

    private Integer refuseCode;

    private String refuseNote;
}
