package com.jxntv.gvideo.user.api.event;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 */
@Data
public class UserPendantCacheEvent implements Serializable {
    /**
     * 类型 1-头像 2-皮肤
     */
    private Integer type;

    private List<Long> jids;

    private Long pendantId;

    public UserPendantCacheEvent(Integer type, List<Long> jids){
        this.type = type;
        this.jids = jids;
    }

    public UserPendantCacheEvent(Integer type, Long pendantId){
        this.type = type;
        this.pendantId = pendantId;
    }
}
