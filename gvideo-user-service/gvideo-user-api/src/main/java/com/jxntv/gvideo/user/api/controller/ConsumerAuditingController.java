package com.jxntv.gvideo.user.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.api.converter.ConsumerUserConverter;
import com.jxntv.gvideo.user.api.entity.ConsumerAvatar;
import com.jxntv.gvideo.user.api.entity.ConsumerInfo;
import com.jxntv.gvideo.user.api.entity.ConsumerNickname;
import com.jxntv.gvideo.user.api.service.ConsumerAvatarService;
import com.jxntv.gvideo.user.api.service.ConsumerInfoService;
import com.jxntv.gvideo.user.api.service.ConsumerNicknameService;
import com.jxntv.gvideo.user.api.utils.PageUtils;
import com.jxntv.gvideo.user.client.ConsumerAuditingClient;
import com.jxntv.gvideo.user.client.dto.*;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
public class ConsumerAuditingController implements ConsumerAuditingClient {

    @Resource
    private ConsumerNicknameService consumerNicknameService;
    @Resource
    private ConsumerInfoService consumerInfoService;
    @Resource
    private ConsumerAvatarService consumerAvatarService;
    @Resource
    private ConsumerUserConverter consumerUserConverter;


    @Override
    public Result<PageDTO<ConsumerNicknameDTO>> searchAuditConsumerNickname(ConsumerNicknameAuditSearchDTO searchDTO) {

        LambdaQueryWrapper<ConsumerNickname> nicknameQuery = Wrappers.lambdaQuery();
        if (Objects.nonNull(searchDTO.getCheckstatus())) {
            nicknameQuery.eq(ConsumerNickname::getCheckstatus, searchDTO.getCheckstatus());
        }
        if (Boolean.TRUE.equals(searchDTO.getProcedureIdIsNull())) {
            nicknameQuery.isNull(ConsumerNickname::getProcedureId);
        }
        if (Objects.nonNull(searchDTO.getPhase())) {
            nicknameQuery.eq(ConsumerNickname::getPhase, searchDTO.getPhase());
        }

        Page<ConsumerNickname> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());

        Page<ConsumerNickname> page = consumerNicknameService.page(pageRequest, nicknameQuery);

        return Result.ok(PageUtils.pageOf(page, consumerUserConverter::convert));
    }

    @Override
    public Result<Boolean> updateAuditingConsumerNicknameProcedureId(Long id, Long procedureId) {
        boolean result = consumerNicknameService.updateProcedureId(id, procedureId);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> effectConsumerNickname(Long id) {
        boolean result = consumerNicknameService.effect(id);
        return Result.ok(result);
    }

    @Override
    public Result<PageDTO<ConsumerInfoDTO>> searchAuditConsumerInfo(ConsumerInfoAuditSearchDTO searchDTO) {

        LambdaQueryWrapper<ConsumerInfo> infoQuery = Wrappers.lambdaQuery();
        if (Objects.nonNull(searchDTO.getCheckstatus())) {
            infoQuery.eq(ConsumerInfo::getCheckstatus, searchDTO.getCheckstatus());
        }
        if (Boolean.TRUE.equals(searchDTO.getProcedureIdIsNull())) {
            infoQuery.isNull(ConsumerInfo::getProcedureId);
        }
        if (Objects.nonNull(searchDTO.getPhase())) {
            infoQuery.eq(ConsumerInfo::getPhase, searchDTO.getPhase());
        }

        Page<ConsumerInfo> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());

        Page<ConsumerInfo> page = consumerInfoService.page(pageRequest, infoQuery);

        return Result.ok(PageUtils.pageOf(page, consumerUserConverter::convert));
    }

    @Override
    public Result<Boolean> updateAuditConsumerInfoProcedureId(Long id, Long procedureId) {
        return Result.ok(consumerInfoService.updateProcedureId(id,procedureId));
    }

    @Override
    public Result<Boolean> effectConsumerInfo(Long id) {
        return Result.ok(consumerInfoService.effect(id));
    }

    @Override
    public Result<PageDTO<ConsumerAvatarDTO>> searchAuditConsumerAvatar(ConsumerAvatarAuditSearchDTO searchDTO) {
        LambdaQueryWrapper<ConsumerAvatar> infoQuery = Wrappers.lambdaQuery();
        if (Objects.nonNull(searchDTO.getCheckstatus())) {
            infoQuery.eq(ConsumerAvatar::getCheckstatus, searchDTO.getCheckstatus());
        }
        if (Boolean.TRUE.equals(searchDTO.getProcedureIdIsNull())) {
            infoQuery.isNull(ConsumerAvatar::getProcedureId);
        }
        if (Objects.nonNull(searchDTO.getPhase())) {
            infoQuery.eq(ConsumerAvatar::getPhase, searchDTO.getPhase());
        }

        Page<ConsumerAvatar> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());

        Page<ConsumerAvatar> page = consumerAvatarService.page(pageRequest, infoQuery);

        return Result.ok(PageUtils.pageOf(page, consumerUserConverter::convert));
    }

    @Override
    public Result<Boolean> updateAuditConsumerAvatarProcedureId(Long id, Long procedureId) {
        return Result.ok(consumerAvatarService.updateProcedureId(id,procedureId));
    }

    @Override
    public Result<Boolean> effectConsumerAvatar(Long id) {
        return Result.ok(consumerAvatarService.effect(id));
    }
}
