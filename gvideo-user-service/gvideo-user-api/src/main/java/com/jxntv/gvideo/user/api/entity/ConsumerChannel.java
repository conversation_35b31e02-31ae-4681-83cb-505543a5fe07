package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * c端渠道表
 *
 * @TableName consumer_channel
 */
@TableName(value = "consumer_channel")
@Data
public class ConsumerChannel implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道名
     */
    private String channelName;

    private Date createDate;


}
