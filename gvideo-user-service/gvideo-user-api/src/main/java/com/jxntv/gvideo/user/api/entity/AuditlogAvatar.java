package com.jxntv.gvideo.user.api.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

/**
 *
 * Created on 2020-05-20
 */
@Data
public class AuditlogAvatar {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 今视频ID
     */
    private Long jid;
    /**
     * 头像文件ID
     */
    private String avatar;
    /**
     * 原头像文件ID
     */
    private String avatarOld;
    /**
     * 审核状态
     */
    private Integer checkstatus;
    /**
     * 审核人ID
     */
    private String auditUserId;
    /**
     * 审核人名
     */
    private String auditUserName;
    /**
     * 用户创建时间
     */
    private Date createDate;
    /**
     * 用户更新时间
     */
    private Date auditDate;

    /**
     * Ai审核状态
     */
    private Integer aiAuditState;
    /**
     * 关联id
     */
    private Long linkId;
}
