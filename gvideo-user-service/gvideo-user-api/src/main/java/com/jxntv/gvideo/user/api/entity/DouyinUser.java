package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.jxntv.gvideo.common.mybatis.AesEncryptTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 抖音小程序-用户表
 * 
 * <AUTHOR>
 * @date 2022-11-01 11:03:05
 */
@Data
public class DouyinUser  implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 今视频jid
	 */
	private Long jid;
	/**
	 * 手机号
	 */
	@TableField(typeHandler = AesEncryptTypeHandler.class)
	private String mobile;
	/**
	 * 状态
	 */
	private Integer status;
	/**
	 * 抖音unionId
	 */
	private String unionId;
	/**
	 * 抖音openid
	 */
	private String openId;
	/**
	 * 抖音昵称
	 */
	private String nickName;
	/**
	 * 抖音头像地址
	 */
	private String avatarUrl;
	/**
	 * 性别
	 */
	private String gender;
	/**
	 * 国家
	 */
	private String country;
	/**
	 * 省
	 */
	private String province;
	/**
	 * 城市
	 */
	private String city;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;
	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

}
