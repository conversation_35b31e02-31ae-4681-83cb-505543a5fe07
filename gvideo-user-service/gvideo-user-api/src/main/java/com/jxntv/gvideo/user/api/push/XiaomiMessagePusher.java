package com.jxntv.gvideo.user.api.push;

import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.api.config.MsgPushProperties;
import com.jxntv.gvideo.user.api.converter.ConsumerUserPushRecordConverter;
import com.jxntv.gvideo.user.api.entity.ConsumerUserPushRecord;
import com.jxntv.gvideo.user.api.service.ConsumerUserPushRecordService;
import com.jxntv.gvideo.user.client.dto.MsgPushDeviceEventDTO;
import com.jxntv.gvideo.user.client.dto.MsgRspDTO;
import com.jxntv.gvideo.user.client.enums.ConsumerUserPushConfigType;
import com.xiaomi.push.sdk.ErrorCode;
import com.xiaomi.xmpush.server.Constants;
import com.xiaomi.xmpush.server.Message;
import com.xiaomi.xmpush.server.Result;
import com.xiaomi.xmpush.server.Sender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 小米 推送实现
 * <a href="https://dev.mi.com/console/doc/detail?pId=1278">参考文档</a>
 *
 * <AUTHOR>
 * @date 2022/4/12
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class XiaomiMessagePusher implements MessagePusher {

    @Resource
    private ConsumerUserPushRecordService consumerUserPushRecordService;
    @Resource
    private MsgPushProperties msgPushProperties;
    @Resource
    private Sender sender;
    @Resource
    private ConsumerUserPushRecordConverter consumerUserPushRecordConverter;

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void push(List<MsgPushDeviceEventDTO> events) throws Exception {
        List<MsgPushDeviceEventDTO> xiaomiList = events.stream().filter(e -> Objects.equals(ConsumerUserPushConfigType.XIAOMI.getCode(), e.getConfig().getType())).collect(Collectors.toList());
        //  按照msg分组处理
        Map<Long, List<MsgPushDeviceEventDTO>> msgGroup = xiaomiList.stream().collect(Collectors.groupingBy(e -> e.getMsg().getId()));

        msgGroup.forEach((msgId, list) -> {
            MsgRspDTO msg = list.get(0).getMsg();

            //  构建消息体
            Message message = getMessage(msg);
            //  构建发送目标
            List<String> tokens = list.stream().map(e -> e.getConfig().getToken()).collect(Collectors.toList());

            try {
                Result result = sender.send(message, tokens, 3);
                log.info("【小米推送】send响应数据: msgId = {}, result = {}", msg.getId(), JsonUtils.toJson(result));


                //  保存推送结果
                if (Objects.equals(ErrorCode.Success.getValue(), result.getErrorCode().getValue())) {

                    List<ConsumerUserPushRecord> records = list.stream().map(e -> {
                        ConsumerUserPushRecord record = consumerUserPushRecordConverter.convert(e);
                        record.setStatus(1);
                        record.setRequestId(result.getMessageId());
                        record.setRemarks(result.getReason());

                        return record;

                    }).collect(Collectors.toList());

                    consumerUserPushRecordService.saveBatch(records);
                } else {
                    //  推送失败
                    List<ConsumerUserPushRecord> records = list.stream().map(e -> {
                        ConsumerUserPushRecord record = consumerUserPushRecordConverter.convert(e);
                        record.setStatus(2);
                        record.setRequestId(result.getMessageId());
                        record.setRemarks(result.getReason());

                        return record;

                    }).collect(Collectors.toList());

                    consumerUserPushRecordService.saveBatch(records);
                }

            } catch (Exception e) {
                log.info("【小米推送】send失败", e);
            }

        });

    }

    private Message getMessage(MsgRspDTO msg) {
        Message message;

        String xiaomi_icon = msg.getXiaomiIcon();
        String xiaomi_pic = msg.getXiaomiPic();

        if (StringUtils.hasText(xiaomi_pic)) {
            message = buildPicMessageBody(msg, xiaomi_pic, xiaomi_icon);
        } else if (StringUtils.hasText(xiaomi_icon)) {
            message = buildIconMessageBody(msg, xiaomi_icon);
        } else {
            message = buildTextMessageBody(msg);
        }

        return message;
    }


    private Message buildTextMessageBody(MsgRspDTO msg) {
        return new Message.Builder()
                .title(getTitle(msg.getTitle()))
                .description(getContent(msg.getContent()))
                .extra(Constants.EXTRA_PARAM_NOTIFY_EFFECT, Constants.NOTIFY_ACTIVITY)
                .extra(Constants.EXTRA_PARAM_INTENT_URI, formatLinkUrl(msg.getLinkUrl(), msg.getId()))
                .restrictedPackageName(msgPushProperties.getXiaomi().getRestrictedPackageName())
                // 使用默认提示音提示
                .notifyType(1)
                .build();
    }

    private Message buildIconMessageBody(MsgRspDTO msg, String xiaomi_icon) {

        return new Message.Builder()
                .title(getTitle(msg.getTitle()))
                .description(getContent(msg.getContent()))
                .extra(new Message.NotificationStyleBuilder().bigTextStyle().largeIconUri(xiaomi_icon).build())
                .extra(Constants.EXTRA_PARAM_NOTIFY_EFFECT, Constants.NOTIFY_ACTIVITY)
                .extra(Constants.EXTRA_PARAM_INTENT_URI, formatLinkUrl(msg.getLinkUrl(), msg.getId()))
                .restrictedPackageName(msgPushProperties.getXiaomi().getRestrictedPackageName())
                .notifyType(1)     // 使用默认提示音提示
                .build();
    }

    private Message buildPicMessageBody(MsgRspDTO msg, String xiaomi_pic, String xiaomi_icon) {

        return new Message.Builder()
                .title(getTitle(msg.getTitle()))
                .description(getContent(msg.getContent()))
                .extra(new Message.NotificationStyleBuilder().bigPictureStyle().bigPicUri(xiaomi_pic).largeIconUri(xiaomi_icon).build())
                .extra(Constants.EXTRA_PARAM_NOTIFY_EFFECT, Constants.NOTIFY_ACTIVITY)
                .extra(Constants.EXTRA_PARAM_INTENT_URI, formatLinkUrl(msg.getLinkUrl(), msg.getId()))
                .restrictedPackageName(msgPushProperties.getXiaomi().getRestrictedPackageName())
                .notifyType(1)     // 使用默认提示音提示
                .build();

    }

    /**
     * <a href="https://dev.mi.com/console/doc/detail?pId=1278">小米官方文档</a>
     * 内容限制长度: 长度小于128, 一个中英文字符均计算为1(通知栏消息必填)
     *
     * @param content 推送内容
     * @return 格式化后文档
     */
    private static String getContent(String content) {
        content = StringUtils.hasText(content) ? content : "";
        if (content.length() > 128) {
            content = String.format("%s...", content.substring(0, 125));
        }
        return content;
    }

    /**
     * <a href="https://dev.mi.com/console/doc/detail?pId=1278">小米官方文档</a>
     * 标题限制长度：20汉字
     *
     * @param title 标题
     * @return 格式化标题
     */
    private static String getTitle(String title) {
        title = StringUtils.hasText(title) ? title : "";
        if (title.length() > 50) {
            title = String.format("%s...", title.substring(0, 47));
        }
        return title;
    }

    private String formatLinkUrl(String linkUrl, Long msgId) {
        return String.format("%s&pushId=%s", linkUrl, msgId);
    }
}
