package com.jxntv.gvideo.user.api.utils;

import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class LocalDateTimeUtils {

    public static LocalDateTime parse(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        return LocalDateTime.parse(str, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String format(LocalDateTime date) {
        if (date == null) return null;
        return date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String join(LocalDateTime start, LocalDateTime end) {
        if (start != null && end != null) {
            return LocalDateTimeUtils.format(start) + " 至 " + LocalDateTimeUtils.format(end);
        }
        return null;
    }
}
