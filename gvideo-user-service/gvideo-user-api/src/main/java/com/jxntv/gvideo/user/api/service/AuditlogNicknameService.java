package com.jxntv.gvideo.user.api.service;

import java.util.Date;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.user.api.entity.AuditlogNickname;

/**
 *
 * Created on 2020-03-03
 */
public interface AuditlogNicknameService extends IService<AuditlogNickname> {

    Page<AuditlogNickname> getAuditlogNicknames(Page<AuditlogNickname> page, Date createStartDate, Date createEndDate,
            Date auditStartDate, Date auditEndDate, Integer checkStatus, String adminUser, String consumerUser,Integer aiAuditState);

}
