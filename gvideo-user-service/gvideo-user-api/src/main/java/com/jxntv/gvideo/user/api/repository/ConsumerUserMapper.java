package com.jxntv.gvideo.user.api.repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.jxntv.gvideo.user.api.entity.ConsumerUser;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 *
 * Created on 2020-02-07
 */
@Mapper
@Repository
public interface ConsumerUserMapper extends BaseMapper<ConsumerUser> {

    List<Map<String, Integer>> getJidCount(Date statisticDate);

    List<Map<String, Integer>> getMobileCount(Date statisticDate);
}
