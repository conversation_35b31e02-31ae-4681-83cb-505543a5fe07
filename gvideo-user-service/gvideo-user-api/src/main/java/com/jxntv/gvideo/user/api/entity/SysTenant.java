package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-02-18
 */
@Data
public class SysTenant {

    /**
     * 租户自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 租户名
     */
    private String name;
    /**
     * 租户描述
     */
    private String note;
    /**
     * 租户状态
     */
    private Integer status;
    /**
     * 是否系统租户
     */
    private Integer isSystem;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;

}
