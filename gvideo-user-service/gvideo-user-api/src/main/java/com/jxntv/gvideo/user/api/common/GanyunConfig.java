package com.jxntv.gvideo.user.api.common;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 文章配置文件
 */
@Data
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "ganyun")
public class GanyunConfig {
    /**
     * 赣云appId
     */
    private String appId;

    /**
     * 赣云appSecret
     */
    private String appSecret;

    /**
     * 移除赣云华为推送token
     */
    private String removeMsgUrl;
}
