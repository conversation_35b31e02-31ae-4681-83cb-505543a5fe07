package com.jxntv.gvideo.user.api.converter;

import com.jxntv.gvideo.user.api.entity.SysTenant;
import com.jxntv.gvideo.user.client.dto.SysTenantDTO;

public class TenantConverter {

    public static SysTenantDTO convert(SysTenant entity) {
        SysTenantDTO dto = new SysTenantDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setNote(entity.getNote());
        dto.setStatus(entity.getStatus());
        dto.setIsSystem(entity.getIsSystem());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    public static SysTenant convert(SysTenantDTO dto) {
        SysTenant entity = new SysTenant();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setNote(dto.getNote());
        entity.setStatus(dto.getStatus());
        entity.setIsSystem(dto.getIsSystem());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }
}
