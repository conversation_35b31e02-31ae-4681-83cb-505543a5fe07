package com.jxntv.gvideo.user.api.event.listener;

import com.github.binarywang.wxpay.bean.transfer.TransferBatchesResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.api.entity.ConsumerUserPurseRecord;
import com.jxntv.gvideo.user.api.event.WechatMerchantTransfersEvent;
import com.jxntv.gvideo.user.api.service.ConsumerUserPurseRecordService;
import com.jxntv.gvideo.user.api.utils.UuidUtils;
import com.jxntv.gvideo.user.api.utils.WechatEntPayUtil;
import com.jxntv.gvideo.user.client.enums.ConsumerUserPurseRecordStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 微信商户转账到个人零钱
 * @date 2022/03/03 17:28
 */
@Component
@Slf4j
public class WechatMerchantTransfersListener {

    @Autowired
    private WechatEntPayUtil wechatEntPayUtil;

    @Autowired
    private ConsumerUserPurseRecordService consumerUserPurseRecordService;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @EventListener
    public void onEvent(WechatMerchantTransfersEvent event) {
        log.info("商家转账到零钱-开始："+JsonUtils.toJson(event));
        ConsumerUserPurseRecord entity = consumerUserPurseRecordService.getById(event.getId());
        if (Objects.isNull(entity) || ConsumerUserPurseRecordStatus.WITHDRAW_AUDIT_PASS.getCode() != entity.getStatus()){
            return;
        }
        /*EntPayResult payResult = null;
        String errorMsg = null;
        try {
            payResult =  wechatEntPayUtil.createEntPayRequest(entity);
            log.info("微信打款返回："+JsonUtils.toJson(payResult));
        }catch (WxPayException e) {
            log.error("微信打款失败："+JsonUtils.toJson(e));
            errorMsg = e.getErrCodeDes();
        }*/

        if (StringUtils.isEmpty(entity.getBatchTradeNo())){
            entity.setBatchTradeNo(UuidUtils.generateId()+System.currentTimeMillis());
        }

        TransferBatchesResult payResult = null;
        String errorMsg = null;
        try {
            payResult =  wechatEntPayUtil.transferBatchesRequest(entity);
            log.info("商家转账到零钱返回："+JsonUtils.toJson(payResult));
        }catch (WxPayException e) {
            log.error("商家转账到零钱失败："+JsonUtils.toJson(e));
            errorMsg = e.getCustomErrorMsg();
        }

        boolean b = this.consumerUserPurseRecordService.saveTransferBatches(entity, payResult, errorMsg);
        log.info("微信打款状态："+b);

    }
}
