package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ConsumerNotificationSettings implements Serializable {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户jid
     */
    private Long jid;
    /**
     * IM推送是否开启：0-不开启，1-开启
     */
    private Boolean imNotificationOpen;
    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

}
