package com.jxntv.gvideo.user.api.push;

import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.api.converter.ConsumerUserPushRecordConverter;
import com.jxntv.gvideo.user.api.entity.ConsumerUserPushRecord;
import com.jxntv.gvideo.user.api.service.ConsumerUserPushRecordService;
import com.jxntv.gvideo.user.client.dto.MsgPushDeviceEventDTO;
import com.jxntv.gvideo.user.client.dto.MsgRspDTO;
import com.jxntv.gvideo.user.client.dto.PushConfigRspDTO;
import com.jxntv.gvideo.user.client.enums.ConsumerUserPushConfigType;
import com.oppo.push.server.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * OPPO 推送实现
 * 参考文档： https://open.oppomobile.com/new/developmentDoc/info?id=11233
 *
 * <AUTHOR>
 * @date 2022/4/12
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class OppoMessagePusher implements MessagePusher {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private ConsumerUserPushRecordService consumerUserPushRecordService;
    @Resource
    private Sender oppoSender;
    @Resource
    private ConsumerUserPushRecordConverter consumerUserPushRecordConverter;

    private static final String PUSH_OPPO_MESSAGE_FORMAT = "push::oppo::message-id::%s";

    @Async
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void push(List<MsgPushDeviceEventDTO> events) throws Exception {
        //  过滤OPPO推送列表
        List<MsgPushDeviceEventDTO> oppoList = events.stream().filter(e -> Objects.equals(ConsumerUserPushConfigType.OPPO.getCode(), e.getConfig().getType())).collect(Collectors.toList());

        //  按照msg分组处理
        Map<Long, List<MsgPushDeviceEventDTO>> msgGroup = oppoList.stream().collect(Collectors.groupingBy(e -> e.getMsg().getId()));

        //  按照是否可以单推再分组
        Map<Boolean, List<List<MsgPushDeviceEventDTO>>> booleanListMap = msgGroup.values().stream().collect(Collectors.partitioningBy(this::isSingle));

        //  单推模式
        singleSend(booleanListMap.get(true));

        //  广播模式
        batchSend(booleanListMap.get(false));

    }

    private String getMessageId(MsgRspDTO msg) {
        String key = String.format(PUSH_OPPO_MESSAGE_FORMAT, msg.getId());
        String messageId = stringRedisTemplate.opsForValue().get(key);
        //  构建新的messageId
        if (StringUtils.isEmpty(messageId)) {
            Notification notification = getNotification(msg);
            try {
                Result saveResult = oppoSender.saveNotification(notification); // 发送保存消息体请求
                log.info("【OPPO推送】saveNotification响应数据: msgId = {}, saveResult = {}", msg.getId(), JsonUtils.toJson(saveResult));
                messageId = saveResult.getMessageId(); //获取messageId
            } catch (Exception e) {
                log.info("【OPPO推送】saveNotification请求失败", e);
            }
            if (StringUtils.isNotEmpty(messageId)) {
                stringRedisTemplate.opsForValue().setIfAbsent(key, messageId, 2L, TimeUnit.HOURS);
            }

        }
        return messageId;
    }

    /**
     * 单推限制条件
     * 1、一条消息只有一个推送对象
     * 2、无图模式，带图片的推送只能使用广播模式
     *
     * @param events 推送事件
     * @return 是否可以使用单推
     */
    private boolean isSingle(List<MsgPushDeviceEventDTO> events) {
        return events.size() == 1 && StringUtils.isEmpty(events.get(0).getMsg().getOppoIcon()) && StringUtils.isEmpty(events.get(0).getMsg().getOppoPic());
    }


    /**
     * 批量单推
     *
     * @param eventsList 推送事件列表
     */
    private void singleSend(List<List<MsgPushDeviceEventDTO>> eventsList) {
        //  无图模式
        Map<Target, Notification> notificationsMessage = new LinkedHashMap<>();
        for (List<MsgPushDeviceEventDTO> events : eventsList) {
            MsgPushDeviceEventDTO dto = events.get(0);
            MsgRspDTO msg = dto.getMsg();
            PushConfigRspDTO config = dto.getConfig();

            Notification notification = getNotification(msg);
            Target target = Target.build(config.getToken());

            notificationsMessage.put(target, notification);
        }

        try {
            Result result = oppoSender.unicastBatchNotification(notificationsMessage);
            //  保存推送结果
            saveUnicastBatchResult(eventsList, result);
        } catch (Exception e) {
            log.error("【OPPO推送】unicastBatchNotification响应数据失败", e);
            saveFailedResult(eventsList, e.getMessage());
        }

    }

    private void saveBroadcastResult(List<List<MsgPushDeviceEventDTO>> eventsList, Result result) {
        //  判断Http请求是否成功
        if (!Objects.equals(200, result.getStatusCode())) {
            //  保存推送失败结果
            saveFailedResult(eventsList, result.getReason());
            return;
        }

        //  判断业务处理是否成功
        ReturnCode returnCode = result.getReturnCode();
        if (Objects.isNull(returnCode) || !Objects.equals(returnCode.getCode(), 0)) {
            //  保存推送失败结果
            saveFailedResult(eventsList, returnCode.getMessage());
            return;
        }


        //  处理成功
        List<Result.BroadcastErrorResult> broadcastErrorResults = result.getBroadcastErrorResults();
        Map<String, Result.BroadcastErrorResult> resultMap = broadcastErrorResults.stream().collect(Collectors.toMap(Result.BroadcastErrorResult::getTargetValue, e -> e, (e1, e2) -> e1));

        //  构建记录
        List<ConsumerUserPushRecord> records = convert(eventsList);

        //  设置推送结果
        for (ConsumerUserPushRecord record : records) {
            Result.BroadcastErrorResult broadcastErrorResult = resultMap.get(record.getToken());
            if (Objects.isNull(broadcastErrorResult)) {
                record.setStatus(2);
                record.setRemarks("未知");
            } else {
                if (Objects.equals(broadcastErrorResult.getErrorCode(), "0")) {
                    record.setStatus(1);
                    record.setRequestId(result.getMessageId());
                } else {
                    record.setStatus(2);
                    record.setRemarks("未知");
                    record.setRequestId(result.getMessageId());
                }

            }

        }

        consumerUserPushRecordService.saveBatch(records);

    }

    private List<ConsumerUserPushRecord> convert(List<List<MsgPushDeviceEventDTO>> eventsList) {
        return eventsList.stream().flatMap(Collection::stream).map(consumerUserPushRecordConverter::convert).collect(Collectors.toList());
    }


    private void saveUnicastBatchResult(List<List<MsgPushDeviceEventDTO>> eventsList, Result result) {
        //  判断Http请求是否成功
        if (!Objects.equals(200, result.getStatusCode())) {
            //  保存推送失败结果
            saveFailedResult(eventsList, result.getReason());
            return;
        }

        //  判断业务处理是否成功
        ReturnCode returnCode = result.getReturnCode();
        if (Objects.isNull(returnCode) || !Objects.equals(returnCode.getCode(), 0)) {
            //  保存推送失败结果
            saveFailedResult(eventsList, returnCode.getMessage());
            return;
        }


        //  推送成功
        List<Result.UnicastBatchResult> unicastBatchResults = result.getUnicastBatchResults();
        Map<String, Result.UnicastBatchResult> resultMap = CollectionUtils.isEmpty(unicastBatchResults) ? Collections.emptyMap() : unicastBatchResults.stream().collect(Collectors.toMap(Result.UnicastBatchResult::getTargetValue, e -> e, (e1, e2) -> e1));

        //  构建记录
        List<ConsumerUserPushRecord> records = convert(eventsList);

        //  设置推送结果
        for (ConsumerUserPushRecord record : records) {
            Result.UnicastBatchResult unicastBatchResult = resultMap.get(record.getToken());
            if (Objects.isNull(unicastBatchResult)) {
                record.setStatus(2);
            } else {
                if (Objects.equals(unicastBatchResult.getErrorCode(), 0)) {
                    record.setStatus(1);
                    record.setRemarks(unicastBatchResult.getErrorMessage());
                    record.setRequestId(unicastBatchResult.getMessageId());
                } else {
                    record.setStatus(2);
                    record.setRemarks(unicastBatchResult.getErrorMessage());
                    record.setRequestId(unicastBatchResult.getMessageId());
                }

            }
        }

        consumerUserPushRecordService.saveBatch(records);
    }

    private void saveFailedResult(List<List<MsgPushDeviceEventDTO>> eventsList, String message) {
        List<ConsumerUserPushRecord> records = convert(eventsList);
        records.forEach(e -> {
            e.setStatus(2);
            e.setRemarks(message);
        });
        consumerUserPushRecordService.saveBatch(records);
    }

    /**
     * 广播模式推送
     *
     * @param eventsList 推送事件列表
     */
    private void batchSend(List<List<MsgPushDeviceEventDTO>> eventsList) {
        for (List<MsgPushDeviceEventDTO> events : eventsList) {
            MsgRspDTO msg = events.get(0).getMsg();

            String messageId = getMessageId(msg);
            //  多个token采用;分隔的方式拼接
            String tokens = events.stream().map(e -> e.getConfig().getToken()).collect(Collectors.joining(";"));
            Target target = Target.build(tokens);

            try {
                Result result = oppoSender.broadcastNotification(messageId, target);
                log.info("【OPPO推送】broadcastNotification响应结果: result{}", JsonUtils.toJson(result));
                saveBroadcastResult(Collections.singletonList(events), result);
            } catch (Exception e) {
                log.info("【OPPO推送】broadcastNotification请求失败", e);
                saveFailedResult(Collections.singletonList(events), e.getMessage());
            }

        }
    }


    private Notification getNotification(MsgRspDTO msg) {

        String oppo_icon = msg.getOppoIcon();
        String oppo_pic = msg.getOppoPic();

        Notification notification = new Notification();
        notification.setTitle(getContent(msg.getTitle(), 50));

        //通知栏样式 1. 标准样式  2. 长文本样式  3. 大图样式 【非必填，默认1-标准样式】
        if (StringUtils.isNotEmpty(oppo_pic)) {
            notification.setStyle(3);
            notification.setBigPictureId(oppo_pic);
            notification.setContent(getContent(msg.getContent(), 50));
        } else {
            notification.setStyle(1);
            notification.setContent(getContent(msg.getContent(), 200));
        }
        if (StringUtils.isNotEmpty(oppo_icon)) {
            notification.setSmallPictureId(oppo_icon);
        }
        // App开发者自定义消息Id，OPPO推送平台根据此ID做去重处理，对于广播推送相同appMessageId只会保存一次，对于单推相同appMessageId只会推送一次
        notification.setAppMessageId(UUID.randomUUID().toString());
        // 点击动作类型0，启动应用；1，打开应用内页（activity的intent action）；2，打开网页；4，打开应用内页（activity）；【非必填，默认值为0】;5,Intent scheme URL
        notification.setClickActionType(5);
        // 应用内页地址【click_action_type为1或4时必填，长度500】
        notification.setClickActionActivity("com.coloros.push.demo.component.InternalActivity");
        // 网页地址【click_action_type为2必填，长度500】
        notification.setClickActionUrl(formatLinkUrl(msg.getLinkUrl(), msg.getId()));
        // 是否进离线消息,【非必填，默认为True】
        notification.setOffLine(true);
        // 离线消息的存活时间(time_to_live) (单位：秒), 【off_line值为true时，必填，最长3天】
        notification.setOffLineTtl(24 * 3600);
        // 时区，默认值：（GMT+08:00）北京，香港，新加坡
        notification.setTimeZone("GMT+08:00");
        // 0：不限联网方式, 1：仅wifi推送
        notification.setNetworkType(0);
        return notification;
    }


    /**
     * oppo官方文档：https://open.oppomobile.com/wiki/doc#id=10688
     * 内容限制长度:
     * 1）标准样式（style 为 1）：字数限制200以内（兼容API文档以前定义，实际手机端通知栏消息只能展示50字数）
     * 2）长文本样式（style 为 2）限制128个以内
     * 3）大图样式（style 为 3）字数限制50以内，中英文均以一个计算】
     *
     * @param content
     * @return
     */
    private static String getContent(String content, int maxLength) {
        content = StringUtils.isNotEmpty(content) ? content : "";
        if (content.length() > maxLength) {
            content = String.format("%s...", content.substring(0, maxLength - 3));
        }
        return content;
    }

    private String formatLinkUrl(String linkUrl, Long msgId) {
        return String.format("%s&pushId=%s", linkUrl, msgId);
    }

}
