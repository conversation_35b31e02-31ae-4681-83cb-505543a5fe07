package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 邀请好友记录表
 * 
 * <AUTHOR>
 */
@Data
public class InviteFriendLog  implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 自增ID
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 邀请人今视频唯一ID
	 */
	private Long jid;
	/**
	 * 被邀请人手机号
	 */
	private String inviteFriendMobile;
	/**
	 * 被邀请人今视频唯一ID
	 */
	private Long inviteFriendJid;
	/**
	 * 渠道 0-app拉新 1-都市放心爱 2-好友签到 3-天天领鸡蛋拉新 4-三周年邀请好友 5-抽奖活动邀请好友
	 */
	private Integer channel;
	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 邀请奖励状态 0-未发放 1-已发放
	 */
	private Integer rewardFlag;

	/**
	 * 创建时间
	 */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;

}
