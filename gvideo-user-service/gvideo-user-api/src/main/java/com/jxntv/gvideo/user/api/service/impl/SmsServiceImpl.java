package com.jxntv.gvideo.user.api.service.impl;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.api.service.SmsService;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    private final static String SMS_ID = "api";

    private final static String SMS_KEY = "96254118d21696f029d56fe8ce50f80b";

    private final static String SMS_URL = "http://sms-api.luosimao.com/v1/send.json";

    private final static Integer SMS_OK = 0;

    private final static RestTemplate template = new RestTemplateBuilder().basicAuthentication(SMS_ID, SMS_KEY).build();


    @Override
    @Async
    public void send(String mobile, String message) {

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        MultiValueMap<String, String> formData = new LinkedMultiValueMap<>();

        formData.add("mobile", mobile);
        formData.add("message", message);
        HttpEntity<MultiValueMap<String, String>> requestBody = new HttpEntity<>(formData, headers);

        ResponseEntity<SMSResponse> responseEntity = template.postForEntity(SMS_URL, requestBody, SMSResponse.class);
        SMSResponse response = responseEntity.getBody();

        //  如果发送失败，打印日志和抛出异常
        trowExceptionIfError(mobile, message, response);

    }

    @SneakyThrows
    private void trowExceptionIfError(String mobile, String message, SMSResponse response) {
        if (Objects.isNull(response) || !SMS_OK.equals(response.getError())) {
            Map<String, Object> map = new HashMap<>();
            map.put("mobile", mobile);
            map.put("message", message);

            if (Objects.nonNull(response)) {
                map.put("error", response.getError());
                map.put("msg", response.getMsg());
            }

            String json = JsonUtils.toJson(map);
            log.error("短信发送失败：{}", json);

            throw new CodeMessageException(CodeMessage.SMS_ERROR);
        }
    }


    @Data
    public static class SMSResponse {

        private Integer error;
        private String msg;
    }


}
