package com.jxntv.gvideo.user.api.service;

import java.util.Date;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.user.api.entity.AuditlogIdentity;

/**
 *
 * Created on 2020-03-03
 */
public interface AuditlogIdentityService extends IService<AuditlogIdentity> {

    Page<AuditlogIdentity> getAuditlogIdentities(Page<AuditlogIdentity> page, Date createStartDate, Date createEndDate,
            Date auditStartDate, Date auditEndDate, Integer checkStatus, String adminUser, String consumerUser);

}
