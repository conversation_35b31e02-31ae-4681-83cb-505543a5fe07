package com.jxntv.gvideo.user.api.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import lombok.Data;

/**
 *
 * Created on 2020-05-06
 */
@Data
public class AppProtocol {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户今视频ID
     */
    private Long jid;
    /**
     * 用户设备号
     */
    private String cid;
    /**
     * 协议类型
     */
    private Integer protocolType;
    /**
     * 协议版本号
     */
    private String protocolVersion;
    /**
     * 用户创建时间
     */
    private Date createDate;
}
