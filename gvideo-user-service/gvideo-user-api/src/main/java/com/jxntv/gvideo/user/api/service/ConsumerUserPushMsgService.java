package com.jxntv.gvideo.user.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.user.api.entity.ConsumerUserPushMsg;

import java.util.List;

/**
 * <AUTHOR> Created on 2021/1/19.
 */
public interface ConsumerUserPushMsgService extends IService<ConsumerUserPushMsg> {
    int getTodayFollowMsgCount(Long jid);

    /**
     * 抓取推送任务
     *
     * @return 消息列表
     */
    List<ConsumerUserPushMsg> listPushTask(int limit);

    /**
     * 分发消息
     *
     * @param msg
     */
    void sendMessage(ConsumerUserPushMsg msg);

}
