package com.jxntv.gvideo.user.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.user.api.entity.UgcVestRelation;
import com.jxntv.gvideo.user.client.dto.UgcVestDTO;
import com.jxntv.gvideo.user.client.dto.UgcVestSearch;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @Entity com.jxntv.gvideo.user.api.entity.UgcVestRelation
 */
@Repository
public interface UgcVestRelationMapper extends BaseMapper<UgcVestRelation> {

    Page<UgcVestDTO> listCustom(@Param("search") UgcVestSearch search, @Param("page") Page<Object> objectPage);
}




