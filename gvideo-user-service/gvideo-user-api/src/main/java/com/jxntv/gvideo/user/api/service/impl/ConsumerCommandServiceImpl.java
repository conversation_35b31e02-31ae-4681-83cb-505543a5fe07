package com.jxntv.gvideo.user.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.api.entity.ConsumerCommand;
import com.jxntv.gvideo.user.api.entity.ConsumerCommandMark;
import com.jxntv.gvideo.user.api.repository.ConsumerCommandMapper;
import com.jxntv.gvideo.user.api.repository.ConsumerCommandMarkMapper;
import com.jxntv.gvideo.user.api.service.ConsumerCommandService;
import com.jxntv.gvideo.user.client.dto.ConsumerCommandMarkDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class ConsumerCommandServiceImpl extends ServiceImpl<ConsumerCommandMapper, ConsumerCommand> implements ConsumerCommandService {

    @Resource
    private ConsumerCommandMarkMapper consumerCommandMarkMapper;


    @Override
    public ConsumerCommandMark checkMark(Long commandId, String deviceId) {
        LambdaQueryWrapper<ConsumerCommandMark> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ConsumerCommandMark::getCommandId,commandId);
        queryWrapper.eq(ConsumerCommandMark::getDeviceId,deviceId);
        queryWrapper.orderByAsc(ConsumerCommandMark::getId);
        queryWrapper.last(" limit 1");

        List<ConsumerCommandMark> list = consumerCommandMarkMapper.selectList(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public boolean mark(Long commandId, String deviceId) {
        ConsumerCommandMark mark = checkMark(commandId,deviceId);
        if (Objects.isNull(mark)) {
            mark = new ConsumerCommandMark();
            mark.setCommandId(commandId);
            mark.setDeviceId(deviceId);
            mark.setCreateDate(LocalDateTime.now());
            return consumerCommandMarkMapper.insert(mark) > 0;
        }
        return Boolean.TRUE;
    }

    @Override
    public Result<Boolean> markData(ConsumerCommandMarkDataDTO dto) {
        ConsumerCommandMark mark = checkMark(Long.valueOf(dto.getCommandId()),dto.getDeviceId());

        if (Objects.nonNull(mark)) {
            mark.setExt(dto.getExt());
            mark.setExtType(dto.getExtType());
            consumerCommandMarkMapper.updateById(mark);
        } else {
            mark = new ConsumerCommandMark();
            mark.setCommandId(Long.valueOf(dto.getCommandId()));
            mark.setDeviceId(dto.getDeviceId());
            mark.setCreateDate(LocalDateTime.now());
            mark.setExt(dto.getExt());
            mark.setExtType(dto.getExtType());
            consumerCommandMarkMapper.insert(mark);
        }
        return Result.ok();
    }

    @Override
    public List<ConsumerCommand> listCommandByDeviceId(String deviceId) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String currentDate = LocalDateTime.now().format(dateTimeFormatter);
        return this.baseMapper.selectListByDeviceId(deviceId,currentDate);
    }
}
