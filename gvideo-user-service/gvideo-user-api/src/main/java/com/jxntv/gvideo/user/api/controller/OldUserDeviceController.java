package com.jxntv.gvideo.user.api.controller;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.api.entity.OldUserDevice;
import com.jxntv.gvideo.user.api.service.OldUserDeviceService;
import com.jxntv.gvideo.user.client.OldUserDeviceClient;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/8/10
 * Email: <EMAIL>
 */
@RestController
public class OldUserDeviceController implements OldUserDeviceClient {
    @Resource
    private OldUserDeviceService oldUserDeviceService;

    @Override
    public Result<Boolean> isOldDevice(String deviceId) {
        OldUserDevice entity = oldUserDeviceService.getByDeviceId(deviceId);
        return Result.ok(Objects.nonNull(entity));
    }
}
