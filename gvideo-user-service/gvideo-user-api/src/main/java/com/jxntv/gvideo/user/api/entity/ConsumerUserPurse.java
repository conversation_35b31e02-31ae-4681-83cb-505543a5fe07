package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * consumer_user_purse C端用户钱包
 * <AUTHOR>
 */
@Data
public class ConsumerUserPurse implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 今视频唯一ID
     */
    private Long jid;

    /**
     * 状态 0：未启用 1：已启用
     */
    private Integer status;

    /**
     * 钱包余额
     */
    private BigDecimal balance;

    /**
     * 微信 openid
     */
    private String wxOpenid;

    /**
     * 微信昵称
     */
    private String wxNickname;

    /**
     * 微信头像
     */
    private String wxHeadImgUrl;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 版本号
     */
    @Version
    private Integer version;

    private static final long serialVersionUID = 1L;
}