package com.jxntv.gvideo.user.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.user.api.entity.ConsumerAvatar;

import java.util.List;

/**
 *
 * Created on 2020-05-21
 */
public interface ConsumerAvatarService extends IService<ConsumerAvatar> {

    Boolean batchPass(List<Long> ids, String username);

    Boolean batchRefuse(List<Long> ids, String username);

    Boolean batchRefuseV2(List<Long> ids, Integer code, String reason, String username);

    Integer getAuditingAvatarCount();

    boolean effect(Long id);

    boolean updateProcedureId(Long id, Long procedureId);
}
