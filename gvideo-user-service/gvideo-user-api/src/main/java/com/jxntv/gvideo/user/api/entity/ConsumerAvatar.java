package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-05-20
 */
@Data
public class ConsumerAvatar {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 今视频ID
     */
    private Long jid;
    /**
     * 头像文件ID
     */
    private String avatar;
    /**
     * 原头像文件ID
     */
    private String avatarOld;
    /**
     * 当前处于阶段：0待机身1待人审
     */
    private Integer phase;
    /**
     * 审核状态
     */
    private Integer checkstatus;
    /**
     * 审核人ID
     */
    private String auditUserId;
    /**
     * 审核人名
     */
    private String auditUserName;
    /**
     * 用户创建时间
     */
    private Date createDate;
    /**
     * 用户更新时间
     */
    private Date auditDate;
    /**
     * 机审
     */
    private Integer aiAuditState;
    /**
     * 流程节点id
     */
    private Long procedureId;

    private Integer refuseCode;

    private String refuseNote;
}
