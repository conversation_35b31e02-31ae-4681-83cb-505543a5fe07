package com.jxntv.gvideo.user.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.user.api.entity.OldUserDevice;
import com.jxntv.gvideo.user.api.repository.OldUserDeviceMapper;
import com.jxntv.gvideo.user.api.service.OldUserDeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/8/10
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class OldUserDeviceServiceImpl extends ServiceImpl<OldUserDeviceMapper, OldUserDevice> implements OldUserDeviceService {
    @Override
    public OldUserDevice getByDeviceId(String deviceId) {
        LambdaQueryWrapper<OldUserDevice> deviceQuery = Wrappers.lambdaQuery();
        deviceQuery.eq(OldUserDevice::getDeviceId, deviceId);
        return this.getOne(deviceQuery);
    }
}
