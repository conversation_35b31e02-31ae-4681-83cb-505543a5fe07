package com.jxntv.gvideo.user.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.binarywang.wxpay.bean.entpay.EntPayResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBatchDetailResult;
import com.github.binarywang.wxpay.bean.transfer.TransferBatchesResult;
import com.jxntv.gvideo.user.api.entity.ConsumerUserPurseRecord;
import com.jxntv.gvideo.user.client.dto.*;

import java.math.BigDecimal;

/**
 * C端用户钱包记录业务
 *
 * <AUTHOR>
 * @date 2022-02-24 11:24:14
 */
public interface ConsumerUserPurseRecordService extends IService<ConsumerUserPurseRecord> {

    /**
     *  保存C端用户发放奖励
     * @param dto
     * @return
     */
    boolean giveReward(ConsumerGiveRewardDTO dto);

    /**
     *  保存C端用户提现申请信息
     * @param dto
     * @return
     */
    boolean saveWithdrawInfo(ConsumerPurseWithdrawDTO dto);

    /**
     *  保存C端用户提现申请审核信息
     * @param dto
     * @return
     */
    boolean saveWithdrawAuditInfo(ConsumerPurseWithdrawAuditDTO dto);

    /**
     *  保存C端用户提现失败，运营线下人工转账信息
     * @param dto
     * @return
     */
    boolean saveTransferArtificial(ConsumerPurseTransferArtificialDTO dto);

    /**
     *  保存微信打款信息
     * @param entity
     * @param payResult
     * @param errorMsg
     * @return
     */
    boolean saveMerchantTransfers(ConsumerUserPurseRecord entity,EntPayResult payResult,String errorMsg) ;


    /**
     *  保存商家转账到零钱信息
     * @param entity
     * @param payResult
     * @param errorMsg
     * @return
     */
    boolean saveTransferBatches(ConsumerUserPurseRecord entity, TransferBatchesResult payResult, String errorMsg) ;


    /**
     *  保存商家转账到零钱结果信息
     * @param entity
     * @param payResult
     * @param errorMsg
     * @return
     */
    boolean saveTransferBatchDetail(ConsumerUserPurseRecord entity, TransferBatchDetailResult payResult, String errorMsg) ;

    /**
     *  查询提现金额
     * @param dto
     * @return
     */
    BigDecimal queryWithdrawAmount(ConsumerPurseWithdrawAmountStatisticsDTO dto);
}

