package com.jxntv.gvideo.user.api.event.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.api.entity.PendantGrant;
import com.jxntv.gvideo.user.api.event.UserPendantCacheEvent;
import com.jxntv.gvideo.user.api.service.PendantGrantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 推送钱包消息
 * @date 2022/03/03 16:55
 */
@Component
@Slf4j
public class UserPendantCacheListener {

    @Resource
    private PendantGrantService pendantGrantService;

    @Transactional(rollbackFor = Exception.class)
    @EventListener
    @Async
    public void onEvent(UserPendantCacheEvent event) {
        log.info("更新用户挂件信息：" + JsonUtils.toJson(event));

        if(!CollectionUtils.isEmpty(event.getJids())){
            event.getJids().forEach(jid -> {
                pendantGrantService.queryAndCacheWearInfo(jid,event.getType());
            });
        }
        if (Objects.nonNull(event.getPendantId())){
            LambdaQueryWrapper<PendantGrant> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(PendantGrant::getJid);
            queryWrapper.eq(PendantGrant::getPendantId,event.getPendantId());
            queryWrapper.eq(PendantGrant::getStatus,0);
            queryWrapper.eq(PendantGrant::getWearFlag,1);
            queryWrapper.eq(PendantGrant::getDelFlag,0);
            this.pendantGrantService.list(queryWrapper).forEach(item -> {
                pendantGrantService.queryAndCacheWearInfo(item.getJid(),event.getType());
            });
        }

    }
}
