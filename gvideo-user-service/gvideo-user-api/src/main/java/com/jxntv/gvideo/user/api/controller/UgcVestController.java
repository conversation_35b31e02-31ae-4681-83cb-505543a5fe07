package com.jxntv.gvideo.user.api.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.common.utils.DesensitizedUtil;
import com.jxntv.gvideo.user.api.converter.UgcVestConverter;
import com.jxntv.gvideo.user.api.entity.SysUser;
import com.jxntv.gvideo.user.api.entity.UgcVestGroup;
import com.jxntv.gvideo.user.api.entity.UgcVestManage;
import com.jxntv.gvideo.user.api.entity.UgcVestRelation;
import com.jxntv.gvideo.user.api.service.UgcVestGroupService;
import com.jxntv.gvideo.user.api.service.UgcVestManageService;
import com.jxntv.gvideo.user.api.service.UgcVestRelationService;
import com.jxntv.gvideo.user.api.service.SysUserService;
import com.jxntv.gvideo.user.api.utils.PageUtils;
import com.jxntv.gvideo.user.client.UgcVestClient;
import com.jxntv.gvideo.user.client.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2021/8/27.
 */
@RestController
public class UgcVestController implements UgcVestClient {
    @Autowired
    private UgcVestGroupService groupService;
    @Autowired
    private UgcVestManageService manageService;
    @Autowired
    private UgcVestRelationService relationService;
    @Autowired
    private UgcVestConverter ugcVestConverter;
    @Autowired
    private SysUserService sysUserService;

    @Override
    public Result<PageDTO<UgcVestGroupDTO>> groupList(UgcVestGroupSearch search) {
        String name = search.getName();
        Long tenantId = search.getTenantId();
        LambdaQueryWrapper<UgcVestGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(name), UgcVestGroup::getName, name);
        queryWrapper.eq(Objects.nonNull(tenantId), UgcVestGroup::getTenantId, tenantId);
        queryWrapper.orderByDesc(UgcVestGroup::getCreateTime);
        Page<UgcVestGroup> pageRst = groupService.page(new Page<>(search.getCurrent(), search.getSize()), queryWrapper);
        return Result.ok(PageUtils.pageOf(pageRst, item -> ugcVestConverter.convertGroupDTO(item, search.getLoginUserId())));
    }

    @Override
    public Result<PageDTO<UgcVestDTO>> list(UgcVestSearch search) {
        Page<UgcVestDTO> pageRst = relationService.listCustom(search);
        return Result.ok(PageUtils.pageOf(pageRst, a -> a));
    }

    @Override
    public Result<Void> saveOrUpdateGroup(UgcVestGroupPost post) {
        UgcVestGroup vestGroup = new UgcVestGroup();
        vestGroup.setId(post.getId());
        vestGroup.setName(post.getName());
        vestGroup.setTenantId(post.getTenantId());
        vestGroup.setCreator(post.getCreator());
        vestGroup.setCreateTime(LocalDateTime.now());
        return groupService.saveOrUpdateGroup(vestGroup);
    }

    @Override
    public Result<Void> ugcVestLink(UgcVestLinkPost post) {
        String mobile = post.getMobile();
        Long vestGroupId = post.getVestGroupId();
        int userCount = relationService.count(Wrappers.<UgcVestRelation>lambdaQuery().eq(UgcVestRelation::getMobile, AESUtil.AESCBCEncode(mobile))
                .eq(UgcVestRelation::getVestGroupId, vestGroupId));
        if (userCount > 0) {
            return Result.fail("用户已关联");
        }
        UgcVestRelation relation = new UgcVestRelation();
        relation.setCreateTime(LocalDateTime.now());
        relation.setCreator(post.getCreator());
        relation.setJid(post.getJid());
        relation.setVestGroupId(vestGroupId);
        relation.setMobile(mobile);
        relation.setMaskMobile(DesensitizedUtil.desensitizedPhone(mobile));
        relationService.save(relation);
        return Result.ok();
    }

    @Override
    public Result<PageDTO<UgcVestManageDTO>> manageList(UgcVestManageSearch search) {
        Page<UgcVestManageDTO> pageRst = manageService.listCustom(search);
        return Result.ok(PageUtils.pageOf(pageRst, a -> a));
    }

    @Override
    public Result<Void> manageSave(UgcVestManagePost post) {
        List<Long> ids = post.getIds();
        Long vestGroupId = post.getVestGroupId();
        List<Long> existIds = manageService.list(Wrappers.<UgcVestManage>lambdaQuery().eq(UgcVestManage::getVestGroupId, vestGroupId))
                .stream().map(UgcVestManage::getSysUserId).collect(Collectors.toList());
        //过滤重复管理员
        ids = ids.stream().filter(item -> !existIds.contains(item)).collect(Collectors.toList());
        LocalDateTime now = LocalDateTime.now();
        Long creator = post.getCreator();

        manageService.saveBatch(ids.stream().map(id -> {
            SysUser user = sysUserService.getById(id);
            if (Objects.isNull(user)) {
                return null;
            }

            UgcVestManage manage = new UgcVestManage();
            manage.setVestGroupId(vestGroupId);
            manage.setCreateTime(now);
            manage.setCreator(creator);
            manage.setSysUserId(id);
            return manage;
        }).filter(Objects::nonNull).collect(Collectors.toList()));
        return Result.ok();
    }

    @Override
    public Result<Void> groupDelete(Long id) {
        groupService.removeById(id);
        return Result.ok();
    }

    @Override
    public Result<Void> ugcDelete(Long id) {
        relationService.removeById(id);
        return Result.ok();
    }

    @Override
    public Result<Void> manageDelete(Long id) {
        manageService.removeById(id);
        return Result.ok();
    }

    @Override
    public Result<Void> check(Long jid, Long vestGroupId) {
        int count = relationService.count(Wrappers.<UgcVestRelation>lambdaQuery().eq(UgcVestRelation::getJid, jid)
                .eq(UgcVestRelation::getVestGroupId, vestGroupId));
        if (count == 0) {
            return Result.fail("当前用户不在马甲组内");
        }
        return Result.ok();
    }

    @Override
    public Result<Boolean> queryByJid(Long jid) {
        boolean exist = relationService.count(Wrappers.<UgcVestRelation>lambdaQuery().eq(UgcVestRelation::getJid, jid)) > 0;
        return Result.ok(exist);
    }

    @Override
    public Result<Map<Long, Boolean>> queryByJids(List<Long> jids) {
        if (CollectionUtils.isEmpty(jids)) {
            return Result.ok(Collections.EMPTY_MAP);
        }
        LambdaQueryWrapper<UgcVestRelation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UgcVestRelation::getJid, jids);
        List<UgcVestRelation> vestRelationList = relationService.list(queryWrapper);
        Map<Long, List<UgcVestRelation>> vestRelationMap = vestRelationList.stream().collect(Collectors.groupingBy(UgcVestRelation::getJid));
        Map<Long, Boolean> result = new HashMap<>(jids.size());
        jids.forEach(jid -> {
            if (CollectionUtils.isEmpty(vestRelationMap.get(jid))) {
                result.put(jid, false);
            } else {
                result.put(jid, true);
            }
        });
        return Result.ok(result);
    }
}
