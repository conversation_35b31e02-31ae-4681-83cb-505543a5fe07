package com.jxntv.gvideo.user.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class ConsumerChannelTab {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 渠道名称
     */
    private String name;

    /**
     * open install id
     */
    private Long openInstallId;

    /**
     * open install 名称
     */
    private String openInstallName;

    /**
     * open install 分组名称
     */
    private String groupName;

    /**
     * 默认首页tab(1-新闻，2-推荐)
     */
    private Long tabId;

}
