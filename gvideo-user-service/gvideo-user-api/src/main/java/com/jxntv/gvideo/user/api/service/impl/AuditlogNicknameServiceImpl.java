package com.jxntv.gvideo.user.api.service.impl;

import java.util.Date;

import com.jxntv.gvideo.user.api.entity.AuditlogNickname;
import com.jxntv.gvideo.user.api.repository.AuditlogNicknameMapper;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.user.api.service.AuditlogNicknameService;

/**
 *
 * Created on 2020-01-19
 */
@Service
public class AuditlogNicknameServiceImpl extends ServiceImpl<AuditlogNicknameMapper, AuditlogNickname> implements
        AuditlogNicknameService {

    @Override
    public Page<AuditlogNickname> getAuditlogNicknames(Page<AuditlogNickname> page, Date createStartDate, Date createEndDate,
            Date auditStartDate, Date auditEndDate, Integer checkStatus, String adminUser, String consumerUser,Integer aiAuditState){
        return page.setRecords(this.baseMapper.getAuditLogNames(page, createStartDate, createEndDate, auditStartDate,
                auditEndDate, checkStatus, adminUser, consumerUser,aiAuditState));
    }

}
