package com.jxntv.gvideo.user.api.client;

import com.jxntv.gvideo.media.client.MediaResourceRewardClient;
import com.jxntv.gvideo.user.api.client.fallback.MediaResourceRewardClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(name = "media-service", contextId = "media-resource-reward", fallbackFactory = MediaResourceRewardClientFallback.class)
public interface MediaResourceRewardService extends MediaResourceRewardClient {
}
