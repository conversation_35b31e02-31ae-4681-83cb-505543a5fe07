<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.user.api.repository.CertificationMapper">


    <select id="queryCertificationByLive" resultType="com.jxntv.gvideo.user.api.entity.Certification">
        SELECT * FROM certification
        WHERE id IN (SELECT DISTINCT certification_id FROM live_broadcast UNION SELECT DISTINCT certification_id FROM interactive_broadcast);
    </select>

</mapper>