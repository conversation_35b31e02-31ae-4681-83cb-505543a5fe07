<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.user.api.repository.SysTenantMapper">

    <select id="getUserTenants" resultType="com.jxntv.gvideo.user.api.entity.SysTenant">
        select t.*
        from sys_user u,
             sys_tenant t,
             sys_tenant_user tu
        where u.id = tu.user_id
          and t.id = tu.tenant_id
          and u.username = #{username}
          and t.status = 1
          and tu.del_flag = 0;
    </select>
    <select id="getByUserId" resultType="com.jxntv.gvideo.user.client.dto.SysTenantDTO">
        select st.*
        from sys_tenant_user stu,
             sys_tenant st
        where stu.tenant_id = st.id
          and stu.is_primary = 1
          and stu.del_flag = 0
          and st.status = 1
          and stu.user_id = #{sysUserId}
        limit 1
    </select>

    <update id="updateSelfPrimary">
        update sys_tenant_user
        set is_primary=0
        where user_id in (select id from sys_user where username = #{username})
    </update>

    <update id="updatePrimary">
        update sys_tenant_user
        set is_primary=1
        where tenant_id = #{tenantId}
          and user_id in (select id from sys_user where username = #{username})
    </update>

</mapper>