<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.user.api.repository.ConsumerNicknameMapper">

    <select id="getAuditingNames" resultType="com.jxntv.gvideo.user.api.entity.ConsumerNickname">
        select au.*, cu.mobile, cu.status from consumer_nickname au, consumer_user cu where au.jid=cu.jid and au.`phase` = 1
        <if test="createStartDate!=null">
            and au.create_date <![CDATA[ >= ]]> #{createStartDate}
        </if>
        <if test="createEndDate!=null">
            and au.create_date <![CDATA[ <= ]]> #{createEndDate}
        </if>
        order by au.create_date desc
    </select>

    <select id="listAuditingPage" resultType="java.lang.Long">
        SELECT DISTINCT t.jid
        from (
            SELECT ca.jid, ca.create_date from consumer_avatar ca WHERE ca.phase = 1
            <if test="createStartDate!=null">
                and ca.create_date <![CDATA[ >= ]]> #{createStartDate}
            </if>
            <if test="createEndDate!=null">
                and ca.create_date <![CDATA[ <= ]]> #{createEndDate}
            </if>
            UNION ALL
            SELECT ci.jid, ci.create_date from consumer_info ci WHERE ci.phase = 1
            <if test="createStartDate!=null">
                and ci.create_date <![CDATA[ >= ]]> #{createStartDate}
            </if>
            <if test="createEndDate!=null">
                and ci.create_date <![CDATA[ <= ]]> #{createEndDate}
            </if>
            UNION ALL
            SELECT cn.jid, cn.create_date from consumer_nickname cn WHERE cn.phase = 1
            <if test="createStartDate!=null">
                and cn.create_date <![CDATA[ >= ]]> #{createStartDate}
            </if>
            <if test="createEndDate!=null">
                and cn.create_date <![CDATA[ <= ]]> #{createEndDate}
            </if>
        ) t
        ORDER BY t.create_date desc
    </select>
    
    <select id="listAuditing" resultType="com.jxntv.gvideo.user.client.dto.ConsumerAuditDTO">
        SELECT
            cu.id,
            cu.jid,
            cu.mobile,
            cu.nickname as cur_nickname,
            cn.id AS nickname_id,
            cn.nickname,
            cn.nickname_old,
            cn.create_date AS nickname_create_date,
            cn.checkstatus AS nicknameCheckStatus,
            cn.phase  AS nickname_phase,
            cn.procedure_id AS nickname_procedure_id,
            cn.ai_audit_state AS nicknameAiAuditState,
            ca.id AS avatar_id,
            ca.avatar,
            ca.avatar_old,
            ca.create_date AS avatar_create_date,
            ca.checkstatus AS avatarCheckStatus,
            ca.ai_audit_state as avatarAiAuditState,
            ca.procedure_id AS avatar_procedure_id,
            ca.phase AS avatar_phase,
            ci.id AS info_id,
            ci.info,
            ci.info_old,
            ci.create_date AS info_create_date,
            ci.checkstatus AS infoCheckStatus,
            ci.ai_audit_state AS introAiAuditState,
            ci.procedure_id AS info_procedure_id,
            ci.phase AS info_phase
        FROM
        consumer_user cu
        LEFT JOIN consumer_nickname cn ON cu.jid = cn.jid
        LEFT JOIN consumer_avatar ca ON cu.jid = ca.jid
        LEFT JOIN consumer_info ci ON cu.jid = ci.jid
        WHERE cu.jid in
        <foreach collection="jidList" item="jid" open="(" close=")" separator=",">
            #{jid}
        </foreach>
        ORDER BY field
        <foreach collection="jidList" item="jid" open="( cu.jid," close=")" separator=",">
            #{jid}
        </foreach>
    </select>

    <select id="getAuditingCount" resultType="java.lang.Integer">
        SELECT
        	COUNT(au.id)
        FROM
        	consumer_nickname au,
        	consumer_user cu
        WHERE
        	au.jid = cu.jid
        AND au.`phase` = 1
    </select>

</mapper>