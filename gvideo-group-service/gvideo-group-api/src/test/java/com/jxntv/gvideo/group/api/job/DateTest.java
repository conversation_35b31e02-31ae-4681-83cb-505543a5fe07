package com.jxntv.gvideo.group.api.job;

import cn.hutool.core.date.DateUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 * @date 2022/5/13
 **/
public class DateTest {


    public static void main(String[] arg) {

//        int day = (int) LocalDate.of(2022, 6, 3).until(LocalDate.of(2022, 6, 8), ChronoUnit.DAYS);
//        System.err.println(day);

        System.err.println(DateUtil.format(LocalDateTime.now(),"yyyy-MM-dd 19:00:00"));
    }
}
