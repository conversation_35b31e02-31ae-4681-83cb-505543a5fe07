package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.group.api.converter.MentorConverter;
import com.jxntv.gvideo.group.api.entity.mentor.GroupFreeMentor;
import com.jxntv.gvideo.group.api.service.GroupFreeMentorService;
import com.jxntv.gvideo.group.api.service.MentorInfoService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.MentorInfoClient;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorEditDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorSearchDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.MentorDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class MentorInfoController implements MentorInfoClient {

    @Resource
    private MentorInfoService mentorInfoService;
    @Resource
    private GroupFreeMentorService groupFreeMentorService;
    @Resource
    private MentorConverter mentorConverter;

    @Override
    public Result<GroupFreeMentorDTO> addFreeMentor(GroupFreeMentorEditDTO dto) {
        GroupFreeMentor mentor = groupFreeMentorService.getByComponentIdAndJid(dto.getComponentId(), dto.getJid());
        if (Objects.nonNull(mentor)) {
            return Result.fail("导师已存在");
        }

        GroupFreeMentor entity = groupFreeMentorService.create(dto);
        return Result.ok(mentorConverter.convert(entity));
    }

    @Override
    public Result<Boolean> deleteFreeMentor(Long componentId, Long jid) {
        GroupFreeMentor mentor = groupFreeMentorService.getByComponentIdAndJid(componentId, jid);
        if (Objects.isNull(mentor)) {
            return Result.fail("导师不存在");
        } else {
            boolean result = groupFreeMentorService.removeById(mentor.getId());
            return Result.ok(result);
        }
    }

    @Override
    public Result<GroupFreeMentorDTO> getById(Long mentorId) {
        GroupFreeMentor mentor = groupFreeMentorService.getById(mentorId);
        AssertUtil.notNull(mentor, CodeMessage.NOT_FOUND);
        return Result.ok(mentorConverter.convert(mentor));
    }

    @Override
    public Result<GroupFreeMentorDTO> getByJid(Long mentorJid, Long groupId) {
        GroupFreeMentor mentor = groupFreeMentorService.getOne(Wrappers.<GroupFreeMentor>lambdaQuery()
                .eq(GroupFreeMentor::getJid, mentorJid)
                .eq(GroupFreeMentor::getGroupId, groupId));
        AssertUtil.notNull(mentor, CodeMessage.NOT_FOUND);
        return Result.ok(mentorConverter.convert(mentor));
    }

    @Override
    public Result<PageDTO<GroupFreeMentorDTO>> searchFreeMentor(GroupFreeMentorSearchDTO searchDTO) {
        Page<GroupFreeMentor> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());

        LambdaQueryWrapper<GroupFreeMentor> freeMentorQuery = Wrappers.lambdaQuery();
        freeMentorQuery.eq(Objects.nonNull(searchDTO.getGroupId()), GroupFreeMentor::getGroupId, searchDTO.getGroupId());
        freeMentorQuery.eq(Objects.nonNull(searchDTO.getComponentId()), GroupFreeMentor::getComponentId, searchDTO.getComponentId());
        freeMentorQuery.eq(Objects.nonNull(searchDTO.getMentorJid()), GroupFreeMentor::getJid, searchDTO.getMentorJid());
        freeMentorQuery.eq(Objects.nonNull(searchDTO.getGroupId()), GroupFreeMentor::getGroupId, searchDTO.getGroupId());

        if (!CollectionUtils.isEmpty(searchDTO.getOperator())) {
            freeMentorQuery.in(GroupFreeMentor::getCreateUserId, searchDTO.getOperator());
        }

        if (Objects.nonNull(searchDTO.getStartTime()) && Objects.nonNull(searchDTO.getEndTime())) {
            freeMentorQuery.between(GroupFreeMentor::getUpdateTime, searchDTO.getStartTime(), searchDTO.getEndTime());
        }

        if ("weights".equals(searchDTO.getOrderBy())) {
            if(Boolean.TRUE.equals(searchDTO.getAsc())){
                freeMentorQuery.orderByAsc(GroupFreeMentor::getWeights).orderByAsc(GroupFreeMentor::getJid);
            }else{
                freeMentorQuery.orderByDesc(GroupFreeMentor::getWeights).orderByAsc(GroupFreeMentor::getJid);
            }

        }

        if ("answerTimes".equals(searchDTO.getOrderBy())) {
            freeMentorQuery.orderByDesc(GroupFreeMentor::getAnswerTimes).orderByDesc(GroupFreeMentor::getWeights);
        }


        Page<GroupFreeMentor> page = groupFreeMentorService.page(pageRequest, freeMentorQuery);
        return Result.ok(PageUtils.pageOf(page, mentorConverter::convert));
    }

    @Override
    public Result<GroupFreeMentorDTO> updateFreeMentor(GroupFreeMentorEditDTO editDTO) {
        GroupFreeMentor mentor = groupFreeMentorService.getByComponentIdAndJid(editDTO.getComponentId(), editDTO.getJid());
        if (Objects.isNull(mentor)) {
            return Result.fail("导师不存在");
        } else {
            GroupFreeMentor entity = groupFreeMentorService.updateById(mentor.getId(), editDTO);
            return Result.ok(mentorConverter.convert(entity));
        }
    }

    @Override
    public Result<List<GroupFreeMentorDTO>> listMentorByComponentId(Long componentId) {
        List<GroupFreeMentor> list = groupFreeMentorService.listByComponentId(componentId);
        return Result.ok(list.stream().map(mentorConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<Boolean> deleteFreeMentorByJid(Long jid) {
        return Result.ok(groupFreeMentorService.deleteFreeMentorByJid(jid));
    }

    @Override
    public Result<Boolean> queryIsMentorByJid(Long jid) {
        return Result.ok(groupFreeMentorService.queryIsMentorByJid(jid));
    }

    @Override
    public Result<Long> addMentor(MentorDTO dto) {
        return Result.ok(mentorInfoService.addMentorInfo(dto));
    }


    @Override
    public Result<Boolean> updateMentorByJid(Long jid, MentorDTO dto) {
        Boolean result = mentorInfoService.updateByJid(jid, dto);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> deleteMentorByJid(Long jid) {
        Boolean result = mentorInfoService.deleteByJid(jid);
        return Result.ok(result);
    }

    @Override
    public Result<List<Long>> searchFreeMentorJidList() {
        return Result.ok(mentorInfoService.searchFreeMentorJidList());
    }

}
