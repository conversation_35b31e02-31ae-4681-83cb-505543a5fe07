package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 圈子组件表
 * @date 2021/06/10 16:31
 */
@TableName(value = "group_gather")
@Data
public class GroupGather implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 圈子ID
     */
    private Long groupId;
    /**
     * 组件名称
     */
    private String name;
    /**
     * 类型：1-话题合集、2-爆料、3-问答、4-广播、5-节目合集、6-弹窗
     */
    private Integer type;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 默认播放标记:0-1
     */
    private Integer defaultDisplay;
    /**
     * 状态：0-禁用、1-启用
     */
    private Integer status;

    /**
     * 问答组件样式： 1： 问答广场， 2：找老师
     */
    private Integer answerType;

    /**
     * 指定回答标题
     */
    private String answerTitle;

    /**
     * 爆料奖励金额社区/话题信息流显示：0-否、1-是
     */
    private Integer showFlow;

    /**
     * 爆料奖励金额详情页显示：0-否、1-是
     */
    private Integer showDetail;
    /**
     * 组件扩展信息，JSON格式存储
     */
    private String extendInfo;

    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private Date updateDate;
}
