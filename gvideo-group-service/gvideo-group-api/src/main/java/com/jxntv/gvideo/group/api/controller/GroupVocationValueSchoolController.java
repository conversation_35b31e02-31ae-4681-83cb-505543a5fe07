package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.OptionsDTO;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.group.api.converter.GroupVocationValueSchoolConverter;
import com.jxntv.gvideo.group.api.entity.GroupVocationValueSchool;
import com.jxntv.gvideo.group.api.service.GroupVocationValueSchoolService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.GroupVocationValueSchoolClient;
import com.jxntv.gvideo.group.sdk.dto.vocation.GroupVocationValueSchoolDTO;
import com.jxntv.gvideo.group.sdk.dto.vocation.GroupVocationValueSchoolSearchDTO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/7/5
 * Email: <EMAIL>
 */
@RestController
public class GroupVocationValueSchoolController implements GroupVocationValueSchoolClient {

    @Resource
    private GroupVocationValueSchoolService groupVocationValueSchoolService;
    @Resource
    private GroupVocationValueSchoolConverter groupVocationValueSchoolConverter;


    @Override
    public Result<Long> create(GroupVocationValueSchoolDTO dto) {
        Long id = groupVocationValueSchoolService.create(dto);
        return Result.ok(id);
    }

    @Override
    public Result<Boolean> updateById(Long id, GroupVocationValueSchoolDTO dto) {
        boolean result = groupVocationValueSchoolService.updateById(id, dto);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> deleteById(Long id) {
        return Result.ok(groupVocationValueSchoolService.removeById(id));
    }

    @Override
    public Result<GroupVocationValueSchoolDTO> getById(Long id) {
        GroupVocationValueSchool entity = groupVocationValueSchoolService.getById(id);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);
        return Result.ok(groupVocationValueSchoolConverter.convert(entity));
    }

    @Override
    public Result<List<GroupVocationValueSchoolDTO>> listByGroupId(Long groupId) {
        List<GroupVocationValueSchool> list = groupVocationValueSchoolService.listByGroupId(groupId);
        return Result.ok(list.stream().map(groupVocationValueSchoolConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<PageDTO<GroupVocationValueSchoolDTO>> search(GroupVocationValueSchoolSearchDTO searchDTO) {
        Page<GroupVocationValueSchool> page = groupVocationValueSchoolService.page(searchDTO);
        return Result.ok(PageUtils.pageOf(page, groupVocationValueSchoolConverter::convert));
    }

    @Override
    public Result<List<OptionsDTO>> listCityByGroupId(Long groupId) {
        return Result.ok(this.groupVocationValueSchoolService.listCityByGroupId(groupId));
    }

    @Override
    public Result<List<OptionsDTO>> listSchoolByGroupIdAndRegionCode(Long groupId, Long cityId,Long districtId) {
        List<GroupVocationValueSchool> list = groupVocationValueSchoolService.listSchoolByGroupIdAndRegionCode(groupId,cityId,districtId);
        return Result.ok(list.stream().map(e->{
            OptionsDTO dto = new OptionsDTO();
            dto.setId(e.getId());
            dto.setName(e.getName());
            return dto;
        }).collect(Collectors.toList()));
    }

    @Override
    public Result<PageDTO<GroupVocationValueSchoolDTO>> searchCityRank(GroupVocationValueSchoolSearchDTO searchDTO) {
        IPage<GroupVocationValueSchool> page = groupVocationValueSchoolService.searchCityRank(searchDTO);
        return Result.ok(PageUtils.pageOf(page, groupVocationValueSchoolConverter::convert));
    }
}
