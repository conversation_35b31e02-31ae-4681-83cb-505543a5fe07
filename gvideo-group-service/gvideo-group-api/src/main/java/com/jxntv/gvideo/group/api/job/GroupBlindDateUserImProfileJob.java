package com.jxntv.gvideo.group.api.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.aliyun.sdk.ImClient;
import com.jxntv.gvideo.aliyun.sdk.dto.im.EditUserProfileDTO;
import com.jxntv.gvideo.aliyun.sdk.enums.ImRoleEnum;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUser;
import com.jxntv.gvideo.group.api.service.GroupBlindDateUserService;
import com.jxntv.gvideo.media.client.enums.WhetherEnum;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/23 11:28
 */
@Slf4j
@Component
@RefreshScope
public class GroupBlindDateUserImProfileJob {

    @Resource
    GroupBlindDateUserService groupBlindDateUserService;

    @Resource
    ImClient imClient;

    @XxlJob("groupBlindDateUserImProfileJob")
    public ReturnT<String> groupBlindDateUserImProfileJob() {
        String jid = XxlJobHelper.getJobParam();
        if (StringUtils.isEmpty(jid)) {

            LambdaQueryWrapper<GroupBlindDateUser> query = new LambdaQueryWrapper<>();
            query.select(GroupBlindDateUser::getJid);
            query.eq(GroupBlindDateUser::getImSettingFlag, WhetherEnum.NO.getCode());
            query.orderByAsc(GroupBlindDateUser::getId);
            IPage<GroupBlindDateUser> page = this.groupBlindDateUserService.page(new Page<GroupBlindDateUser>(1, 99), query);
            if (!CollectionUtils.isEmpty(page.getRecords())) {

                List<Long> jids = page.getRecords().stream().map(entity -> {
                    EditUserProfileDTO dto = new EditUserProfileDTO();
                    dto.setAccount(String.valueOf(entity.getJid()));
                    dto.setRoleType(ImRoleEnum.BLIND_DATE.getCode());
                    Boolean result = imClient.editUserProfile(dto).orElse(false);
                    if (Boolean.TRUE.equals(result)) {
                        return entity.getJid();
                    }
                    return null;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                this.groupBlindDateUserService.batchUpdateImSettingFlag(jids);
            }

        }else{
            EditUserProfileDTO dto = new EditUserProfileDTO();
            dto.setAccount(jid);
            dto.setRoleType(ImRoleEnum.BLIND_DATE.getCode());
            Boolean result = imClient.editUserProfile(dto).orElse(false);
            if (Boolean.TRUE.equals(result)){
                this.groupBlindDateUserService.batchUpdateImSettingFlag(Collections.singletonList(Long.parseLong(jid)));
            }
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("groupBlindDateUserImProfileClearJob")
    public ReturnT<String> groupBlindDateUserImProfileClearJob() {
        String param = XxlJobHelper.getJobParam();
        List<String> jidList = Arrays.asList(param.split(","));
        XxlJobHelper.log("放心爱IM角色标识清除开始，param:"+param);

        if (!CollectionUtils.isEmpty(jidList)) {

            List<Long> jids = jidList.stream().map(e -> {
                EditUserProfileDTO dto = new EditUserProfileDTO();
                dto.setAccount(e);
                dto.setRoleType(ImRoleEnum.NORMAL.getCode());
                Boolean result = imClient.editUserProfile(dto).orElse(false);
                if (Boolean.TRUE.equals(result)) {
                    try {
                        return Long.parseLong(e);
                    }catch (Exception exception){
                        return null;
                    }
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            this.groupBlindDateUserService.batchUpdateImSettingFlag(jids);
            XxlJobHelper.log("放心爱IM角色标识清除成功，jidList:"+ JSON.toJSONString(jids));
        }
        return ReturnT.SUCCESS;
    }

}
