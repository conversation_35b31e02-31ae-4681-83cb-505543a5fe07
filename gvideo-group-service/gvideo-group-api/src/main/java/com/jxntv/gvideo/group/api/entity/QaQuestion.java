package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 *  QA问题库
 *  <AUTHOR>
 *  @date 2022/6/13 15:22
 */
@Getter
@Setter
@ToString
@TableName(value = "qa_questions")
public class QaQuestion implements Serializable {
    /**
     * 自增ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 题目组别(0:不限制，10:小学组，20:初中组)
     */
    @TableField(value = "`group`")
    private String group;

    /**
     * 10:选择题，20:对错题
     */
    @TableField(value = "`type`")
    private Integer type;

    /**
     * 问题标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 答案，如A、对
     */
    @TableField(value = "answer")
    private String answer;

    /**
     * 问题回答，用!^隔开
     */
    @TableField(value = "selections")
    private String selections;

    /**
     * 问题解释
     */
    @TableField(value = "explanation")
    private String explanation;

    /**
     * 1:启动,0:禁用
     */
    @TableField(value = "question_status")
    private Integer questionStatus;

    /**
     * 添加时间
     */
    @TableField(value = "date_created")
    private Integer dateCreated;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_GROUP = "group";

    public static final String COL_TYPE = "type";

    public static final String COL_TITLE = "title";

    public static final String COL_ANSWER = "answer";

    public static final String COL_SELECTIONS = "selections";

    public static final String COL_EXPLANATION = "explanation";

    public static final String COL_QUESTION_STATUS = "question_status";

    public static final String COL_DATE_CREATED = "date_created";
}
