package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 圈子用户定向券
 */
@TableName(value = "group_user_orient_coupon")
public class GroupUserOrientCoupon {
    public static final String COL_JID = "jid";
    /**
     * 主键id;主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 社区id
     */
    @TableField(value = "group_id")
    private Integer groupId;

    /**
     * 社区名称;社区名称
     */
    @TableField(value = "group_name")
    private String groupName;

    /**
     * 操作来源;1:定向发券，2:今日聊优惠
     */
    @TableField(value = "`source`")
    private Integer source;

    /**
     * 活动标题
     */
    @TableField(value = "title")
    private String title;

    /**
     * 手机号，逗号分隔
     */
    @TableField(value = "mobiles")
    private String mobiles;

    /**
     * 今视频用户id列表，逗号分隔
     */
    @TableField(value = "jids")
    private String jids;

    /**
     * 今视频昵称列表，逗号分隔
     */
    @TableField(value = "`names`")
    private String names;

    /**
     * 店铺id
     */
    @TableField(value = "shop_id")
    private String shopId;

    /**
     * 店铺名称
     */
    @TableField(value = "shop_name")
    private String shopName;

    /**
     * 优惠券id
     */
    @TableField(value = "coupon_id")
    private String couponId;

    /**
     * 优惠券名称
     */
    @TableField(value = "coupon_name")
    private String couponName;

    /**
     * 优惠券限制日期
     */
    @TableField(value = "coupon_limit_time")
    private String couponLimitTime;

    /**
     * 优惠券金额
     */
    @TableField(value = "coupon_money")
    private String couponMoney;

    /**
     * 发放时间;定时任务根据发放时间执行发放
     */
    @TableField(value = "release_time")
    private Date releaseTime;

    /**
     * 领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 通知文案
     */
    @TableField(value = "notice_content")
    private String noticeContent;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 创建用户id
     */
    @TableField(value = "create_user_id")
    private Long createUserId;

    /**
     * 创建用户名称
     */
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 更新用户id
     */
    @TableField(value = "update_user_id")
    private Long updateUserId;

    /**
     * 更新用户名称
     */
    @TableField(value = "update_user_name")
    private String updateUserName;

    public static final String COL_ID = "id";

    public static final String COL_GROUP_ID = "group_id";

    public static final String COL_GROUP_NAME = "group_name";

    public static final String COL_SOURCE = "source";

    public static final String COL_TITLE = "title";

    public static final String COL_MOBILES = "mobiles";

    public static final String COL_JIDS = "jids";

    public static final String COL_NAMES = "names";

    public static final String COL_SHOP_ID = "shop_id";

    public static final String COL_SHOP_NAME = "shop_name";

    public static final String COL_COUPON_ID = "coupon_id";

    public static final String COL_COUPON_NAME = "coupon_name";

    public static final String COL_COUPON_LIMIT_TIME = "coupon_limit_time";

    public static final String COL_COUPON_MONEY = "coupon_money";

    public static final String COL_RELEASE_TIME = "release_time";

    public static final String COL_STATUS = "status";

    public static final String COL_NOTICE_CONTENT = "notice_content";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_CREATE_USER_ID = "create_user_id";

    public static final String COL_CREATE_USER_NAME = "create_user_name";

    public static final String COL_UPDATE_TIME = "update_time";

    public static final String COL_UPDATE_USER_ID = "update_user_id";

    public static final String COL_UPDATE_USER_NAME = "update_user_name";

    /**
     * 获取主键id;主键id
     *
     * @return id - 主键id;主键id
     */
    public Long getId() {
        return id;
    }

    /**
     * 设置主键id;主键id
     *
     * @param id 主键id;主键id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取社区id
     *
     * @return group_id - 社区id
     */
    public Integer getGroupId() {
        return groupId;
    }

    /**
     * 设置社区id
     *
     * @param groupId 社区id
     */
    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    /**
     * 获取社区名称;社区名称
     *
     * @return group_name - 社区名称;社区名称
     */
    public String getGroupName() {
        return groupName;
    }

    /**
     * 设置社区名称;社区名称
     *
     * @param groupName 社区名称;社区名称
     */
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    /**
     * 获取操作来源;1:定向发券，2:今日聊优惠
     *
     * @return source - 操作来源;1:定向发券，2:今日聊优惠
     */
    public Integer getSource() {
        return source;
    }

    /**
     * 设置操作来源;1:定向发券，2:今日聊优惠
     *
     * @param source 操作来源;1:定向发券，2:今日聊优惠
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * 获取活动标题
     *
     * @return title - 活动标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置活动标题
     *
     * @param title 活动标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取手机号，逗号分隔
     *
     * @return mobiles - 手机号，逗号分隔
     */
    public String getMobiles() {
        return mobiles;
    }

    /**
     * 设置手机号，逗号分隔
     *
     * @param mobiles 手机号，逗号分隔
     */
    public void setMobiles(String mobiles) {
        this.mobiles = mobiles;
    }

    /**
     * 获取今视频用户id列表，逗号分隔
     *
     * @return jids - 今视频用户id列表，逗号分隔
     */
    public String getJids() {
        return jids;
    }

    /**
     * 设置今视频用户id列表，逗号分隔
     *
     * @param jids 今视频用户id列表，逗号分隔
     */
    public void setJids(String jids) {
        this.jids = jids;
    }

    /**
     * 获取今视频昵称列表，逗号分隔
     *
     * @return names - 今视频昵称列表，逗号分隔
     */
    public String getNames() {
        return names;
    }

    /**
     * 设置今视频昵称列表，逗号分隔
     *
     * @param names 今视频昵称列表，逗号分隔
     */
    public void setNames(String names) {
        this.names = names;
    }

    /**
     * 获取店铺id
     *
     * @return shop_id - 店铺id
     */
    public String getShopId() {
        return shopId;
    }

    /**
     * 设置店铺id
     *
     * @param shopId 店铺id
     */
    public void setShopId(String shopId) {
        this.shopId = shopId;
    }

    /**
     * 获取店铺名称
     *
     * @return shop_name - 店铺名称
     */
    public String getShopName() {
        return shopName;
    }

    /**
     * 设置店铺名称
     *
     * @param shopName 店铺名称
     */
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    /**
     * 获取优惠券id
     *
     * @return coupon_id - 优惠券id
     */
    public String getCouponId() {
        return couponId;
    }

    /**
     * 设置优惠券id
     *
     * @param couponId 优惠券id
     */
    public void setCouponId(String couponId) {
        this.couponId = couponId;
    }

    /**
     * 获取优惠券名称
     *
     * @return coupon_name - 优惠券名称
     */
    public String getCouponName() {
        return couponName;
    }

    /**
     * 设置优惠券名称
     *
     * @param couponName 优惠券名称
     */
    public void setCouponName(String couponName) {
        this.couponName = couponName;
    }

    /**
     * 获取优惠券限制日期
     *
     * @return coupon_limit_time - 优惠券限制日期
     */
    public String getCouponLimitTime() {
        return couponLimitTime;
    }

    /**
     * 设置优惠券限制日期
     *
     * @param couponLimitTime 优惠券限制日期
     */
    public void setCouponLimitTime(String couponLimitTime) {
        this.couponLimitTime = couponLimitTime;
    }

    /**
     * 获取优惠券金额
     *
     * @return coupon_money - 优惠券金额
     */
    public String getCouponMoney() {
        return couponMoney;
    }

    /**
     * 设置优惠券金额
     *
     * @param couponMoney 优惠券金额
     */
    public void setCouponMoney(String couponMoney) {
        this.couponMoney = couponMoney;
    }

    /**
     * 获取发放时间;定时任务根据发放时间执行发放
     *
     * @return release_time - 发放时间;定时任务根据发放时间执行发放
     */
    public Date getReleaseTime() {
        return releaseTime;
    }

    /**
     * 设置发放时间;定时任务根据发放时间执行发放
     *
     * @param releaseTime 发放时间;定时任务根据发放时间执行发放
     */
    public void setReleaseTime(Date releaseTime) {
        this.releaseTime = releaseTime;
    }

    /**
     * 获取领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     *
     * @return status - 领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     *
     * @param status 领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取通知文案
     *
     * @return notice_content - 通知文案
     */
    public String getNoticeContent() {
        return noticeContent;
    }

    /**
     * 设置通知文案
     *
     * @param noticeContent 通知文案
     */
    public void setNoticeContent(String noticeContent) {
        this.noticeContent = noticeContent;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取创建用户id
     *
     * @return create_user_id - 创建用户id
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建用户id
     *
     * @param createUserId 创建用户id
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取创建用户名称
     *
     * @return create_user_name - 创建用户名称
     */
    public String getCreateUserName() {
        return createUserName;
    }

    /**
     * 设置创建用户名称
     *
     * @param createUserName 创建用户名称
     */
    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取更新用户id
     *
     * @return update_user_id - 更新用户id
     */
    public Long getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置更新用户id
     *
     * @param updateUserId 更新用户id
     */
    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取更新用户名称
     *
     * @return update_user_name - 更新用户名称
     */
    public String getUpdateUserName() {
        return updateUserName;
    }

    /**
     * 设置更新用户名称
     *
     * @param updateUserName 更新用户名称
     */
    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }
}
