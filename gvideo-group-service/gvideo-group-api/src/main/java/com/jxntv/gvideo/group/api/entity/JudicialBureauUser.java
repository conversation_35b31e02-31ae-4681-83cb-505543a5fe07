package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: niedamin
 * @Date: 2022/08/09 17:16
 */
@Data
public class JudicialBureauUser {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 用户唯一id
     */
    private Long jid;

    private String commissionNature;

    private Long cityId;

    private Long countyId;

    private String address;

    private String mediationOrganization;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}
