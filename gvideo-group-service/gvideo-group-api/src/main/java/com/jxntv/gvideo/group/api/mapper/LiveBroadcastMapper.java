package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.group.api.entity.LiveBroadcast;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface LiveBroadcastMapper extends BaseMapper<LiveBroadcast> {
    int updateBatch(List<LiveBroadcast> list);

    int batchInsert(@Param("list") List<LiveBroadcast> list);
}
