package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.api.entity.GroupGatherAnswerReminderPopUp;
import com.jxntv.gvideo.group.api.mapper.GroupGatherAnswerReminderPopUpMapper;
import com.jxntv.gvideo.group.api.service.GroupAnswerReminderPopUpService;
import com.jxntv.gvideo.group.sdk.dto.GroupAnswerReminderPopUpDTO;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class GroupAnswerReminderPopUpServiceImpl extends ServiceImpl<GroupGatherAnswerReminderPopUpMapper, GroupGatherAnswerReminderPopUp> implements GroupAnswerReminderPopUpService {

    @Override
    public Result<Long> saveOrUpdateByGatherId(Long id, GroupAnswerReminderPopUpDTO dto) {
        GroupGatherAnswerReminderPopUp groupGatherAnswerReminderPopUp = convert(id, dto);
        LambdaQueryWrapper<GroupGatherAnswerReminderPopUp> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GroupGatherAnswerReminderPopUp::getGatherId, id);
        GroupGatherAnswerReminderPopUp one = this.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
            groupGatherAnswerReminderPopUp.setId(one.getId());
            groupGatherAnswerReminderPopUp.setUpdateDate(dto.getUpdateDate());
            groupGatherAnswerReminderPopUp.setUpdateUserId(dto.getUpdateUserId());
            this.updateById(groupGatherAnswerReminderPopUp);
        } else {
            groupGatherAnswerReminderPopUp.setCreateDate(dto.getCreateDate());
            groupGatherAnswerReminderPopUp.setCreateUserId(dto.getCreateUserId());
            groupGatherAnswerReminderPopUp.setUpdateDate(dto.getCreateDate());
            groupGatherAnswerReminderPopUp.setUpdateUserId(dto.getCreateUserId());
            this.save(groupGatherAnswerReminderPopUp);
        }
        return Result.ok(id);
    }

    @Override
    public Result<GroupAnswerReminderPopUpDTO> getByGroupId(Long groupId) {
        LambdaQueryWrapper<GroupGatherAnswerReminderPopUp> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GroupGatherAnswerReminderPopUp::getGroupId, groupId);
        return Result.ok(convert(this.getOne(queryWrapper)));
    }

    private GroupAnswerReminderPopUpDTO convert(GroupGatherAnswerReminderPopUp entity) {
        GroupAnswerReminderPopUpDTO dto = new GroupAnswerReminderPopUpDTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setAnswerPopUp(entity.getAnswerPopUp());
        dto.setQuestionEntry(entity.getQuestionEntry());
        dto.setWeight(entity.getWeight());
        dto.setGatherId(entity.getGatherId());
        dto.setUpdateDate(entity.getUpdateDate());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setCreateUserId(entity.getCreateUserId());
        return dto;
    }

    private GroupGatherAnswerReminderPopUp convert(Long id, GroupAnswerReminderPopUpDTO dto) {
        GroupGatherAnswerReminderPopUp groupGatherAnswerReminderPopUp = new GroupGatherAnswerReminderPopUp();
        groupGatherAnswerReminderPopUp.setGroupId(dto.getGroupId());
        groupGatherAnswerReminderPopUp.setGatherId(id);
        groupGatherAnswerReminderPopUp.setQuestionEntry(dto.getQuestionEntry());
        groupGatherAnswerReminderPopUp.setWeight(dto.getWeight());
        groupGatherAnswerReminderPopUp.setAnswerPopUp(dto.getAnswerPopUp());
        return groupGatherAnswerReminderPopUp;
    }
}
