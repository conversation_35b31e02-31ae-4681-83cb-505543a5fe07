package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.group.api.entity.mentor.MentorInfoEntity;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface MentorInfoEntityMapper extends BaseMapper<MentorInfoEntity> {

    @Select("<script>" +
            " select DISTINCT(jid) jid from group_free_mentor " +
            "</script>")
    List<Long> searchFreeMentorJidList();
}
