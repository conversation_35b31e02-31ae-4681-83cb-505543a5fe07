package com.jxntv.gvideo.group.api.service.cycle.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.cycle.task.UserCycleJob;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.*;
import com.jxntv.gvideo.group.sdk.enums.user.cycle.UserCycleJobStatus;
import com.jxntv.gvideo.group.sdk.enums.user.cycle.UserCycleJobType;

import java.time.LocalDate;
import java.util.List;


/**
 * 周期任务
 *
 * <AUTHOR>
 * @date 2022/5/13
 **/
public interface UserCycleJobService extends IService<UserCycleJob> {



    /**
     * 操作任务
     *
     * @param userCycleTaskDTO
     * @return 0 无奖励发放  1 奖励发放
     */
    Integer doTask(UserCycleTaskDTO userCycleTaskDTO);
    /**
     * 是否可以可以发放奖励
     *
     * @return
     */
    boolean shouldGrantReward(Long userId,Long groupId);

    /**
     * 查询进度
     *
     * @param userId
     * @param groupId
     * @return first 长度  second 已做的进度
     */
    UserCycleProgressDTO getProgress(Long userId,Long groupId);


    /**
     * 模版方法， 任务类型  1 签到 2 互动任务
     *
     * @return
     */
    UserCycleJobType getJobType();

    /**
     * 发放奖励
     * 增加提问次数
     */

    void grantReward(Long userId,Long groupId);

    default UserCycleJob getLastJob(Long jid, Long groupId) {

        LambdaQueryWrapper<UserCycleJob> jobQuery = Wrappers.lambdaQuery();
        jobQuery.eq(UserCycleJob::getJobType, this.getJobType().getType());
        jobQuery.eq(UserCycleJob::getJid, jid);
        jobQuery.eq(UserCycleJob::getGroupId, groupId);
        jobQuery.orderByDesc(UserCycleJob::getId);
        jobQuery.last(" limit 1 ");

        return this.getOne(jobQuery);
    }

    default Boolean todayMarkStatus(Long jid, Long groupId) {

        LambdaQueryWrapper<UserCycleJob> jobQuery = Wrappers.lambdaQuery();
        jobQuery.eq(UserCycleJob::getJobType, this.getJobType().getType());
        jobQuery.eq(UserCycleJob::getJid, jid);
        jobQuery.eq(UserCycleJob::getGroupId, groupId);
        jobQuery.eq(UserCycleJob::getLastDate, LocalDate.now());

        return this.count(jobQuery) > 0;
    }

    /**
     * 查询用户签到日历信息
     * @param userId
     * @param groupId
     * @return
     */
    List<UserCycleJobCalendarDTO> getUserCycleJobCalendar(Long userId, Long groupId);

    /**
     * 查询用户签到任务状态
     * @param userId
     * @param groupId
     * @return
     */
    UserCycleJobStatus getUserMarkStatus(Long userId, Long groupId);

    /**
     * 查询用户签到抽奖信息
     * @param jid
     * @param groupId
     * @return
     */
    UserMarkDrawInfoDTO getDrawInfo(Long jid, Long groupId);

    /**
     * 补签
     * @param dto
     * @return
     */
    boolean replenishSign(ReplenishSignDTO dto);
}
