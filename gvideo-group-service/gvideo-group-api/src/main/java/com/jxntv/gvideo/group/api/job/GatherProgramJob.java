package com.jxntv.gvideo.group.api.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.group.api.entity.GroupGather;
import com.jxntv.gvideo.group.api.entity.GroupGatherInfo;
import com.jxntv.gvideo.group.api.entity.GroupTopicContent;
import com.jxntv.gvideo.group.api.service.GroupGatherInfoService;
import com.jxntv.gvideo.group.api.service.GroupGatherService;
import com.jxntv.gvideo.group.api.service.GroupTopicContentService;
import com.jxntv.gvideo.group.sdk.dto.GroupLogField;
import com.jxntv.gvideo.group.sdk.dto.TopicMediaStatus;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherStatusEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramSearchDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 栏目组件数据同步
 *
 * <AUTHOR>
 * @date 2022/2/7
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class GatherProgramJob {


    @Resource
    private GroupGatherService groupGatherService;
    @Resource
    private GroupGatherInfoService groupGatherInfoService;
    @Resource
    private GroupTopicContentService groupTopicContentService;
    @Resource
    private TvProgramClient tvProgramClient;
    @Resource
    private MediaResourceClient mediaResourceClient;

    /**
     * 定时拉取关联了栏目的组件
     * 将节目信息和社区组件关联
     */

    @XxlJob("gatherProgramJob")
    public ReturnT<String> execute() {
        log.info("节目组件任务开始...");
        //  查询所有的节目组件
        LambdaQueryWrapper<GroupGather> gatherQuery = Wrappers.lambdaQuery();
        gatherQuery.eq(GroupGather::getType, GroupGatherTypeEnum.PROGRAM_COLLECTION.getCode());
        gatherQuery.eq(GroupGather::getStatus, GroupGatherStatusEnum.ENABLE.getCode());
        List<GroupGather> groupGathers = groupGatherService.list(gatherQuery);

        //  遍历组件填充关联电视内容
        for (GroupGather groupGather : groupGathers) {
            LambdaQueryWrapper<GroupGatherInfo> infoQuery = Wrappers.lambdaQuery();
            infoQuery.eq(GroupGatherInfo::getGatherId, groupGather.getId());
            infoQuery.eq(GroupGatherInfo::getFieldId, GroupLogField.GATHER_LINK_COLUMN.getCode());
            List<GroupGatherInfo> list = groupGatherInfoService.list(infoQuery);

            for (GroupGatherInfo groupGatherInfo : list) {
                //  提取栏目id
                String fieldValue = groupGatherInfo.getFieldValue();
                if (NumberUtils.isNumber(fieldValue)) {
                    List<GroupTopicContent> contentList = new ArrayList<>();
                    Long columnId = Long.valueOf(fieldValue);
                    GroupTopicContent lastContent = getLastContent(groupGather.getId());
                    Long mediaId = Objects.isNull(lastContent) ? null : lastContent.getMediaId();
                    String playDateStart = getPlayDateStart(mediaId);

                    for (int i = 1; ; i++) {

                        TvProgramSearchDTO searchDTO = new TvProgramSearchDTO();
                        searchDTO.setColumnId(columnId);
                        searchDTO.setPlayDateStart(playDateStart);
                        searchDTO.setCurrent(i);
                        searchDTO.setSize(200);
                        searchDTO.setStatus(1);
                        PageDTO<TvProgramDTO> page = tvProgramClient.page(searchDTO).orElseThrow();

                        if (!CollectionUtils.isEmpty(page.getList())) {
                            List<TvProgramDTO> programList = page.getList();
                            if (Objects.nonNull(mediaId)) {
                                programList = programList.stream().filter(e -> e.getMediaId().compareTo(mediaId) > 0).collect(Collectors.toList());
                            }
                            for (TvProgramDTO tvProgramDTO : programList) {

                                MediaResourceDTO mediaResourceDTO = mediaResourceClient.get(tvProgramDTO.getMediaId()).orElseThrow();

                                GroupTopicContent groupContent = new GroupTopicContent();
                                groupContent.setGroupId(groupGather.getGroupId());
                                groupContent.setGatherId(groupGather.getId());
                                groupContent.setMediaId(mediaResourceDTO.getId());
                                groupContent.setMediaReleaseType(mediaResourceDTO.getReleaseType());
                                groupContent.setMediaReleaseId(mediaResourceDTO.getReleaseId());
                                groupContent.setMediaStatus(mediaResourceDTO.getStatus());
                                groupContent.setTopicMediaStatus(TopicMediaStatus.ENABLE);

                                contentList.add(groupContent);

                            }

                        }


                        if (page.getPageNum() * page.getPageSize() >= page.getTotal()) {
                            break;
                        }

                    }

                    //  批量插入关联关系表
                    if (!CollectionUtils.isEmpty(contentList)) {
                        //  按照媒体资源id自增排序
                        contentList = contentList.stream().sorted(Comparator.comparing(GroupTopicContent::getMediaId)).collect(Collectors.toList());

                        groupTopicContentService.saveBatch(contentList);
                    }

                }

            }


        }

        return ReturnT.SUCCESS;
    }

    private GroupTopicContent getLastContent(Long gatherId) {

        //  查找最后一条节目
        LambdaQueryWrapper<GroupTopicContent> gatherContentQuery = Wrappers.lambdaQuery();
        gatherContentQuery.eq(GroupTopicContent::getGatherId, gatherId);
        gatherContentQuery.orderByDesc(GroupTopicContent::getMediaId);
        gatherContentQuery.last(" limit 1");
        return groupTopicContentService.getOne(gatherContentQuery);
    }


    private String getPlayDateStart(Long mediaId) {
        String playDateStart = "2010-01-01";
        //  最后一条内容对应的播放日期
        if (Objects.nonNull(mediaId)) {
            TvProgramDTO tvProgramDTO = tvProgramClient.getByMediaId(mediaId).orElseThrow();
            playDateStart = tvProgramDTO.getPlayDate();
        }

        return playDateStart;
    }


}
