package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.group.api.converter.SchoolGradeStuConverter;
import com.jxntv.gvideo.group.api.entity.GroupGather;
import com.jxntv.gvideo.group.api.entity.SchoolDetail;
import com.jxntv.gvideo.group.api.entity.SchoolGradeStu;
import com.jxntv.gvideo.group.api.mapper.SchoolGradeStuMapper;
import com.jxntv.gvideo.group.api.service.ActivityNameAuditService;
import com.jxntv.gvideo.group.api.service.GroupGatherService;
import com.jxntv.gvideo.group.api.service.SchoolGradePkSchoolDetailService;
import com.jxntv.gvideo.group.api.service.SchoolGradeStuService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.dto.*;
import com.jxntv.gvideo.group.sdk.enums.ActivityTypeEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-06-13 11:32
 * @description
 */
@Slf4j
@Service
public class SchoolGradeStuServiceImpl extends ServiceImpl<SchoolGradeStuMapper, SchoolGradeStu> implements SchoolGradeStuService {

    @Autowired
    private SchoolGradeStuMapper schoolGradeStuMapper;
    @Autowired
    private SchoolGradeStuConverter schoolGradeStuConverter;
    @Autowired
    private SchoolGradePkSchoolDetailService schoolGradePkSchoolDetailService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private GroupGatherService groupGatherService;

    @Resource
    private ConsumerUserClient consumerUserClient;

    @Resource
    private ActivityNameAuditService activityNameAuditService;

    @Override
    public SchoolGradeStuDTO queryStudentBaseInfoById(Long stuID) {
        if (Objects.isNull(stuID)) {
            throw new NullPointerException(CodeMessage.BAD_REQUEST.getMessage());
        }

        SchoolGradeStu schoolGradeStu = this.getById(stuID);
        if (Objects.isNull(schoolGradeStu)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学生不存在");
        }
        return schoolGradeStuConverter.entityConvertDTOBase(schoolGradeStu);
    }

    @Override
    public SchoolGradeStuDTO queryStudentDetailById(Long stuID) {
        SchoolGradeStu schoolGradeStu = this.getById(stuID);
        if (Objects.isNull(schoolGradeStu)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学生不存在");
        }
        SchoolDetail schoolDetail = schoolGradePkSchoolDetailService.getById(schoolGradeStu.getSchoolDetailId());
        SchoolGradeStuDTO stuDTO = schoolGradeStuConverter.entityConvertDTODetail(schoolGradeStu);
        stuDTO.setSchoolCode(schoolDetail.getSchoolCode());
        return stuDTO;
    }

    @Override
    @Transactional
    public Boolean saveSchoolGradeStu(SchoolGradeStuDTO dto) {
        if (Objects.isNull(dto)) {
            throw new NullPointerException(CodeMessage.BAD_REQUEST.getMessage());
        }
        if (Objects.isNull(dto.getJid())) {
            log.error("班级PK大赛学生报名有误，今id不能为空。SchoolGradeStuServiceImpl.saveSchoolGradeStu = {}", dto);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛学生报名有误，今id不能为空");
        }
        log.info("班级PK大赛学生报名信息，SchoolGradeStuServiceImpl.saveSchoolGradeStu 入参= {}", dto);
        SchoolGradeStu isEnroll = this.query().eq("jid", dto.getJid()).one();
        if (Objects.nonNull(isEnroll)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "报名失败，该用户已报名");
        }

        ConsumerUserDTO consumerUserDTO = consumerUserClient.getUserByJid(dto.getJid()).orElse(null);
        if (Objects.isNull(consumerUserDTO)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "报名失败，该用户不存在");
        }

        // 初次注册使用昵称替换
        String name = dto.getName();
        dto.setName(consumerUserDTO.getNickname());

        SchoolGradeStu entity = schoolGradeStuConverter.dtoConvertEntityBase(dto);
        SchoolDetail schoolDetail = getSchoolDetailByInfo(dto);
        if (Objects.isNull(schoolDetail)) {
            log.error("班级PK大赛新增学生查找学校/校区/年级/班级，有误，SchoolGradeStuServiceImpl.saveSchoolGradeStu = {}", dto);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学校/校区/年级/班级，有误");
        }
        entity.setSchoolDetailId(schoolDetail.getId());

        String schoolName = entity.getSchoolName();
        String schoolArea = entity.getSchoolArea();
        String schoolGrade = entity.getSchoolGrade();
        String schoolClass = entity.getSchoolClass();

        //设置我在班级上的排名
        Integer myRank = findMyRank(schoolName, schoolArea, schoolGrade, schoolClass);
        entity.setMyRank(myRank);

        //设置我在年级排名
        Integer schoolRank = findMyGradeRankInSchool(schoolName, schoolArea, schoolGrade);
        entity.setSchoolRank(schoolRank);

        //设置我的班级在学校的排名
        Integer myClassRankInSchool = findMyClassRankInSchool(schoolDetail.getId(), schoolName, schoolArea, schoolGrade);
        entity.setClassRank(myClassRankInSchool);

        // 插入昵称审核表
        List<ActivityNameAuditDTO> activityNameAuditDTOs = new ArrayList<>();
        ActivityNameAuditDTO activityNameAuditDTO = new ActivityNameAuditDTO();
        activityNameAuditDTO.setNickName(consumerUserDTO.getNickname());
        activityNameAuditDTO.setNewName(name);
        activityNameAuditDTO.setMobile(AESUtil.AESCBCEncode(consumerUserDTO.getMobile()));
        activityNameAuditDTO.setMaskMobile(consumerUserDTO.getMaskMobile());
        activityNameAuditDTO.setActivityName(ActivityTypeEnum.SCHOOL_GRADE_PK.getDesc());
        activityNameAuditDTO.setCreateDate(new Date());
        activityNameAuditDTO.setCheckStatus(0);
        activityNameAuditDTO.setUpdateDate(new Date());
        activityNameAuditDTO.setUserId(dto.getJid());
        activityNameAuditDTO.setCreateUserId(dto.getJid());
        activityNameAuditDTO.setUpdateUserId(dto.getJid());
        activityNameAuditDTOs.add(activityNameAuditDTO);
        activityNameAuditService.batchAdd(activityNameAuditDTOs);

        return this.save(entity);
    }

    /**
     * 查找我在班内的排名
     *
     * @param schoolName
     * @param schoolArea
     * @param schoolGrade
     * @param schoolClass
     * @return
     */
    private Integer findMyRank(String schoolName, String schoolArea, String schoolGrade, String schoolClass) {

        Integer myRank = null;

        List<SchoolGradeStu> myRankList = this.query().eq("school_name", schoolName).
                eq("school_area", schoolArea).eq("school_grade", schoolGrade).eq("school_class", schoolClass)
                .eq("deleted", 0).orderByDesc("total_score").orderByAsc("create_date").list();
        if (CollectionUtils.isEmpty(myRankList)) {
            myRank = 1;
            return myRank;
        } else {
            myRank = myRankList.size() + 1;
        }

        return myRank;
    }

    /***
     * 查找我在年级的排名
     * @param schoolName
     * @param schoolArea
     * @param schoolGrade
     * @return
     */
    private Integer findMyGradeRankInSchool(String schoolName, String schoolArea, String schoolGrade) {
        //初始学校排名
        Integer schoolRank = null;
        List<SchoolGradeStu> gradeStuList = this.query().eq("school_name", schoolName).
                eq("school_area", schoolArea).eq("school_grade", schoolGrade).
                orderByDesc("total_score").orderByAsc("create_date").eq("deleted", 0).list();
        if (CollectionUtils.isEmpty(gradeStuList)) {
            schoolRank = 1;
            return schoolRank;
        } else {
            schoolRank = gradeStuList.size() + 1;
        }

        return schoolRank;
    }

    /***
     * 查找我的班级在学校的排名
     * @param schoolDetailId
     * @param schoolName
     * @param schoolArea
     * @param schoolGrade
     * @return
     */
    private Integer findMyClassRankInSchool(Long schoolDetailId, String schoolName, String schoolArea, String schoolGrade) {
        //初始我的班级在学校的排名
        Integer classRank = null;
        List<SchoolGradeStuDTO> classStuList = schoolGradeStuMapper.queryPageClassRankInSchool(schoolName, schoolArea, schoolGrade, null, null);
        List<SchoolGradeStu> gradeStuList = this.query().eq("school_name", schoolName).eq("school_area", schoolArea).list();
        if (CollectionUtils.isEmpty(gradeStuList)) {
            //第一个默认排第一
            classRank = 1;
            return classRank;
        } else {
            for (int i = 0; i < classStuList.size(); i++) {
                SchoolGradeStuDTO temp = classStuList.get(i);
                if (Objects.isNull(temp)) {
                    classRank = 1;
                } else if (Objects.equals(schoolDetailId, temp.getSchoolDetailId())) {
                    //如果是同班级直接赋值
                    classRank = temp.getClassRank();
                } else {
                    //没有同班， 拿总班级排名+1
                    classRank = classStuList.size();
                }
            }
        }

        return classRank;
    }


    @Override
    @Transactional
    public Boolean updateStudent(SchoolGradeStuDTO dto) {
        if (Objects.isNull(dto)) {
            throw new NullPointerException(CodeMessage.BAD_REQUEST.getMessage());
        }
        log.info("班级PK大赛更新学生信息，SchoolGradeStuServiceImpl.updateStudent 入参= {}", dto);

        SchoolGradeStu old = this.getById(dto.getId());
        if (Objects.isNull(old)) {
            throw new NullPointerException(CodeMessage.USER_NOT_EXIST.getMessage());
        }
        if (!StringUtils.equals(dto.getSchoolName(), old.getSchoolName())) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学校不允许修改");
        }

        SchoolGradeStu entity = schoolGradeStuConverter.dtoConvertEntityBase(dto);
        SchoolDetail schoolDetail = getSchoolDetailByInfo(dto);
        if (Objects.isNull(schoolDetail)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学校/校区/年级/班级，有误");
        }
        entity.setSchoolDetailId(schoolDetail.getId());

        // 修改名称不生效，需要审核通过后生效
        entity.setName(null);

        boolean result = this.updateById(entity);
        //Integer result = schoolGradeStuMapper.updateStuInfo(entity);
        log.info("班级PK大赛更新学生信息结果，result= {}", result);

        if (!StringUtils.equals(dto.getSchoolArea(), old.getSchoolArea()) ||
                !StringUtils.equals(dto.getSchoolGrade(), old.getSchoolGrade()) ||
                !StringUtils.equals(dto.getSchoolClass(), old.getSchoolClass())) {
            //班级或者年级或者学区改变，需要维护学生排名数据， 发布更新事件
            //applicationEventPublisher.publishEvent(entity);
            updateStudentRank(entity.getId());
        }

        if (result) {
            // 插入昵称审核表
            ConsumerUserDTO consumerUserDTO = consumerUserClient.getUserByJid(dto.getJid()).orElse(null);
            if (Objects.isNull(consumerUserDTO)) {
                throw new CodeMessageException(CodeMessage.NOT_FOUND, "该用户不存在");
            }
            List<ActivityNameAuditDTO> activityNameAuditDTOs = new ArrayList<>();
            ActivityNameAuditDTO activityNameAuditDTO = new ActivityNameAuditDTO();
            activityNameAuditDTO.setNickName(consumerUserDTO.getNickname());
            activityNameAuditDTO.setNewName(dto.getName());
            activityNameAuditDTO.setOldName(old.getName());
            activityNameAuditDTO.setMobile(AESUtil.AESCBCEncode(consumerUserDTO.getMobile()));
            activityNameAuditDTO.setMaskMobile(consumerUserDTO.getMaskMobile());
            activityNameAuditDTO.setActivityName(ActivityTypeEnum.SCHOOL_GRADE_PK.getDesc());
            activityNameAuditDTO.setCreateDate(new Date());
            activityNameAuditDTO.setCheckStatus(0);
            activityNameAuditDTO.setUpdateDate(new Date());
            activityNameAuditDTO.setUserId(dto.getJid());
            activityNameAuditDTO.setCreateUserId(dto.getJid());
            activityNameAuditDTO.setUpdateUserId(dto.getJid());
            activityNameAuditDTOs.add(activityNameAuditDTO);
            activityNameAuditService.batchAdd(activityNameAuditDTOs);
        }

        return result;
    }

    @Override
    public Boolean updateStudentAvatar(SchoolGradeStuDTO dto) {
        if (Objects.isNull(dto) || Objects.isNull(dto.getId()) || Objects.isNull(dto.getAvatarUrl())) {
            log.error("班级PK大赛更新头像错误，参数有问题={}", dto);
            return false;
        }
        SchoolGradeStu entity = new SchoolGradeStu();
        entity.setId(dto.getId());
        entity.setAvatarUrl(dto.getAvatarUrl());
        return this.updateById(entity);
    }

    @Override
    public Boolean updateStudentByJid(Long jid, SchoolGradeStuDTO dto, boolean audit) {
        SchoolGradeStu entity = this.query().eq("jid", jid).one();
        dto.setJid(jid);

        if (audit) {
            if (activityNameAuditService.hasNotCheckName(jid, ActivityTypeEnum.SCHOOL_GRADE_PK.getDesc())) {
                throw new CodeMessageException(CodeMessage.ERROR, "姓名正在审核中");
            }

            // 插入昵称审核表
            ConsumerUserDTO consumerUserDTO = consumerUserClient.getUserByJid(dto.getJid()).orElse(null);
            if (Objects.isNull(consumerUserDTO)) {
                throw new CodeMessageException(CodeMessage.NOT_FOUND, "该用户不存在");
            }
            List<ActivityNameAuditDTO> activityNameAuditDTOs = new ArrayList<>();
            ActivityNameAuditDTO activityNameAuditDTO = new ActivityNameAuditDTO();
            activityNameAuditDTO.setNickName(consumerUserDTO.getNickname());
            activityNameAuditDTO.setNewName(dto.getName());
            activityNameAuditDTO.setOldName(entity.getName());
            activityNameAuditDTO.setMobile(AESUtil.AESCBCEncode(consumerUserDTO.getMobile()));
            activityNameAuditDTO.setMaskMobile(consumerUserDTO.getMaskMobile());
            activityNameAuditDTO.setActivityName(ActivityTypeEnum.SCHOOL_GRADE_PK.getDesc());
            activityNameAuditDTO.setCreateDate(new Date());
            activityNameAuditDTO.setCheckStatus(0);
            activityNameAuditDTO.setUpdateDate(new Date());
            activityNameAuditDTO.setUserId(dto.getJid());
            activityNameAuditDTO.setCreateUserId(dto.getJid());
            activityNameAuditDTO.setUpdateUserId(dto.getJid());
            activityNameAuditDTOs.add(activityNameAuditDTO);
            activityNameAuditService.batchAdd(activityNameAuditDTOs);
            return true;
        }

        LambdaUpdateWrapper<SchoolGradeStu> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(SchoolGradeStu::getJid, dto.getJid());
        updateWrapper.set(Objects.nonNull(dto.getName()), SchoolGradeStu::getName, dto.getName());
        return this.update(updateWrapper);
    }

    @Override
    public List<SchoolGradeStuDTO> page(Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<SchoolGradeStu> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.orderByDesc(SchoolGradeStu::getCreateDate);
        Page<SchoolGradeStu> page = super.page(new Page<>(pageNum, pageSize), lambdaQueryWrapper);
        return PageUtils.pageOf(page).map(schoolGradeStuConverter::entityConvertDTOBase).getList();
    }

    @Override
    public PageDTO<SchoolGradeStuDTO> pageMyRankInClass(Long stuID, Integer pageNum, Integer pageSize) {
        pageNum = Math.max(pageNum, 1);
        pageSize = Math.max(pageSize, 20);

        if (Objects.isNull(stuID)) {
            throw new NullPointerException(CodeMessage.BAD_REQUEST.getMessage());
        }

        SchoolGradeStu stu = this.getById(stuID);

        if (Objects.isNull(stu)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "我的班级排名查询分页失败，学生不存在");
        }

        Page<SchoolGradeStu> page = this.query().eq("school_name", stu.getSchoolName()).
                eq("school_area", stu.getSchoolArea()).eq("school_grade", stu.getSchoolGrade()).eq("school_class", stu.getSchoolClass()).
                orderByDesc("total_score").orderByAsc("create_date").eq("deleted", 0).page(new Page<>(pageNum, pageSize));
        if (Objects.isNull(page)) {
            log.info("班级PK大赛查询班级排名分页异常，SchoolGradeStuServiceImpl.pageMyRankInClass = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛查询班级排名分页异常，没查到数据");
        }
        return PageUtils.pageOf(page, schoolGradeStuConverter::entityConvertDTOBase);

    }

    @Override
    public PageDTO<SchoolGradeStuDTO> pageSchoolRankInGrade(Long stuID, Integer pageNum, Integer pageSize) {
        if (Objects.isNull(stuID)) {
            throw new NullPointerException(CodeMessage.BAD_REQUEST.getMessage());
        }
        pageNum = Math.max(pageNum, 1);
        pageSize = Math.max(pageSize, 20);

        SchoolGradeStu stu = this.getById(stuID);


        if (Objects.isNull(stu)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学校年级排名失败，学生不存在");
        }

        Page<SchoolGradeStu> page = this.query().eq("school_name", stu.getSchoolName()).
                eq("school_area", stu.getSchoolArea()).eq("school_grade", stu.getSchoolGrade()).
                orderByDesc("total_score").orderByAsc("create_date").eq("deleted", 0).page(new Page<>(pageNum, pageSize));
        if (Objects.isNull(page)) {
            log.info("班级PK大赛查询班级排名分页异常，SchoolGradeStuServiceImpl.pageSchoolRankInGrade = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛查询班级排名分页异常，没查到数据");
        }
        return PageUtils.pageOf(page, schoolGradeStuConverter::entityConvertDTOBase);
    }

    @Override
    public SchoolGradePKPageDTO pageClassRankInSchool(Long stuID, Integer pageNum, Integer pageSize) {
        pageNum = Math.max(pageNum, 1);
        pageSize = Math.max(pageSize, 20);

        Integer startIndex = (pageNum - 1) * pageSize;
        SchoolGradeStu stu = this.getById(stuID);
        List<SchoolGradeStuDTO> result = null;


        if (Objects.isNull(stu)) {
            //说明用户没报名，默认查班级总分数最高的
            result = schoolGradeStuMapper.queryPageClassRankInSchool(null, null, null, startIndex, pageSize);
        } else {
            result = schoolGradeStuMapper.queryPageClassRankInSchool(stu.getSchoolName(), stu.getSchoolArea(), stu.getSchoolGrade(), startIndex, pageSize);
        }

        if (CollectionUtils.isEmpty(result)) {
            log.info("班级PK大赛查询班级排名分页没找到数据，SchoolGradeStuServiceImpl.queryPageClassRankInSchool = {}", stuID);
            return null;
        }


        if (Objects.equals(1, pageNum)) {
            //查询前三个班级全部的头像的url
            for (int i = 0; i < 3; i++) {
                if (i >= result.size()) {
                    continue;
                }
                SchoolGradeStuDTO dto = result.get(i);
                if (Objects.isNull(dto)) {
                    continue;
                }
                List<SchoolGradeStu> list = this.query().eq("school_name", dto.getSchoolName()).eq("school_area", dto.getSchoolArea()).
                        eq("school_grade", dto.getSchoolGrade()).eq("school_class", dto.getSchoolClass()).eq("deleted", 0).list();
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }
                List<String> urlList = list.stream().filter(s -> Objects.nonNull(s.getAvatarUrl())).
                        map(SchoolGradeStu::getAvatarUrl).collect(Collectors.toList());
                dto.setUrlList(urlList);
                result.set(i, dto);
            }
        }

        SchoolGradePKPageDTO pageDTO = new SchoolGradePKPageDTO();
        pageDTO.setPageNum(pageNum);
        pageDTO.setPageSize(pageSize);
        pageDTO.setTotal(result.size());
        pageDTO.setResult(result);

        return pageDTO;
    }

    @Override
    public SchoolGradePKPageDTO queryPageClassState(Long jid, Integer pageNum, Integer pageSize) {
        log.info("班级PK大赛查询班级动态SchoolGradeStuServiceImpl.queryPageClassState入参={}", jid);

        pageNum = Math.max(pageNum, 1);
        pageSize = Math.max(pageSize, 20);

        SchoolGradeStuDTO stuDTO = queryStudentBaseInfoByJid(jid);
        if (Objects.isNull(stuDTO)) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛查询班级动态异常，学生不存在");
        }
        List<SchoolGradeStu> sutResult = this.query().eq("school_name", stuDTO.getSchoolName()).eq("school_area", stuDTO.getSchoolArea()).
                eq("school_grade", stuDTO.getSchoolGrade()).eq("school_class", stuDTO.getSchoolClass()).eq("deleted", 0).list();
        if (CollectionUtils.isEmpty(sutResult)) {
            log.error("班级PK大赛查询班级动态异常，学生不存在，SchoolGradeStuServiceImpl.queryPageClassState = {}", stuDTO);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛查询班级动态异常，学生不存在");
        }
        List<Long> jidIdList = sutResult.stream().map(SchoolGradeStu::getJid).collect(Collectors.toList());

        Integer startIndex = (pageNum - 1) * pageSize;
        List<SchoolGradePKStateDTO> result = schoolGradeStuMapper.queryPageClassState(jidIdList, startIndex, pageSize);
        if (CollectionUtils.isEmpty(result)) {
            log.info("班级PK大赛没查询到班级动态，SchoolGradeStuServiceImpl.queryPageClassState = {}", stuDTO);
            return null;
        }
        SchoolGradePKPageDTO pageDTO = new SchoolGradePKPageDTO();
        pageDTO.setPageNum(pageNum);
        pageDTO.setPageSize(pageSize);
        pageDTO.setTotal(result.size());
        pageDTO.setResult(result);

        return pageDTO;
    }

    @Override
    @Transactional
    public Boolean updateStudentRank(Long stuID) {
        if (Objects.isNull(stuID)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "学生stuId有误");
        }
        //维护学生排名数据
        SchoolGradeStu stu = this.getById(stuID);
        if (Objects.isNull(stu)) {
            log.error("班级PK大赛更新学生排名异常，SchoolGradeStuServiceImpl.updateStudentRank = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛更新学生排名异常，学生不存在");
        }
        String schoolName = stu.getSchoolName();
        String schoolArea = stu.getSchoolArea();
        String schoolGrade = stu.getSchoolGrade();
        String schoolClass = stu.getSchoolClass();
        //更新我的排名
        updateMyRank(stuID, schoolName, schoolArea, schoolGrade, schoolClass);
        //更新年级排名
        updateGradeRank(stuID, schoolName, schoolArea, schoolGrade);
        //更新我的班级排名 后期定时任务维护数据
        //updateClassRank(stuID, schoolName, schoolArea, schoolGrade);

        return Boolean.TRUE;
    }

    @Override
    @Transactional
    public Boolean updateStudentTotalScore(Long jid, Long theQaScore) {
        if (Objects.isNull(jid)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "学生stuId有误");
        }
        SchoolGradeStu entity = this.query().eq("jid", jid).eq("deleted", 0).one();
        log.error("班级PK大赛答题更新学生分数SchoolGradeStuServiceImpl.updateStudentTotalScore 入参 entity= {};theQaScore={}", entity, theQaScore);

        if (Objects.isNull(entity)) {
            log.error("班级PK大赛答题更新学生分数异常，SchoolGradeStuServiceImpl.updateStudentTotalScore = {}", jid);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学生不存在");
        }
        Long newScore = theQaScore + entity.getTotalScore();
        entity.setTotalScore(newScore);
        updateById(entity);
        //维护学生排名
        updateStudentRank(entity.getId());
        return Boolean.TRUE;
    }

    private SchoolDetail getSchoolDetailByInfo(SchoolGradeStuDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        //学校校名+校区+年级+班级确定唯一一条学校明细表中的数据
        String schoolName = dto.getSchoolName();
        String schoolArea = dto.getSchoolArea();
        String schoolGrade = dto.getSchoolGrade();
        String schoolClass = dto.getSchoolClass();

        SchoolDetail schoolDetail = schoolGradePkSchoolDetailService.getSchoolDetailByInfo(schoolName, schoolArea, schoolGrade, schoolClass);

        if (Objects.isNull(schoolDetail) && Objects.isNull(schoolDetail.getId())) {
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "学校明细不存在");
        }
        return schoolDetail;
    }

    @Override
    public GroupGatherDTO querySchoolGradePkGroupId() {
        List<GroupGather> gatherList = groupGatherService.query().eq("type", GroupGatherTypeEnum.SCHOOL_GRADE_PK.getCode())
                .eq("status", 1).list();
        if (CollectionUtils.isEmpty(gatherList)) {
            log.error("班级PK大赛groupId异常，SchoolGradeStuServiceImpl.querySchoolGradePkGroupId = {}", gatherList);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛获取圈子id失败:圈子不存在");
        }

        GroupGather groupGather = gatherList.get(0);
        GroupGatherDTO dto = new GroupGatherDTO();
        dto.setGroupId(groupGather.getGroupId());

        return dto;
    }

    @Override
    public SchoolGradeStuDTO queryStudentBaseInfoByJid(Long jid) {
        if (Objects.isNull(jid)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "学生jid不能为空");
        }
        log.info("根据jid查询班级pk赛SchoolGradeStuServiceImpl.queryStudentBaseInfoByJid入参={}", jid);
        SchoolGradeStu entity = this.query().eq("jid", jid).one();
        if (Objects.isNull(entity)) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "根据jid没查询搭到查询班级pk赛用户");
        }
        SchoolGradeStuDTO stuDTO = schoolGradeStuConverter.entityConvertDTOBase(entity);
        SchoolDetail schoolDetail = schoolGradePkSchoolDetailService.getById(entity.getSchoolDetailId());
        stuDTO.setSchoolCode(schoolDetail.getSchoolCode());
        //实时查询我的班级排名
        List<SchoolGradeStuDTO> classStuList = schoolGradeStuMapper.queryPageClassRankInSchool(entity.getSchoolName(), entity.getSchoolArea(), entity.getSchoolGrade(), null, null);
        if (!CollectionUtils.isEmpty(classStuList)) {
            for (int i = 0; i < classStuList.size(); i++) {
                SchoolGradeStuDTO temp = classStuList.get(i);
                if (Objects.equals(entity.getSchoolDetailId(), temp.getSchoolDetailId())) {
                    stuDTO.setClassRank(i + 1);
                }
            }
        }

        return stuDTO;
    }


    /**
     * 更新我在班级的排名
     *
     * @param stuID       学生id
     * @param schoolName  学校名字
     * @param schoolArea  学校地区
     * @param schoolGrade 年级
     * @param schoolClass 班级
     */
    @Transactional
    public void updateMyRank(Long stuID, String schoolName, String schoolArea, String schoolGrade, String schoolClass) {

        //更新我的排名
        List<SchoolGradeStu> stuList = this.query().eq("school_name", schoolName).
                eq("school_area", schoolArea).eq("school_grade", schoolGrade).
                eq("school_class", schoolClass).orderByDesc("total_score").orderByAsc("create_date").eq("deleted", 0).list();
        if (CollectionUtils.isEmpty(stuList)) {
            log.error("班级PK大赛更新学生我的排名异常，SchoolGradeStuServiceImpl.updateMyRank = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "更新我在班级的排名失败，学生不存在");
        }
        int stop = stuList.size();
        List<SchoolGradeStu> newStuList = new ArrayList<>(stop);
        for (int i = 0; i < stop; i++) {
            SchoolGradeStu temp = stuList.get(i);
            temp.setMyRank(i + 1);
            newStuList.add(temp);
        }
        //数据库更新我的排名
        this.updateBatchById(newStuList);
    }

    /**
     * 更新我在年级的排名
     *
     * @param stuID       学生id
     * @param schoolName  学校名字
     * @param schoolArea  学校地区
     * @param schoolGrade 年级
     */
    @Transactional
    public void updateGradeRank(Long stuID, String schoolName, String schoolArea, String schoolGrade) {
        int stop;

        List<SchoolGradeStu> gradeStuList = this.query().eq("school_name", schoolName).
                eq("school_area", schoolArea).eq("school_grade", schoolGrade).
                orderByDesc("total_score").orderByAsc("create_date").eq("deleted", 0).list();
        if (CollectionUtils.isEmpty(gradeStuList)) {
            log.error("班级PK大赛更新学生的年级排名异常，SchoolGradeStuServiceImpl.updateGradeRank = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "更新我在年级排名失败，学生不存在");
        }

        stop = gradeStuList.size();
        List<SchoolGradeStu> newGradeStuList = new ArrayList<>(stop);
        for (int i = 0; i < stop; i++) {
            SchoolGradeStu temp = gradeStuList.get(i);
            temp.setSchoolRank(i + 1);
            newGradeStuList.add(temp);
        }
        //数据库更新班级排名
        this.updateBatchById(newGradeStuList);
    }

    /**
     * 更新我的班级在学校的排名
     *
     * @param stuID       学生id
     * @param schoolName  学校名字
     * @param schoolArea  学校地区
     * @param schoolGrade 年级
     */
    private void updateClassRank(Long stuID, String schoolName, String schoolArea, String schoolGrade) {
        int stop;
        //更新我的班级排名

        List<SchoolGradeStuDTO> classStuList = schoolGradeStuMapper.queryPageClassRankInSchool(schoolName, schoolArea, schoolGrade, null, null);
        List<SchoolGradeStu> oldStuList = this.query().eq("school_name", schoolName).eq("school_area", schoolArea).eq("school_grade", schoolGrade).list();
        if (CollectionUtils.isEmpty(oldStuList)) {
            log.error("班级PK大赛更新学生的班级排名异常，SchoolGradeStuServiceImpl.updateClassRank = {}", stuID);
            throw new CodeMessageException(CodeMessage.ERROR.getCode(), "班级PK大赛更新我的班级在学校的排名异常，学生不存在");
        }
        stop = classStuList.size();
        List<SchoolGradeStu> newStuList = new ArrayList<>(stop);
        for (int i = 0; i < stop; i++) {
            SchoolGradeStuDTO temp = classStuList.get(i);
            SchoolGradeStu entity = new SchoolGradeStu();
            entity.setClassRank(i + 1);
            entity.setSchoolName(temp.getSchoolName());
            entity.setSchoolArea(temp.getSchoolArea());
            entity.setSchoolGrade(temp.getSchoolGrade());
            entity.setSchoolClass(temp.getSchoolClass());
            newStuList.add(entity);
        }
        //数据库更新我的班级排名
        schoolGradeStuMapper.batchUpdateStuClassRank(newStuList);
    }

}
