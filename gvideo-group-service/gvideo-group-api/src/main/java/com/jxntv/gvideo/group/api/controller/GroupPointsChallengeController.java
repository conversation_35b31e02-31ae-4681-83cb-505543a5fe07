package com.jxntv.gvideo.group.api.controller;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.api.service.GroupPointsChallengeMemberService;
import com.jxntv.gvideo.group.sdk.client.GroupPointsChallengeClient;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeEnrollDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeRankResultDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeSearchRankDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/11/8 16:56
 */
@Slf4j
@RestController
@RefreshScope
public class GroupPointsChallengeController implements GroupPointsChallengeClient {

    @Resource
    private GroupPointsChallengeMemberService groupPointsChallengeMemberService;


    @Override
    public Result<Boolean> saveEnrollInfo(GroupPointsChallengeEnrollDTO dto) {
        return Result.ok(groupPointsChallengeMemberService.saveEnrollInfo(dto));
    }

    @Override
    public Result<GroupPointsChallengeInfoDTO> queryInfo(Long groupId, Long jid) {
        return Result.ok(groupPointsChallengeMemberService.queryInfo(groupId,jid));
    }

    @Override
    public Result<GroupPointsChallengeRankResultDTO> search(GroupPointsChallengeSearchRankDTO searchDTO) {
        return Result.ok(groupPointsChallengeMemberService.search(searchDTO));
    }
}
