package com.jxntv.gvideo.group.api.converter;

import com.jxntv.gvideo.group.api.entity.GroupGatherInstruction;
import com.jxntv.gvideo.group.sdk.dto.instruction.GroupGatherInstructionDTO;
import com.jxntv.gvideo.group.sdk.dto.instruction.GroupGatherInstructionInsertDTO;
import com.jxntv.gvideo.group.sdk.dto.instruction.GroupGatherInstructionUpdateDTO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 组件课堂教学转换
 */
@Component
public class GroupGatherInstructionConvert {

    public  GroupGatherInstructionDTO convert(GroupGatherInstruction entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherInstructionDTO dto = new GroupGatherInstructionDTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setGroupGatherId(entity.getGroupGatherId());
        dto.setName(entity.getName());
        dto.setTag(entity.getTag());
        dto.setMediaId(entity.getMediaId());
        dto.setCoverId(entity.getCoverId());
        dto.setVideoId(entity.getVideoId());
        dto.setStatus(entity.getStatus());
        dto.setReleaseId(entity.getReleaseId());
        dto.setWeight(entity.getWeight());
        dto.setOnLineDate(entity.getOnLineDate());
        dto.setOffLineDate(entity.getOffLineDate());
        return dto;
    }

    public  GroupGatherInstruction convertInsert(GroupGatherInstructionInsertDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherInstruction entity = new GroupGatherInstruction();
        entity.setGroupId(dto.getGroupId());
        entity.setGroupGatherId(dto.getGroupGatherId());
        entity.setName(dto.getName());
        entity.setTag(dto.getTag());
        entity.setCoverId(dto.getCoverId());
        entity.setVideoId(dto.getVideoId());
        entity.setMediaId(dto.getMediaId());
        entity.setStatus(0);
        entity.setReleaseId(dto.getReleaseId());
        entity.setDelFlag(0);
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setCreateDate(new Date());
        return entity;
    }

    public  GroupGatherInstruction convertUpdate(GroupGatherInstructionUpdateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherInstruction entity = new GroupGatherInstruction();
        entity.setName(dto.getName());
        entity.setTag(dto.getTag());
        entity.setCoverId(dto.getCoverId());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(new Date());
        return entity;
    }
}
