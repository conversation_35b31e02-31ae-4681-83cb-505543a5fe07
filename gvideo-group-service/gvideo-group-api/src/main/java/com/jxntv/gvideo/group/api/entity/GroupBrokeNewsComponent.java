package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 社区爆料组件详细
 *
 * <AUTHOR>
 * @date 2022/5/12
 * Email: <EMAIL>
 */
@Data
public class GroupBrokeNewsComponent {

    /**
     * 问答组件详细表ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 组件关联社区id
     */
    private Long groupId;
    /**
     * 社区组件主表ID
     */
    private Long componentId;
    /**
     * 爆料奖励金额社区/话题信息流显示：0-否、1-是
     */
    private Integer showFlow;
    /**
     * 爆料奖励金额详情页显示：0-否、1-是
     */
    private Integer showDetail;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;


}
