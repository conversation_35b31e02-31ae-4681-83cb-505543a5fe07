package com.jxntv.gvideo.group.api.converter;

import com.jxntv.gvideo.group.api.entity.GroupVocationValueComponent;
import com.jxntv.gvideo.group.sdk.dto.vocation.GroupVocationValueComponentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/7/5
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class GroupVocationValueConverter {

    public GroupVocationValueComponent convert(GroupVocationValueComponentDTO dto){
        GroupVocationValueComponent entity = new GroupVocationValueComponent();
        entity.setId(dto.getId());
        entity.setComponentId(dto.getComponentId());
        entity.setSignupUrl(dto.getSignupUrl());
        entity.setRankUrl(dto.getRankUrl());
        entity.setLoginGuideText(dto.getLoginGuideText());
        entity.setSignupGuideText(dto.getSignupGuideText());
        entity.setHotIntroduction(dto.getHotIntroduction());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateDate(dto.getUpdateDate());
        entity.setHotCountType(dto.getHotCountType());
        return entity;
    }


    public GroupVocationValueComponentDTO convert(GroupVocationValueComponent entity){
        GroupVocationValueComponentDTO dto = new GroupVocationValueComponentDTO();
        dto.setId(entity.getId());
        dto.setComponentId(entity.getComponentId());
        dto.setSignupUrl(entity.getSignupUrl());
        dto.setRankUrl(entity.getRankUrl());
        dto.setLoginGuideText(entity.getLoginGuideText());
        dto.setSignupGuideText(entity.getSignupGuideText());
        dto.setHotIntroduction(entity.getHotIntroduction());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setUpdateDate(entity.getUpdateDate());
        dto.setHotCountType(entity.getHotCountType());
        return dto;
    }

}
