package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.group.api.entity.today.talk.GroupTodayTalk;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 圈子-今日聊话题表
 *
 * <AUTHOR>
 * @date 2022-10-18 14:56:01
 */
@Repository
public interface GroupTodayTalkMapper extends BaseMapper<GroupTodayTalk> {

    Page<Long> getVoteContributeList(@Param("page") Page<GroupTodayTalk> page,
                                               @Param("groupId") Long groupId,
                                               @Param("firstStart") LocalDateTime firstStart,
                                               @Param("firstEnd") LocalDateTime firstEnd,
                                               @Param("secondStart") LocalDateTime secondStart,
                                               @Param("secondEnd") LocalDateTime secondEnd,
                                               @Param("thirdStart") LocalDateTime thirdStart,
                                               @Param("thirdEnd") LocalDateTime thirdEnd);

    List<GroupTodayTalk> selectRewardUserList(@Param("groupId") Long groupId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
