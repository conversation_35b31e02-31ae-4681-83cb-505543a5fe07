package com.jxntv.gvideo.group.api.event.listener;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.api.client.MessageService;
import com.jxntv.gvideo.group.api.event.GroupSuggestionEvent;
import com.jxntv.gvideo.interact.client.dto.MessageDTO;
import com.jxntv.gvideo.interact.client.dto.enums.MessageContentTypeEnum;
import com.jxntv.gvideo.interact.client.dto.enums.MessageStatusEnum;
import com.jxntv.gvideo.interact.client.dto.enums.MessageToTypeEnum;
import com.jxntv.gvideo.interact.client.dto.enums.MessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @Author: niedamin
 * @Date: 2023/03/20 16:02
 */
@Component
@RefreshScope
@Slf4j
public class GroupSuggestionEventListener {

    @Resource
    private MessageService messageService;

    @Value("${blind-date.group-id}")
    private Long blindDateGroupId;

    @EventListener
    @Async
    public void onEvent(GroupSuggestionEvent event) {
        Long groupId = event.getGroupId();
        if (Objects.equals(blindDateGroupId, groupId)) {
            // 都市放心爱
            MessageDTO dto = new MessageDTO();
            dto.setTitle("社区反馈");
            dto.setDetail("感谢你对放心爱平台的反馈和建议，平台会因为你的存在而变得更好！");
            dto.setContentType(MessageContentTypeEnum.USER_SUGGESTION.getId());
            dto.setStatus(MessageStatusEnum.STATUS_SEND.getId());
            dto.setType(MessageTypeEnum.TEXT.getId());
            dto.setFromType(0);
            dto.setFromId(0L);
            dto.setFromStatus(true);
            dto.setToType(MessageToTypeEnum.MESSAGE_TO_HIT.getId());
            dto.setToIds(String.valueOf(event.getJid()));
            dto.setAdminUserId(0);
            dto.setAdminUserName("");
            dto.setCreateDate(new Date());
            dto.setExecuteType(1);
            dto.setExecuteDate(new Date());
            dto.setGroupId(groupId);
            Result<Long> rst = messageService.addMessage(dto);
            if (rst.getCode() != CodeMessage.OK.getCode()) {
                log.error("都市放心爱-举报-系统消息：" + JsonUtils.toJson(rst));
            }
        }
    }
}
