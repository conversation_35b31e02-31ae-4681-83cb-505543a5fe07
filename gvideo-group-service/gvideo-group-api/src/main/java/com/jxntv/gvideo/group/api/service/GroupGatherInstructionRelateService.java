package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.GroupGatherInstructionRelate;
import com.jxntv.gvideo.group.sdk.dto.MediaIdDTO;
import com.jxntv.gvideo.group.sdk.dto.instruction.GroupGatherInstructionLearnSearchDTO;

/**
 * 社区-组件-课堂-关联跟随学习资源业务
 *
 * <AUTHOR>
 * @date 2023-07-04 17:45:29
 */
public interface GroupGatherInstructionRelateService extends IService<GroupGatherInstructionRelate> {

    IPage<MediaIdDTO> queryLearnList(GroupGatherInstructionLearnSearchDTO dto);
}

