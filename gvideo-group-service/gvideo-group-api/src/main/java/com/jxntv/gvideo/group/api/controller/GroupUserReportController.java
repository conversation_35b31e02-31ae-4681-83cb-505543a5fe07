package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.api.converter.GroupUserReportConvert;
import com.jxntv.gvideo.group.api.entity.GroupUserReport;
import com.jxntv.gvideo.group.api.service.GroupUserReportService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.GroupUserReportClient;
import com.jxntv.gvideo.group.sdk.dto.GroupUserReportDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupUserReportProcessDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupUserReportSearchDTO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/03/17 15:05
 */
@RestController
public class GroupUserReportController implements GroupUserReportClient {

    @Resource
    private GroupUserReportService groupUserReportService;

    @Resource
    private GroupUserReportConvert convert;

    @Override
    public Result<Long> add(GroupUserReportDTO dto) {
        return Result.ok(groupUserReportService.add(dto));
    }

    @Override
    public Result<PageDTO<GroupUserReportDTO>> page(GroupUserReportSearchDTO searchDTO) {
        IPage<GroupUserReport> page = groupUserReportService.page(searchDTO);
        return Result.ok(PageUtils.pageOf(page, convert::convert));
    }

    @Override
    public Result<List<String>> getType() {
        return Result.ok(groupUserReportService.getType());
    }

    @Override
    public Result<Void> process(GroupUserReportProcessDTO dto) {
        return groupUserReportService.process(dto);
    }
}
