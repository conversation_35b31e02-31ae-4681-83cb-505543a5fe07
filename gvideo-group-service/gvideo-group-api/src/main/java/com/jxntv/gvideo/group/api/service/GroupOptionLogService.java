package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.*;
import com.jxntv.gvideo.group.sdk.dto.*;

import java.util.List;

/**
 *
 */
public interface GroupOptionLogService extends IService<GroupOptionLog> {

    /**
     * 圈子信息操作日志
     *
     * @param dbData
     * @param groupInfo
     * @param logDTO
     * @param labels
     */
    void addGroupLog(GroupInfo dbData, GroupInfo groupInfo, GroupLogDTO logDTO, List<Long> labels);

    /**
     * 话题操作日志
     *
     * @param dbData
     * @param groupTopic
     * @param logDTO
     * @param labels
     */
    void addTopicLog(GroupTopic dbData, GroupTopic groupTopic, GroupLogDTO logDTO, List<Long> labels);

    /**
     * 新增内容日志
     * 记录圈子关联动态的状态变更
     *
     * @param dbData
     * @param status
     * @param logDTO
     */
    void addContentLog(GroupTopicContent dbData, TopicMediaStatus status, GroupLogDTO logDTO);

    /**
     * 新增组件变更日志
     *
     * @param dbData
     * @param groupGather
     * @param logDTO
     */
    void addGatherLog(GroupGatherDTO dbData, GroupGatherDTO groupGather, GroupLogDTO logDTO);

    /**
     * 新增组件广播变更日志
     *
     * @param dbData
     * @param broadcast
     * @param logDTO
     */
    void addGatherBroadcastLog(GroupGather groupGather, GroupGatherBroadcastDTO dbData, GroupGatherBroadcastDTO broadcast, GroupLogDTO logDTO);

    /**
     * 组件关联话题日志
     *
     * @param groupId 圈子ID
     * @param flag    关联/移除话题标识
     * @param logDTO  日志对象
     * @param size    次数
     */
    void addGatherTopicLog(Long groupId, boolean flag, GroupLogDTO logDTO, int size);

    /**
     * 组件关联内容日志
     *
     * @param groupId     圈子ID
     * @param logDTO      日志对象
     * @param contentList 具体内容
     */
    void addGatherContentLog(Long groupId, GroupLogDTO logDTO, List<GroupGatherContentDTO> contentList);

    /**
     * 组件关联内容权重日志
     *
     * @param groupId
     * @param logDTO
     * @param dbWeight
     * @param curWeight
     */
    void editGatherContentWeightLog(Long groupId, GroupLogDTO logDTO, Long gatherId, Integer dbWeight, Integer curWeight);

    /**
     * 组件关联内容日志
     *
     * @param groupId    圈子ID
     * @param logDTO     日志对象
     * @param mediaTitle 具体内容
     */
    void removeGatherContentLog(Long groupId, Long gatherId,GroupLogDTO logDTO, String mediaTitle);
}
