package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 社区问答组件详细表
 *
 * <AUTHOR>
 * @date 2022/5/12
 * Email: <EMAIL>
 */
@Data
public class GroupQaComponent {

    /**
     * 问答组件详细表ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 组件关联社区id
     */
    private Long groupId;
    /**
     * 社区组件主表ID
     */
    private Long componentId;

    /**
     * 展示样式:1-问答广场，2-找导师
     */
    private Integer answerType;
    /**
     * 显示标题
     */
    private String answerTitle;
    /**
     * 是否开启问答限制
     */
    private Boolean enableQaLimit;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;


}
