package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.api.client.MediaResourceService;
import com.jxntv.gvideo.group.api.converter.GroupContentConvert;
import com.jxntv.gvideo.group.api.converter.GroupTodayTalkConvert;
import com.jxntv.gvideo.group.api.entity.GroupGather;
import com.jxntv.gvideo.group.api.entity.GroupTopicContent;
import com.jxntv.gvideo.group.api.service.*;
import com.jxntv.gvideo.group.sdk.dto.GroupContentEditDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupGatherDTO;
import com.jxntv.gvideo.group.sdk.dto.TopicMediaStatus;
import com.jxntv.gvideo.group.sdk.dto.today.talk.GroupTodayTalkGatherExtendInfoDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.group.sdk.enums.today.talk.TodayTalkContribute;
import com.jxntv.gvideo.group.sdk.enums.today.talk.TodayTalkStatus;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkFakeVoteParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkVoteContributeSearchParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkVoteParam;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.enums.EnableEnum;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jxntv.gvideo.group.api.mapper.GroupTodayTalkMapper;
import com.jxntv.gvideo.group.api.entity.today.talk.GroupTodayTalk;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 圈子-今日聊话题业务实现类
 *
 * <AUTHOR>
 * @date 2022-10-18 14:56:01
 */
@Service("groupTodayTalkService")
@RefreshScope
public class GroupTodayTalkServiceImpl extends ServiceImpl<GroupTodayTalkMapper, GroupTodayTalk> implements GroupTodayTalkService {

    @Resource
    private MediaResourceService mediaResourceService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 默认今日聊官方账号id
     */
    @Value("${todayTalk.releaseId}")
    public Long releaseId;

    @Resource
    private GroupTopicContentService groupTopicContentService;

    @Resource
    private GroupContentConvert groupContentConvert;

    @Resource
    private GroupGatherService groupGatherService;
    @Resource
    private GroupTodayTalkVoteService groupTodayTalkVoteService;
    @Resource
    private GroupTodayTalkFakeVoteService groupTodayTalkFakeVoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(GroupTodayTalkParam dto) {
        this.updateReleaseDate(dto.getGroupId(), dto.getReleaseDate(), null);

        MediaResourceDTO mediaResourceDTO = GroupTodayTalkConvert.createMediaResource(dto);
        Result<Long> result = mediaResourceService.createTodayTalk(mediaResourceDTO);
        if (!result.callSuccess()) {
            throw new CodeMessageException(result.getCode(), result.getMessage());
        }
        //  插入和社区的关联关系
        GroupContentEditDTO groupContentEditDTO = new GroupContentEditDTO();
        groupContentEditDTO.setGroupId(dto.getGroupId());
        groupContentEditDTO.setGatherId(getGatherId(dto));
        groupContentEditDTO.setMediaId(result.getResult());
        groupContentEditDTO.setTopicMediaStatus(TopicMediaStatus.ENABLE.getCode());
        groupContentEditDTO.setWeight(0);
        GroupTopicContent groupTopicContent = groupContentConvert.convert(groupContentEditDTO);
        groupTopicContentService.save(groupTopicContent);

        GroupTodayTalk entity = GroupTodayTalkConvert.convert(dto);
        entity.setMediaStatus(MediaResourceStatus.AUDIT.getCode());
        entity.setCreateDate(LocalDateTime.now());
        entity.setContributeDate(LocalDateTime.now());
        entity.setMediaId(result.getResult());
        entity.setReleaseId(getOfficeReleaseId(dto.getGroupId()));
        boolean status = this.save(entity);
        if (status && TodayTalkContribute.UGC.getCode().equals(dto.getContributeType())) {
            applicationEventPublisher.publishEvent(entity);
        }
        return status;
    }

    /**
     * 获取关联组件id
     *
     * @param dto 今日聊入参
     * @return gatherId
     */
    private Long getGatherId(GroupTodayTalkParam dto) {
        if (Objects.nonNull(dto.getGatherId())) {
            return dto.getGatherId();
        }

        List<GroupGatherDTO> gatherList = groupGatherService.listByType(dto.getGroupId(), GroupGatherTypeEnum.TODAY_TALK.getCode());
        return CollectionUtils.isEmpty(gatherList) ? null : gatherList.get(0).getId();
    }


    /**
     * 获取官方发布账号id
     * 如果组件里面有设置官方发布账号，使用组件里面设置的
     *
     * @param groupId 社区id
     * @return 官方发布账号
     */
    private Long getOfficeReleaseId(Long groupId) {
        GroupGather gather = getTodayTalkGather(groupId);

        return Optional.ofNullable(gather).map(GroupGather::getExtendInfo).map(s -> JsonUtils.fromJson(s, GroupTodayTalkGatherExtendInfoDTO.class)).map(GroupTodayTalkGatherExtendInfoDTO::getOfficeReleaseId).orElse(releaseId);
    }


    private GroupGather getTodayTalkGather(Long groupId) {
        LambdaQueryWrapper<GroupGather> gatherQuery = Wrappers.lambdaQuery();
        gatherQuery.eq(GroupGather::getGroupId, groupId);
        gatherQuery.eq(GroupGather::getType, GroupGatherTypeEnum.TODAY_TALK.getCode());
        gatherQuery.eq(GroupGather::getStatus, EnableEnum.ENABLE.getCode());
        gatherQuery.orderByDesc(GroupGather::getWeight);
        gatherQuery.last(" limit 1");

        return groupGatherService.getOne(gatherQuery);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(GroupTodayTalkParam dto) {

        AssertUtil.notNull(dto.getId(), CodeMessage.BAD_REQUEST);
        GroupTodayTalk groupTodayTalk = this.getById(dto.getId());
        AssertUtil.notNull(groupTodayTalk, CodeMessage.NOT_FOUND);
        this.updateReleaseDate(groupTodayTalk.getGroupId(), dto.getReleaseDate(), groupTodayTalk.getId());
        GroupTodayTalk entity = GroupTodayTalkConvert.convert(dto);
        entity.setUpdateDate(LocalDateTime.now());
        if (Objects.isNull(entity.getReleaseDate())) {
            entity.setReleaseDate(groupTodayTalk.getReleaseDate());
        }
        if (TodayTalkStatus.OFF_LINE.getCode().equals(dto.getStatus())) {
            entity.setOfflineTime(LocalDateTime.now());
        }
        return this.updateById(entity);
    }

    @Override
    public GroupTodayTalk getByMediaId(Long mediaId) {
        return this.getOne(Wrappers.<GroupTodayTalk>lambdaQuery().eq(GroupTodayTalk::getMediaId, mediaId));
    }

    @Override
    public IPage<GroupTodayTalk> getVoteContributeList(GroupTodayTalkVoteContributeSearchParam dto) {
        Long groupId = dto.getGroupId();
        GroupGather todayTalkGather = getTodayTalkGather(groupId);
        Integer voteLimitDays = Optional.ofNullable(todayTalkGather).map(GroupGather::getExtendInfo).map(s -> JsonUtils.fromJson(s, GroupTodayTalkGatherExtendInfoDTO.class)).map(GroupTodayTalkGatherExtendInfoDTO::getVoteLimitDays).orElse(7);

        LocalDateTime now = LocalDateTime.now();

        //  第一档
        LocalDateTime firstStart = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0).minusDays(voteLimitDays - 1);
        LocalDateTime firstEnd = LocalDateTime.now();
        //  第二档
        LocalDateTime secondStart = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0);
        LocalDateTime secondEnd = LocalDateTime.now();
        //  第三档
        LocalDateTime thirdStart = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0).minusDays(voteLimitDays - 1);
        LocalDateTime thirdEnd = LocalDateTime.of(now.getYear(), now.getMonth(), now.getDayOfMonth(), 0, 0);


        Page<Long> voteContributeList = this.baseMapper.getVoteContributeList(Page.of(dto.getCurrent(), dto.getSize()), groupId, firstStart, firstEnd, secondStart, secondEnd, thirdStart, thirdEnd);

        return voteContributeList.convert(this::getById);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long vote(GroupTodayTalkVoteParam dto) {

        //  添加投票
        Long voteId = groupTodayTalkVoteService.create(dto);
        //  重新计算投票
        calculateVote(dto.getTodayTalkId());

        return voteId;
    }

    @Override
    public Long fakeVote(GroupTodayTalkFakeVoteParam dto) {

        //  添加人工干预历史
        Long voteId = groupTodayTalkFakeVoteService.create(dto);

        //  重新计算投票
        calculateVote(dto.getTodayTalkId());

        return voteId;
    }

    /**
     * 计算得票数据
     * 赞成票+1
     * 反对票-1
     * 人工干预直接累加
     *
     * @param todayTalkId 今日聊ID
     */
    private void calculateVote(Long todayTalkId) {
        GroupTodayTalk entity = this.getById(todayTalkId);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);


        int realAgreeCount = groupTodayTalkVoteService.getAgreeCount(todayTalkId);
        int real = groupTodayTalkVoteService.getDisagreeCount(todayTalkId);
        int fakeAgreeVote = groupTodayTalkFakeVoteService.getFakeAgreeVote(todayTalkId);
        int fakeDisAgreeVote = groupTodayTalkFakeVoteService.getFakeDisAgreeVote(todayTalkId);

        int agreeCount = realAgreeCount + fakeAgreeVote;
        int disagreeCount = real + fakeDisAgreeVote;

        int vote = agreeCount - disagreeCount;

        entity.setVote(vote);
        entity.setAgreeVote(agreeCount);
        entity.setDisagreeVote(disagreeCount);
        entity.setFakeVote(fakeDisAgreeVote);
        this.updateById(entity);
    }


    void updateReleaseDate(Long groupId, String releaseDate, Long todayTalkId) {
        if (StringUtils.isEmpty(releaseDate) || Objects.isNull(groupId)) {
            return;
        }

        LambdaQueryWrapper<GroupTodayTalk> query = new LambdaQueryWrapper<>();
        query.eq(GroupTodayTalk::getGroupId, groupId);
        query.eq(GroupTodayTalk::getReleaseDate, LocalDate.parse(releaseDate.replaceAll("-", ""), DateTimeFormatter.ofPattern("yyyyMMdd")));
        query.in(GroupTodayTalk::getStatus, Arrays.asList(TodayTalkStatus.NONE.getCode(), TodayTalkStatus.ON_LINE.getCode()));
        query.ne(Objects.nonNull(todayTalkId), GroupTodayTalk::getId, todayTalkId);
        GroupTodayTalk entity = this.getOne(query);
        if (Objects.nonNull(entity)) {
            if (TodayTalkStatus.ON_LINE.getCode().equals(entity.getStatus())) {
                throw new CodeMessageException(CodeMessage.ERROR.getCode(), "操作失败，该发布日期的今日聊话题已上线");
            }
            entity.setReleaseDate(null);
            entity.setUpdateDate(LocalDateTime.now());
            this.updateById(entity);
        }
    }
}