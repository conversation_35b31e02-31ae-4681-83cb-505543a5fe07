package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 内容推荐-首页推荐热门话题
 *
 * @TableName adm_topic_recommendation_d
 */
@TableName(value = "adm_topic_recommendation_d")
@Data
public class AdmTopicRecommendationD implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     *
     */
    private Integer topicId;

    /**
     *
     */
    private Integer rankNum;
}