package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <AUTHOR>
 * @description 圈子组件-广播信息
 * @date 2021/09/18 9:59
 */
@TableName(value = "group_gather_broadcast")
@Data
public class GroupGatherBroadcast implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 圈子ID
     */
    private Long groupId;
    /**
     * 组件ID
     */
    private Long gatherId;
    /**
     * 节目名称
     */
    private String name;
    /**
     * 节目地址
     */
    private String url;
    /**
     * 主持人类型：0-pgc、1-ugc
     */
    private Integer releaseType;
    /**
     * 主持人账号ID
     */
    private Long releaseId;
    /**
     * 开始时间
     */
    private LocalTime startTime;
    /**
     * 结束时间
     */
    private LocalTime endTime;
    /**
     * 周期：1-每周一、2-每周二、3-每周三、4-每周四、5-每周五、6-每周六、7-每周天，多个英文逗号分隔
     */
    private String weekLoop;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Integer del_flag;
}
