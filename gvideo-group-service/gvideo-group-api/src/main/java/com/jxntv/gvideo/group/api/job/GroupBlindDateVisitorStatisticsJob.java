package com.jxntv.gvideo.group.api.job;

import com.jxntv.gvideo.group.api.event.GroupBlindDateVisitorStatisticsEvent;
import com.jxntv.gvideo.group.api.mapper.GroupBlindDateVisitorMapper;
import com.jxntv.gvideo.group.sdk.dto.blind.date.visitor.GroupBlindDateVisitorStatisticsPushDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/11 11:28
 */
@Slf4j
@Component
@RefreshScope
public class GroupBlindDateVisitorStatisticsJob {

    @Resource
    GroupBlindDateVisitorMapper groupBlindDateVisitorMapper;

    @Resource
    ApplicationEventPublisher applicationEventPublisher;

    @XxlJob("groupBlindDateVisitorStatisticsJob")
    public ReturnT<String> execute() {
        XxlJobHelper.log("放心爱-访客-统计任务开始");
        List<GroupBlindDateVisitorStatisticsPushDTO> list = groupBlindDateVisitorMapper.pushStatistics();
        list.forEach(e -> {
            GroupBlindDateVisitorStatisticsEvent event = new GroupBlindDateVisitorStatisticsEvent();
            event.setJid(e.getJid());
            event.setVisitTotal(e.getVisitTotal());
            applicationEventPublisher.publishEvent(event);
        });

        XxlJobHelper.log("放心爱-访客-统计任务结束");
        return ReturnT.SUCCESS;
    }
}
