package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 *  用户问答总结
 *  <AUTHOR>
 *  @date 2022/6/13 15:22
 */
@Getter
@Setter
@ToString
@TableName(value = "qa_user_summary")
public class QaUserSummary implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 学生id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 答题状态:0:已出题,1:完成答题
     */
    @TableField(value = "`status`")
    private Integer status;

    /**
     * 总数
     */
    @TableField(value = "total_num")
    private Integer totalNum;

    /**
     * 正确数量
     */
    @TableField(value = "correct_num")
    private Integer correctNum;

    /**
     * 错误数量
     */
    @TableField(value = "wrong_num")
    private Integer wrongNum;

    /**
     * 超时数量
     */
    @TableField(value = "miss_num")
    private Integer missNum;

    /**
     * 总分
     */
    @TableField(value = "total_score")
    private BigDecimal totalScore;

    /**
     * 题目id,逗号分隔
     */
    @TableField(value = "question_ids")
    private String questionIds;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public static final String COL_ID = "id";

    public static final String COL_USER_ID = "user_id";

    public static final String COL_STATUS = "status";

    public static final String COL_TOTAL_NUM = "total_num";

    public static final String COL_CORRECT_NUM = "correct_num";

    public static final String COL_WRONG_NUM = "wrong_num";

    public static final String COL_MISS_NUM = "miss_num";

    public static final String COL_TOTAL_SCORE = "total_score";

    public static final String COL_RANKING = "ranking";

    public static final String COL_QUESTION_IDS = "question_ids";

    public static final String COL_CREATE_TIME = "create_time";

    public static final String COL_UPDATE_TIME = "update_time";
}
