package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.converter.GroupUserMarkComponentRewardConverter;
import com.jxntv.gvideo.group.api.entity.GroupUserMarkComponentReward;
import com.jxntv.gvideo.group.api.mapper.GroupUserMarkComponentRewardMapper;
import com.jxntv.gvideo.group.api.service.GroupUserMarkComponentRewardService;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.GroupUserMarkComponentRewardDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 社区用户签到奖励
 *
 * <AUTHOR>
 * @date 2022/6/23
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class GroupUserMarkComponentRewardServiceImpl extends ServiceImpl<GroupUserMarkComponentRewardMapper, GroupUserMarkComponentReward> implements GroupUserMarkComponentRewardService {

    @Resource
    private GroupUserMarkComponentRewardConverter groupUserMarkComponentRewardConverter;

    @Override
    public List<GroupUserMarkComponentRewardDTO> queryRewardListByMarkId(Long markId) {
        LambdaQueryWrapper<GroupUserMarkComponentReward> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GroupUserMarkComponentReward::getMarkId, markId);
        List<GroupUserMarkComponentReward> list = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.stream().map(groupUserMarkComponentRewardConverter::convert).collect(Collectors.toList());
    }

    @Override
    public List<GroupUserMarkComponentRewardDTO> queryRewardListByComponentId(Long componentId) {
        List<GroupUserMarkComponentReward> list = this.baseMapper.queryRewardListByComponentId(componentId);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.stream().map(groupUserMarkComponentRewardConverter::convert).collect(Collectors.toList());
    }

    @Override
    public GroupUserMarkComponentRewardDTO queryRewardInfoByComponentId(Long componentId, Integer rewardType, Integer markDays) {
        GroupUserMarkComponentReward entity = this.baseMapper.queryRewardInfoByComponentId(componentId, rewardType, markDays);
        return groupUserMarkComponentRewardConverter.convert(entity);
    }

    @Override
    public List<GroupUserMarkComponentRewardDTO> queryRewardListByComponentIdAndRewardType(Long componentId, Integer rewardType) {
        List<GroupUserMarkComponentReward> list = this.baseMapper.queryRewardListByComponentIdAndRewardType(componentId,rewardType);
        if (CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.stream().map(groupUserMarkComponentRewardConverter::convert).collect(Collectors.toList());
    }
}
