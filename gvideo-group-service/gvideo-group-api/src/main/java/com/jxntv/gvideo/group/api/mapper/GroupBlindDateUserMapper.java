package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUser;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateDicDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/16
 */
public interface GroupBlindDateUserMapper extends BaseMapper<GroupBlindDateUser> {
    /**
     * 获取兴趣爱好字典
     * @return
     */
    List<GroupBlindDateDicDTO>  hobbyDic();

    /**
     * 获取职业字典
     * @return
     */
    List<GroupBlindDateDicDTO>  occupationDic();

    /**
     * 获取单位字典
     * @return
     */
    List<GroupBlindDateDicDTO>  companyDic();

    /**
     * 通过职业ID获取职业名称
     * @return
     */
    String occupationById(@Param("id") Integer id);

    /**
     *  获取单位待审核的报名信息记录
     * @param jid
     * @return
     */
    GroupBlindDateUser getCompanyAuditRecord(@Param("jid") Long jid);

    /**
     *  通过JID查询该用户有无单位审核权限
     * @param jid
     * @return
     */
    Integer getCompanyCountByJid(@Param("jid") Long jid);

    /**
     * 通过职业ID获取职业信息
     * @return
     */
    GroupBlindDateDicDTO occupationDicById(@Param("id") Integer id);

}
