package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.group.api.converter.GroupBlindDateAccostConvert;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateAccost;
import com.jxntv.gvideo.group.api.event.GroupBlindDateAccostEvent;
import com.jxntv.gvideo.group.api.mapper.GroupBlindDateAccostMapper;
import com.jxntv.gvideo.group.api.service.GroupBlindDateAccostService;
import com.jxntv.gvideo.group.api.service.GroupBlindDateRedBeansService;
import com.jxntv.gvideo.group.sdk.dto.blind.date.accost.GroupBlindDateAccostSendDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.redBeans.GroupBlindDateRedBeansAdjustDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.redBeans.GroupBlindDateRedBeansDTO;
import com.jxntv.gvideo.group.sdk.enums.group.blind.date.RedBeansAdjustEnum;
import com.jxntv.gvideo.group.sdk.enums.group.blind.date.RedBeansTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 圈子-都市放心爱-招呼业务实现类
 *
 * <AUTHOR>
 * @date 2022-07-05 14:57:28
 */
@Service("groupBlindDateAccostService")
public class GroupBlindDateAccostServiceImpl extends ServiceImpl<GroupBlindDateAccostMapper, GroupBlindDateAccost> implements GroupBlindDateAccostService {

    @Resource
    private GroupBlindDateRedBeansService groupBlindDateRedBeansService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAccost(GroupBlindDateAccostSendDTO dto) {
        LambdaQueryWrapper<GroupBlindDateAccost> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupBlindDateAccost::getSendJid,dto.getSendJid());
        queryWrapper.eq(GroupBlindDateAccost::getReceiveJid,dto.getReceiveJid());
        int count = this.count(queryWrapper);
        if (count > 0){
            throw new CodeMessageException(CodeMessage.ERROR,"您已打过招呼，请勿重复操作");
        }
        if (Boolean.TRUE.equals(dto.getCheckRedBeans())){
            GroupBlindDateRedBeansDTO redBeansDTO = this.groupBlindDateRedBeansService.queryByJid(dto.getSendJid());
            if (Objects.isNull(redBeansDTO)){
                throw new CodeMessageException(CodeMessage.ERROR,"获取红豆信息失败");
            }
            if (redBeansDTO.getAccostTimes() >= dto.getFreeAccostTimes() && redBeansDTO.getAmount() < RedBeansTypeEnum.ACCOST_SEND.getAmount()){
                throw new CodeMessageException(CodeMessage.ERROR,"红豆不足");
            }
        }

        GroupBlindDateAccost entity = GroupBlindDateAccostConvert.convert(dto);
        boolean status = this.save(entity);
        if (status){
            if (Boolean.TRUE.equals(dto.getCheckRedBeans())) {
                GroupBlindDateRedBeansAdjustDTO adjustDTO = new GroupBlindDateRedBeansAdjustDTO();
                adjustDTO.setJid(dto.getSendJid());
                adjustDTO.setType(RedBeansTypeEnum.ACCOST_SEND.getCode());
                adjustDTO.setTitle(RedBeansTypeEnum.ACCOST_SEND.getTitle());
                adjustDTO.setAmount(RedBeansTypeEnum.ACCOST_SEND.getAmount());
                adjustDTO.setAdjustType(RedBeansAdjustEnum.DEDUCT.getCode());
                adjustDTO.setRemarks("打招呼对象jid:" + dto.getReceiveJid());
                adjustDTO.setFreeAccostTimes(dto.getFreeAccostTimes());
                this.groupBlindDateRedBeansService.saveRedBeanAdjust(adjustDTO);
            }
            GroupBlindDateAccostEvent event  = new GroupBlindDateAccostEvent();
            event.setId(entity.getId());
            event.setSendJid(entity.getSendJid());
            event.setReceiveJid(entity.getReceiveJid());
            event.setStatus(entity.getStatus());
            event.setDeductRedBeansFlag(dto.getCheckRedBeans());
            event.setFreeAccostTimes(dto.getFreeAccostTimes());
            applicationEventPublisher.publishEvent(event);
        }
        return status;
    }
}