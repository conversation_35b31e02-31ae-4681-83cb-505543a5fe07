package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.entity.GroupIcon;
import com.jxntv.gvideo.group.api.entity.gkmn.GkmnQuestions;
import com.jxntv.gvideo.group.api.mapper.GkmnQuestionsMapper;
import com.jxntv.gvideo.group.api.mapper.GroupIconMapper;
import com.jxntv.gvideo.group.api.service.GkmnQuestionLogService;
import com.jxntv.gvideo.group.api.service.GkmnQuestionsService;
import com.jxntv.gvideo.group.api.service.GroupIconService;
import org.springframework.stereotype.Service;

/**
 * @Author: huyapeng
 * @Date: 2023/08/03 09:40
 */
@Service
public class GkmnQuestionsServiceImpl extends ServiceImpl<GkmnQuestionsMapper, GkmnQuestions> implements GkmnQuestionsService {
}
