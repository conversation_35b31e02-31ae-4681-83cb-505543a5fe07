package com.jxntv.gvideo.group.api.service.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jxntv.gvideo.group.api.mapper.GroupPrivacyAgreementConfirmMapper;
import com.jxntv.gvideo.group.api.entity.GroupPrivacyAgreementConfirm;
import com.jxntv.gvideo.group.api.service.GroupPrivacyAgreementConfirmService;

/**
 * 圈子-隐私协议-确认业务实现类
 *
 * <AUTHOR>
 * @date 2022-07-12 09:39:08
 */
@Service("groupPrivacyAgreementConfirmService")
public class GroupPrivacyAgreementConfirmServiceImpl extends ServiceImpl<GroupPrivacyAgreementConfirmMapper, GroupPrivacyAgreementConfirm> implements GroupPrivacyAgreementConfirmService {


}