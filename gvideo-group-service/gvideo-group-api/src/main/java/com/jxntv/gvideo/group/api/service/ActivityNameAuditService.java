package com.jxntv.gvideo.group.api.service;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.ActivityNameAuditDTO;
import com.jxntv.gvideo.group.sdk.dto.ActivityNameAuditSearchDTO;
import com.jxntv.gvideo.group.sdk.dto.BatchActivityNameAuditDTO;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2022/09/16 10:40
 */
public interface ActivityNameAuditService {

    Result<PageDTO<ActivityNameAuditDTO>> page(ActivityNameAuditSearchDTO activityNameAuditSearchDTO);

    Result<Boolean> batchAdd(List<ActivityNameAuditDTO> activityNameAuditDTOs);

    Result<Boolean> batchPassOrReject(BatchActivityNameAuditDTO batchActivityNameAuditDTOt);

    Result<Boolean> reset(ActivityNameAuditDTO activityNameAuditDTO);

    boolean effectActivityNameAudit(Long id);

    boolean hasNotCheckName(Long jid, String type);

    boolean updateAuditProcedureId(Long id, Long procedureId);
}
