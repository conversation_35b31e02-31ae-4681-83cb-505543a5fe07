package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.GroupGatherContent;
import com.jxntv.gvideo.group.sdk.dto.*;

/**
 * <AUTHOR>
 * @description
 * @date 2021/08/18 15:46
 */
public interface GroupGatherContentService extends IService<GroupGatherContent> {

    /**
     * 关联内容
     *
     * @param relateDTO
     */
    void relateContent(GroupGatherContentRelateDTO relateDTO);

    /**
     * 修改内容权重
     * @param gatherContent
     * @param dto
     */
    void editRelateContentWeight(GroupGatherContent gatherContent, GroupGatherContentWeightDTO dto);

    /**
     * 移除内容
     *
     * @param id
     */
    void deleteRelateContent(Long id, Long groupId, Long gatherId, String mediaTitle, GroupLogDTO logDTO);

    /**
     * 分页查询组件内容列表
     *
     * @param page
     * @param searchDTO
     * @return
     */
    IPage<GroupGatherContentDTO> queryGatherContentPage(Page<GroupGatherContentDTO> page, GatherContentSearchDTO searchDTO);

    GroupGatherContentDTO queryGatherContentByMediaId(Long mediaId);
}
