package com.jxntv.gvideo.group.api.converter;

import com.jxntv.gvideo.common.utils.DesensitizedUtil;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivity;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivityMember;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivityMemberInvite;
import com.jxntv.gvideo.group.api.utils.DateUtils;
import com.jxntv.gvideo.group.sdk.dto.activity.*;
import com.jxntv.gvideo.group.sdk.dto.activity.plan.GroupGatherActivityPlanInviteCreateDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.plan.GroupGatherActivityPlanInviteDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.plan.GroupGatherActivityPlanMemberDTO;
import com.jxntv.gvideo.group.sdk.enums.ActivityStatusEnum;
import com.jxntv.gvideo.group.sdk.enums.MemberAuditStatusEnum;
import com.jxntv.gvideo.media.client.enums.WhetherEnum;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 组件活动转换
 */
@Component
public class GroupGatherActivityConvert {

    public static GroupGatherActivityDTO convertActivity(GroupGatherActivity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherActivityDTO dto = new GroupGatherActivityDTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setGroupGatherId(entity.getGroupGatherId());
        dto.setTitle(entity.getTitle());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setJoinStartDate(entity.getJoinStartDate());
        dto.setJoinEndDate(entity.getJoinEndDate());
        dto.setAddress(entity.getAddress());
        dto.setCostFlag(entity.getCostFlag());
        dto.setCost(entity.getCost());
        dto.setMemberFrom(entity.getMemberFrom());
        dto.setMemberTo(entity.getMemberTo());
        dto.setIntroduction(entity.getIntroduction());
        dto.setPushFlag(entity.getPushFlag());
        dto.setPushContent(entity.getPushContent());
        dto.setStatus(entity.getStatus());
        dto.setWeight(entity.getWeight());
        dto.setRedBeansFlag(entity.getRedBeansFlag());
        dto.setRedBeansAmount(entity.getRedBeansAmount());
        dto.setTagId(entity.getTagId());
        dto.setLatitude(entity.getLatitude());
        dto.setLongitude(entity.getLongitude());
        dto.setType(entity.getType());
        return dto;
    }

    public static GroupGatherActivityV2DTO convertActivityV2(GroupGatherActivity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherActivityV2DTO dto = new GroupGatherActivityV2DTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setGroupGatherId(entity.getGroupGatherId());
        dto.setTitle(entity.getTitle());
        dto.setStartDate(entity.getStartDate());
        dto.setEndDate(entity.getEndDate());
        dto.setJoinStartDate(entity.getJoinStartDate());
        dto.setJoinEndDate(entity.getJoinEndDate());
        dto.setAddress(entity.getAddress());
        dto.setCostFlag(entity.getCostFlag());
        dto.setCost(entity.getCost());
        dto.setMemberFrom(entity.getMemberFrom());
        dto.setMemberTo(entity.getMemberTo());
        dto.setIntroduction(entity.getIntroduction());
        dto.setPushFlag(entity.getPushFlag());
        dto.setPushContent(entity.getPushContent());
        dto.setStatus(entity.getStatus());
        dto.setWeight(entity.getWeight());
        dto.setRedBeansFlag(entity.getRedBeansFlag());
        dto.setRedBeansAmount(entity.getRedBeansAmount());
        dto.setTagId(entity.getTagId());
        dto.setLatitude(entity.getLatitude());
        dto.setLongitude(entity.getLongitude());
        dto.setType(entity.getType());
        return dto;
    }

    public static GroupGatherActivityMemberDTO convertMember(GroupGatherActivityMember entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherActivityMemberDTO dto = new GroupGatherActivityMemberDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setJid(entity.getJid());
        dto.setEnrollDate(entity.getEnrollDate());
        dto.setAuditStatus(entity.getAuditStatus());
        dto.setAuditDate(entity.getAuditDate());
        dto.setAuditUserId(entity.getAuditUserId());
        dto.setAuditUserName(entity.getAuditUserName());
        dto.setRemarks(entity.getRemarks());
        dto.setMobile(entity.getMobile());
        dto.setMaskMobile(entity.getMaskMobile());
        dto.setName(entity.getName());
        dto.setGender(entity.getGender());
        dto.setBirthday(entity.getBirthday());
        dto.setHeight(entity.getHeight());
        dto.setEducation(entity.getEducation());
        dto.setMarital(entity.getMarital());
        dto.setCompanyId(entity.getCompanyId());
        dto.setCompanyName(entity.getCompanyName());
        dto.setPostType(entity.getPostType());
        dto.setProvinceId(entity.getProvinceId());
        dto.setProvinceName(entity.getProvinceName());
        dto.setCityId(entity.getCityId());
        dto.setCityName(entity.getCityName());
        dto.setNickname(entity.getNickname());
        dto.setPhotoOssId(entity.getPhotoOssId());
        dto.setManifesto(entity.getManifesto());
        dto.setPairedFlag(entity.getPairedFlag());
        dto.setPairedJid(entity.getPairedJid());
        dto.setPairedTime(entity.getPairedTime());
        return dto;
    }


    public static GroupGatherActivityMember convertMemberEnroll(GroupGatherActivityMemberEnrollDTO dto, ConsumerUserDTO userDTO) {

        Date current = new Date();

        GroupGatherActivityMember entity = new GroupGatherActivityMember();
        entity.setActivityId(dto.getActivityId());
        entity.setJid(dto.getJid());
        entity.setEnrollDate(current);
        entity.setCreateDate(current);
        entity.setAuditStatus(MemberAuditStatusEnum.UN_AUDIT.getCode());
        entity.setNickname(dto.getNickname());
        entity.setManifesto(dto.getManifesto());
        if (Objects.nonNull(dto.getBlindDateUser())) {
            entity.setMobile(dto.getBlindDateUser().getMobile());
            entity.setMaskMobile(DesensitizedUtil.desensitizedPhone(dto.getBlindDateUser().getMobile()));
            entity.setName(dto.getBlindDateUser().getName());
            entity.setGender(dto.getBlindDateUser().getGender());
            entity.setBirthday(DateUtils.parse(dto.getBlindDateUser().getBirthday(), "yyyy-MM-dd"));
            entity.setHeight(dto.getBlindDateUser().getHeight());
            entity.setEducation(dto.getBlindDateUser().getEducation());
            entity.setMarital(dto.getBlindDateUser().getMarital());
            entity.setCompanyId(dto.getBlindDateUser().getCompanyId());
            entity.setCompanyName(dto.getBlindDateUser().getCompanyName());
            entity.setPostType(dto.getBlindDateUser().getPostType());
            entity.setProvinceId(Objects.nonNull(dto.getBlindDateUser().getProvinceId()) ? dto.getBlindDateUser().getProvinceId().intValue() : null);
            entity.setProvinceName(dto.getBlindDateUser().getProvinceName());
            entity.setCityId(Objects.nonNull(dto.getBlindDateUser().getCityId()) ? dto.getBlindDateUser().getCityId().intValue() : null);
            entity.setCityName(dto.getBlindDateUser().getCityName());
            entity.setPhotoOssId(dto.getPhotoOssId());
        } else {
            entity.setName(userDTO.getNickname());
            entity.setMobile(userDTO.getMobile());
            entity.setGender(userDTO.getGender());
            entity.setBirthday(userDTO.getBirthday());
            entity.setPhotoOssId(userDTO.getAvatar());

        }
        return entity;
    }

    public static GroupGatherActivity convertActivityInsert(GroupGatherActivityInsertDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherActivity entity = new GroupGatherActivity();
        entity.setGroupId(dto.getGroupId());
        entity.setGroupGatherId(dto.getGroupGatherId());
        entity.setTitle(dto.getTitle());
        entity.setStartDate(DateUtils.parse(dto.getStartDate(), "yyyy-MM-dd HH:mm"));
        entity.setEndDate(DateUtils.parse(dto.getEndDate(), "yyyy-MM-dd HH:mm"));
        entity.setJoinStartDate(DateUtils.parse(dto.getJoinStartDate(), "yyyy-MM-dd HH:mm"));
        entity.setJoinEndDate(DateUtils.parse(dto.getJoinEndDate(), "yyyy-MM-dd HH:mm"));
        entity.setAddress(dto.getAddress());
        entity.setCostFlag(dto.getCostFlag());
        entity.setCost(dto.getCost());
        entity.setMemberFrom(dto.getMemberFrom());
        entity.setMemberTo(dto.getMemberTo());
        entity.setIntroduction(dto.getIntroduction());
        entity.setStatus(ActivityStatusEnum.UN_START.getCode());
        entity.setPushFlag(dto.getPushFlag());
        entity.setPushContent(dto.getPushContent());
        entity.setDelFlag(0);
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setCreateDate(new Date());
        entity.setWeight(dto.getWeight());
        entity.setRedBeansFlag(dto.getRedBeansFlag());
        entity.setRedBeansAmount(dto.getRedBeansAmount());
        entity.setLatitude(dto.getLatitude());
        entity.setLongitude(dto.getLongitude());
        entity.setType(dto.getType());
        entity.setTagId(dto.getTagId());
        return entity;
    }

    public static GroupGatherActivity convertActivityUpdate(GroupGatherActivityUpdateDTO dto, GroupGatherActivity entity) {
        if (Objects.isNull(dto)) {
            return null;
        }
        entity.setTitle(dto.getTitle());
        entity.setStartDate(DateUtils.parse(dto.getStartDate(), "yyyy-MM-dd HH:mm"));
        entity.setEndDate(DateUtils.parse(dto.getEndDate(), "yyyy-MM-dd HH:mm"));
        entity.setJoinStartDate(DateUtils.parse(dto.getJoinStartDate(), "yyyy-MM-dd HH:mm"));
        entity.setJoinEndDate(DateUtils.parse(dto.getJoinEndDate(), "yyyy-MM-dd HH:mm"));
        entity.setAddress(dto.getAddress());
        entity.setCostFlag(dto.getCostFlag());
        entity.setCost(dto.getCost());
        entity.setMemberFrom(dto.getMemberFrom());
        entity.setMemberTo(dto.getMemberTo());
        entity.setIntroduction(dto.getIntroduction());
        entity.setDelFlag(0);
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(new Date());
        entity.setWeight(dto.getWeight());
        entity.setLatitude(dto.getLatitude());
        entity.setLongitude(dto.getLongitude());
        entity.setTagId(dto.getTagId());
        return entity;
    }


    public static GroupGatherActivityPlanInviteDTO convertInvite(GroupGatherActivityMemberInvite entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherActivityPlanInviteDTO dto = new GroupGatherActivityPlanInviteDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setJid(entity.getJid());
        dto.setInviteJid(entity.getInviteJid());
        dto.setInvitation(entity.getInvitation());
        dto.setStatus(entity.getStatus());
        dto.setInviteReadFlag(entity.getInviteReadFlag());
        return dto;
    }

    public static GroupGatherActivityMemberInvite convertInvite(GroupGatherActivityPlanInviteCreateDTO dto) {
        if (Objects.isNull(dto)) {
            return null;
        }
        GroupGatherActivityMemberInvite entity = new GroupGatherActivityMemberInvite();
        entity.setActivityId(dto.getActivityId());
        entity.setJid(dto.getLoginJid());
        entity.setInviteJid(dto.getInviteJid());
        entity.setInvitation(dto.getInvitation());
        entity.setStatus(WhetherEnum.NO.getCode());
        entity.setInviteReadFlag(WhetherEnum.NO.getCode());
        entity.setCreateDate(new Date());
        entity.setUpdateDate(new Date());
        return entity;
    }

    public static GroupGatherActivityPlanMemberDTO convertPlanMember(GroupGatherActivityMember entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        GroupGatherActivityPlanMemberDTO dto = new GroupGatherActivityPlanMemberDTO();
        dto.setId(entity.getId());
        dto.setActivityId(entity.getActivityId());
        dto.setJid(entity.getJid());
        dto.setManifesto(entity.getManifesto());
        dto.setPairedFlag(entity.getPairedFlag());
        dto.setNickname(entity.getNickname());
        dto.setGender(entity.getGender());
        dto.setAge(DateUtils.getAge(entity.getBirthday()));
        return dto;
    }
}
