package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.GroupUserMarkComponent;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.GroupUserMarkComponentDTO;

/**
 * <AUTHOR>
 * @date 2022/5/24
 * Email: <EMAIL>
 */
public interface GroupUserMarkComponentService extends IService<GroupUserMarkComponent> {

    GroupUserMarkComponent getByComponentId(Long componentId);

    Long saveOrUpdateByComponentId(Long componentId, GroupUserMarkComponentDTO dto);

    Boolean deleteByComponentId(Long componentId);

}
