package com.jxntv.gvideo.group.api.entity.gkmn;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GkmnMember {

    @TableId(type = IdType.AUTO)
    private Long id;
    private Long jid;
    private Long groupId;
    private Integer score;
    private String province;
    private String city;
    private Integer district;
    private String school;
    private String grade;
    @TableField("class")
    private String className;
    private String studentName;
    private String mobile;
    private String teacherName;
    private Integer profileStatus;
    private Integer energyValue;
    private LocalDateTime energyLastupdate;
    private Integer highestScore;
    private Integer highestUnixtime;
    private Integer status;
    private LocalDateTime created;
    private LocalDateTime lastLogin;
    private LocalDateTime modified;
    private LocalDateTime completeTime;

}
