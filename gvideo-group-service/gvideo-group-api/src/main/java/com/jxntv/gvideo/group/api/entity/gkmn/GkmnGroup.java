package com.jxntv.gvideo.group.api.entity.gkmn;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class GkmnGroup {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String name;
    private Integer questionOrder;
    private Integer totalQuestions;
    private Integer dailyQuestions;
    private Integer status;
    private LocalDateTime created;
}
