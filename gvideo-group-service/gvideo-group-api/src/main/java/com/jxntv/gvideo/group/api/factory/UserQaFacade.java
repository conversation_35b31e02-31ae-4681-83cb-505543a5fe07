package com.jxntv.gvideo.group.api.factory;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.params.qa.QaAnswerParam;
import com.jxntv.gvideo.group.sdk.params.qa.QaSummaryParam;
import com.jxntv.gvideo.group.sdk.params.qa.QaQuestionParam;

/**
 *  用户问答接口
 *  <AUTHOR>
 *  @date 2022/6/13 14:42
 */
public interface UserQaFacade {

    /**
     * 获取问答列表
     * @param param
     * @return
     */
    Result getQuestions(QaQuestionParam param);

    /**
     * 获取当前问题的答案
     * @param param
     * @return
     */
    Result getAnswer(QaAnswerParam param);

    /**
     * 获取问答总结
     * @param param
     * @return
     */
    Result summary(QaSummaryParam param);


    /**
     * 当天答题次数
     * @param userId
     * @return
     */
    Result todayTimes(Long userId);
}
