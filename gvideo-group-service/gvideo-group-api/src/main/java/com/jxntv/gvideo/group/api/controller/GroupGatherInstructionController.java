package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.group.api.converter.GroupGatherInstructionConvert;
import com.jxntv.gvideo.group.api.entity.GroupGatherInstruction;
import com.jxntv.gvideo.group.api.entity.GroupGatherInstructionRelate;
import com.jxntv.gvideo.group.api.service.GroupGatherInstructionRelateService;
import com.jxntv.gvideo.group.api.service.GroupGatherInstructionService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.GroupGatherInstructionClient;
import com.jxntv.gvideo.group.sdk.dto.MediaIdDTO;
import com.jxntv.gvideo.group.sdk.dto.instruction.*;
import com.jxntv.gvideo.group.sdk.enums.DelFlagEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;


/**
 * <AUTHOR>
 * @ClassName GroupGatherInstructionController.java
 * @Description 圈子-组件-课堂教学
 */
@Slf4j
@RestController
public class GroupGatherInstructionController implements GroupGatherInstructionClient {

    @Resource
    private GroupGatherInstructionService groupGatherInstructionService;

    @Resource
    private GroupGatherInstructionConvert groupGatherInstructionConvert;

    @Resource
    private GroupGatherInstructionRelateService groupGatherInstructionRelateService;


    @Override
    public Result<PageDTO<GroupGatherInstructionDTO>> page(GroupGatherInstructionSearchDTO searchDTO) {
        LambdaQueryWrapper<GroupGatherInstruction> query = new LambdaQueryWrapper<>();

        query.eq(Objects.nonNull(searchDTO.getId()), GroupGatherInstruction::getId, searchDTO.getId());
        query.eq(Objects.nonNull(searchDTO.getGroupId()), GroupGatherInstruction::getGroupId, searchDTO.getGroupId());
        query.eq(Objects.nonNull(searchDTO.getGroupGatherId()), GroupGatherInstruction::getGroupGatherId, searchDTO.getGroupGatherId());
        query.eq(Objects.nonNull(searchDTO.getStatus()), GroupGatherInstruction::getStatus, searchDTO.getStatus());
        query.eq(GroupGatherInstruction::getDelFlag, DelFlagEnum.No.getCode());
        query.like(StringUtils.hasText(searchDTO.getName()), GroupGatherInstruction::getName, searchDTO.getName());
        query.like(StringUtils.hasText(searchDTO.getTag()), GroupGatherInstruction::getTag, searchDTO.getTag());

        if (CollectionUtils.isEmpty(searchDTO.getSorts())) {
            //  默认按照创建时间倒排
            query.orderByDesc(GroupGatherInstruction::getCreateDate);

        } else {
            //  设置排序参数
            for (SearchDTO.Sort sort : searchDTO.getSorts()) {
                //  按照上线日期排序
                query.orderBy("onLineDate".equals(sort.getOrderBy()), sort.isAsc(), GroupGatherInstruction::getOnLineDate);
            }
        }
        IPage<GroupGatherInstruction> page = this.groupGatherInstructionService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), query);
        return Result.ok(PageUtils.pageOf(page, groupGatherInstructionConvert::convert));
    }


    @Override
    public Result<GroupGatherInstructionDTO> getById(Long id) {
        if (Objects.isNull(id)) {
            return Result.fail("ID为空");
        }
        GroupGatherInstruction entity = this.groupGatherInstructionService.getById(id);
        if (Objects.isNull(entity) || Objects.equals(entity.getDelFlag(), DelFlagEnum.Yes.getCode())) {
            return Result.ok(null);
        }

        return Result.ok(groupGatherInstructionConvert.convert(entity));
    }

    @Override
    public Result<Long> add(GroupGatherInstructionInsertDTO dto) {
        GroupGatherInstruction entity = groupGatherInstructionConvert.convertInsert(dto);
        this.groupGatherInstructionService.save(entity);
        return Result.ok(entity.getId()) ;
    }

    @Override
    public Result<Boolean> update(GroupGatherInstructionUpdateDTO dto) {
        GroupGatherInstruction entity = this.groupGatherInstructionService.getById(dto.getId());
        if (Objects.isNull(entity) || Objects.equals(entity.getDelFlag(), DelFlagEnum.Yes.getCode())) {
            return Result.fail(CodeMessage.NOT_FOUND.getCode(), CodeMessage.NOT_FOUND.getMessage());
        }
        GroupGatherInstruction groupGatherInstruction = groupGatherInstructionConvert.convertUpdate(dto);
        groupGatherInstruction.setId(entity.getId());
        return Result.ok(this.groupGatherInstructionService.updateById(groupGatherInstruction));
    }

    @Override
    public Result<Boolean> delete(GroupGatherInstructionDeleteDTO dto) {
        if (Objects.isNull(dto.getId())) {
            return Result.fail("ID为空");
        }
        GroupGatherInstruction entity = this.groupGatherInstructionService.getById(dto.getId());
        if (Objects.isNull(entity)) {
            return Result.fail(CodeMessage.NOT_FOUND.getCode(), CodeMessage.NOT_FOUND.getMessage());
        }
        if (Objects.equals(DelFlagEnum.Yes.getCode(), entity.getDelFlag())) {
            return Result.fail("数据已删除，请勿重复操作");
        }
        entity.setDelFlag(DelFlagEnum.Yes.getCode());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(new Date());
        return Result.ok(this.groupGatherInstructionService.updateById(entity));
    }

    @Override
    public Result<Boolean> updateStatus(GroupGatherInstructionStatusDTO dto) {
        GroupGatherInstruction entity = this.groupGatherInstructionService.getById(dto.getId());
        if (Objects.isNull(entity) || Objects.equals(entity.getDelFlag(), DelFlagEnum.Yes.getCode())) {
            return Result.fail(CodeMessage.NOT_FOUND.getCode(), CodeMessage.NOT_FOUND.getMessage());
        }
        if (Objects.equals(2,dto.getStatus())){
            entity.setOnLineDate(new Date());
        }
        if (Objects.equals(3,dto.getStatus())){
            entity.setOffLineDate(new Date());
        }
        entity.setStatus(dto.getStatus());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(new Date());
        return Result.ok(this.groupGatherInstructionService.updateById(entity));
    }

    @Override
    public Result<PageDTO<MediaIdDTO>> queryLearnList(GroupGatherInstructionLearnSearchDTO dto) {
        IPage<MediaIdDTO> result = this.groupGatherInstructionRelateService.queryLearnList(dto);
        return Result.ok(PageUtils.pageOf(result));
    }

    @Override
    public Result<GroupGatherInstructionDTO> getByMediaId(Long mediaId) {
        LambdaQueryWrapper<GroupGatherInstruction> programQuery = Wrappers.lambdaQuery();
        programQuery.eq(GroupGatherInstruction::getMediaId, mediaId);
        GroupGatherInstruction entity = this.groupGatherInstructionService.getOne(programQuery);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);
        return Result.ok(groupGatherInstructionConvert.convert(entity));
    }

    @Override
    public Result<Boolean> saveLearnRelate(GroupGatherInstructionLearnRelateDTO dto) {
        GroupGatherInstruction instruction = this.groupGatherInstructionService.getById(dto.getInstructionId());
        if (Objects.nonNull(instruction)){
            GroupGatherInstructionRelate entity = new GroupGatherInstructionRelate();
            entity.setInstructionId(instruction.getId());
            entity.setInstructionMediaId(instruction.getMediaId());
            entity.setContentId(dto.getMediaId());
            entity.setCreateDate(new Date());
            this.groupGatherInstructionRelateService.save(entity);
        }
        return Result.ok(Boolean.TRUE);
    }
}


