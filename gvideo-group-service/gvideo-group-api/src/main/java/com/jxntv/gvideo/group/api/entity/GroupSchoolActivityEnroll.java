package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxntv.gvideo.common.mybatis.AesEncryptTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 社区-学校活动-报名表
 * 
 * <AUTHOR>
 * @date 2023-06-15 16:36:32
 */
@Data
@TableName(autoResultMap = true)
public class GroupSchoolActivityEnroll  implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 自增ID
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 活动id
	 */
	private Long activityId;
	/**
	 * 今视频唯一ID
	 */
	private Long jid;
	/**
	 * 资源ID
	 */
	private Long resourceId;
	/**
	 * 手机号
	 */
	@TableField(typeHandler = AesEncryptTypeHandler.class)
	private String mobile;
	/**
	 * 手机号脱敏
	 */
	private String maskMobile;
	/**
	 * 姓名
	 */
	private String name;
	/**
	 * 证件类型 1 居民身份证
	 */
	private Integer cardType;
	/**
	 * 证件号码
	 */
	@TableField(typeHandler = AesEncryptTypeHandler.class)
	private String cardNo;
	/**
	 * 脱敏证件号
	 */
	private String maskCardNo;
	/**
	 * 性别 1、男 2、女
	 */
	private Integer gender;
	/**
	 * 单位名称
	 */
	private String school;
	/**
	 * 参赛作品名称
	 */
	private String entryName;
	/**
	 * 参赛类别类别 1-个人 2-集体
	 */
	private Integer participantsType;
	/**
	 * 参赛组别
	 */
	private Integer groupCode;
	/**
	 * 参赛组别名称
	 */
	private String groupName;
	/**
	 * 参赛赛区
	 */
	private Integer divisionCode;
	/**
	 * 参赛赛区名称
	 */
	private String divisionName;

	/**
	 * 指导老师
	 */
	private String instructor;
	/**
	 * 监护人姓名
	 */
	private String guardianName;
	/**
	 * 监护人手机号
	 */
	@TableField(typeHandler = AesEncryptTypeHandler.class)
	private String guardianMobile;
	/**
	 * 监护人手机号脱敏
	 */
	private String guardianMaskMobile;
	/**
	 * 视频阿里云ID
	 */
	private String videoOssId;
	/**
	 * 封面图阿里云ID
	 */
	private String coverOssId;
	/**
	 * 得票数
	 */
	private Integer pollNum;
	/**
	 * 报名时间
	 */
	private Date enrollDate;
	/**
	 * 创建时间
	 */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;

	/**
	 * 年龄
	 */
	private String age;

	/**
	 * 身高
	 */
	private String height;

	/**
	 * 学历
	 */
	private String education;

	/**
	 * 婚姻状况
	 */
	private String marital;

	/**
	 * 扩展信息
	 */
	private String extendInfo;

}
