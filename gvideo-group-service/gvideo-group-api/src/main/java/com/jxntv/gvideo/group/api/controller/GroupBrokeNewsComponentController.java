package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.group.api.converter.GroupBrokeNewsComponentConvert;
import com.jxntv.gvideo.group.api.entity.GroupBrokeNewsComponent;
import com.jxntv.gvideo.group.api.service.GroupBrokeNewsComponentService;
import com.jxntv.gvideo.group.sdk.client.GroupBrokeNewsComponentClient;
import com.jxntv.gvideo.group.sdk.dto.GroupBrokeNewsComponentDTO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/5/13
 * Email: <EMAIL>
 */
@RestController
public class GroupBrokeNewsComponentController implements GroupBrokeNewsComponentClient {
    @Resource
    private GroupBrokeNewsComponentService groupBrokeNewsComponentService;
    @Resource
    private GroupBrokeNewsComponentConvert groupBrokeNewsComponentConvert;


    @Override
    public Result<Long> add(GroupBrokeNewsComponentDTO dto) {
        GroupBrokeNewsComponent entity = groupBrokeNewsComponentConvert.convert(dto);
        groupBrokeNewsComponentService.save(entity);
        return Result.ok(entity.getId());
    }

    @Override
    public Result<Boolean> updateByComponentId(Long componentId, GroupBrokeNewsComponentDTO dto) {
        LambdaQueryWrapper<GroupBrokeNewsComponent> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(GroupBrokeNewsComponent::getComponentId, componentId);
        GroupBrokeNewsComponent exist = groupBrokeNewsComponentService.getOne(lambdaQuery);
        AssertUtil.notNull(exist, CodeMessage.NOT_FOUND);

        GroupBrokeNewsComponent entity = groupBrokeNewsComponentConvert.convert(dto);
        entity.setId(exist.getId());
        return Result.ok(groupBrokeNewsComponentService.updateById(entity));
    }

    @Override
    public Result<GroupBrokeNewsComponentDTO> getByComponentId(Long  componentId) {
        LambdaQueryWrapper<GroupBrokeNewsComponent> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(GroupBrokeNewsComponent::getComponentId, componentId);
        GroupBrokeNewsComponent entity = groupBrokeNewsComponentService.getOne(lambdaQuery);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);

        return Result.ok(groupBrokeNewsComponentConvert.convert(entity));
    }
}
