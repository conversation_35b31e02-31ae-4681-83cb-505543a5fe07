package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.GroupBlindBoxRecommendGuest;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateRecommendGuestDTO;

import java.util.List;

/**
 * 社区-放心爱-盲盒-推荐嘉宾业务
 *
 * <AUTHOR>
 * @date 2023-01-06 14:43:14
 */
public interface GroupBlindBoxRecommendGuestService extends IService<GroupBlindBoxRecommendGuest> {

    /**
     * 查询被打招呼数最高的top 100
     * @return
     */
    List<GroupBlindBoxRecommendGuest>  queryTopRecommendGuestList();


    /**
     * 获取推荐嘉宾信息
     * @param loginJid
     * @param guestSize
     * @return
     */
    List<GroupBlindDateRecommendGuestDTO> queryRecommendGuestList(Long loginJid,Integer guestSize);
}

