package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.GroupInfo;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoSaveDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupLogDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupOptionDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupSortType;

import java.util.List;

/**
 *
 */
public interface GroupInfoService extends IService<GroupInfo> {

    /**
     * 保存圈子
     *
     * @param saveDTO
     * @return
     */
    Long saveGroup(GroupInfoSaveDTO saveDTO);

    IPage<Long> myGroupContent(Long jid, Integer current, Integer size);

    /**
     * 申请加入圈子
     *
     * @param groupId
     * @param jid
     * @param reason
     */
    void apply(Long groupId, Long jid, String reason);

    /**
     * 清除圈子数据
     *
     * @param ids
     */
    void clear(List<Long> ids);

    /**
     * 修改内容排序规则
     *
     * @param id
     * @param sortType
     * @param logDTO
     */
    void sortTypeModify(Long id, GroupSortType sortType, GroupLogDTO logDTO);

    /**
     * c端用户我的圈子
     * @param jid
     * @param keyword
     * @param roleTypeList
     * @param current
     * @param size
     * @return
     */
    IPage<GroupInfo> myGroupPage(Long jid, String keyword, List<Integer> roleTypeList, Integer current, Integer size);


    IPage<GroupOptionDTO> optionPage(Long jid, String nameLike, Long lastGroupId, Integer current, Integer size,List<Long>  relayGroupIds);

    /**
     * 获取社区信息
     * @param ids
     * @return
     */
    List<GroupInfo> getGroupInfo(List<Long> ids);
}
