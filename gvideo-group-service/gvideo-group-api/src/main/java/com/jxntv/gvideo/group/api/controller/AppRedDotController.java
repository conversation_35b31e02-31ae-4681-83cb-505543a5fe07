package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.api.entity.AppRedDot;
import com.jxntv.gvideo.group.api.service.AppRedDotService;
import com.jxntv.gvideo.group.sdk.client.AppRedDotClient;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotDTO;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotSearchDTO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: niedamin
 * @Date: 2023/01/29 16:24
 */
@RestController
public class AppRedDotController implements AppRedDotClient {

    @Resource
    private AppRedDotService appRedDotService;


    @Override
    public Result<Void> create(AppRedDotDTO dto) {
        if (Objects.isNull(dto.getJid())) {
            return Result.fail("用户jid不允许为空");
        }

        if (Objects.isNull(dto.getBusinessType())) {
            return Result.fail("业务类型不允许为空");
        }
        AppRedDot appRedDot = new AppRedDot();
        BeanUtils.copyProperties(dto, appRedDot);
        appRedDotService.save(appRedDot);
        return Result.ok();
    }

    @Override
    public Result<Void> modify(AppRedDotDTO dto) {
        if (Objects.isNull(dto.getJid())) {
            return Result.fail("用户jid不允许为空");
        }

        if (Objects.isNull(dto.getBusinessType())) {
            return Result.fail("业务类型不允许为空");
        }
        LambdaUpdateWrapper<AppRedDot> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(AppRedDot::getJid, dto.getJid());
        updateWrapper.eq(AppRedDot::getBusinessType, dto.getBusinessType());
        updateWrapper.set(Objects.nonNull(dto.getRedDotNum()), AppRedDot::getRedDotNum, dto.getRedDotNum());
        updateWrapper.set(Objects.nonNull(dto.getIsShow()), AppRedDot::getIsShow, dto.getIsShow());
        appRedDotService.update(updateWrapper);
        return Result.ok();
    }

    @Override
    public Result<List<AppRedDotDTO>> query(AppRedDotSearchDTO searchDTO) {
        LambdaQueryWrapper<AppRedDot> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AppRedDot::getJid, searchDTO.getJid());
        queryWrapper.eq(AppRedDot::getBusinessType, searchDTO.getBusinessType());
        List<AppRedDot> list = appRedDotService.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok();
        }
        List<AppRedDotDTO> dtoList = list.stream().map(appRedDot -> {
            AppRedDotDTO dto = new AppRedDotDTO();
            BeanUtils.copyProperties(appRedDot, dto);
            return dto;
        }).collect(Collectors.toList());

        return Result.ok(dtoList);
    }

}
