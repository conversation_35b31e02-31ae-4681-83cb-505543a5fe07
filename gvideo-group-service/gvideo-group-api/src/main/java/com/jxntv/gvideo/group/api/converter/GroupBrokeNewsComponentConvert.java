package com.jxntv.gvideo.group.api.converter;

import com.jxntv.gvideo.group.api.entity.GroupBrokeNewsComponent;
import com.jxntv.gvideo.group.sdk.dto.GroupBrokeNewsComponentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/13
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class GroupBrokeNewsComponentConvert {

    public GroupBrokeNewsComponentDTO convert(GroupBrokeNewsComponent entity) {
        GroupBrokeNewsComponentDTO dto = new GroupBrokeNewsComponentDTO();
        dto.setId(entity.getId());
        dto.setGroupId(entity.getGroupId());
        dto.setComponentId(entity.getComponentId());
        dto.setShowFlow(entity.getShowFlow());
        dto.setShowDetail(entity.getShowDetail());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }


    public GroupBrokeNewsComponent convert(GroupBrokeNewsComponentDTO dto) {
        GroupBrokeNewsComponent entity = new GroupBrokeNewsComponent();
        entity.setId(dto.getId());
        entity.setGroupId(dto.getGroupId());
        entity.setComponentId(dto.getComponentId());
        entity.setShowFlow(dto.getShowFlow());
        entity.setShowDetail(dto.getShowDetail());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }

}
