package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 内容推荐兜底表
 */
@Data
@TableName(value = "adm_backup_content_d")
public class AdmBackupContentD implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内容id
     */
    private Long contentId;

    /**
     * 排名
     */
    private Integer rankNum;
}