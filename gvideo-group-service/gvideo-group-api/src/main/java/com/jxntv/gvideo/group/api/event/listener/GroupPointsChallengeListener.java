package com.jxntv.gvideo.group.api.event.listener;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jxntv.gvideo.canal.client.dto.CommentPrimaryBinlogEvent;
import com.jxntv.gvideo.canal.client.dto.MediaResourceBinlogEvent;
import com.jxntv.gvideo.group.api.config.PointsChallengeConfig;
import com.jxntv.gvideo.group.api.entity.GroupTopicContent;
import com.jxntv.gvideo.group.api.service.GroupPointsChallengeMemberService;
import com.jxntv.gvideo.group.api.service.GroupTopicContentService;
import com.jxntv.gvideo.media.client.enums.DataType;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 金牌调解-积分挑战赛
 * <AUTHOR>
 * @date 2023/11/8 17:58
 */
@Slf4j
@Component
public class GroupPointsChallengeListener {

    @Resource
    private GroupTopicContentService groupTopicContentService;
    @Resource
    private PointsChallengeConfig pointsChallengeConfig;
    @Resource
    private GroupPointsChallengeMemberService groupPointsChallengeMemberService;


    /**
     * 发帖审核通过加积分
     * @param event
     */
    @Async
    @EventListener
    public void onEvent(MediaResourceBinlogEvent event) {
        if (Objects.equals(MediaResourceStatus.ENABLE.getCode(), event.getStatus()) && Objects.equals(DataType.NORMAL.getCode(),event.getDataType())) {
            List<GroupTopicContent> groupTopicContentList = groupTopicContentService.listByMediaId(event.getId());
            if (!CollectionUtils.isEmpty(groupTopicContentList)) {
                Map<Long, List<GroupTopicContent>> groupTopicContentMap = groupTopicContentList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(GroupTopicContent::getGroupId));

                groupTopicContentMap.forEach((groupId, list) -> {
                    if (pointsChallengeConfig.getGroupIds().contains(groupId)) {
                        Long releaseId = list.get(0).getMediaReleaseId();
                        this.groupPointsChallengeMemberService.issueScore(groupId,releaseId,1,event.getId());
                    }
                });
            }
        }
    }

    /**
     * 主评论审核通过加积分
     * @param event
     */
    @Async
    @EventListener
    public void onEvent(CommentPrimaryBinlogEvent event) {
        if (event.getStatus() == 1 && event.getCheckstatus() == 1){
            List<GroupTopicContent> groupTopicContentList = groupTopicContentService.listByMediaId(event.getMediaId());
            if (!CollectionUtils.isEmpty(groupTopicContentList)) {
                Map<Long, List<GroupTopicContent>> groupTopicContentMap = groupTopicContentList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(GroupTopicContent::getGroupId));
                groupTopicContentMap.forEach((groupId, list) -> {
                    Long releaseId = list.get(0).getMediaReleaseId();
                    if (pointsChallengeConfig.getGroupIds().contains(groupId) && !Objects.equals(releaseId,event.getFromJid())) {
                        this.groupPointsChallengeMemberService.issueScore(groupId,event.getFromJid(),2,event.getId());
                    }
                });
            }
        }
    }

}
