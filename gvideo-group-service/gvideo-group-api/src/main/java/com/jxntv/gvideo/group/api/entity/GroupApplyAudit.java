package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import com.jxntv.gvideo.group.sdk.dto.GroupAuditStatus;
import lombok.Data;

/**
 * 圈子审核表
 *
 * @TableName group_apply_audit
 */
@TableName(value = "group_apply_audit")
@Data
public class GroupApplyAudit implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 圈子id
     */
    private Long groupId;

    /**
     * 申请人手机
     */
    private String mobile;

    /**
     * jid
     */
    private Long jid;

    /**
     * 申请人昵称
     */
    private String nickname;

    /**
     * 申请时间
     */
    private LocalDateTime createDate;

    /**
     * 审核时间
     */
    private LocalDateTime auditDate;

    /**
     * 审核状态 1 待审核 2 通过 3 拒绝
     */
    private GroupAuditStatus groupAuditStatus;

    /**
     * 审核人id
     */
    private Integer auditUserId;

    /**
     * 申请理由
     */
    private String applyReason;
}