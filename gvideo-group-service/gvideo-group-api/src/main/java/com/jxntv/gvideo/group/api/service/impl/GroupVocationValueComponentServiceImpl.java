package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.converter.GroupVocationValueConverter;
import com.jxntv.gvideo.group.api.entity.GroupVocationValueComponent;
import com.jxntv.gvideo.group.api.mapper.GroupVocationValueComponentMapper;
import com.jxntv.gvideo.group.api.service.GroupVocationValueComponentService;
import com.jxntv.gvideo.group.sdk.dto.vocation.GroupVocationValueComponentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/5
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class GroupVocationValueComponentServiceImpl extends ServiceImpl<GroupVocationValueComponentMapper, GroupVocationValueComponent>
        implements GroupVocationValueComponentService {
    @Resource
    private GroupVocationValueConverter groupVocationValueConverter;


    @Override
    public GroupVocationValueComponent getByComponentId(Long componentId) {
        LambdaQueryWrapper<GroupVocationValueComponent> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(GroupVocationValueComponent::getComponentId, componentId);
        return this.getOne(lambdaQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateByComponentId(Long componentId, GroupVocationValueComponentDTO dto) {
        GroupVocationValueComponent exists = this.getByComponentId(componentId);
        GroupVocationValueComponent entity = groupVocationValueConverter.convert(dto);
        if (Objects.nonNull(exists)) {
            entity.setId(exists.getId());
            entity.setCreateUserId(exists.getCreateUserId());
            entity.setCreateDate(exists.getCreateDate());
        }
        this.saveOrUpdate(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByComponentId(Long componentId) {
        GroupVocationValueComponent entity = this.getByComponentId(componentId);
        if (Objects.nonNull(entity)) {
            this.removeById(entity.getId());
        }
        return true;
    }

    @Override
    public GroupVocationValueComponent getByGroupId(Long groupId) {
        return this.baseMapper.getByGroupId(groupId);
    }
}
