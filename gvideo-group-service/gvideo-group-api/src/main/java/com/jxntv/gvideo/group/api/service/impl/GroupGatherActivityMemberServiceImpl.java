package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.group.api.converter.GroupGatherActivityConvert;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUser;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivity;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivityMember;
import com.jxntv.gvideo.group.api.entity.GroupGatherActivityMemberPay;
import com.jxntv.gvideo.group.api.mapper.GroupGatherActivityMemberMapper;
import com.jxntv.gvideo.group.api.service.GroupActivityMemberPayService;
import com.jxntv.gvideo.group.api.service.GroupBlindDateUserService;
import com.jxntv.gvideo.group.api.service.GroupGatherActivityMemberService;
import com.jxntv.gvideo.group.api.service.GroupGatherActivityService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.dto.MyJoinActivityDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityEnrollMemberExportDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityMemberDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityMemberSearchDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.plan.GroupGatherActivityPlanMemberDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupBlindDateGenderEnum;
import com.jxntv.gvideo.group.sdk.enums.MemberAuditStatusEnum;
import com.jxntv.gvideo.group.sdk.params.activity.GroupGatherActivityPlanMemberParam;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 圈子(社区)-组件-活动--报名业务实现类
 *
 * <AUTHOR>
 * @date 2022-04-13 11:10:36
 */
@Service("groupGatherActivityMemberService")
public class GroupGatherActivityMemberServiceImpl extends ServiceImpl<GroupGatherActivityMemberMapper, GroupGatherActivityMember> implements GroupGatherActivityMemberService {

    @Resource
    private GroupBlindDateUserService groupBlindDateUserService;
    @Resource
    private GroupGatherActivityService activityService;

    @Resource
    private GroupActivityMemberPayService activityMemberPayService;


    @Override
    public List<MyJoinActivityDTO> listMyActivity(Long jid,Long groupId,Integer type) {
        return this.baseMapper.listMyActivity(jid,groupId,type);
    }

    @Override
    public List<GroupGatherActivityMemberDTO> listAllEnroll(GroupGatherActivityMemberSearchDTO searchDTO) {
        return this.baseMapper.listAllEnroll(searchDTO);
    }

    @Override
    public List<GroupGatherActivityEnrollMemberExportDTO> queryEnrollList(Long activityId, Integer status) {
        return this.baseMapper.queryEnrollList(activityId,status);
    }

    @Override
    public Integer queryEnrollCount(Long activityId, Integer auditStatus) {
        LambdaQueryWrapper<GroupGatherActivityMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupGatherActivityMember::getActivityId, activityId);
        queryWrapper.eq(GroupGatherActivityMember::getAuditStatus, auditStatus);
        return  this.count(queryWrapper);
    }

    @Override
    public PageDTO<GroupGatherActivityPlanMemberDTO> planMemberPage(GroupGatherActivityPlanMemberParam param) {
        int gender = GroupBlindDateGenderEnum.FEMALE.getCode();
        LambdaQueryWrapper<GroupBlindDateUser> query = new LambdaQueryWrapper<>();
        query.eq(GroupBlindDateUser::getJid, param.getLoginJid());
        GroupBlindDateUser entity = groupBlindDateUserService.getOne(query);
        if (Objects.nonNull(entity)){
            gender =  Objects.equals(GroupBlindDateGenderEnum.FEMALE.getCode(),entity.getGender()) ? GroupBlindDateGenderEnum.MALE.getCode() : GroupBlindDateGenderEnum.FEMALE.getCode();
        }
        LambdaQueryWrapper<GroupGatherActivityMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupGatherActivityMember::getActivityId, param.getActivityId());
        queryWrapper.eq(GroupGatherActivityMember::getAuditStatus, MemberAuditStatusEnum.PASS.getCode());
        queryWrapper.eq(GroupGatherActivityMember::getGender, gender);
        queryWrapper.orderByAsc(GroupGatherActivityMember::getPairedFlag);
        queryWrapper.orderByDesc(GroupGatherActivityMember::getId);
        IPage<GroupGatherActivityMember> page = this.page(new Page<>(param.getCurrent(), param.getSize()), queryWrapper);
        return PageUtils.pageOf(page, GroupGatherActivityConvert::convertPlanMember);
    }

    @Override
    public Boolean queryMemberEnrollStatus(Long activityId, Long jid) {
        if (Objects.isNull(activityId) || Objects.isNull(jid)) {
            return Boolean.FALSE;
        }
        // 查询活动类型
        GroupGatherActivity activity = activityService.getById(activityId);
        // 免费活动
        LambdaQueryWrapper<GroupGatherActivityMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GroupGatherActivityMember::getActivityId, activityId);
        queryWrapper.eq(GroupGatherActivityMember::getJid, jid);
        List<GroupGatherActivityMember> resultList = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(resultList)) {
            return Boolean.FALSE;
        }
        if (activity.getCostFlag() == 0) {
            return Boolean.TRUE;
        } else if (activity.getCostFlag() > 0) {
            // 付费或交押金活动，则看以否已支付
            LambdaQueryWrapper<GroupGatherActivityMemberPay> payLambdaQueryWrapper = new LambdaQueryWrapper<>();
            payLambdaQueryWrapper.eq(GroupGatherActivityMemberPay::getEnrollId, resultList.get(0).getId());
            payLambdaQueryWrapper.eq(GroupGatherActivityMemberPay::getPayState, 1);
            return (this.activityMemberPayService.count(payLambdaQueryWrapper) > 0);
        }
        return Boolean.FALSE;
    }
}
