package com.jxntv.gvideo.group.api.event.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.canal.client.dto.GroupTopicContentBinlogEvent;
import com.jxntv.gvideo.canal.client.dto.InteractFavoriteBinlogEvent;
import com.jxntv.gvideo.group.api.entity.GroupGather;
import com.jxntv.gvideo.group.api.service.GroupGatherService;
import com.jxntv.gvideo.group.api.service.GroupTopicContentService;
import com.jxntv.gvideo.group.api.service.GroupVocationValuePersonService;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherStatusEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/7
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class GroupVocationValueListener {

    @Resource
    private GroupTopicContentService groupTopicContentService;
    @Resource
    private GroupGatherService groupGatherService;
    @Resource
    private GroupVocationValuePersonService groupVocationValuePersonService;
    @Resource
    private InteractClient interactClient;

    /**
     * 用户在开启了职业价值的社区发帖，会增加相应的热度和学校热度
     *
     * @param event 点赞事件
     */

    @Async
    @EventListener
    public void onEvent(InteractFavoriteBinlogEvent event) {
       /* //  查询资源关联社区信息
        List<GroupTopicContent> groupTopicContents = groupTopicContentService.listByMediaId(event.getMediaId());

        for (GroupTopicContent content : groupTopicContents) {
            //  判断是否开启了职业生涯规划
            if (enableVocationValue(content.getGroupId())) {
                //  查找参与活动的用户
                GroupVocationValuePerson person = groupVocationValuePersonService.getByGroupIdAndJid(content.getGroupId(), content.getMediaReleaseId());
                if (Objects.nonNull(person)) {
                    //  查询当前资源的点赞总数
                    Integer favoriteSum = interactClient.favoriteSum(event.getMediaId()).orElse(0);
                    person.setHotNum(favoriteSum);
                    //  更新参与活动用户热度值
                    groupVocationValuePersonService.updateById(person);

                }

            }

        }*/
        groupVocationValuePersonService.hotCount(event.getMediaId());

    }
    @Async
    @EventListener
    public void onEvent(GroupTopicContentBinlogEvent event) {
        if (Objects.equals(MediaResourceStatus.ENABLE.getCode(), event.getMediaStatus()) || Objects.equals(MediaResourceStatus.DELETE.getCode(), event.getMediaStatus())) {
            groupVocationValuePersonService.hotCount(event.getMediaId());
        }
    }

    /**
     * 判断社区是否开启了
     *
     * @param groupId 社区id
     * @return 是否开启职业生涯
     */
    private boolean enableVocationValue(Long groupId) {
        LambdaQueryWrapper<GroupGather> componentQuery = Wrappers.lambdaQuery();
        componentQuery.eq(GroupGather::getGroupId, groupId);
        componentQuery.eq(GroupGather::getType, GroupGatherTypeEnum.VOCATION_VALUE.getCode());
        componentQuery.eq(GroupGather::getStatus, GroupGatherStatusEnum.ENABLE.getCode());
        return groupGatherService.count(componentQuery) > 0;
    }


}
