package com.jxntv.gvideo.group.api.converter;

import com.jxntv.gvideo.group.api.entity.SchoolDetail;
import com.jxntv.gvideo.group.sdk.dto.SchoolDetailDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-06-11 14:49
 * @description
 */
@Component
public class SchoolDetailConverter {


    public SchoolDetailDTO entityConvertDTOBase(SchoolDetail entity) {
        if (Objects.isNull(entity)) {
            return null;
        }

        SchoolDetailDTO schoolDetailDTO = new SchoolDetailDTO();
        BeanUtils.copyProperties(entity, schoolDetailDTO);
        return schoolDetailDTO;
    }

    public SchoolDetailDTO entityListConvertDTODetail(List<SchoolDetail> schoolDetailList) {
        if (CollectionUtils.isEmpty(schoolDetailList)) {
            return null;
        }
        SchoolDetailDTO schoolDetailDTO = entityConvertDTOBase(schoolDetailList.get(0));

        Map<String, Map<String, List<String>>> schoolAreaGradeClassList = new LinkedHashMap<>();

        for (SchoolDetail schoolDetail : schoolDetailList) {
            String schoolArea = schoolDetail.getSchoolArea();
            //校区查重
            if (schoolAreaGradeClassList.containsKey(schoolArea)) {
                Map<String, List<String>> oldGradeClassMap = schoolAreaGradeClassList.get(schoolArea);
                //年级查重
                if (!CollectionUtils.isEmpty(oldGradeClassMap) && oldGradeClassMap.containsKey(schoolDetail.getSchoolGrade())) {
                    List<String> oldClassList = oldGradeClassMap.get(schoolDetail.getSchoolGrade());
                    oldClassList.add(schoolDetail.getSchoolClass());
                    oldGradeClassMap.put(schoolDetail.getSchoolGrade(), oldClassList);
                    schoolAreaGradeClassList.put(schoolDetail.getSchoolArea(), oldGradeClassMap);
                } else {
                    List<String> classList = new ArrayList<>();
                    classList.add(schoolDetail.getSchoolClass());
                    oldGradeClassMap.put(schoolDetail.getSchoolGrade(), classList);
                    schoolAreaGradeClassList.put(schoolDetail.getSchoolArea(), oldGradeClassMap);
                }
            } else {
                Map<String, List<String>> gradeClassMap = new LinkedHashMap<>();
                List<String> classList = new ArrayList<>();
                classList.add(schoolDetail.getSchoolClass());
                gradeClassMap.put(schoolDetail.getSchoolGrade(), classList);
                schoolAreaGradeClassList.put(schoolDetail.getSchoolArea(), gradeClassMap);
            }
            schoolDetailDTO.setSchoolAreaGradeClassList(schoolAreaGradeClassList);
        }

        return schoolDetailDTO;
    }

    //public static void main(String[] args) {
    //
    //    List<SchoolDetail> details = new ArrayList<>();
    //
    //    for (int i = 1; i < 5; i++) {
    //        SchoolDetail schoolDetail = new SchoolDetail();
    //        schoolDetail.setSchoolArea("一中校区" + i);
    //        schoolDetail.setSchoolClass(Integer.valueOf(i));
    //        schoolDetail.setSchoolGrade(Integer.valueOf(i));
    //        details.add(schoolDetail);
    //    }
    //    SchoolDetail schoolDetail = new SchoolDetail();
    //    schoolDetail.setSchoolArea("一中校区1");
    //    schoolDetail.setSchoolClass(2);
    //    schoolDetail.setSchoolGrade(1);
    //    details.add(schoolDetail);
    //
    //    SchoolDetail schoolDetail2 = new SchoolDetail();
    //
    //    schoolDetail2.setSchoolArea("一中校区1");
    //    schoolDetail2.setSchoolClass(23);
    //    schoolDetail2.setSchoolGrade(1);
    //    details.add(schoolDetail);
    //
    //    schoolDetail.setSchoolArea("一中校区1");
    //    schoolDetail.setSchoolClass(23);
    //    schoolDetail.setSchoolGrade(2);
    //    details.add(schoolDetail);
    //
    //
    //    System.out.println("details = " + details);
    //    SchoolDetailDTO schoolDetailDTO = entityListConvertDTODetail(details);
    //    System.out.println("schoolDetailDTO = " + schoolDetailDTO);
    //    String s = JsonUtils.toJson(schoolDetailDTO);
    //    System.out.println("s = " + s);
    //
    //}
}
