package com.jxntv.gvideo.group.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName(value = "group_gather_answer_pop_up")
public class GroupGatherAnswerReminderPopUp {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 圈子ID
     */
    private Long groupId;


    /**
     * 组件ID
     */
    private Long gatherId;


    /**
     * 是否开启问答提醒弹窗
     */
    private Integer answerPopUp;

    /**
     * 是否开启提问限制
     */
    private Integer questionEntry;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;

    /**
     * 更新人
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Integer del_flag;
}
