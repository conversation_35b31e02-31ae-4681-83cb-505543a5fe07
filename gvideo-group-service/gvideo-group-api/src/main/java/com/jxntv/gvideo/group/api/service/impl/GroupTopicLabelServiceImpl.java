package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.entity.GroupTopic;
import com.jxntv.gvideo.group.api.entity.GroupTopicLabel;
import com.jxntv.gvideo.group.api.service.GroupTopicLabelService;
import com.jxntv.gvideo.group.api.mapper.GroupTopicLabelMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Service
public class GroupTopicLabelServiceImpl extends ServiceImpl<GroupTopicLabelMapper, GroupTopicLabel>
        implements GroupTopicLabelService {
    @Override
    public List<GroupTopicLabel> listByGroupId(Long groupId) {
        return this.list(Wrappers.<GroupTopicLabel>lambdaQuery()
                .eq(GroupTopicLabel::getGroupId, groupId)
                .eq(GroupTopicLabel::getTopicId, 0));
    }
}




