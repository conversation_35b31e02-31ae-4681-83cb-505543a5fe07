package com.jxntv.gvideo.group.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.group.api.entity.today.talk.GroupTodayTalk;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkFakeVoteParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkVoteContributeSearchParam;
import com.jxntv.gvideo.group.sdk.params.today.talk.GroupTodayTalkVoteParam;

/**
 * 圈子-今日聊话题业务
 *
 * <AUTHOR>
 * @date 2022-10-18 14:56:01
 */
public interface GroupTodayTalkService extends IService<GroupTodayTalk> {

    /**
     * 新增今日聊话题
     * @param param
     * @return
     */
    Boolean save(GroupTodayTalkParam param);

    /**
     * 修改今日聊话题
     * @param param
     * @return
     */
    Boolean update(GroupTodayTalkParam param);

    GroupTodayTalk getByMediaId(Long mediaId);

    IPage<GroupTodayTalk> getVoteContributeList(GroupTodayTalkVoteContributeSearchParam dto);

    Long vote(GroupTodayTalkVoteParam dto);
    Long fakeVote(GroupTodayTalkFakeVoteParam dto);
}

