package com.jxntv.gvideo.group.api.entity.mentor;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 社区付费导师
 * 每一个成员ID 关联到组件（问答组件）
 */


@Data
@TableName
public class GroupPaidMentor implements Serializable {
    /**
     * 主键 id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 导师jid
     */
    private Long jid;
    /**
     * 关联社区ID
     */
    private Long groupId;
    /**
     * 组件 id
     */
    private Long componentId;
    /**
     * 权重
     */
    private Integer weights;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 折扣价
     */
    private BigDecimal discountPrice;
    /**
     * 创建人ID
     */
    private Long createUserId;
    /**
     * 更新人ID
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
