package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.converter.GroupConvert;
import com.jxntv.gvideo.group.api.converter.GroupGatherConvert;
import com.jxntv.gvideo.group.api.entity.*;
import com.jxntv.gvideo.group.api.mapper.GroupGatherMapper;
import com.jxntv.gvideo.group.api.service.*;
import com.jxntv.gvideo.group.sdk.dto.*;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherStatusEnum;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 圈子组件表
 * @date 2021/06/10 17:29
 */
@Service
public class GroupGatherServiceImpl extends ServiceImpl<GroupGatherMapper, GroupGather> implements GroupGatherService {
    @Autowired
    private GroupGatherInfoService gatherInfoService;
    @Autowired
    private GroupGatherConvert groupGatherConvert;
    @Autowired
    private GroupOptionLogService logService;
    @Resource
    private GroupTopicContentService groupTopicContentService;
    @Autowired
    private GroupConvert groupConvert;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveGroupGather(GroupGatherDTO dto) {
        GroupGather groupGather = groupGatherConvert.convert(dto);
        Integer row = this.baseMapper.insert(groupGather);
        if (Objects.isNull(row) || row < 1) {
            return null;
        }
        if (!CollectionUtils.isEmpty(dto.getInfoList())) {
            List<GroupGatherInfo> infoList = dto.getInfoList().stream().map(groupGatherConvert::convert).collect(Collectors.toList());
            for (GroupGatherInfo gatherInfo : infoList) {
                gatherInfo.setGatherId(groupGather.getId());
            }
            gatherInfoService.saveBatch(infoList);
        }
        //构造操作日志
        GroupLogDTO logDTO = dto.getLogDTO();
        groupConvert.buildRoleName(logDTO, dto.getGroupId());
        GroupOptionLog groupLog = groupConvert.buildLog(logDTO, "-", GroupLogContentType.GATHER_LOG);
        groupLog.setChangeField(GroupLogField.GATHER_ADD);
        groupLog.setGroupId(dto.getGroupId());
        groupLog.setRoleType(logDTO.getRoleType());
        logService.save(groupLog);
        return groupGather.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGroupGather(GroupGatherDTO dbData, GroupGatherDTO dto) {
        GroupGather groupGather = groupGatherConvert.convert(dto);
        List<GroupGatherInfo> infoList = null;
        if (!CollectionUtils.isEmpty(dto.getInfoList())) {
            infoList = dto.getInfoList().stream().map(groupGatherConvert::convert).collect(Collectors.toList());
        }
        this.baseMapper.updateById(groupGather);
        if (!CollectionUtils.isEmpty(infoList)) {
            List<GroupGatherInfo> insertDataList = new ArrayList<>(infoList.size());
            List<GroupGatherInfo> updateDataList = new ArrayList<>(infoList.size());
            for (GroupGatherInfo gatherInfo : infoList) {
                gatherInfo.setGatherId(dbData.getId());
                if (Objects.isNull(gatherInfo.getId())) {
                    insertDataList.add(gatherInfo);
                } else {
                    updateDataList.add(gatherInfo);
                }
            }
            if (!CollectionUtils.isEmpty(insertDataList)) {
                gatherInfoService.saveBatch(insertDataList);
            }
            if (!CollectionUtils.isEmpty(updateDataList)) {
                gatherInfoService.updateBatchById(updateDataList);
            }
        }

        //  如果是电视节目，跟换节目id，需要删除关联内容信息表
        removeContentIfColumnIdModified(dbData, dto);

        //记录操作日志
        logService.addGatherLog(dbData, dto, dto.getLogDTO());
    }

    /**
     * 如果关联电视节目id变动，就删除之前所有的关联节目
     *
     * @param dbData
     * @param dto
     */
    private void removeContentIfColumnIdModified(GroupGatherDTO dbData, GroupGatherDTO dto) {
        if (GroupGatherTypeEnum.PROGRAM_COLLECTION.getCode().equals(dbData.getType())) {
            List<GroupGatherInfoDTO> oldInfos = dbData.getInfoList();
            List<GroupGatherInfoDTO> newInfos = dto.getInfoList();
            String oldField = CollectionUtils.isEmpty(oldInfos) ? "" : oldInfos.get(0).getFieldValue();
            String newField = CollectionUtils.isEmpty(newInfos) ? "" : newInfos.get(0).getFieldValue();
            if (!Objects.equals(oldField, newField)) {
                groupTopicContentService.remove(Wrappers.<GroupTopicContent>lambdaQuery().eq(GroupTopicContent::getGatherId, dto.getId()));
            }
        }
    }

    @Override
    public List<GroupGatherDTO> listByType(Long groupId, Integer type) {
        LambdaQueryWrapper<GroupGather> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(groupId), GroupGather::getGroupId, groupId);
        queryWrapper.eq(GroupGather::getType, type);
        queryWrapper.eq(GroupGather::getStatus, GroupGatherStatusEnum.ENABLE.getCode());
        queryWrapper.orderByDesc(GroupGather::getWeight);
        List<GroupGather> gatherList = this.baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(gatherList)) {
            return Collections.emptyList();
        }

        List<GroupGatherDTO> gatherDTOList = gatherList.stream().map(groupGatherConvert::convert).collect(Collectors.toList());

        List<Long> gatherIdList = gatherList.stream().map(GroupGather::getId).collect(Collectors.toList());
        LambdaQueryWrapper<GroupGatherInfo> queryInfoWrapper = new LambdaQueryWrapper<>();
        queryInfoWrapper.in(GroupGatherInfo::getGatherId, gatherIdList);
        List<GroupGatherInfo> gatherInfoList = gatherInfoService.list(queryInfoWrapper);
        if (!CollectionUtils.isEmpty(gatherInfoList)) {
            Map<Long, List<GroupGatherInfo>> gatherInfoMap = gatherInfoList.stream().collect(Collectors.groupingBy(GroupGatherInfo::getGatherId));
            gatherDTOList.forEach(dto -> {
                List<GroupGatherInfo> infoList = gatherInfoMap.get(dto.getId());
                if (!CollectionUtils.isEmpty(infoList)) {
                    dto.setInfoList(infoList.stream().map(groupGatherConvert::convert).collect(Collectors.toList()));
                }
            });
        }
        return gatherDTOList;
    }

    @Override
    public GroupGatherDTO getEnableGather(Long groupId) {
        LambdaQueryWrapper<GroupGather> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(GroupGather::getGroupId, groupId);
        queryWrapper.eq(GroupGather::getType, GroupGatherTypeEnum.ANSWER_REMINDER_POP_UP.getCode());
        queryWrapper.eq(GroupGather::getStatus, GroupGatherStatusEnum.ENABLE.getCode());
        return groupGatherConvert.convert(this.getOne(queryWrapper));
    }
}
