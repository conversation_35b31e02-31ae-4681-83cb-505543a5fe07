package com.jxntv.gvideo.group.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.group.api.entity.mentor.GroupPaidMentor;
import com.jxntv.gvideo.group.api.mapper.GroupPaidMentorMapper;
import com.jxntv.gvideo.group.api.service.GroupPaidMentorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2022/7/1
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class GroupPaidMentorServiceImpl extends ServiceImpl<GroupPaidMentorMapper, GroupPaidMentor> implements GroupPaidMentorService {
}
