package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.api.converter.GroupConvert;
import com.jxntv.gvideo.group.api.entity.GroupOptionLog;
import com.jxntv.gvideo.group.api.service.GroupOptionLogService;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.GroupLogClient;
import com.jxntv.gvideo.group.sdk.dto.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR> Created on 2021/5/13.
 */
@RestController
public class GroupLogController implements GroupLogClient {
    @Autowired
    private GroupOptionLogService logService;
    @Autowired
    private GroupConvert groupConvert;

    @Override
    public Result<PageDTO<GroupLogPageDTO>> page(GroupPageSearchDTO searchDTO) {
        LambdaQueryWrapper<GroupOptionLog> queryWrapper = new LambdaQueryWrapper<>();
        Long groupId = searchDTO.getGroupId();
        queryWrapper.eq(Objects.nonNull(groupId), GroupOptionLog::getGroupId, groupId);
        GroupLogContentType contentType = searchDTO.getContentType();
        queryWrapper.eq(Objects.nonNull(contentType), GroupOptionLog::getLogContentType, contentType);
        String subject = searchDTO.getSubject();
        queryWrapper.eq(StringUtils.isNotEmpty(subject), GroupOptionLog::getSubject, subject);
        GroupLogField field = searchDTO.getField();
        queryWrapper.eq(Objects.nonNull(field), GroupOptionLog::getChangeField, field);
        String createUserName = searchDTO.getCreateUserName();
        queryWrapper.eq(StringUtils.isNotEmpty(createUserName), GroupOptionLog::getCreateUserName, createUserName);
        RoleType roleType = searchDTO.getRoleType();
        queryWrapper.eq(Objects.nonNull(roleType), GroupOptionLog::getRoleType, roleType);
        LocalDateTime start = searchDTO.getStart();
        queryWrapper.ge(Objects.nonNull(start), GroupOptionLog::getCreateDate, start);
        LocalDateTime end = searchDTO.getEnd();
        queryWrapper.lt(Objects.nonNull(end), GroupOptionLog::getCreateDate, end);
        if (searchDTO.getAsc()) {
            queryWrapper.orderByAsc(GroupOptionLog::getCreateDate);
        } else {
            queryWrapper.orderByDesc(GroupOptionLog::getCreateDate);
        }
        IPage<GroupOptionLog> rst = logService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), queryWrapper);
        return Result.ok(PageUtils.pageOf(rst, groupConvert::convertLog));
    }
}
