package com.jxntv.gvideo.group.api.service.cycle.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.api.client.MessageService;
import com.jxntv.gvideo.group.api.entity.cycle.task.UserCycleJob;
import com.jxntv.gvideo.group.api.entity.cycle.task.UserCycleTaskRecord;
import com.jxntv.gvideo.group.api.mapper.UserCycleJobMapper;
import com.jxntv.gvideo.group.api.mapper.UserCycleTaskRecordMapper;
import com.jxntv.gvideo.group.api.service.GroupQaUserService;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.*;
import com.jxntv.gvideo.group.sdk.enums.user.cycle.UserCycleJobStatus;
import com.jxntv.gvideo.group.sdk.enums.user.cycle.UserCycleJobType;
import com.jxntv.gvideo.interact.client.dto.MessageDTO;
import com.jxntv.gvideo.interact.client.dto.enums.*;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 互动任务
 *
 * <AUTHOR>
 * @date 2022/5/10
 **/
@Slf4j
@Service
public class UserAnswerJobService extends ServiceImpl<UserCycleJobMapper, UserCycleJob> implements UserCycleJobService {


    @Resource
    private MessageService messageService;
    @Resource
    private UserCycleTaskRecordMapper userCycleTaskRecordMapper;
    @Resource
    private GroupQaUserService groupQaUserService;
    @Resource
    private MediaResourceClient mediaResourceClient;


    /**
     * @return
     */
    @Override
    public UserCycleJobType getJobType() {
        return UserCycleJobType.ANSWER_JOB;
    }


    @Override
    public Integer doTask(UserCycleTaskDTO userCycleTaskDTO) {
        log.info("回答问题参数{}", JsonUtils.toJson(userCycleTaskDTO));
        //  获取最近的job
        UserCycleJob lastJob = getLastJob(userCycleTaskDTO.getUserId(), userCycleTaskDTO.getGroupId());

        if (Objects.isNull(lastJob)) {
            //  新建任务
            UserCycleJob userCycleJob = newAnswerJob(userCycleTaskDTO);
            //  添加任务记录
            this.addTaskRecord(userCycleJob.getId(), userCycleTaskDTO);
        } else if (isRunningJob(lastJob)) {
            //  添加任务记录
            this.addTaskRecord(lastJob.getId(), userCycleTaskDTO);
        } else if (isRewardAfterWeek(lastJob)) {
            //  新建任务
            UserCycleJob userCycleJob = newAnswerJob(userCycleTaskDTO);
            //  添加任务记录
            this.addTaskRecord(userCycleJob.getId(), userCycleTaskDTO);
        }

        return 1;
    }

    private UserCycleJob newAnswerJob(UserCycleTaskDTO userCycleTaskDTO) {

        UserCycleJob userCycleJob = new UserCycleJob();
        userCycleJob.setJobType(this.getJobType().getType());
        userCycleJob.setJid(userCycleTaskDTO.getUserId());
        userCycleJob.setGroupId(userCycleTaskDTO.getGroupId());
        userCycleJob.setFirstDate(LocalDate.now());
        userCycleJob.setLastDate(LocalDate.now());
        userCycleJob.setCreateTime(LocalDateTime.now());
        userCycleJob.setStatus(UserCycleJobStatus.RUNNING.getCode());

        this.save(userCycleJob);

        return userCycleJob;
    }

    private void addTaskRecord(Long jobId, UserCycleTaskDTO userCycleTaskDTO) {

        if (isValidAnswer(jobId, userCycleTaskDTO)) {
            //   构建新的记录
            UserCycleTaskRecord record = new UserCycleTaskRecord();
            record.setUserCycleJobId(jobId);
            record.setJobType(this.getJobType().getType());
            record.setUserCycleJobId(jobId);
            record.setJid(userCycleTaskDTO.getUserId());
            record.setGroupId(userCycleTaskDTO.getGroupId());
            record.setExtra(userCycleTaskDTO.getExtra());
            record.setCreateTime(LocalDateTime.now());

            userCycleTaskRecordMapper.insert(record);

            //  更新任务进度
            UserCycleJob userCycleJob = this.getById(jobId);
            userCycleJob.setProgress(getProgress(jobId));
            userCycleJob.setLastDate(LocalDate.now());
            userCycleJob.setUpdateTime(LocalDateTime.now());
            this.updateById(userCycleJob);
        }

    }

    private int getProgress(Long jobId) {
        LambdaQueryWrapper<UserCycleTaskRecord> recordQuery = Wrappers.lambdaQuery();
        recordQuery.eq(UserCycleTaskRecord::getUserCycleJobId, jobId);
        return userCycleTaskRecordMapper.selectCount(recordQuery);
    }

    /**
     * 是否为有效回答
     *
     * @param jobId            任务id
     * @param userCycleTaskDTO 任务参数
     * @return
     */
    private boolean isValidAnswer(Long jobId, UserCycleTaskDTO userCycleTaskDTO) {
        //  不能超过进度最大值
        LambdaQueryWrapper<UserCycleTaskRecord> recordQuery = Wrappers.lambdaQuery();
        recordQuery.eq(UserCycleTaskRecord::getUserCycleJobId, jobId);
        List<UserCycleTaskRecord> records = userCycleTaskRecordMapper.selectList(recordQuery);
        if (records.size() >= this.getJobType().getCycleLength()) {
            return false;
        }

        UserAnswerJobExtraDTO jobExtraDTO = JsonUtils.fromJson(userCycleTaskDTO.getExtra(), UserAnswerJobExtraDTO.class);
        if (Objects.isNull(jobExtraDTO)) {
            return false;
        }

        //  自己不能回答自己的问题
        Long questionId = jobExtraDTO.getQuestionId();
        MediaResourceDTO questionDTO = mediaResourceClient.get(questionId).orElse(null);
        if (Objects.isNull(questionDTO) || Objects.equals(userCycleTaskDTO.getUserId(), questionDTO.getReleaseId())) {
            return false;
        }

        //  回答内容需要超过10个字
        if ( StringUtils.isEmpty(jobExtraDTO.getAnswerContent()) || jobExtraDTO.getAnswerContent().length() < 10) {
            return false;
        }

        //  对同一个问题的重复回答无效
        Set<Long> questionIdSet = records.stream().map(e -> JsonUtils.fromJson(e.getExtra(), UserAnswerJobExtraDTO.class))
                .filter(Objects::nonNull)
                .map(UserAnswerJobExtraDTO::getQuestionId)
                .collect(Collectors.toSet());
        if (questionIdSet.contains(jobExtraDTO.getQuestionId())) {
            return false;
        }
        return true;
    }


    /**
     * 是否为奖励一个礼拜之后
     *
     * @param userCycleJob 任务信息
     * @return 是否因为正在进行中的任务
     */
    private boolean isRewardAfterWeek(UserCycleJob userCycleJob) {
        if (Objects.isNull(userCycleJob)) {
            return false;
        }

        if (UserCycleJobStatus.REWARD.getCode().equals(userCycleJob.getStatus())) {
            return userCycleJob.getLastDate().until(LocalDate.now(), ChronoUnit.DAYS) > 6;
        }
        return false;
    }


    /**
     * 判断是否为正在进行中的任务
     *
     * @param userCycleJob 任务信息
     * @return 是否因为正在进行中的任务
     */
    private boolean isRunningJob(UserCycleJob userCycleJob) {
        if (Objects.isNull(userCycleJob)) {
            return false;
        }

        if (UserCycleJobStatus.RUNNING.getCode().equals(userCycleJob.getStatus())) {
            LambdaQueryWrapper<UserCycleTaskRecord> recordQuery = Wrappers.lambdaQuery();
            recordQuery.eq(UserCycleTaskRecord::getUserCycleJobId, userCycleJob.getId());

            Integer count = userCycleTaskRecordMapper.selectCount(recordQuery);

            return count < this.getJobType().getCycleLength();
        }
        return false;
    }

    /**
     * 是否可以可以发放奖励
     *
     * @param userId
     * @param groupId
     * @return
     */
    @Override
    public boolean shouldGrantReward(Long userId, Long groupId) {
        UserCycleJob lastJob = getLastJob(userId, groupId);

        return Objects.nonNull(lastJob)
                && UserCycleJobStatus.RUNNING.getCode().equals(lastJob.getStatus())
                && getJobType().getCycleLength().equals(lastJob.getProgress());
    }

    private Integer getTotal(Long userId, Long groupId) {
        LambdaQueryWrapper<UserCycleTaskRecord> recordQuery = Wrappers.lambdaQuery();
        recordQuery.eq(UserCycleTaskRecord::getGroupId, groupId);
        recordQuery.eq(UserCycleTaskRecord::getJid, userId);
        recordQuery.eq(UserCycleTaskRecord::getJobType, this.getJobType().getType());
        return userCycleTaskRecordMapper.selectCount(recordQuery);
    }

    /**
     * 查询进度
     *
     * @param userId
     * @param groupId
     * @return first 长度  second 已做的进度
     */
    @Override
    public UserCycleProgressDTO getProgress(Long userId, Long groupId) {
        UserCycleJob lastJob = this.getLastJob(userId, groupId);
        int total = getTotal(userId, groupId);
        if (Objects.nonNull(lastJob) && lastJob.getLastDate().until(LocalDate.now(), ChronoUnit.DAYS) < 7) {
            return new UserCycleProgressDTO(lastJob.getId(),lastJob.getProgress(), this.getJobType().getCycleLength(), total,total);
        } else {
            return new UserCycleProgressDTO(null,0, this.getJobType().getCycleLength(), total,total);
        }

    }


    @Override
    public void grantReward(Long userId, Long groupId) {
        UserCycleJob lastJob = this.getLastJob(userId, groupId);
        if (Objects.nonNull(lastJob) && UserCycleJobStatus.RUNNING.getCode().equals(lastJob.getStatus())) {
            //  增加额外的奖励次数
            groupQaUserService.addExtraTimes(userId, groupId, 1);
            //  更新任务状态为已奖励状态
            lastJob.setStatus(UserCycleJobStatus.REWARD.getCode());
            this.updateById(lastJob);

            //发送站内信
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setMsgType(MessageMsgTypeEnum.SYSTEM_MSG.getId());
            messageDTO.setTitle("系统站内信-活动消息");
            messageDTO.setIntro("恭喜你完成回答任务，已为你发放提问次数1次，请注意查收，7天后可再次进行任务。");
            messageDTO.setDetail("恭喜你完成回答任务，已为你发放提问次数1次，请注意查收，7天后可再次进行任务。");
            messageDTO.setContentType(MessageContentTypeEnum.ANSWER_QUESTION.getId());
            messageDTO.setContentId(-1L);
            messageDTO.setStatus(MessageStatusEnum.STATUS_SEND.getId());
            messageDTO.setType(MessageTypeEnum.TEXT.getId());
            messageDTO.setFromType(0);
            messageDTO.setFromId(0L);
            messageDTO.setFromStatus(true);
            messageDTO.setToType(MessageToTypeEnum.MESSAGE_TO_HIT.getId());
            messageDTO.setToIds(String.valueOf(userId));
            messageDTO.setAdminUserId(0);
            messageDTO.setAdminUserName("");
            messageDTO.setCreateDate(new Date());
            messageDTO.setExecuteType(1);
            messageDTO.setExecuteDate(new Date());
            messageService.addMessage(messageDTO);
        }

    }

    @Override
    public List<UserCycleJobCalendarDTO> getUserCycleJobCalendar(Long userId, Long groupId) {
        return null;
    }

    @Override
    public UserCycleJobStatus getUserMarkStatus(Long userId, Long groupId) {
       return UserCycleJobStatus.NEW;
    }

    @Override
    public UserMarkDrawInfoDTO getDrawInfo(Long jid, Long groupId) {
        return null;
    }

    @Override
    public boolean replenishSign(ReplenishSignDTO dto) {
        return false;
    }
}

