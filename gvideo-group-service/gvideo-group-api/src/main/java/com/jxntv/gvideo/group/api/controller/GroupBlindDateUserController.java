package com.jxntv.gvideo.group.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.group.api.converter.GroupBlindDateConvert;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateNeeds;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUser;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUserImage;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateUserImageAuditing;
import com.jxntv.gvideo.group.api.mapper.GroupBlindDateUserMapper;
import com.jxntv.gvideo.group.api.service.GroupBlindDateNeedsService;
import com.jxntv.gvideo.group.api.service.GroupBlindDateUserImageAuditingService;
import com.jxntv.gvideo.group.api.service.GroupBlindDateUserImageService;
import com.jxntv.gvideo.group.api.service.GroupBlindDateUserService;
import com.jxntv.gvideo.group.api.utils.DateUtils;
import com.jxntv.gvideo.group.api.utils.PageUtils;
import com.jxntv.gvideo.group.sdk.client.GroupBlindDateUserClient;
import com.jxntv.gvideo.group.sdk.constant.GroupBlindDateConstants;
import com.jxntv.gvideo.group.sdk.dto.blind.date.*;
import com.jxntv.gvideo.group.sdk.params.blind.date.user.BlindDateUserJidQueryParam;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/3/18
 */
@RestController
public class GroupBlindDateUserController implements GroupBlindDateUserClient {

    @Resource
    private GroupBlindDateUserService groupBlindDateUserService;

    @Resource
    private GroupBlindDateNeedsService groupBlindDateNeedsService;

    @Resource
    private GroupBlindDateUserImageService groupBlindDateUserImageService;

    @Resource
    private GroupBlindDateUserImageAuditingService groupBlindDateUserImageAuditingService;

    @Resource
    private GroupBlindDateUserMapper groupBlindDateUserMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public Result<Boolean> updateInfo(GroupBlindDateInfoUpdateDTO dto) {
        this.deleteCacheUser(dto.getJid());
        return Result.ok(this.groupBlindDateUserService.updateInfo(dto));
    }

    @Override
    public Result<GroupBlindDateUserDTO> simpleUserInfoByJid(Long jid) {
        return Result.ok(this.queryUserInfoByJid(jid));
    }

    @Override
    public Result<GroupBlindDateUserInfoDTO> completeUserInfoByJid(Long jid) {
        GroupBlindDateUserInfoDTO userInfoDTO = new GroupBlindDateUserInfoDTO();
        userInfoDTO.setUserDTO(this.queryUserInfoByJid(jid));
        userInfoDTO.setNeedsDTO(this.queryNeedsByJid(jid));
        userInfoDTO.setImageList(this.queryImageListByJid(jid));
        return Result.ok(userInfoDTO);
    }

    public GroupBlindDateUserDTO queryUserInfoByJid(Long jid) {

        GroupBlindDateUserDTO userDTO = getCacheUser(jid);
        if (Objects.nonNull(userDTO)) {
            return userDTO;
        }

        LambdaQueryWrapper<GroupBlindDateUser> query = new LambdaQueryWrapper<>();
        query.eq(GroupBlindDateUser::getJid, jid);
        GroupBlindDateUser entity = groupBlindDateUserService.getOne(query);
        userDTO = GroupBlindDateConvert.convertUser(entity);
        cacheUser(jid, userDTO);
        return userDTO;
    }


    public GroupBlindDateNeedsDTO queryNeedsByJid(Long jid) {
        LambdaQueryWrapper<GroupBlindDateNeeds> query = new LambdaQueryWrapper<>();
        query.eq(GroupBlindDateNeeds::getJid, jid);
        GroupBlindDateNeeds entity = groupBlindDateNeedsService.getOne(query);
        return GroupBlindDateConvert.convertFriend(entity);
    }


    public List<GroupBlindDateUserImageDTO> queryImageListByJid(Long jid) {
        LambdaQueryWrapper<GroupBlindDateUserImage> query = new LambdaQueryWrapper<>();
        query.eq(GroupBlindDateUserImage::getJid, jid);
        query.orderByAsc(Arrays.asList(GroupBlindDateUserImage::getType, GroupBlindDateUserImage::getSerialNo));
        List<GroupBlindDateUserImage> list = groupBlindDateUserImageService.list(query);
        return list.stream().map(GroupBlindDateConvert::convertImage).collect(Collectors.toList());
    }


    @Override
    public Result<PageDTO<GroupBlindDateUserDTO>> auditPage(GroupBlindDateUserAuditSearchDTO searchDTO) {
        LambdaQueryWrapper<GroupBlindDateUser> query = new LambdaQueryWrapper<>();

        query.eq(Objects.nonNull(searchDTO.getId()), GroupBlindDateUser::getId, searchDTO.getId());
        query.eq(Objects.nonNull(searchDTO.getAuditStatus()), GroupBlindDateUser::getAuditStatus, searchDTO.getAuditStatus());
        query.eq(Objects.nonNull(searchDTO.getCompanyId()), GroupBlindDateUser::getCompanyId, searchDTO.getCompanyId());
        query.eq(Objects.nonNull(searchDTO.getEducation()), GroupBlindDateUser::getEducation, searchDTO.getEducation());
        query.eq(Objects.nonNull(searchDTO.getGender()), GroupBlindDateUser::getGender, searchDTO.getGender());
        query.eq(Objects.nonNull(searchDTO.getMarital()), GroupBlindDateUser::getMarital, searchDTO.getMarital());
        query.eq(Objects.nonNull(searchDTO.getCityId()), GroupBlindDateUser::getCityId, searchDTO.getCityId());
        query.eq(Objects.nonNull(searchDTO.getUnionId()), GroupBlindDateUser::getUnionId, searchDTO.getUnionId());
        query.eq(Objects.nonNull(searchDTO.getChannel()), GroupBlindDateUser::getChannel, searchDTO.getChannel());
        query.eq(Objects.nonNull(searchDTO.getAstType()), GroupBlindDateUser::getAstType, searchDTO.getAstType());
        query.eq(Objects.nonNull(searchDTO.getCompanyVerifyStatus()), GroupBlindDateUser::getCompanyVerifyStatus, searchDTO.getCompanyVerifyStatus());
        query.like(!StringUtils.isEmpty(searchDTO.getName()), GroupBlindDateUser::getName, searchDTO.getName());
        query.like(!StringUtils.isEmpty(searchDTO.getSchoolName()), GroupBlindDateUser::getSchoolName, searchDTO.getSchoolName());
        if (!StringUtils.isEmpty(searchDTO.getMobile())) {
            query.and(wrapper -> wrapper.eq(GroupBlindDateUser::getMobile, AESUtil.AESCBCEncode(searchDTO.getMobile())).or().like(GroupBlindDateUser::getMaskMobile, searchDTO.getMobile()));
        }
        //query.like(!StringUtils.isEmpty(searchDTO.getMobile()), GroupBlindDateUser::getMobile, AESUtil.AESCBCEncode(searchDTO.getMobile()));
        query.like(!StringUtils.isEmpty(searchDTO.getCompanyName()), GroupBlindDateUser::getCompanyName, searchDTO.getCompanyName());
        query.like(!StringUtils.isEmpty(searchDTO.getNickname()), GroupBlindDateUser::getNickname, searchDTO.getNickname());
        query.like(!StringUtils.isEmpty(searchDTO.getDistrictName()), GroupBlindDateUser::getDistrictName, searchDTO.getDistrictName());

        if (Objects.nonNull(searchDTO.getAge())) {
            query.gt(GroupBlindDateUser::getBirthday, DateUtils.getAgeStart(-searchDTO.getAge()));
            query.lt(GroupBlindDateUser::getBirthday, DateUtils.getAgeEnd(-searchDTO.getAge()));
        }

        query.orderByDesc(GroupBlindDateUser::getUpdateDate);
        IPage<GroupBlindDateUser> page = groupBlindDateUserService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), query);
        return Result.ok(PageUtils.pageOf(page, GroupBlindDateConvert::convertUser));
    }

    @Override
    public Result<GroupBlindDateUserInfoDTO> userInfoById(Long id) {
        GroupBlindDateUser entity = groupBlindDateUserService.getById(id);
        if (Objects.isNull(entity)) {
            return Result.fail("数据不存在");
        }
        GroupBlindDateUserInfoDTO userInfoDTO = new GroupBlindDateUserInfoDTO();
        userInfoDTO.setUserDTO(GroupBlindDateConvert.convertUser(entity));
        userInfoDTO.setNeedsDTO(this.queryNeedsByJid(entity.getJid()));
        userInfoDTO.setImageList(this.queryImageListByJid(entity.getJid()));
        return Result.ok(userInfoDTO);
    }

    @Override
    public Result<Boolean> saveAudit(GroupBlindDateAuditDTO auditDTO) {
        Long jid = groupBlindDateUserService.saveAudit(auditDTO);
        Boolean status = Objects.nonNull(jid);
        if (Boolean.TRUE.equals(status)){
            this.deleteCacheUser(jid);
        }
        return Result.ok(status);
    }

    @Override
    public Result<List<GroupBlindDateDicDTO>> hobbyDic() {
        List<GroupBlindDateDicDTO> list = this.groupBlindDateUserMapper.hobbyDic();
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(Collections.emptyList());
        }

        Map<Integer, List<GroupBlindDateDicDTO>> dicGroup = list.stream().filter(e -> Objects.nonNull(e.getUpid())).collect(Collectors.groupingBy(GroupBlindDateDicDTO::getUpid));
        List<GroupBlindDateDicDTO> dicList = list.stream().filter(item -> Objects.equals(item.getLevel(), 1)).peek(item -> item.setChild(dicGroup.get(item.getId()))).collect(Collectors.toList());

        return Result.ok(dicList);
    }

    @Override
    public Result<List<GroupBlindDateDicDTO>> occupationDic() {
        List<GroupBlindDateDicDTO> list = this.groupBlindDateUserMapper.occupationDic();
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok(Collections.emptyList());
        }

        Map<Integer, List<GroupBlindDateDicDTO>> dicGroup = list.stream().filter(e -> Objects.nonNull(e.getUpid())).collect(Collectors.groupingBy(GroupBlindDateDicDTO::getUpid));
        List<GroupBlindDateDicDTO> dicList = list.stream().filter(item -> Objects.equals(item.getLevel(), 1)).peek(item -> item.setChild(dicGroup.get(item.getId()))).collect(Collectors.toList());

        return Result.ok(dicList);
    }

    @Override
    public Result<List<GroupBlindDateDicDTO>> companyDic() {
        return Result.ok(this.groupBlindDateUserMapper.companyDic());
    }

    @Override
    public Result<Boolean> saveEnrollInfo(GroupBlindDateUserEnrollDTO dto) {
        Boolean status = this.groupBlindDateUserService.saveEnrollInfo(dto);
        this.deleteCacheUser(dto.getJid());
        return Result.ok(status);
    }

    @Override
    public Result<GroupBlindDateUserInfoDTO> getCompanyAuditRecord(Long auditUserJid) {
        GroupBlindDateUserInfoDTO dto = new GroupBlindDateUserInfoDTO();
        GroupBlindDateUserDTO userDto = this.groupBlindDateUserService.getCompanyAuditRecord(auditUserJid);
        dto.setUserDTO(userDto);
        if (Objects.nonNull(userDto)) {
            dto.setImageList(this.queryImageListByJid(userDto.getJid()));
        }
        return Result.ok(dto);
    }

    @Override
    public Result<Boolean> saveCompanyAudit(GroupBlindDateCompanyAuditDTO dto) {
        GroupBlindDateUser entity = this.groupBlindDateUserService.getById(dto.getId());
        if (Objects.nonNull(entity)) {
            this.deleteCacheUser(entity.getJid());
        }
        return Result.ok(this.groupBlindDateUserService.saveCompanyAudit(dto));
    }

    @Override
    public Result<List<GroupBlindDateUserDTO>> simpleUserInfoByJidBatch(List<Long> jids) {
        if (CollectionUtils.isEmpty(jids)) {
            return Result.ok(null);
        }
        List<GroupBlindDateUserDTO> list = new ArrayList<>();
        for (Long jid : jids) {
            GroupBlindDateUserDTO dto = this.queryUserInfoByJid(jid);
            if (Objects.nonNull(dto)) {
                list.add(dto);
            }

        }
        return Result.ok(list);
    }

    @Override
    public Result<Boolean> removeUserInfoByJid(Long jid) {
        if (Objects.nonNull(jid)) {
            this.deleteBlindDateCache(jid);
        }
        return Result.ok(this.groupBlindDateUserService.removeUserInfoByJid(jid));
    }

    @Override
    public Result<Boolean> removeUserInfoById(Long id) {
        if (Objects.nonNull(id)) {
            GroupBlindDateUser entity = groupBlindDateUserService.getById(id);
            if (Objects.isNull(entity)) {
                return Result.fail("数据不存在");
            }
            this.deleteBlindDateCache(entity.getJid());
            return Result.ok(this.groupBlindDateUserService.removeUserInfoByJid(entity.getJid()));
        }
        return Result.ok();
    }

    @Override
    public Result<List<BlindDateUserJidDTO>> getJidList(BlindDateUserJidQueryParam param) {
        Integer limit = param.getLimit();
        Long maxId = param.getMaxId();

        LambdaQueryWrapper<GroupBlindDateUser> userQuery = Wrappers.lambdaQuery();
        userQuery.select(GroupBlindDateUser::getId, GroupBlindDateUser::getJid);
        userQuery.gt(GroupBlindDateUser::getId, maxId);
        userQuery.orderByAsc(GroupBlindDateUser::getId);
        userQuery.last(" limit " + limit);

        List<GroupBlindDateUser> list = groupBlindDateUserService.list(userQuery);
        List<BlindDateUserJidDTO> collect = list.stream().map(e -> {
            BlindDateUserJidDTO dto = new BlindDateUserJidDTO();
            dto.setId(e.getId());
            dto.setJid(e.getJid());
            return dto;
        }).collect(Collectors.toList());

        return Result.ok(collect);
    }

    @Override
    public Result<List<GroupBlindDateUserImageDTO>> queryImageListByJid(Long jid, Integer type) {
        LambdaQueryWrapper<GroupBlindDateUserImage> query = new LambdaQueryWrapper<>();
        query.eq( GroupBlindDateUserImage::getJid, jid);
        query.eq(Objects.nonNull(type), GroupBlindDateUserImage::getType, type);
        query.orderByAsc(Arrays.asList(GroupBlindDateUserImage::getType,GroupBlindDateUserImage::getSerialNo));
        List<GroupBlindDateUserImage> list = groupBlindDateUserImageService.list(query);
        return Result.ok(list.stream().map(GroupBlindDateConvert::convertImage).collect(Collectors.toList()));
    }

    @Override
    public Result<List<GroupBlindDateUserImageDTO>> queryAuditingImageListByJid(Long jid, Integer type) {
        LambdaQueryWrapper<GroupBlindDateUserImageAuditing> query = new LambdaQueryWrapper<>();
        query.eq( GroupBlindDateUserImageAuditing::getJid, jid);
        query.eq(Objects.nonNull(type), GroupBlindDateUserImageAuditing::getType, type);
        query.orderByAsc(Arrays.asList(GroupBlindDateUserImageAuditing::getType,GroupBlindDateUserImageAuditing::getSerialNo));
        List<GroupBlindDateUserImageAuditing> list = groupBlindDateUserImageAuditingService.list(query);
        return Result.ok(list.stream().map(GroupBlindDateConvert::convertAuditingImage).collect(Collectors.toList()));
    }

    @Override
    public Result<Boolean> updateUserInfo(GroupBlindDateUserInfoUpdateDTO dto) {
        GroupBlindDateUser entity = this.groupBlindDateUserService.getById(dto.getId());
        if (Objects.nonNull(entity)) {
            this.deleteCacheUser(entity.getJid());
        }
        return Result.ok(this.groupBlindDateUserService.updateUserInfo(dto));
    }

    private void cacheUser(Long jid, GroupBlindDateUserDTO dto) {
        if (Objects.isNull(dto)) {
            stringRedisTemplate.opsForValue().set(GroupBlindDateConstants.BLIND_DATE_USER_CACHE_KEY_PREFIX + jid, new Gson().toJson(dto), 5, TimeUnit.MINUTES);
        } else {
            stringRedisTemplate.opsForValue().set(GroupBlindDateConstants.BLIND_DATE_USER_CACHE_KEY_PREFIX + jid, new Gson().toJson(dto), 1, TimeUnit.DAYS);
        }
    }

    private GroupBlindDateUserDTO getCacheUser(Long jid) {
        String userStr = stringRedisTemplate.opsForValue().get(GroupBlindDateConstants.BLIND_DATE_USER_CACHE_KEY_PREFIX + jid);
        return new Gson().fromJson(userStr, GroupBlindDateUserDTO.class);
    }

    private void deleteCacheUser(Long jid) {
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_DATE_USER_CACHE_KEY_PREFIX + jid);
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_RECOMMEND_GUEST_KEY_PREFIX + jid);
    }

    private void deleteBlindDateCache(Long jid) {
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_DATE_USER_CACHE_KEY_PREFIX + jid);
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_DATA_LOVE_KEY_PREFIX + jid);
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_DATA_LOVE_MEET_KEY_PREFIX + jid);
        stringRedisTemplate.delete(GroupBlindDateConstants.BLIND_RECOMMEND_GUEST_KEY_PREFIX + jid);
    }

}
