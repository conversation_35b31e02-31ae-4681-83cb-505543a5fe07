package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.OptionsDTO;
import com.jxntv.gvideo.group.api.entity.GroupVocationValueSchool;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/5
 * Email: <EMAIL>
 */
public interface GroupVocationValueSchoolMapper extends BaseMapper<GroupVocationValueSchool> {

    @Select("<script>" +
            "select city_id id,city_name name from group_vocation_value_school where group_id=#{groupId} group by city_id,city_name having count(1)>0 order by city_id" +
            "</script>")
    List<OptionsDTO> listCityByGroupId(@Param("groupId") Long groupId);

    @Select("<script>" +
            "SELECT cityId,hotNum,rankNum FROM " +
            " (SELECT tt.hot_num hotNum,tt.city_id cityId,( @rownum := @rownum + 1 ) rankNum FROM ( SELECT city_id city_id, sum( hot_num ) hot_num, @rownum := 0 AS rn FROM group_vocation_value_school WHERE group_id = #{groupId} GROUP BY city_id ) tt ORDER BY tt.hot_num DESC,tt.city_id ASC ) t" +
            "</script>")
    IPage<GroupVocationValueSchool> searchCityRank(@Param("page") Page<GroupVocationValueSchool> page, @Param("groupId") Long groupId);

}