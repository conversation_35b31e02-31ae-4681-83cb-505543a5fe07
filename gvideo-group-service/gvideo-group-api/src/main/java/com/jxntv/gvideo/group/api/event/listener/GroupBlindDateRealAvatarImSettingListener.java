package com.jxntv.gvideo.group.api.event.listener;

import com.jxntv.gvideo.aliyun.sdk.ImClient;
import com.jxntv.gvideo.aliyun.sdk.dto.im.EditUserProfileDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.api.event.GroupBlindDateRealAvatarImSettingEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/6/26 9:58
 */
@Component
@Slf4j
@RefreshScope
public class GroupBlindDateRealAvatarImSettingListener {

    @Resource
    private ImClient imClient;

    @Transactional(rollbackFor = Exception.class)
    @Async
    @EventListener
    public void onEvent(GroupBlindDateRealAvatarImSettingEvent event) {
        EditUserProfileDTO dto = new EditUserProfileDTO();
        dto.setAccount(String.valueOf(event.getJid()));
        dto.setAvatarId(event.getOssId());
        Result<Boolean> result = imClient.editUserProfile(dto);
        log.info("放心爱-真实头像-设置为IM头像,event={},result={}", JsonUtils.toJson(event),JsonUtils.toJson(result));

    }
}
