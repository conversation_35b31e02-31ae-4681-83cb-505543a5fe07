package com.jxntv.gvideo.group.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateLoveActivity;
import com.jxntv.gvideo.group.sdk.dto.blind.date.love.GroupBlindDateLovePairedExportDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.love.GroupBlindDateLoveSecondaryExportDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 圈子-都是放心爱-相亲-活动表
 * 
 * <AUTHOR>
 * @date 2022-05-13 15:49:48
 */
@Repository
public interface GroupBlindDateLoveActivityMapper extends BaseMapper<GroupBlindDateLoveActivity> {

    /**
     * 查询相亲活动 配对列表（用于导出）
     * @param activityId
     * @return
     */

    List<GroupBlindDateLovePairedExportDTO> queryPairedList(@Param("activityId") Long activityId);

    /**
     * 查询相亲活动 复选列表（用于导出）
     * @param activityId
     * @return
     */

    List<GroupBlindDateLoveSecondaryExportDTO> querySecondaryList(@Param("activityId") Long activityId);
}
