package com.jxntv.gvideo.group.api.job;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.api.client.MessageService;
import com.jxntv.gvideo.group.api.entity.GroupBlindDateRedBeans;
import com.jxntv.gvideo.group.api.mapper.GroupBlindDateRedBeansLogMapper;
import com.jxntv.gvideo.group.api.service.GroupBlindDateRedBeansService;
import com.jxntv.gvideo.group.sdk.dto.blind.date.redBeans.GroupBlindDateRedBeansPostFavoriteDTO;
import com.jxntv.gvideo.group.sdk.enums.group.blind.date.RedBeansTypeEnum;
import com.jxntv.gvideo.interact.client.dto.MessageDTO;
import com.jxntv.gvideo.interact.client.dto.enums.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * 放心爱用户红豆任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupBlindDateRedBeansJob {

    @Resource
    private GroupBlindDateRedBeansService groupBlindDateRedBeansService;

    @Resource
    private GroupBlindDateRedBeansLogMapper groupBlindDateRedBeansLogMapper;

    @Resource
    private MessageService messageService;

    @Value("${blind-date.group-id}")
    private Long blindDateGroupId;
    /**
     * 每天0点刷新
     * 刷新流程，将所有用户当日的任务重置为0
     */
    @XxlJob("resetDailyRedBeansTaskJob")
    public ReturnT<String> resetAnswerTimesJob() {
        XxlJobHelper.log("【放心爱用户红豆当日任务】 刷新开始");
        LambdaUpdateWrapper<GroupBlindDateRedBeans> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(GroupBlindDateRedBeans::getDailyPostFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyPostPopFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyReplyFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyReplyPopFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyMarkFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyMarkPopFlag,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyRecommendTimes,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyVisitTimes,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyFavoriteTimes,0);
        updateWrapper.set(GroupBlindDateRedBeans::getDailyAccostRespondTimes,0);
        this.groupBlindDateRedBeansService.update(updateWrapper);
        XxlJobHelper.log("【放心爱用户红豆当日任务】 刷新完成");
        return ReturnT.SUCCESS;
    }


    /**
     * 每天10点统计昨日动态被点赞数
     */
    @XxlJob("postFavoriteDailyRedBeansTaskJob")
    public ReturnT<String> postFavoriteDailyRedBeansTaskJob() {
        XxlJobHelper.log("【放心爱-红豆-动态被点赞任务】 刷新开始");

        LocalDate localDate = LocalDate.now().plusDays(-1);
        List<GroupBlindDateRedBeansPostFavoriteDTO> postFavoriteList = this.groupBlindDateRedBeansLogMapper.postFavoriteStatistics(localDate.toString(), RedBeansTypeEnum.POST_BE_FAVORITE.getCode());
        if (!CollectionUtils.isEmpty(postFavoriteList)){
            postFavoriteList.forEach(e -> {

                MessageDTO dto = new MessageDTO();
                dto.setMsgType(MessageMsgTypeEnum.SYSTEM_MSG.getId());
                dto.setTitle("系统通知");
                dto.setDetail("昨日你的动态共收获了"+e.getTimes()+"个喜欢，红豆+"+e.getTotal());
                dto.setContentType(MessageContentTypeEnum.RED_BEANS_POST_FAVORITE.getId());
                dto.setContentId(e.getJid());
                dto.setStatus(MessageStatusEnum.STATUS_SEND.getId());
                dto.setType(MessageTypeEnum.TEXT.getId());
                /*dto.setType(MessageTypeEnum.BUTTON_LINK.getId());
                dto.setExtra("去查看");
                dto.setOuterUrl("jinshipin://jumpPage?login=1&pid=redBeansBudget");*/
                dto.setFromType(0);
                dto.setFromId(0L);
                dto.setFromStatus(true);
                dto.setToType(MessageToTypeEnum.MESSAGE_TO_HIT.getId());
                dto.setToIds(String.valueOf(e.getJid()));
                dto.setAdminUserId(0);
                dto.setAdminUserName("");
                dto.setCreateDate(new Date());
                dto.setExecuteType(1);
                dto.setExecuteDate(new Date());
                dto.setGroupId(blindDateGroupId);

                Result<Long> rst = this.messageService.addMessage(dto);
                if (rst.getCode() != CodeMessage.OK.getCode()) {
                    log.error("放心爱-红豆-动态被点赞系统消息："+ JsonUtils.toJson(rst));
                }

            });
        }

        XxlJobHelper.log("【放心爱-红豆-动态被点赞任务】 刷新完成");
        return ReturnT.SUCCESS;
    }
}
