<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxntv.gvideo.group.api.mapper.GroupGatherActivityMemberMapper">
  <resultMap id="BaseResultMap" type="com.jxntv.gvideo.group.api.entity.GroupGatherActivityMember">
    <!--@mbg.generated-->
    <!--@Table group_gather_activity_member-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="activity_id" jdbcType="BIGINT" property="activityId" />
    <result column="jid" jdbcType="BIGINT" property="jid" />
    <result column="enroll_date" jdbcType="TIMESTAMP" property="enrollDate" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="audit_date" jdbcType="TIMESTAMP" property="auditDate" />
    <result column="audit_user_id" jdbcType="BIGINT" property="auditUserId" />
    <result column="audit_user_name" jdbcType="VARCHAR" property="auditUserName" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="mask_mobile" jdbcType="VARCHAR" property="maskMobile" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="gender" jdbcType="BOOLEAN" property="gender" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="height" jdbcType="INTEGER" property="height" />
    <result column="education" jdbcType="BOOLEAN" property="education" />
    <result column="marital" jdbcType="BOOLEAN" property="marital" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="post_type" jdbcType="BOOLEAN" property="postType" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_id" jdbcType="INTEGER" property="cityId" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="photo_oss_id" jdbcType="VARCHAR" property="photoOssId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, activity_id, jid, enroll_date, audit_status, audit_date, audit_user_id, audit_user_name,
    remarks, mobile, mask_mobile, `name`, gender, birthday, height, education, marital,
    company_id, company_name, post_type, province_id, province_name, city_id, city_name,
    nickname, photo_oss_id, create_date, update_date
  </sql>

  <select id="listMyActivity" resultType="com.jxntv.gvideo.group.sdk.dto.MyJoinActivityDTO">
    select
           ac.id as activityId,
           ac.title as title,
           acm.audit_status as auditStatus,
           ac.cost_flag as costFlag,
           ggamp.pay_state as payState,
           acm.remarks as remarks,
           ac.type
    from group_gather_activity_member acm
           join group_gather_activity  ac on acm.activity_id=ac.id
           left join group_gather_activity_member_pay ggamp on ggamp.enroll_id = acm.id
    where acm.jid=#{jid}  and ac.del_flag=0 and ac.group_id=#{groupId}
      <if test="type != null">
        and ac.type = #{type}
      </if>

      order by acm.enroll_date desc
  </select>

  <select id="listAllEnroll" resultType="com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityMemberDTO">
    select ggam.id as id,
           ggam.activity_id as activityId,
           ggam.jid as jid,
           ggam.enroll_date as enrollDate,
           ggam.audit_status as auditStatus,
           ggamp.pay_state as payState,
           ggamp.refund_state as refundState,
           ggam.audit_date as auditDate,
           ggam.audit_user_id as auditUserId,
           ggam.audit_user_name as auditUserName,
           ggam.remarks as remarks,
           ggam.mobile as mobile,
           ggam.mask_mobile as maskMobile,
           ggam.name as name,
           ggam.gender as gender,
           ggam.birthday as birthday,
           ggam.height as height,
           ggam.education as education,
           ggam.marital  as marital,
           ggam.company_id as companyId,
           ggam.company_name as companyName,
           ggam.post_type as postType,
           ggam.province_id as provinceId,
           ggam.province_name as provinceName,
           ggam.city_id as cityId,
           ggam.city_name as cityName,
           ggam.nickname as nickname,
           ggam.photo_oss_id as photoOssId,
           ggam.manifesto as manifesto,
           ggam.paired_flag as pairedFlag,
           ggam.paired_jid as pairedJid,
           ggam.paired_time as pairedTime
    from  group_gather_activity_member ggam
            left join  group_gather_activity_member_pay ggamp on ggamp.enroll_id = ggam.id
    where (ggamp.pay_state is null or ggamp.pay_state = 1)
      <if test="id != null"> and ggam.id = #{id}</if>
      <if test="activityId != null"> and ggam.activity_id = #{activityId}</if>
      <if test="gender != null"> and ggam.gender = #{gender}</if>
      <if test="marital != null"> and ggam.marital = #{marital}</if>
      <if test="education != null"> and ggam.education = #{education} </if>
      <if test="auditStatus != null">
          <if test="auditStatus == 3" >
              and ggam.audit_status in (1,2)
          </if>
          <if test="auditStatus != 3">
              and ggam.audit_status = #{auditStatus}
          </if>
      </if>

      <if test="pairedStatus != null">
            and ggam.audit_status = 1 and  ggam.paired_flag =#{pairedStatus}
      </if>

      <if test="heightFrom != null">
          and ggam.height >= #{heightFrom}
      </if>
      <if test="heightTo != null">
          and ggam.height <![CDATA[<=]]> #{heightTo}
      </if>
      <if test="yearFrom != null">
          and ggam.birthday  <![CDATA[>=]]> concat(#{yearFrom},'-01-01 00:00:00')
      </if>
      <if test="yearTo != null">
          and ggam.birthday <![CDATA[<=]]> concat(#{yearFrom},'-12-31 00:00:00')
      </if>

      <if test="name != null and name != ''">
          and ggam.name like concat('%',#{name},'%')
      </if>
      <if test="mobile != null and mobile != ''">
          and ggam.mobile like concat('%',#{mobile},'%')
      </if>
      <if test="refundState != null">
          and ggamp.refund_state=#{refundState}
      </if>
      <if test="asc == true">
          <if test="orderBy != null and orderBy == 'auditDate'">
              order by ggam.audit_date
          </if>
          <if test="orderBy != null and orderBy == 'pairedDate'">
              order by ggam.paired_time
          </if>
          <if test="orderBy == null or orderBy == 'enrollDate'">
              order by ggam.enroll_date
          </if>
      </if>
      <if test="asc == null or asc == false">
          <if test="orderBy != null and orderBy == 'auditDate'">
              order by ggam.audit_date desc
          </if>
          <if test="orderBy != null and orderBy == 'pairedDate'">
              order by ggam.paired_time desc
          </if>
          <if test="orderBy == null or orderBy == 'enrollDate'">
              order by ggam.enroll_date desc
          </if>
      </if>

    </select>
    <select id="queryEnrollList"
            resultType="com.jxntv.gvideo.group.sdk.dto.activity.GroupGatherActivityEnrollMemberExportDTO">

        SELECT
            t2.NAME,
            t2.mobile,
            t2.gender,
            DATE_FORMAT(t2.birthday, '%Y') year,
            t2.height,
            t2.education,
            t2.province_name,
            t2.city_name,
            t2.marital,
            t2.company_name,
            t2.daily_photos_audit,
            t2.company_verify_status,
            t2.occupation_name,
            t2.post_type,
            t1.enroll_date,
            t1.audit_status,
            t1.audit_date
        FROM
            group_gather_activity_member t1
        left JOIN group_blind_date_user t2 ON t1.jid = t2.jid
        left join  group_gather_activity_member_pay ggamp on ggamp.enroll_id = t1.id
        <where>
            t1.activity_id = #{activityId} and  (ggamp.pay_state is null or ggamp.pay_state = 1)
            <if test="status == 0">
                and t1.audit_status=#{status}
            </if>
            <if test="status == 1">
                and t1.audit_status between 1 and 2
            </if>

        </where>
        order by t1.enroll_date desc,t1.jid
    </select>
    <select id="queryEnrollInfo" resultType="com.jxntv.gvideo.group.api.event.GatherActivityEnrollEvent">
        SELECT t2.id activityId,t1.jid FROM group_gather_activity_member t1
         INNER  JOIN group_gather_activity t2 on t1.activity_id = t2.id
        where t1.id = #{enrollId}
    </select>
</mapper>
