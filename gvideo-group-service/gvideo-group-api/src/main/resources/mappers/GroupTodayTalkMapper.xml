<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.group.api.mapper.GroupTodayTalkMapper">
    <select id="getVoteContributeList" resultType="Long">
        (select id
         from group_today_talk
         where group_id = #{groupId}
           and status = 0
           and accept_flag = 0
           and create_date between #{firstStart} and #{firstEnd}
         order by vote desc limit 1)
        union
        (select id
         from group_today_talk
         where group_id = #{groupId}
           and status = 0
           and accept_flag = 0
           and create_date between #{secondStart} and #{secondEnd}
         order by create_date desc)
        union
        (select id
         from group_today_talk
         where group_id = #{groupId}
           and status = 0
           and accept_flag = 0
           and create_date between #{thirdStart} and #{thirdEnd}
         order by vote desc)
    </select>

    <select id="selectRewardUserList" resultType="com.jxntv.gvideo.group.api.entity.today.talk.GroupTodayTalk">
        select min(id) as id,contribute_id,media_id from group_today_talk
        where status = 1
        and group_id=#{groupId}
        and release_date between #{startDate} and #{endDate}
        group by contribute_id;
    </select>
</mapper>
