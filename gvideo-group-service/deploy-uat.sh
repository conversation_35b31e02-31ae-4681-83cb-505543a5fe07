#!/bin/bash
# 参数配置
project_name="gvideo-group-service"
module_name="gvideo-group-service/gvideo-group-api"
jar_name="group.jar"
jar_path="gvideo-group-service/gvideo-group-api/target/${jar_name}"
log_path="/data/gvideo/logs/${project_name}.out"
env="uat"
# 更新代码
cd .. && git pull
# 打包服务
mvn clean package -T 1C -Dmaven.test.skip=true -Dmaven.compile.fork=true -am -pl ${module_name} -P ${env}
# 关闭服务
pid=$(ps ax | grep -i ${jar_name} | grep java | grep -v grep | awk '{print $1}')
if [ -z "$pid" ]; then
  echo "No ${project_name} running."
else
  echo "The ${project_name}(${pid}) is running..."
  kill -9 ${pid}
  echo "Send shutdown request to ${project_name}(${pid}) OK"
fi
# 启动服务
echo "" > ${log_path}
nohup java -jar -Xmx800m -Xms512m ${jar_path} >> ${log_path} 2>&1 &
tail -f ${log_path}
