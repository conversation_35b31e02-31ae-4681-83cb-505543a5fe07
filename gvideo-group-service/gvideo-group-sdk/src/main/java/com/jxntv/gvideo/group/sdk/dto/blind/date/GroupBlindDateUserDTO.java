package com.jxntv.gvideo.group.sdk.dto.blind.date;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/3/18
 */
@Data
public class GroupBlindDateUserDTO implements Serializable {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 今视频唯一ID
     */
    private Long jid;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 脱敏手机号
     */
    private String maskMobile;
    /**
     * 证件类型 1 居民身份证
     */
    private Integer cardType;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 真实姓名
     */
    private String name;
    /**
     * 证件号码
     */
    private String cardNo;
    /**
     * 脱敏证件号码
     */
    private String maskCardNo;
    /**
     * 实人认证 0、未认证 1、已认证
     */
    private Integer verifyFlag;
    /**
     * 性别 1、男 2、女
     */
    private Integer gender;
    /**
     * 出生日期
     */
    private String birthday;
    /**
     * 身高（cm）
     */
    private Integer height;
    /**
     * 学历 1、高中 2、中专 3、大专 4、大学本科 5、硕士 6、博士
     */
    private Integer education;
    /**
     * 职业ID
     */
    private Integer occupationId;

    /**
     * 职业名称
     */
    private String occupationName;

    /**
     * 职业父节点ID
     */
    private Integer occupationParentId;

    /**
     * 职业父节点名称
     */
    private String occupationParentName;

    /**
     * 婚姻状况 1、未婚 2、离异 3、丧偶
     */
    private Integer marital;
    /**
     * 单位ID
     */
    private Long companyId;
    /**
     * 单位名称
     */
    private String companyName;
    /**
     * 岗位性质
     */
    private Integer postType;
    /**
     * 薪水（月收入）起始
     */
    private Integer salaryFrom;
    /**
     * 薪水（月收入）结束
     */
    private Integer salaryTo;
    /**
     * 星座
     */
    private Integer constellation;

    /**
     * 内心独白
     */
    private String monologue;
    /**
     * 兴趣爱好
     */
    private String hobby;
    /**
     * 省/直辖市ID
     */
    private Long provinceId;
    /**
     * 省/直辖市名称
     */
    private String provinceName;
    /**
     * 地市ID
     */
    private Long cityId;
    /**
     * 地市名称
     */
    private String cityName;
    /**
     * 区县ID
     */
    private Long districtId;
    /**
     * 区县名称
     */
    private String districtName;
    /**
     * 个人生活照审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer dailyPhotosAudit;

    /**
     * 薪水（月收入）审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer salaryAudit;

    /**
     * 内心独白审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer monologueAudit;
    /**
     * 房产购置状态 0、未购置 1、已购置
     */
    private Integer houseAcqStatus;
    /**
     * 房产购置审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer houseAcqAudit;
    /**
     * 车辆购置状态 0、未购置 1、已购置
     */
    private Integer carAcqStatus;
    /**
     * 车辆购置审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer carAcqAudit;

    /**
     * 审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer auditStatus;

    /**
     * 单位认证状态 0未认证 1认证通过 2认证不通过
     */
    private Integer companyVerifyStatus;

    /**
     * 数据渠道  0-app 1-单位链接  2-工会链接 3-盲盒 4-985高校
     */
    private Integer channel;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 真实头像状态 0未审核 1审核通过 2审核不通过
     */
    private Integer realAvatarAudit;

    /**
     * 恋爱目标：
     * 1、慢慢接触再考虑  2、谈一段甜甜的恋爱 3、先谈恋爱，合适可以结婚  4、短期内想结婚
     */
    private Integer loveGoal;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 科协类型 0-科协未认证 1-科协已认证
     */
    private Integer astType;
}
