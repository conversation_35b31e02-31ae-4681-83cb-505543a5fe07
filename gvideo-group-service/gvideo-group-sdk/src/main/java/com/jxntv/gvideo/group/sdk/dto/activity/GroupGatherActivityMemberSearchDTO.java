package com.jxntv.gvideo.group.sdk.dto.activity;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

/**
 *  圈子(社区)-组件-活动-报名查询
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class GroupGatherActivityMemberSearchDTO extends SearchDTO {

    /**
     * ID
     */
    private Long id;
    /**
     * 圈子组件活动id
     */
    private Long activityId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 真实姓名
     */
    private String name;
    /**
     * 性别 1、男 2、女
     */
    private Integer gender;
    /**
     * 出生年份开始
     */
    private Integer yearFrom;

    /**
     * 出生年份结束
     */
    private Integer yearTo;
    /**
     * 身高起始（cm）
     */
    private Integer heightFrom;

    /**
     * 身高结束（cm）
     */
    private Integer heightTo;

    /**
     * 学历 1、高中及以下 2、中专 3、大专 4、大学本科 5、硕士 6、博士
     */
    private Integer education;
    /**
     * 婚姻状况 1、未婚 2、离异 3、丧偶
     */
    private Integer marital;

    /**
     * 审核状态，0-未审核，1-通过，2-拒绝  3审核过（1-通过、2-拒绝的合集）
     */
    private Integer auditStatus;

    /**
     *  配对状态，0-未配对 1-已配对
     */
    private Integer pairedStatus;


    /**
     * 退款状态: 0:未退款 ,1:退款成功 2：退款失败
     */
    private Integer refundState;

}
