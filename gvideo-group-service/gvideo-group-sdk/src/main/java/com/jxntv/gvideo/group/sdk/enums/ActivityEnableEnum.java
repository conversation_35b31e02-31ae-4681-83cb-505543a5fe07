package com.jxntv.gvideo.group.sdk.enums;

import lombok.Getter;

@Getter
public enum ActivityEnableEnum {

    //是否开启: 0-禁用、1-启用
    DISABLE(0, "禁用"),
    ENABLE(1, "启动"),

    ;

    private final Integer code;
    private final String desc;

    ActivityEnableEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityEnableEnum parse(Integer code) {
        for (ActivityEnableEnum value : ActivityEnableEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


}
