package com.jxntv.gvideo.group.sdk.dto.activity;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.group.sdk.dto.activity.plan.GroupGatherActivityPlanMemberDTO;
import lombok.Data;

import java.io.Serializable;

/**
 * 圈子(社区)-组件-活动
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class GroupGatherActivityV2DTO extends GroupGatherActivityDTO implements Serializable {

    /**
     * 标签内容
     */
    private String tagName;

    /**
     * 同标签的约会计划条数
     */
    private Integer tagPlanCount;

    /**
     * 报名人员列表
     */
    private PageDTO<GroupGatherActivityPlanMemberDTO> record;

    /**
     * 用户报名状态: 0-未报名 1-报名审核中 2-报名审核成功，匹配中  3-报名审核失败 4-匹配成功
     */
    private Integer enrollStatus;

    /**
     * 是否满员
     */
    private Boolean enrollFullFlag;

    /**
     * 邀请数（新增）
     */
    private Integer newInviteCount;

    /**
     * 邀请数
     */
    private Integer inviteCount;

    /**
     * 报名人员信息（新增）
     */
    private GroupGatherActivityPlanMemberDTO newEnrollInfo;

    /**
     * 报名人数
     */
    private Integer enrollCount;
}
