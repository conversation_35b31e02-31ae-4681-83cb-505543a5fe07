package com.jxntv.gvideo.group.sdk.dto.activity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *  圈子-活动-组件-报名人员列表
 * <AUTHOR>
 * @date 2022/5/13
 */
@Data
public class GroupGatherActivityEnrollMemberExportDTO implements Serializable {
    /**
     * 真实姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 性别 1、男 2、女
     */
    private Integer gender;
    /**
     * 出生年份
     */
    private String year;
    /**
     * 身高（cm）
     */
    private Integer height;
    /**
     * 学历 1、高中 2、中专 3、大专 4、大学本科 5、硕士 6、博士
     */
    private Integer education;
    /**
     * 职业名称
     */
    private String occupationName;
    /**
     * 岗位性质
     */
    private Integer postType;

    /**
     * 婚姻状况 1、未婚 2、离异 3、丧偶
     */
    private Integer marital;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 省/直辖市名称
     */
    private String provinceName;

    /**
     * 地市名称
     */
    private String cityName;

    /**
     * 个人生活照审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer dailyPhotosAudit;

    /**
     * 单位认证状态 0未认证 1认证通过 2认证不通过
     */
    private Integer companyVerifyStatus;

    /**
     * 报名时间
     */
    private Date enrollDate;

    /**
     * 审核状态，0-未审核，1-通过，2-拒绝
     */
    private Integer auditStatus;

    /**
     * 审核时间
     */
    private Date auditDate;

}
