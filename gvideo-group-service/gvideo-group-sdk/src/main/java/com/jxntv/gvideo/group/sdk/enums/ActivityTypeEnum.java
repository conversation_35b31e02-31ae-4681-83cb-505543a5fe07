package com.jxntv.gvideo.group.sdk.enums;

import lombok.Getter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public enum ActivityTypeEnum {

    //是否开启: 0-禁用、1-启用
    HIGH_SCHOOL_LIFE(1, "高中生活"),
    SCHOOL_GRADE_PK(2, "班级PK赛"),
    JUDICIAL_BUREAU(3, "司法厅"),
    HUNDRED_CITIES_AND_NIGHTS(4, "百城百夜"),
    DANCE_PERFORMANCE(5, "舞蹈展演"),
    SQUARE_DANCING(6, "广场舞"),
    VILLAGE_QUEEN(7, "村花村草"),

    ;

    private final Integer code;
    private final String desc;

    ActivityTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityTypeEnum parse(Integer code) {
        for (ActivityTypeEnum value : ActivityTypeEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static List<Map<String, Object>> toList() {
        List<Map<String, Object>> list = new ArrayList<>();
        for (ActivityTypeEnum activityTypeEnum : ActivityTypeEnum.values()) {
            Map<String, Object> map = new HashMap<>();
            map.put("code", activityTypeEnum.getCode());
            map.put("desc", activityTypeEnum.getDesc());
            list.add(map);
        }
        return list;
    }


}
