package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.qa.GroupQaComponentDTO;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/5/12
 * Email: <EMAIL>
 */
public interface GroupQaComponentClient {

    @GetMapping("/api/group/qa/component/{componentId}")
    Result<GroupQaComponentDTO> getByComponentId(@PathVariable("componentId") Long componentId);

    @PutMapping("/api/group/qa/component/{componentId}")
    Result<Long> saveOrUpdateByComponentId(@PathVariable("componentId") Long componentId, @RequestBody GroupQaComponentDTO dto);

}
