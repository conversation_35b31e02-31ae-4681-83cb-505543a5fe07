package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.fallback.GroupTodayTalkClientFallback;
import com.jxntv.gvideo.group.sdk.dto.today.talk.GroupTodayTalkDTO;
import com.jxntv.gvideo.group.sdk.dto.today.talk.GroupTodayTalkFakeVoteDTO;
import com.jxntv.gvideo.group.sdk.params.today.talk.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 圈子-今日聊话题
 *
 * <AUTHOR>
 * @date 2022-10-18 14:56:01
 */
@FeignClient(name = "group-service", contextId = "todayTalk", fallbackFactory = GroupTodayTalkClientFallback.class)
public interface GroupTodayTalkClient {

    String PREFIX = "/api/group/today-talk";

    /**
     * 分页查询列表
     *
     * @param searchDTO 搜索参数列表
     * @return
     */
    @PostMapping(PREFIX + "/page")
    Result<PageDTO<GroupTodayTalkDTO>> page(@RequestBody GroupTodayTalkSearchParam searchDTO);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping(PREFIX + "/{id}")
    Result<GroupTodayTalkDTO> get(@PathVariable("id") Long id);

    /**
     * 通过mediaId查询详情
     *
     * @param mediaId
     * @return
     */
    @GetMapping(PREFIX + "/byMediaId/{mediaId}")
    Result<GroupTodayTalkDTO> getByMediaId(@PathVariable("mediaId") Long mediaId);

    /**
     * 新增话题
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX)
    Result<Boolean> insert(@RequestBody GroupTodayTalkParam dto);

    /**
     * 修改话题
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX)
    Result<Boolean> update(@RequestBody GroupTodayTalkParam dto);

    /**
     * 获取用于投票的投稿列表
     *
     * @param param
     * @return
     */
    @PostMapping(PREFIX + "/vote/contribute/list")
    Result<PageDTO<GroupTodayTalkDTO>> getVoteContributeList(@RequestBody GroupTodayTalkVoteContributeSearchParam param);

    @PostMapping(PREFIX + "/vote")
    Result<Long> vote(@RequestBody GroupTodayTalkVoteParam dto);

    @GetMapping(PREFIX + "/vote/agree")
    Result<Integer> isAgree(@RequestParam Long todayTalkId, @RequestParam Long jid);

    @PostMapping(PREFIX + "/fake-vote")
    Result<Long> fakeVote(@RequestBody GroupTodayTalkFakeVoteParam dto);


    @PostMapping(PREFIX + "/fake-vote/page")
    Result<PageDTO<GroupTodayTalkFakeVoteDTO>> searchFakeVote(@RequestBody GroupTodayTalkFakeVoteSearchParam dto);


}
