package com.jxntv.gvideo.group.sdk.dto.phone.listen;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 电话倾听（隐私号）-绑定日志
 * 
 * <AUTHOR>
 * @date 2023-04-19 09:21:20
 */
@Data
public class GroupPhoneListenBlindLogDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * ID
	 */
	private Long id;
	/**
	 * 隐私号ID
	 */
	private Long secretNoId;

	/**
	 * 隐私号
	 */
	private String secretNo;
	/**
	 * 绑定的的手机号
	 */
	private String phoneNo;
	/**
	 * 绑定的的手机号脱敏
	 */
	private String phoneNoMask;
	/**
	 * 隐私号绑定关系ID
	 */
	private String subId;

	/**
	 * 0-启用中 1-已失效
	 */
	private Integer status;
	/**
	 * 绑定开始时间
	 */
	private Date bindStartTime;
	/**
	 * 绑定失效时间
	 */
	private Date bindEndTime;
	/**
	 * 是否录音 0-不录音 1-录音
	 */
	private Integer recordFlag;
	/**
	 * 创建时间
	 */
	private Date createDate;
	/**
	 * 更新时间
	 */
	private Date updateDate;

}
