package com.jxntv.gvideo.group.sdk.params.liveguide;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 添加圈子直播入口引导配置
 *
 * <AUTHOR>
 * @date 2022/7/18 15:38
 */
@Data
@EqualsAndHashCode
public class AddGroupLiveGuideParam implements Serializable {

    /**
     * 圈子组件配置id
     */
    private Integer gatherId;

    /**
     * 标题
     */
    private String title;

    /**
     * 描述
     */
    private String description;

    /**
     * 背景图片
     */
    private String bgImg;

    /**
     * 预告中直播图
     */
    private String previewBgImg;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 直播开始时间
     */
    private Date liveStartTime;

    /**
     * 资源类型:1:互动,2:活动
     */
    private Integer liveType;

    /**
     * 直播id
     */
    private String liveId;

    /**
     * 用户可见范围类型:用户可见范围类型:1:全部用户可见,2:仅加入社区用户可见,3:仅单位认证可见,4:仅已参加互选活动用户,5:指定用户可见,6:仅报名参加活动用户
     */
    private Integer visibleType;

    /**
     * 当可见范围为4时,该字段有值,逗号分隔
     */
    private String visibleActivityIds;

    /**
     * 提示文案
     */
    private String tips;

    /**
     * 状态：1：开启，2：关闭
     */
    private Integer state;

}
