package com.jxntv.gvideo.group.sdk.dto.blind.date;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/3/17
 */
@Data
public class GroupBlindDateAuditDTO implements Serializable {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 个人生活照审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer dailyPhotosAudit;

    /**
     * 内心独白状态 0未审核 1审核通过 2审核不通过
     */
    private Integer monologueAudit;

    /**
     * 薪水（月收入）审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer salaryAudit;
    /**
     * 房产购置审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer houseAcqAudit;

    /**
     * 车辆购置审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer carAcqAudit;

    /**
     * 真实头像状态 0未审核 1审核通过 2审核不通过
     */
    private Integer realAvatarAudit;

    /**
     * 操作人id
     */
    private Long operateUserId;

    /**
     * 操作人名称
     */
    private String operateUserName;

}
