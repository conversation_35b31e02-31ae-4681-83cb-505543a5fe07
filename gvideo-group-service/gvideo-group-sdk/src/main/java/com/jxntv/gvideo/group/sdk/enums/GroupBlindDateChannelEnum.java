package com.jxntv.gvideo.group.sdk.enums;

/**
 *  都市放心爱-相亲-数据来源渠道
 * <AUTHOR>
 */

public enum GroupBlindDateChannelEnum {
    //数据渠道  0-app 1-单位链接  2-工会链接
    APP (0, "APP"),
    COMPANY(1, "单位"),
    UNION(2, "工会"),
    BLIND_BOX(3, "盲盒链接"),
    PROJECT_985(4, "985高校"),
    OTHER(99, "其他"),

    ;

    private int code;
    private String desc;

    GroupBlindDateChannelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (GroupBlindDateChannelEnum value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
