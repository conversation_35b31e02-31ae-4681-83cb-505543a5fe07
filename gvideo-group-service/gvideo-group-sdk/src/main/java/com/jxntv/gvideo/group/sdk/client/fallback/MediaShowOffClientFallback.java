package com.jxntv.gvideo.group.sdk.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupMediaShowOffClient;
import com.jxntv.gvideo.group.sdk.dto.show.off.GroupMediaShowOffComponentDTO;
import com.jxntv.gvideo.group.sdk.dto.show.off.MediaShowOffDTO;
import com.jxntv.gvideo.group.sdk.dto.show.off.MediaShowOffRelateDTO;
import com.jxntv.gvideo.group.sdk.params.show.off.CheckRelateParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MediaShowOffClientFallback implements FallbackFactory<GroupMediaShowOffClient> {

    @Override
    public GroupMediaShowOffClient create(Throwable throwable) {
        return new GroupMediaShowOffClient() {
            @Override
            public Result<MediaShowOffDTO> getById(Long id) {
                log.error("GroupMediaShowOffClient.getById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupMediaShowOffComponentDTO> getByComponentId(Long componentId) {
                log.error("GroupMediaShowOffClient.getByComponentId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> saveOrUpdateByComponentId(GroupMediaShowOffComponentDTO editDTO) {
                log.error("GroupMediaShowOffClient.saveOrUpdateByComponentId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


            @Override
            public Result<Long> addRelate(MediaShowOffRelateDTO dto) {
                log.error("GroupMediaShowOffClient.addRelate() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaShowOffRelateDTO> getRelateByMediaId(Long mediaId) {
                log.error("GroupMediaShowOffClient.getRelateByMediaId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> hasEnableRelate(CheckRelateParam checkRelateParam) {
                log.error("GroupMediaShowOffClient.hasEnableRelate() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


        };
    }
}
