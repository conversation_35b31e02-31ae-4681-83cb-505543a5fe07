package com.jxntv.gvideo.group.sdk.dto.user.coupon;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户定向发券
 * <AUTHOR>
 * @date 2023/1/30 11:01
 */
@Data
public class UserCouponDTO implements Serializable {
    /**
     * 主键id;主键id
     */
    private Long id;

    private String title;

    /**
     * 社区id
     */
    private Integer groupId;

    /**
     * 圈子名称
     */
    private String groupName;

    /**
     * 今视频用户id
     */
    private String jids;

    /**
     * 今视频昵称
     */
    private String names;

    /**
     * 店铺id
     */
    private String shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 优惠券id
     */
    private String couponId;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券限制日期
     */
    private String couponLimitTime;

    /**
     * 优惠券金额
     */
    private String couponMoney;


    /**
     * 发放时间;定时任务根据发放时间执行发放
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseTime;

    /**
     * 领取状态;1:待领取，2:已领取，3:后续扩展...,100:领取失败，
     */
    private Integer status;

    private String noticeContent;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建用户id
     */
    private Long createUserId;

    /**
     * 创建用户名称
     */
    private String createUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新用户id
     */
    private Long updateUserId;
}
