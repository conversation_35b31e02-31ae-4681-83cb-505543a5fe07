package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.*;
import com.jxntv.gvideo.group.sdk.dto.GroupBrokeNewsComponentDTO;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/5/13
 * Email: <EMAIL>
 */
public interface GroupBrokeNewsComponentClient {

    @PostMapping("/api/group/broke-news/component")
    Result<Long> add(@RequestBody GroupBrokeNewsComponentDTO dto);

    @PutMapping("/api/group/broke-news/component/{componentId}")
    Result<Boolean> updateByComponentId(@PathVariable("componentId") Long componentId, @RequestBody GroupBrokeNewsComponentDTO dto);

    @GetMapping("/api/group/broke-news/component/{componentId}")
    Result<GroupBrokeNewsComponentDTO> getByComponentId(@PathVariable("componentId") Long componentId);
}
