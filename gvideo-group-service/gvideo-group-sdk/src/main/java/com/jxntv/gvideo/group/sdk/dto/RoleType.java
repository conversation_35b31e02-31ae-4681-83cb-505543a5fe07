package com.jxntv.gvideo.group.sdk.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> Created on 2021/4/29.
 */
@Getter
public enum RoleType {
    NORMAL(1, "普通用户"),
    MANAGE(2, "管理员"),
    SUPER_MANAGE(4, "超级管理员"),
    OWNER(5, "圈主");

    @EnumValue
    private int code;
    private String value;

    RoleType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static RoleType parse(Integer code) {
        if (code == null) {
            return null;
        }
        Optional<RoleType> first = Arrays.stream(values()).filter(applyMode -> applyMode.code == code).findFirst();
        return first.orElse(null);
    }

    public boolean admin() {
        return Objects.equals(this, RoleType.SUPER_MANAGE);
    }
}
