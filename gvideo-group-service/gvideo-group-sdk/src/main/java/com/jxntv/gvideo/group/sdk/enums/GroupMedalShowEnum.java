package com.jxntv.gvideo.group.sdk.enums;

/**
 * <AUTHOR>
 * @create 2022/4/26 12:55 下午
 */
public enum GroupMedalShowEnum {
    Hide(0, "不显示"),
    Show(1, "显示"),
    ;

    private Integer code;
    private String desc;

    GroupMedalShowEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (GroupMedalShowEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
