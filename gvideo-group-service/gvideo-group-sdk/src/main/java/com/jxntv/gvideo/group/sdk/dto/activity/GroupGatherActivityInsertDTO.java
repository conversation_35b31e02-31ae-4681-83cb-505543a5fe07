package com.jxntv.gvideo.group.sdk.dto.activity;

import lombok.Data;

/**
 * 圈子(社区)-组件-活动 新增
 *
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class GroupGatherActivityInsertDTO extends GroupGatherActivityOperationBaseDTO {

    /**
     * 活动关联圈子ID
     */
    private Long groupId;
    /**
     * 活动关联圈子组件ID
     */
    private Long groupGatherId;
    /**
     * 推送标记：0-不推送，1-推送
     */
    private Integer pushFlag;
    /**
     * 推送内容
     */
    private String pushContent;
    /**
     * 创建用户id
     */
    private Long createUserId;
    /**
     * 创建用户账号
     */
    private String createUserName;

    /**
     * 活动参与消耗红豆标识 0-免费 1-收费
     */
    private Integer redBeansFlag;
    /**
     * 活动参与耗红豆数
     */
    private Integer redBeansAmount;

    /**
     * 活动类型  0-普通活动 1-约会计划
     */
    private Integer type;


}
