package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.FeedDTO;
import com.jxntv.gvideo.group.sdk.dto.*;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.params.group.QueryGroupMemberParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> Created on 2021/5/11.
 */
public interface GroupManageClient {
    String PREFIX = "/api/group/manage";

    /**
     * 圈子人员列表
     *
     * @return
     */
    @PostMapping(PREFIX + "/page")
    Result<PageDTO<GroupUserDTO>> page(@RequestBody GroupUserSearchDTO searchDTO);

    /**
     * 添加人员
     *
     * @param addDTO
     * @return
     */
    @PostMapping(PREFIX)
    Result<Long> addManage(@RequestBody GroupManageAddDTO addDTO);

    /**
     * 角色变更
     *
     * @param id
     * @param roleType
     * @param groupId
     * @param logDTO
     * @return
     */
    @PutMapping(PREFIX + "/{id}")
    Result<Void> roleModify(@PathVariable Long id, @RequestParam RoleType roleType, @RequestParam Long groupId, @RequestBody GroupLogDTO logDTO);

    /**
     * 移除超管
     *
     * @param id
     * @return
     */
    @DeleteMapping(PREFIX)
    Result<Void> remove(@RequestParam Long id);

    /**
     * 条件查询
     *
     * @param userId
     * @return
     */
    @GetMapping(PREFIX + "/condition")
    Result<GroupUserDTO> queryByCondition(@RequestParam Long userId, @RequestParam Long groupId);

    /**
     * fixme 过期接口下个版本剔除,当前版本release-20230310
     * 条件查询
     *
     * @return
     */
    @Deprecated
    @PostMapping(PREFIX + "/ugc-list")
    Result<List<Long>> listUgcByGroupId(@RequestBody List<Long> groupIdList);


    /**
     * 查询社区成员ID列表
     *
     * @param param 查询参数
     * @return
     */
    @PostMapping(PREFIX + "/member-ids")
    Result<PageDTO<GroupManageDTO>> queryGroupMemberIds(@RequestBody QueryGroupMemberParam param);


    /**
     * 条件查询
     *
     * @param id
     * @return
     */
    @GetMapping(PREFIX + "/id")
    Result<GroupUserDTO> queryById(@RequestParam Long id);

    /**
     * 拉黑、回复用户
     *
     * @return
     */
    @PutMapping(PREFIX + "/blacklist")
    Result<Void> blacklist(@RequestBody GroupBlacklistDTO dto);

    /**
     * 恢复黑名单
     *
     * @param groupId
     * @param jid
     * @param type
     * @return
     */
    @PutMapping(PREFIX + "/blacklist/restore")
    Result<Void> restore(@RequestParam Long groupId, @RequestParam Long jid, @RequestParam Integer type);

    /**
     * 用户是否拥有圈子管理权限
     *
     * @param jid
     * @param groupId
     * @return
     */
    @GetMapping(PREFIX + "/app/permission")
    Result<Void> appManagePermission(@RequestParam Long jid, @RequestParam Long groupId);

    /**
     * 红人列表
     *
     * @param groupId
     * @param pageNum
     * @return
     */
    @GetMapping(PREFIX + "/app/favorite/page")
    Result<PageDTO<GroupUserDTO>> favorite(@RequestParam Long groupId, @RequestParam Integer pageNum, @RequestParam(defaultValue = "10", required = false) Integer pageSize);

    /**
     * 社区成员列表
     *
     * @param searchDTO 分页大小
     * @return 社区成员信息流
     */
    @PostMapping(PREFIX + "/user/feed")
    Result<FeedDTO<GroupUserDTO>> favoriteFeed(@RequestBody GroupUserFeedSearchDTO searchDTO);

    /**
     * 定时调度处理禁言用户
     *
     * @return
     */
    @PutMapping(PREFIX + "/job/mute")
    Result<Void> dealMuteUser();

    @GetMapping(PREFIX + "/join/group")
    Result<List<Long>> queryJoinGroup(@RequestParam Long jid);

    /**
     * 退出所有的社区
     *
     * @param jid 用户id
     * @return
     */
    @DeleteMapping(PREFIX + "/exit/all")
    Result<Void> exitAllByJid(@RequestParam Long jid);


    /**
     * 获取社区拥有者
     *
     * @param groupId 社区id
     * @return 社区管理者
     */
    @GetMapping("/api/group/{groupId}/manage/owners")
    Result<List<GroupUserDTO>> getOwners(@PathVariable Long groupId);

    /**
     * 获取社区用户总数
     *
     * @param groupId 社区id
     * @return 社区用户总数
     */
    @GetMapping("/api/group/{groupId}/manage/count")
    Result<Integer> getUserCount(@PathVariable Long groupId);

    /**
     * 增加社区贡献值
     *
     * @param groupId 社区id
     * @param userId  用户Id
     */
    @GetMapping("/api/group/{groupId}/manage/{userId}/score")
    Result<Boolean> addScore(@PathVariable Long groupId, @PathVariable("userId") Long userId);


}
