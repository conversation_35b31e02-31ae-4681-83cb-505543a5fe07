package com.jxntv.gvideo.group.sdk.enums;

/**
 * <AUTHOR>
 * @create 2022/4/25 11:36 下午
 */
public enum GroupRedNameOperationEnum {

    RedName(1, "红名"),
    Cancel(2, "取消红名"),
    ;

    private Integer code;
    private String desc;

    GroupRedNameOperationEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (GroupRedNameOperationEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
