package com.jxntv.gvideo.group.sdk.enums;

/**
 *  都市放心爱-招呼-阅知状态
 * <AUTHOR>
 */

public enum GroupBlindDateVisitorTypeEnum {
    /**
     * 状态 0-未阅知 1-已阅知
     */
    UNKNOWN(0, "未知"),
    DIFFERENT(1, "异性"),
    SAME(2, "同性"),
    ;

    private int code;
    private String desc;

    GroupBlindDateVisitorTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (GroupBlindDateVisitorTypeEnum value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
