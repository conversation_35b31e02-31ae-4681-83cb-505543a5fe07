package com.jxntv.gvideo.group.sdk.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/03/17 11:21
 */
@Data
public class GroupUserReportDTO implements Serializable {

    /**
     * 主键id;主键id
     */
    private Long id;
    /**
     * 举报类型
     */
    private String type;
    /**
     * 举报用户id
     */
    private Long reportUserId;
    /**
     * 举报用户昵称
     */
    private String reportUserNickname;
    /**
     * 举报用户手机号
     */
    private String reportUserPhone;
    /**
     * 被举报用户id
     */
    private Long reportedUserId;
    /**
     * 被举报用户昵称
     */
    private String reportedUserNickname;
    /**
     * 图片ID列表
     */
    private List<String> reportImages;
    /**
     * 来源
     */
    private String source;
    /**
     * 举报内容
     */
    private String reportContent;
    /**
     * 举报处理结果
     */
    private String reportProcessResult;
    /**
     * 处理状态 0 未处理 1 已处理 2 驳回
     */
    private Integer processStatus;
    /**
     * 举报时间
     */
    private LocalDateTime reportDate;
    /**
     * 禁言时间
     */
    private LocalDateTime noSpeakingDate;
}
