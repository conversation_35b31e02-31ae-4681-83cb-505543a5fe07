package com.jxntv.gvideo.group.sdk.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class GroupContentFeedSearchDTO extends SearchDTO {
    /**
     * 社区id
     */
    private Long groupId;
    /**
     * 需要隐藏的话题列表
     */
    private List<Long> hideTopicIds;

    private Integer mediaStatus;

    /**
     * 数据平台
     */
    private Integer platform;

}
