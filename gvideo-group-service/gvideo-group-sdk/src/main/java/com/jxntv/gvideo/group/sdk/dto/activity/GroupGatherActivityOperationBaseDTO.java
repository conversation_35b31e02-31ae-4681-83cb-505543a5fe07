package com.jxntv.gvideo.group.sdk.dto.activity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *  圈子(社区)-组件-活动 信息操作基础类
 * <AUTHOR>
 * @date 2022/3/16
 */
@Data
public class GroupGatherActivityOperationBaseDTO {
    /**
     * 活动主题
     */
    private String title;
    /**
     * 活动开始时间
     */
    private String startDate;
    /**
     * 活动结束时间
     */
    private String endDate;
    /**
     * 活动报名开始时间
     */
    private String joinStartDate;
    /**
     * 活动报名结束时间
     */
    private String joinEndDate;
    /**
     * 活动地址
     */
    private String address;
    /**
     * 活动参与费用标识 0-免费 1-收费
     */
    private Integer costFlag;
    /**
     * 活动参与费用
     */
    private BigDecimal cost;
    /**
     * 活动参与人数起始
     */
    private Integer memberFrom;
    /**
     * 活动参与人数截止
     */
    private Integer memberTo;
    /**
     * 活动简介
     */
    private String introduction;

    /**
     *  权重
     */
    private Integer weight;

    /**
     * 活动海报ossId
     */
    private List<String> imageList;

    /**
     * 活动标签id
     */
    private Long tagId;

    /**
     * 活动标签名称
     */
    private String tagName;

    /**
     * 经度
     */
    private Double longitude;
    /**
     * 纬度
     */
    private Double latitude;

}
