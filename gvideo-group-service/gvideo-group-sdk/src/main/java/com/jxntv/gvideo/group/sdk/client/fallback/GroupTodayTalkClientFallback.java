package com.jxntv.gvideo.group.sdk.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupTodayTalkClient;
import com.jxntv.gvideo.group.sdk.dto.today.talk.GroupTodayTalkDTO;
import com.jxntv.gvideo.group.sdk.dto.today.talk.GroupTodayTalkFakeVoteDTO;
import com.jxntv.gvideo.group.sdk.params.today.talk.*;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupTodayTalkClientFallback implements FallbackFactory<GroupTodayTalkClient> {

    @Override
    public GroupTodayTalkClient create(Throwable throwable) {
        return new GroupTodayTalkClient() {
            @Override
            public Result<PageDTO<GroupTodayTalkDTO>> page(GroupTodayTalkSearchParam searchDTO) {
                log.error("GroupTodayTalkClient.page() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupTodayTalkDTO> get(Long id) {
                log.error("GroupTodayTalkClient.get() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupTodayTalkDTO> getByMediaId(Long mediaId) {
                log.error("GroupTodayTalkClient.getByMediaId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> insert(GroupTodayTalkParam dto) {
                log.error("GroupTodayTalkClient.get() insert", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> update(GroupTodayTalkParam dto) {
                log.error("GroupTodayTalkClient.update() ", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<GroupTodayTalkDTO>> getVoteContributeList(GroupTodayTalkVoteContributeSearchParam param) {
                log.error("GroupTodayTalkClient.getVoteContributeList() ", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> vote(GroupTodayTalkVoteParam dto) {
                log.error("GroupTodayTalkClient.addVote() ", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> isAgree(Long todayTalkId, Long jid) {
                log.error("GroupTodayTalkClient.isAgree() ", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> fakeVote(GroupTodayTalkFakeVoteParam dto) {
                log.error("GroupTodayTalkClient.addFakeVote() ", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<GroupTodayTalkFakeVoteDTO>> searchFakeVote(GroupTodayTalkFakeVoteSearchParam dto) {
                log.error("GroupTodayTalkClient.get()", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

        };
    }
}
