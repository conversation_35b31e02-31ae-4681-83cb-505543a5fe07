package com.jxntv.gvideo.group.sdk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: niedamin
 * @Date: 2022/08/08 17:44
 */
@Data
public class GroupActivityNoticeDTO {
    /**
     * 主键id
     */
    private Long id;

    private String name;

    /**
     * 圈子ID
     */
    private Long groupId;

    /**
     * 组件ID
     */
    private Long gatherId;

    private String imageId;

    private String imageUrl;

    private String activityIntroduction;

    private Integer weight;

    /**
     * 0-原生，1-H5链接
     */
    private Integer jumpType;

    private Integer contentType;

    private Long contentId;

    private String contentName;

    /**
     * type=0,原生链接，比如：jinship:xxx
     * type=1,H5链接，比如：https:www.baiduc.om
     */
    private String linkUrl;

    private Integer status;

    private String createUserName;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime createDate;

    /**
     * 更新人
     */
    private Long updateUserId;

    private String updateUserName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime updateDate;
}
