package com.jxntv.gvideo.group.sdk.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR> Created on 2021/4/29.
 */
@Getter
public enum GroupSortType {
    NEWEST(0, "默认最新排序"),
    RECOMMEND(1, "默认推荐排序")
    ;
    @EnumValue
    private int code;
    private String value;

    GroupSortType(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static GroupSortType parse(Integer code) {
        if (code == null) {
            return null;
        }
        Optional<GroupSortType> first = Arrays.stream(values()).filter(applyMode -> applyMode.code == code).findFirst();
        return first.orElse(null);
    }
}
