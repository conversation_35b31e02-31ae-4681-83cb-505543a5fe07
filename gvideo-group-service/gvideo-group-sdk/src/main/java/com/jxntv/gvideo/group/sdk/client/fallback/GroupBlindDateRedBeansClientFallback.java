package com.jxntv.gvideo.group.sdk.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupBlindDateRedBeansClient;
import com.jxntv.gvideo.group.sdk.dto.blind.date.redBeans.*;
import com.jxntv.gvideo.group.sdk.params.blind.date.redBeans.GroupBlindDateRedBeansLogSearchParam;
import com.jxntv.gvideo.group.sdk.params.blind.date.redBeans.GroupBlindDateUserRedBeansSearchParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupBlindDateRedBeansClientFallback implements FallbackFactory<GroupBlindDateRedBeansClient> {

    @Override
    public GroupBlindDateRedBeansClient create(Throwable throwable) {
        return new GroupBlindDateRedBeansClient() {

            @Override
            public Result<GroupBlindDateRedBeansDTO> queryByJid(Long jid) {
                log.error("GroupBlindDateRedBeansClient.queryByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupBlindDateRedBeansLogPageDTO> pageLog(GroupBlindDateRedBeansLogSearchParam param) {
                log.error("GroupBlindDateRedBeansClient.pageLog() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updatePopStatus(GroupBlindDateRedBeansPopStatusDTO dto) {
                log.error("GroupBlindDateRedBeansClient.updatePopStatus() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<GroupBlindDateRedBeansPopInitInfoDTO>> queryInitInfoList(Long jid) {
                log.error("GroupBlindDateRedBeansClient.queryInitInfoList() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<GroupBlindDateUserRedBeansDTO>> pageUser(GroupBlindDateUserRedBeansSearchParam param) {
                log.error("GroupBlindDateRedBeansClient.pageUser() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> saveRedBeanAdjust(GroupBlindDateRedBeansAdjustDTO dto) {
                log.error("GroupBlindDateRedBeansClient.saveRedBeanAdjust() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
