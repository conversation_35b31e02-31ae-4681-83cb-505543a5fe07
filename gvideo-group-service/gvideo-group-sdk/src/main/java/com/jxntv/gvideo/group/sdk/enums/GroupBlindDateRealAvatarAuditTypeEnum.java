package com.jxntv.gvideo.group.sdk.enums;

/**
 *  都市放心爱-真实头像-审核类型
 * <AUTHOR>
 */

public enum GroupBlindDateRealAvatarAuditTypeEnum {
    AI_AUDIT_PASS(1, "ai审核通过"),
    AI_AUDIT_FAIL(2, "ai审核失败"),
    MANUAL_AUDIT_PASS(3, "人工审核通过"),
    MANUAL_AUDIT_FAIL(4, "人工审核失败"),
    ;

    private Integer code;
    private String desc;


    GroupBlindDateRealAvatarAuditTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (GroupBlindDateRealAvatarAuditTypeEnum value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
