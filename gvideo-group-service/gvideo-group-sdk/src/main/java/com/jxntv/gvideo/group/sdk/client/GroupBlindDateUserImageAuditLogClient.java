package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.fallback.GroupBlindDateUserImageAuditLogClientFallback;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateUserImageAuditLogDTO;
import com.jxntv.gvideo.group.sdk.params.blind.date.GroupBlindDateImageAuditLogSearchParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 圈子-都是放心爱-访客Client
 *
 * <AUTHOR>
 * @date 2022-06-08 11:39:48
 */
@FeignClient(name = "group-service", contextId = "imageAuditLog", fallbackFactory = GroupBlindDateUserImageAuditLogClientFallback.class)
public interface GroupBlindDateUserImageAuditLogClient {

    String PREFIX = "/api/group/blind-date/image/audit-log/";

    /**
     * 查询列表
     *
     * @param param
     * @return
     */
    @PostMapping(PREFIX + "/list")
    Result<List<GroupBlindDateUserImageAuditLogDTO>> list(@RequestBody GroupBlindDateImageAuditLogSearchParam param);

}
