package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.GroupGatherDTO;
import com.jxntv.gvideo.group.sdk.dto.SchoolDetailDTO;
import com.jxntv.gvideo.group.sdk.dto.SchoolGradePKPageDTO;
import com.jxntv.gvideo.group.sdk.dto.SchoolGradeStuDTO;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-06-11 10:40
 * @description
 */
public interface SchoolGradePkClient {

    String PREFIX = "/api/group/school-grade-pk/";

    /**
     * 查询学校的校区年级班级等信息
     *
     * @param schoolCode 学校id
     * @return
     */
    @GetMapping(PREFIX + "school/schoolDetail")
    Result<SchoolDetailDTO> querySchoolDetailBySchoolCode(@RequestParam Long schoolCode);

    /**
     * 查询学生基本信息
     *
     * @param stuID
     * @return
     */
    @GetMapping(PREFIX + "stu/info")
    Result<SchoolGradeStuDTO> queryStudentBaseInfoById(@RequestParam Long stuID);

    /**
     * 根据jid查询学生基本信息
     *
     * @param jid
     * @return
     */
    @GetMapping(PREFIX + "stu/infoByJid")
    Result<SchoolGradeStuDTO> queryStudentBaseInfoByJid(@RequestParam Long jid);

    /**
     * 查询学生详细信息
     *
     * @param stuID
     * @return
     */
    @GetMapping(PREFIX + "stu/detail")
    Result<SchoolGradeStuDTO> queryStudentDetailById(@RequestParam Long stuID);

    /**
     * 报名入口
     *
     * @param dto
     * @return
     */
    @PostMapping(PREFIX + "stu/enroll")
    Result<Boolean> saveSchoolGradeStu(@RequestBody SchoolGradeStuDTO dto);

    /**
     * 修改的学生个人信息，学校是无法修改的
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX + "stu/modify")
    Result<Boolean> updateStudent(@RequestBody SchoolGradeStuDTO dto);

    /**
     * 修改的学生头像
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX + "stu/avatarModify")
    Result<Boolean> updateStudentAvatar(@RequestBody SchoolGradeStuDTO dto);

    /**
     * 查询班内的学生排名情况，班级范围内
     *
     * @param stuID
     * @return
     */
    @GetMapping(PREFIX + "stu/myRankPage")
    Result<PageDTO<SchoolGradeStuDTO>> pageMyRankInClass(@RequestParam Long stuID,
                                                         @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                         @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize);

    /**
     * 查询年级的学生排名情况，学校年级范围内
     *
     * @param stuID
     * @return
     */
    @GetMapping(PREFIX + "stu/schoolRankPage")
    Result<PageDTO<SchoolGradeStuDTO>> pageSchoolRankInGrade(@RequestParam Long stuID,
                                                             @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                             @RequestParam(value = "pageSize", required = false, defaultValue = "20") Integer pageSize);

    /**
     * 查询校内的班级排名情况，班级维度。非必传，，默认查班级分
     *
     * @param stuID
     * @return
     */
    @GetMapping(PREFIX + "class/classRankPage")
    Result<SchoolGradePKPageDTO> pageClassRankInSchool(@RequestParam(required = false) Long stuID,
                                                       @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                       @RequestParam(required = false, defaultValue = "20") Integer pageSize);

    /**
     * 查询学生班级动态排名情况
     *
     * @param jid
     * @return
     */
    @GetMapping(PREFIX + "stu/classStatePage")
    Result<SchoolGradePKPageDTO> queryPageClassState(@RequestParam Long jid,
                                                     @RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                     @RequestParam(required = false, defaultValue = "20") Integer pageSize);


    /**
     * 查询班级pk圈子id
     *
     * @param
     * @return
     */
    @GetMapping(PREFIX + "groupId")
    Result<GroupGatherDTO> querySchoolGradePkGroupId();

    /**
     * 修改的学生个人昵称
     *
     * @param dto
     * @return
     */
    @PutMapping(PREFIX + "stu/modifyName")
    Result<Boolean> updateStudentName(@RequestBody SchoolGradeStuDTO dto);

}
