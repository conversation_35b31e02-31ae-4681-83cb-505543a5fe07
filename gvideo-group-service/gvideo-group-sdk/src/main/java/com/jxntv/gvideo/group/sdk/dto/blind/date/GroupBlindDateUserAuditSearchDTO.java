package com.jxntv.gvideo.group.sdk.dto.blind.date;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/3/16
 * Email: <EMAIL>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupBlindDateUserAuditSearchDTO extends SearchDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 真实姓名
     */
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别 1、男 2、女
     */
    private Integer gender;

    /**
     * 学历 1、高中 2、中专 3、大专 4、大学本科 5、硕士 6、博士
     */
    private Integer education;

    /**
     * 婚姻状况 1、未婚 2、离异 3、丧偶
     */
    private Integer marital;

    /**
     * 单位ID
     */
    private Long companyId;

    /**
     * 单位名称
     */
    private String companyName;

    /**
     * 审核状态 0未审核 1审核通过 2审核不通过
     */
    private Integer auditStatus;

    /**
     * 单位认证状态 0未认证 1认证通过 2认证不通过
     */
    private Integer companyVerifyStatus;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 单位地区
     */
    private String districtName;

    /**
     * 城市ID
     */
    private Long cityId;

    /**
     * 工会ID
     */
    private Long unionId;

    /**
     * 数据渠道  0-app 1-单位链接  2-工会链接 3-盲盒 4-985高校
     */
    private Integer channel;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 科协类型 0-科协未认证 1-科协已认证
     */
    private Integer astType;
}
