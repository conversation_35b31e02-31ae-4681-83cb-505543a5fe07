package com.jxntv.gvideo.group.sdk.dto.blind.date.love;

import lombok.Data;

import java.io.Serializable;

/**
 *  圈子-都是放心爱-相亲-活动-活动配对信息
 * <AUTHOR>
 * @date 2022/5/13
 */
@Data
public class GroupBlindDateLoveSecondaryExportDTO implements Serializable {

    /**
     * 复选配对序号
     */
    private Integer secondaryPairedSerialNo;

    /**
     * 嘉宾编号
     */
    private Integer guestSerialNo;

    /**
     * 性别
     */
    private String gender;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 姓名
     */
    private String name;

    /**
     * 电话号码
     */
    private String mobile;

    /**
     * 一选
     */
    private String primaries;

    /**
     * 复选
     */
    private String secondary;
}
