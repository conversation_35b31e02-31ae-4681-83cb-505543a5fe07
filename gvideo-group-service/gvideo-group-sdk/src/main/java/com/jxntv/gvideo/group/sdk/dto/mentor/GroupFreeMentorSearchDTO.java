package com.jxntv.gvideo.group.sdk.dto.mentor;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class GroupFreeMentorSearchDTO extends SearchDTO {

    private Long groupId;

    private Long componentId;

    private Long mentorJid;

    private List<Long> operator;

    private LocalDateTime startTime;

    private LocalDateTime endTime;
}
