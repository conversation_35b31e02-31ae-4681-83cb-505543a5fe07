package com.jxntv.gvideo.group.sdk.dto.liveguide;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 *  直播选项列表
 *  <AUTHOR>
 *  @date 2022/7/18 16:27
 */
@Data
@EqualsAndHashCode
public class LiveOptionsDTO implements Serializable {

    /**
     * 直播Id
     */
    private Long id;

    /**
     * 直播类型：1：互动直播，2：活动直播
     */
    private Integer mediaType;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面
     */
    private String thumb;

}
