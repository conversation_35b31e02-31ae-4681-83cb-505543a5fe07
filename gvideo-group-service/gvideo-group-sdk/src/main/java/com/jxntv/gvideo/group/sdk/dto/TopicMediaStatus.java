package com.jxntv.gvideo.group.sdk.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR> Created on 2021/4/29.
 */
@Getter
public enum TopicMediaStatus {
    UN_AUDIT(0, "待审核"),
    ENABLE(1, "启用"),
    DISABLE(2, "禁用"),
    TOP(3, "置顶"),
    VALUABLE(4, "精华"),
    CANCEL_TOP(5, "取消置顶"),
    CANCEL_VALUABLE(6, "取消精华"),
    AUHTOR_VALUABLE(7, "仅作者可见"),
    ALL_VISIBLE(8, "全部可见"),
            ;
    @EnumValue
    private int code;
    private String value;

    TopicMediaStatus(int code, String value) {
        this.code = code;
        this.value = value;
    }

    public static TopicMediaStatus parse(Integer code) {
        if (code == null) {
            return null;
        }
        Optional<TopicMediaStatus> first = Arrays.stream(values()).filter(applyMode -> applyMode.code == code).findFirst();
        return first.orElse(null);
    }
}
