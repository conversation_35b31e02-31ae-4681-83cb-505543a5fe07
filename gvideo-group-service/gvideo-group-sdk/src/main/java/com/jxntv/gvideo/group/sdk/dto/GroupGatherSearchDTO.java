package com.jxntv.gvideo.group.sdk.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 圈子组件搜索条件
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GroupGatherSearchDTO extends SearchDTO {
    /**
     * 圈子ID
     */
    private Long groupId;
    /**
     * 组件名称
     */
    private String name;
    /**
     * 类型：1-话题合集、2-爆料、3-问答、4-广播、5-节目合集、6-弹窗
     */
    private Integer type;
    /**
     * 状态：0-禁用、1-启用
     */
    private Integer status;
}
