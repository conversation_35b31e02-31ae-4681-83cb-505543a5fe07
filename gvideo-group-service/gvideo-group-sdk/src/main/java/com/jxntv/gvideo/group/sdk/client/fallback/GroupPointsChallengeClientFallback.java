package com.jxntv.gvideo.group.sdk.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupPointsChallengeClient;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeEnrollDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeRankResultDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupPointsChallengeSearchRankDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class GroupPointsChallengeClientFallback implements FallbackFactory<GroupPointsChallengeClient> {

    @Override
    public GroupPointsChallengeClient create(Throwable throwable) {
        return new GroupPointsChallengeClient() {
            @Override
            public Result<Boolean> saveEnrollInfo(GroupPointsChallengeEnrollDTO dto) {
                log.error("GroupPointsChallengeClient.saveEnrollInfo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupPointsChallengeInfoDTO> queryInfo(Long groupId, Long jid) {
                log.error("GroupPointsChallengeClient.queryInfo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupPointsChallengeRankResultDTO> search(GroupPointsChallengeSearchRankDTO searchDTO) {
                log.error("GroupPointsChallengeClient.search() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
