package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.GroupActivityNoticeDTO;
import com.jxntv.gvideo.group.sdk.params.GroupActivityNoticeParam;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2022/08/08 17:41
 */
public interface GroupActivityNoticeClient {

    @PostMapping("/api/group/activity-notice/page")
    Result<PageDTO<GroupActivityNoticeDTO>> page(@RequestBody GroupActivityNoticeParam groupActivityNoticeParam);

    @PostMapping("/api/group/activity-notice")
    Result<Boolean> add(@RequestBody GroupActivityNoticeDTO groupActivityNoticeDTO);

    @PutMapping("/api/group/activity-notice")
    Result<Boolean> update(@RequestBody GroupActivityNoticeDTO groupActivityNoticeDTO);

    @GetMapping("/api/group/activity-notice")
    Result<List<GroupActivityNoticeDTO>> getByGroupIdAndGatherId(@RequestParam Long groupId, @RequestParam Long gatherId);

    @GetMapping("/api/group/activity-notice/{id}")
    Result<GroupActivityNoticeDTO> getById(@PathVariable("id") Long id);

}
