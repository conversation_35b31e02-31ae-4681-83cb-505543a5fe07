package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.fallback.GroupIconClientFallback;
import com.jxntv.gvideo.group.sdk.dto.GroupIconDTO;
import com.jxntv.gvideo.group.sdk.params.icon.GroupIconEditParam;
import com.jxntv.gvideo.group.sdk.params.icon.GroupIconSearchParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2022/12/05 11:08
 */
@FeignClient(name = "group-service", contextId = "GroupIcon", fallbackFactory = GroupIconClientFallback.class)
public interface GroupIconClient {

    String PREFIX = "/api/group/icon";

    @PostMapping(PREFIX)
    Result<Long> add(@RequestBody GroupIconEditParam editParam);

    @PutMapping(PREFIX + "/{id}")
    Result<Long> modify(@PathVariable Long id, @RequestBody GroupIconEditParam editParam);

    @DeleteMapping(PREFIX + "/{id}")
    Result<Long> delete(@PathVariable Long id);

    @GetMapping(PREFIX + "/{id}")
    Result<GroupIconDTO> get(@PathVariable Long id);

    @PostMapping(PREFIX + "/page")
    Result<PageDTO<GroupIconDTO>> page(@RequestBody GroupIconSearchParam searchParam);

    @PostMapping(PREFIX + "/publish")
    Result<Void> publish(@RequestBody List<Long> ids);

    @GetMapping(PREFIX + "/{id}/publish/list")
    Result<List<GroupIconDTO>> getPublishList(@PathVariable("id") Long componentId);
}
