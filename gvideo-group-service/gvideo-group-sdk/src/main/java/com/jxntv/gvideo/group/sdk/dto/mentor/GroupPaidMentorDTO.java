package com.jxntv.gvideo.group.sdk.dto.mentor;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 社区付费导师
 * <AUTHOR>
 * @date 2022/7/4
 * Email: <EMAIL>
 */
@Data
public class GroupPaidMentorDTO implements Serializable {


    private Long id;
    /**
     * 导师jid
     */
    private Long jid;
    /**
     * 关联社区ID
     */
    private Long groupId;
    /**
     * 组件 id
     */
    private Long componentId;
    /**
     * 权重
     */
    private Integer weights;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 折扣价
     */
    private BigDecimal discountPrice;
    /**
     * 导师擅长领域
     */
    private List<String> areasOfExpertise;

    /**
     * 创建人ID
     */
    private Long createUserId;
    /**
     * 更新人ID
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
