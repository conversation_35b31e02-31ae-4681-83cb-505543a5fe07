package com.jxntv.gvideo.group.sdk.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotDTO;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotSearchDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/01/29 16:17
 */
@FeignClient(name = "group-service", contextId = "red-dot")
public interface AppRedDotClient {

    @PostMapping("/api/red-dot")
    Result<Void> create(@RequestBody AppRedDotDTO dto);

    @PutMapping("/api/red-dot")
    Result<Void> modify(@RequestBody AppRedDotDTO dto);

    @PostMapping("/api/red-dot/query")
    Result<List<AppRedDotDTO>> query(@RequestBody AppRedDotSearchDTO searchDTO);
}
