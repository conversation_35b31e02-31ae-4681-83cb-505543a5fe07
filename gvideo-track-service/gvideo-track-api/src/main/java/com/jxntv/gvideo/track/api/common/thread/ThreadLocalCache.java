package com.jxntv.gvideo.track.api.common.thread;

/**
 * Created on 2020-02-13
 * <AUTHOR>
 */
public class ThreadLocalCache {

    private final static ThreadLocal<TrackBaseInfo> TRACK_THREAD_LOCAL = new ThreadLocal<>();

    public static void add(TrackBaseInfo trackBaseInfo) {
        TRACK_THREAD_LOCAL.set(trackBaseInfo);
    }

    public static TrackBaseInfo get() {
        return TRACK_THREAD_LOCAL.get();
    }

    public static void remove() {
        TRACK_THREAD_LOCAL.remove();
    }

}
