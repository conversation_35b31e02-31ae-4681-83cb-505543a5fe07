package com.jxntv.gvideo.track.sdk.enums;

import lombok.Getter;

/**
 *  埋点上报头信息枚举
 * <AUTHOR>
 */

@Getter
public enum TrackHeaderEnum {

    //
    DEVICE("device", "设备ID"),
    OS("os", "操作系统"),
    OS_VERSION("osVersion", "操作系统版本"),
    APP_VERSION("appVersion", "app版本"),
    BRAND("brand", "设备品牌"),
    MODEL("model", "设备型号"),
    TIME("time", "事件发生时间"),
    IS_WIFI("wifi", "是否为wifi"),
    IP("ip", "ip"),
    SCREEN_ORIENTATION("screen-orientation", "屏幕方向"),
    NETWORK_TYPE("network-type", "网络类型"),
    IS_FIRST_DAY("is-first-day", "是否首次访问"),
    CARRIER("carrier", "运营商"),
    LONGITUDE("longitude", "经度"),
    LATITUDE("latitude", "纬度"),
    USER_AGENT("User-Agent", "引擎"),
    SESSION_ID("session-id", "session_id"),
    PLATFORM("platform", "数据来源 app 、 applet"),

    ;

    private final String code;
    private final String desc;

    TrackHeaderEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TrackHeaderEnum parse(String code) {
        for (TrackHeaderEnum value : TrackHeaderEnum.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }


}
