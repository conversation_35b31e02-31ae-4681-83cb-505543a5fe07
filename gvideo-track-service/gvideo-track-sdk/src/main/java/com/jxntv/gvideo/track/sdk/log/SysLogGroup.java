package com.jxntv.gvideo.track.sdk.log;

import lombok.Getter;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 日志类型
 */
@Getter
public enum SysLogGroup {

    CONTENT_RESOURCE("CONTENT_RESOURCE", "内容资源管理"),
    UGC_RESOURCE("UGC_RESOURCE", "UGC内容管理"),
    OFFICIAL_IM("OFFICIAL_IM", "官方私信"),
    KING_KONG_POSITION("KING_KONG_POSITION", "金刚位管理"),
    OPERATION_MATERIAL("OPERATION_MATERIAL", "运营素材管理"),
    OPEN_ADVERT("OPEN_ADVERT", "开屏广告管理"),
    GROUP("GROUP", "圈子管理"),
    BANNER_POP("BANNER_POP", "banner弹窗"),
    COLD_START("COLD_START", "冷启动管理"),
    LIVE_BROADCAST_COMMENT("LIVE_BROADCAST_COMMENT", "活动直播评论"),
    COMMENT_AUDIT("COMMENT_AUDIT", "评论审核"),
    USER_AUTHENTICATION("USER_AUTHENTICATION", "用户认证"),
    CONSUMER_USER("CONSUMER_USER", "C端用户管理"),
    RESOURCE_AUDIT("RESOURCE_AUDIT", "资源审核"),
    SEARCH_MARKETING("SEARCH_MARKETING", "搜索营销"),

    ;


    private final String code;
    private final String desc;

    SysLogGroup(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SysLogGroup parse(String code) {
        if (Objects.nonNull(code)){
            for (SysLogGroup value : SysLogGroup.values()) {
                if (value.code.equals(code.toUpperCase())) {
                    return value;
                }
            }
        }

        return null;
    }

    public static String toStr(String code) {
        if (Objects.isNull(code)){
            return "";
        }
        for (SysLogGroup value : values()) {
            if (StringUtils.startsWithIgnoreCase(value.code,code)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
