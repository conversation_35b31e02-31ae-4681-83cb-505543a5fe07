package com.jxntv.gvideo.track.sdk.report;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Service
public class AppTrackEventServiceImpl implements AppTrackEventService {

    @Autowired
    private List<AppTrackReportHandler> appTrackReportHandlers;

    @Async
    @Override
    public void report(AppTrackReportBO trackReportBO) {
        //  分发事件处理
        appTrackReportHandlers.stream().filter(e -> e.support(trackReportBO)).forEach(e -> e.handler(trackReportBO));
    }


}

