package com.jxntv.gvideo.farm.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.farm.api.entity.*;
import com.jxntv.gvideo.farm.api.service.*;
import com.jxntv.gvideo.farm.sdk.dto.TaskInviteDTO;
import com.jxntv.gvideo.farm.sdk.dto.TaskInviteSpecDTO;
import com.jxntv.gvideo.farm.sdk.enums.TaskTypeEnum;
import com.jxntv.gvideo.farm.sdk.param.InviteParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Service
public class TaskInviteServiceImpl implements TaskInviteService {

    @Resource
    private TaskService taskService;
    @Resource
    private TaskProgressService taskProgressService;
    @Resource
    private TaskRewardIssuanceService taskRewardIssuanceService;
    @Resource
    private TaskRewardService taskRewardService;
    @Resource
    private FarmerService farmerService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean invite(InviteParam param) {
        Farmer farmer = farmerService.getOrCreateFarmer(param.getJid());
        Long farmerId = farmer.getId();

        //  是否配置，邀请好友任务
        Task task = taskService.getTaskByType(TaskTypeEnum.INVITE.name());
        if (Objects.isNull(task)) {
            return false;
        }

        TaskInviteSpecDTO inviteSpecDTO = JsonUtils.fromJson(task.getSpec(), TaskInviteSpecDTO.class);
        int progressTotal = inviteSpecDTO.getProgressTotal();
        //  获取当前任务周期标记
        String period = getPeriod(task);

        LocalDateTime now = LocalDateTime.now();
        //  计算当前周期进度
        LambdaQueryWrapper<TaskProgress> progressQuery = Wrappers.lambdaQuery();
        progressQuery.eq(TaskProgress::getTaskId, task.getId());
        progressQuery.eq(TaskProgress::getFarmerId, farmerId);
        progressQuery.eq(TaskProgress::getPeriod, period);
        progressQuery.orderByDesc(TaskProgress::getId);
        progressQuery.last("limit 1");
        TaskProgress taskProgress = taskProgressService.getOne(progressQuery);
        if (Objects.isNull(taskProgress)) {
            //  构建邀请好友任务信息
            taskProgress = new TaskProgress();
            taskProgress.setPeriod(period);
            taskProgress.setFarmerId(farmerId);
            taskProgress.setProgress(1);
            taskProgress.setProgressTotal(progressTotal);
            taskProgress.setTaskId(task.getId());
            taskProgress.setTaskType(task.getType());
            taskProgress.setExecuteDate(LocalDate.now());
            taskProgress.setExecuteTime(now);
            taskProgressService.save(taskProgress);
        }else{
            taskProgress.setProgress(taskProgress.getProgress() + 1);
            taskProgress.setExecuteDate(LocalDate.now());
            taskProgress.setExecuteTime(now);
            taskProgressService.updateById(taskProgress);
        }
        //  邀请好友成功奖励
        if (Objects.nonNull(inviteSpecDTO.getInviteRewardId()) && inviteSpecDTO.getInviteRewardId() > 0L) {

            TaskRewardIssuance rewardIssuance = new TaskRewardIssuance();
            rewardIssuance.setRewardId(inviteSpecDTO.getInviteRewardId());
            rewardIssuance.setFarmerId(farmerId);
            rewardIssuance.setContentId(taskProgress.getId());
            rewardIssuance.setTaskId(task.getId());
            rewardIssuance.setTaskType(task.getType());
            rewardIssuance.setDescription("邀请好友奖励");
            rewardIssuance.setStatus(0);
            rewardIssuance.setStartDate(task.getStartDate());
            rewardIssuance.setEndDate(task.getEndDate());
            rewardIssuance.setCreatorId(-1L);
            rewardIssuance.setCreateDate(now);
            rewardIssuance.setUpdaterId(-1L);
            rewardIssuance.setUpdateDate(now);
            taskRewardIssuanceService.save(rewardIssuance);
        }

        //  新用户奖励
        if (Objects.nonNull(inviteSpecDTO.getNewUserRewardId()) && inviteSpecDTO.getNewUserRewardId() > 0L) {

            Farmer newFarmer = this.farmerService.getOrCreateFarmer(param.getNewUserJid());

            TaskRewardIssuance rewardIssuance = new TaskRewardIssuance();
            rewardIssuance.setRewardId(inviteSpecDTO.getNewUserRewardId());
            rewardIssuance.setFarmerId(newFarmer.getId());
            rewardIssuance.setContentId(param.getJid());
            rewardIssuance.setTaskId(task.getId());
            rewardIssuance.setTaskType(TaskTypeEnum.NEW_USER.name());
            rewardIssuance.setDescription("新用户奖励");
            rewardIssuance.setStatus(0);
            rewardIssuance.setStartDate(task.getStartDate());
            rewardIssuance.setEndDate(task.getEndDate());
            rewardIssuance.setCreatorId(-1L);
            rewardIssuance.setCreateDate(now);
            rewardIssuance.setUpdaterId(-1L);
            rewardIssuance.setUpdateDate(now);
            taskRewardIssuanceService.save(rewardIssuance);
        }


        return true;
    }

    @Override
    public TaskInviteDTO inviteInfo(Long farmerId) {
        //  是否配置，邀请好友任务
        Task task = taskService.getTaskByType(TaskTypeEnum.INVITE.name());
        if (Objects.isNull(task)) {
            return null;
        }
        //  获取当前任务周期标记
        String period = getPeriod(task);

        //  计算当前周期进度
        LambdaQueryWrapper<TaskProgress> progressQuery = Wrappers.lambdaQuery();
        progressQuery.eq(TaskProgress::getTaskId, task.getId());
        progressQuery.eq(TaskProgress::getFarmerId, farmerId);
        progressQuery.eq(TaskProgress::getPeriod, period);
        progressQuery.orderByDesc(TaskProgress::getId);
        progressQuery.last("limit 1");
        TaskProgress taskProgress = taskProgressService.getOne(progressQuery);

        TaskInviteDTO taskInviteDTO = new TaskInviteDTO();
        TaskInviteSpecDTO inviteSpecDTO = JsonUtils.fromJson(task.getSpec(), TaskInviteSpecDTO.class);
        TaskReward reward = taskRewardService.getById(inviteSpecDTO.getInviteRewardId());
        if (Objects.isNull(taskProgress)){
            taskInviteDTO.setInviteCount(0);
            taskInviteDTO.setRewardFeedAmount(0);
        }else{
            taskInviteDTO.setInviteCount(taskProgress.getProgress());
            taskInviteDTO.setRewardFeedAmount(reward.getAmount().intValue() * taskProgress.getProgress());
        }
        taskInviteDTO.setInviteFeed(reward.getAmount().intValue());
        return taskInviteDTO;
    }


    private String getPeriod(Task task) {
        return task.getStartDate() + "|" + task.getEndDate();
    }

}
