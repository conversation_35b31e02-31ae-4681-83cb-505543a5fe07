package com.jxntv.gvideo.farm.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.farm.api.entity.Chicken;
import com.jxntv.gvideo.farm.api.entity.Farmer;
import com.jxntv.gvideo.farm.api.respository.FarmerMapper;
import com.jxntv.gvideo.farm.api.service.ChickenService;
import com.jxntv.gvideo.farm.api.service.FarmerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

@Slf4j
@Service
public class FarmerServiceImpl extends ServiceImpl<FarmerMapper, Farmer> implements FarmerService {
    @Resource
    private ChickenService chickenService;
    @Override
    public Farmer getByJid(Long jid) {
        LambdaQueryWrapper<Farmer> farmerQuery = Wrappers.lambdaQuery();
        farmerQuery.eq(Farmer::getJid, jid);
        return this.getOne(farmerQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Farmer getOrCreateFarmer(Long jid) {
        Farmer farmer = this.getByJid(jid);
        if (Objects.isNull(farmer)) {
            //  创建农场主
            farmer = new Farmer();
            farmer.setJid(jid);
            farmer.setFeedBalance(0);
            farmer.setEggsBalance(0);
            farmer.setCreateTime(LocalDateTime.now());
            farmer.setUpdateTime(LocalDateTime.now());
            farmer.setCreatorId(jid);
            farmer.setUpdaterId(jid);

            this.save(farmer);

            //  构建小鸡
            Chicken chicken = new Chicken();
            chicken.setName("蔡徐坤");
            chicken.setFarmerId(farmer.getId());
            chicken.setEggsTotal(0);

            chickenService.save(chicken);

        }
        return farmer;
    }

}
