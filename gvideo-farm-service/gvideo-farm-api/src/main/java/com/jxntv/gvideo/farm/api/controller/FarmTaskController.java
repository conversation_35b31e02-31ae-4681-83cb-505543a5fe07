package com.jxntv.gvideo.farm.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.farm.api.converter.TaskConverter;
import com.jxntv.gvideo.farm.api.converter.TaskProgressConverter;
import com.jxntv.gvideo.farm.api.converter.TaskRewardConverter;
import com.jxntv.gvideo.farm.api.converter.TaskRewardIssuanceConverter;
import com.jxntv.gvideo.farm.api.entity.*;
import com.jxntv.gvideo.farm.api.service.*;
import com.jxntv.gvideo.farm.sdk.FarmTaskClient;
import com.jxntv.gvideo.farm.sdk.dto.*;
import com.jxntv.gvideo.farm.sdk.enums.TaskTypeEnum;
import com.jxntv.gvideo.farm.sdk.param.*;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
public class FarmTaskController implements FarmTaskClient {

    @Resource
    private TaskSignService taskSignService;
    @Resource
    private TaskService farmTaskService;
    @Resource
    private TaskConverter farmTaskConverter;
    @Resource
    private TaskNewsViewService taskNewsViewService;
    @Resource
    private TaskProgressConverter taskProgressConverter;
    @Resource
    private TaskProgressService taskProgressService;
    @Resource
    private TaskGroupViewService taskGroupViewService;
    @Resource
    private TaskPostService taskPostService;
    @Resource
    private TaskRewardService taskRewardService;
    @Resource
    private TaskRewardConverter taskRewardConverter;
    @Resource
    private TaskRewardIssuanceService taskRewardIssuanceService;
    @Resource
    private TaskRewardIssuanceConverter taskRewardIssuanceConverter;
    @Resource
    private FarmerService farmerService;
    @Resource
    private TaskInviteService taskInviteService;

    @Override
    public Result<List<TaskDTO>> getTasks(TaskQueryParam taskQueryParam) {
        LambdaQueryWrapper<Task> taskQuery = Wrappers.lambdaQuery();
        taskQuery.eq(StringUtils.hasText(taskQueryParam.getStyle()), Task::getStyle, taskQueryParam.getStyle());
        taskQuery.eq(Task::getStatus, 1);

        taskQuery.orderByDesc(Task::getWeight);
        List<Task> list = farmTaskService.list(taskQuery);

        List<TaskDTO> collect = list.stream().map(farmTaskConverter::convert).collect(Collectors.toList());
        return Result.ok(collect);
    }

    @Override
    public Result<TaskProgressDTO> getCurrentTaskProgress(TaskProgressCurrentQueryParam taskProgressCurrentQueryParam) {
        Long taskId = taskProgressCurrentQueryParam.getTaskId();
        Long farmerId = taskProgressCurrentQueryParam.getFarmerId();

        LambdaQueryWrapper<TaskProgress> lastProgressQuery = Wrappers.lambdaQuery();
        lastProgressQuery.eq(TaskProgress::getTaskId, taskId);
        lastProgressQuery.eq(TaskProgress::getFarmerId, farmerId);
        lastProgressQuery.eq(TaskProgress::getExecuteDate, LocalDate.now());

        lastProgressQuery.orderByDesc(TaskProgress::getExecuteTime);
        lastProgressQuery.last(" limit 1");

        TaskProgress lastProgress = taskProgressService.getOne(lastProgressQuery);

        return Result.ok(Objects.isNull(lastProgress) ? null : taskProgressConverter.convert(lastProgress));
    }

    @Override
    public Result<Boolean> sign(SignParam signParam) {
        boolean result = taskSignService.sign(signParam);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> isSign(Long farmerId) {
        boolean result = taskSignService.isSign(farmerId, LocalDate.now());
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> newsView(NewsViewParam newsViewParam) {
        boolean result = taskNewsViewService.view(newsViewParam);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> groupView(GroupViewParam groupViewParam) {
        boolean result = taskGroupViewService.view(groupViewParam);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> post(PostParam postParam) {
        boolean result = taskPostService.post(postParam);
        return Result.ok(result);
    }

    @Override
    public Result<Boolean> invite(InviteParam param) {
        boolean result = taskInviteService.invite(param);
        return Result.ok(result);
    }

    @Override
    public Result<TaskInviteDTO> inviteInfo(Long farmerId) {
        return Result.ok(taskInviteService.inviteInfo(farmerId));
    }

    @Override
    public Result<Integer> toBeClaimed(Long farmerId) {
        boolean isSign = taskSignService.isSign(farmerId, LocalDate.now());
        if (!isSign){
            taskSignService.sign(new SignParam(farmerId));
        }
        List<TaskRewardIssuance> unclaimedList = taskRewardIssuanceService.getUnclaimedList(farmerId,null);
        if (CollectionUtils.isEmpty(unclaimedList)){
            return Result.ok(0);
        }
        unclaimedList.removeIf(e -> Objects.equals(TaskTypeEnum.NEW_USER.name(),e.getTaskType()));
        int sum = unclaimedList.stream().mapToInt(e -> {
            TaskReward reward = taskRewardService.getById(e.getRewardId());
            return Objects.isNull(reward) ? 0 : reward.getAmount().intValue();
        }).sum();
        return Result.ok(sum);
    }

    @Override
    public Result<Boolean> batchClaimedReward(Long jid) {
        Farmer farmer = farmerService.getOrCreateFarmer(jid);
        List<TaskRewardIssuance> unclaimedList = taskRewardIssuanceService.getUnclaimedList(farmer.getId(),null);
        if (CollectionUtils.isEmpty(unclaimedList)){
            return Result.ok(null);
        }
        unclaimedList.forEach(e -> {
            if(!Objects.equals(TaskTypeEnum.NEW_USER.name(),e.getTaskType())) {
                taskRewardIssuanceService.claim(e.getId());
            }
          }
        );
        return Result.ok(true);
    }

    @Override
    public Result<TaskRewardDTO> getRewardById(Long rewardId) {
        TaskReward reward = taskRewardService.getById(rewardId);
        return Result.ok(taskRewardConverter.convert(reward));
    }

    @Override
    public Result<List<TaskRewardIssuanceDTO>> getUnclaimedList(Long farmerId) {
        List<TaskRewardIssuance> unclaimedList = taskRewardIssuanceService.getUnclaimedList(farmerId,null);
        return Result.ok(unclaimedList.stream().map(taskRewardIssuanceConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<Boolean> claimedReward(Long issuanceId) {
        return Result.ok(taskRewardIssuanceService.claim(issuanceId));
    }
}
