package com.jxntv.gvideo.farm.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.farm.api.converter.*;
import com.jxntv.gvideo.farm.api.entity.*;
import com.jxntv.gvideo.farm.api.service.*;
import com.jxntv.gvideo.farm.api.utils.PageUtils;
import com.jxntv.gvideo.farm.sdk.FarmClient;
import com.jxntv.gvideo.farm.sdk.dto.*;
import com.jxntv.gvideo.farm.sdk.enums.FarmActivityTypeEnum;
import com.jxntv.gvideo.farm.sdk.enums.TaskTypeEnum;
import com.jxntv.gvideo.farm.sdk.param.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


@RestController
public class FarmController implements FarmClient {

    @Resource
    private FarmerService farmerService;
    @Resource
    private FarmerConverter farmerConverter;
    @Resource
    private ChickenService chickenService;
    @Resource
    private ChickenConverter chickenConverter;
    @Resource
    private ChickenFeedService chickenFeedService;

    @Resource
    private EggsCollectionService eggsCollectionService;
    @Resource
    private EggsCollectionConverter eggsCollectionConverter;
    @Resource
    private EggsConfigService eggsConfigService;
    @Resource
    private TaskTimedOpenService taskTimedOpenService;
    @Resource
    private FarmActivityService farmActivityService;
    @Resource
    private FarmActivityConverter farmActivityConverter;
    @Resource
    private GoodsExchangeService goodsExchangeService;
    @Resource
    private GoodsExchangeConverter goodsExchangeConverter;
    @Resource
    private TaskRewardIssuanceService taskRewardIssuanceService;
    @Resource
    private TaskRewardService taskRewardService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<FarmerDTO> getOrCreateFarmer(FarmerGetOrCreateParam dto) {

        Farmer farmer = farmerService.getOrCreateFarmer(dto.getJid());
        //  记打开农场一次
        taskTimedOpenService.open(farmer.getId());

        return Result.ok(farmerConverter.convert(farmer));
    }

    @Override
    public Result<FarmerDTO> getFarmerByJid(Long jid) {
        Farmer farmer = farmerService.getByJid(jid);
        return Result.ok(farmerConverter.convert(farmer));
    }

    @Override
    public Result<FarmActivityDTO> getActivity(FarmActivityQueryParam dto) {
        LambdaQueryWrapper<FarmActivity> activityQuery = Wrappers.lambdaQuery();
        activityQuery.eq(FarmActivity::getType, dto.getType());
        activityQuery.eq(FarmActivity::getStatus, 1);
        activityQuery.last(" limit 1");

        FarmActivity one = farmActivityService.getOne(activityQuery);
        return Result.ok(farmActivityConverter.convert(one));
    }

    @Override
    public Result<ChickenDTO> getChicken(Long farmerId) {
        Chicken chicken = chickenService.getByFarmerId(farmerId);
        AssertUtil.notNull(chicken, CodeMessage.NOT_FOUND);
        return Result.ok(chickenConverter.convert(chicken));
    }

    @Override
    public Result<NewUserRewardIssuanceDTO> feedChicken(ChickenFeedParam chickenFeedParam) {
        boolean result = chickenFeedService.feed(chickenFeedParam);
        if (result){
            List<TaskRewardIssuance> newUserList = taskRewardIssuanceService.getUnclaimedList(chickenFeedParam.getFarmerId(),TaskTypeEnum.NEW_USER.name());
            if (!CollectionUtils.isEmpty(newUserList)){
                TaskRewardIssuance taskRewardIssuance = newUserList.get(0);
                NewUserRewardIssuanceDTO dto = new NewUserRewardIssuanceDTO();
                dto.setId(taskRewardIssuance.getId());
                dto.setInviterJid(taskRewardIssuance.getContentId());
                TaskReward reward = taskRewardService.getById(taskRewardIssuance.getRewardId());
                dto.setRewardFeed(Objects.isNull(reward) ? 0 : reward.getAmount().intValue());
                return Result.ok(dto);
            }
        }
        return Result.ok(null);
    }

    @Override
    public Result<FeedProgressDTO> getChickenFeedProgress(Long chickenId) {
        Chicken chicken = chickenService.getById(chickenId);
        int eggsTotal = chicken.getEggsTotal();

        //  进度分母
        LambdaQueryWrapper<EggsConfig> eggsConfigQuery = Wrappers.lambdaQuery();
        eggsConfigQuery.eq(EggsConfig::getEggs, eggsTotal + 1);
        EggsConfig eggsConfig = eggsConfigService.getOne(eggsConfigQuery);

        //  进度分子
        LambdaQueryWrapper<ChickenFeed> feedQuery = Wrappers.lambdaQuery();
        feedQuery.eq(ChickenFeed::getChickenId, chickenId);
        feedQuery.eq(ChickenFeed::getEggs, eggsTotal + 1);

        int count = chickenFeedService.count(feedQuery);

        FeedProgressDTO feedProgressDTO = new FeedProgressDTO();
        feedProgressDTO.setChickenId(chickenId);
        feedProgressDTO.setCurrent(count);
        feedProgressDTO.setTotal(eggsConfig.getFeedTimes());
        feedProgressDTO.setFeedCost(eggsConfig.getFeedCostPerTime());

        return Result.ok(feedProgressDTO);
    }


    @Override
    public Result<PageDTO<EggsCollectionDTO>> getEggsCollection(EggsCollectionQueryParam queryParam) {
        LambdaQueryWrapper<EggsCollection> collectionQuery = Wrappers.lambdaQuery();
        collectionQuery.eq(Objects.nonNull(queryParam.getFarmerId()), EggsCollection::getFarmerId, queryParam.getFarmerId());
        collectionQuery.eq(Objects.nonNull(queryParam.getChickenId()), EggsCollection::getChickenId, queryParam.getChickenId());

        Page<EggsCollection> pageRequest = Page.of(queryParam.getCurrent(), queryParam.getSize());


        Page<EggsCollection> page = eggsCollectionService.page(pageRequest, collectionQuery);

        return Result.ok(PageUtils.pageOf(page, e -> eggsCollectionConverter.convert(e)));
    }

    @Override
    public Result<Boolean> eggsExchange(EggsExchangeParam exchangeParam) {
        boolean result = goodsExchangeService.eggsExchange(exchangeParam);
        return Result.ok(result);
    }

    @Override
    public Result<List<GoodsExchangeDTO>> getEggsExchangeList(EggsExchangeListQueryParam queryParam) {

        FarmActivity one = farmActivityService.getByType(FarmActivityTypeEnum.EGGS_EXCHANGE.name());

        LambdaQueryWrapper<GoodsExchange> goodsExchangeQuery = Wrappers.lambdaQuery();
        goodsExchangeQuery.eq(GoodsExchange::getGoodsId, one.getGoodsId());
        goodsExchangeQuery.eq(GoodsExchange::getFarmerId, queryParam.getFarmerId());

        List<GoodsExchange> list = goodsExchangeService.list(goodsExchangeQuery);
        return Result.ok(list.stream().map(goodsExchangeConverter::convert).collect(Collectors.toList()));
    }



}
