package com.jxntv.gvideo.farm.api.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxntv.gvideo.common.model.PageDTO;

import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
public class PageUtils {

    public static <T, E> PageDTO<T> pageOf(IPage<E> page, Function<E, T> mapper) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords().stream().map(mapper).collect(Collectors.toList()));
        pageDTO.setPageNum(Math.toIntExact(page.getCurrent()));
        pageDTO.setPageSize(Math.toIntExact(page.getSize()));
        pageDTO.setTotal(Math.toIntExact(page.getTotal()));
        return pageDTO;
    }

    public static <E> PageDTO<E> pageOf(IPage<E> page) {
        PageDTO<E> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords());
        pageDTO.setPageNum(Math.toIntExact(page.getCurrent()));
        pageDTO.setPageSize(Math.toIntExact(page.getSize()));
        pageDTO.setTotal(Math.toIntExact(page.getTotal()));
        return pageDTO;
    }
}
