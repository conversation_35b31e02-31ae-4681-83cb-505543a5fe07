package com.jxntv.gvideo.farm.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.farm.api.entity.TaskRewardIssuance;
import com.jxntv.gvideo.farm.api.entity.Task;
import com.jxntv.gvideo.farm.api.entity.TaskProgress;
import com.jxntv.gvideo.farm.api.service.TaskRewardIssuanceService;
import com.jxntv.gvideo.farm.api.service.TaskGroupViewService;
import com.jxntv.gvideo.farm.api.service.TaskProgressService;
import com.jxntv.gvideo.farm.api.service.TaskService;
import com.jxntv.gvideo.farm.sdk.dto.TaskGroupViewSpecDTO;
import com.jxntv.gvideo.farm.sdk.enums.TaskTypeEnum;
import com.jxntv.gvideo.farm.sdk.param.GroupViewParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskGroupViewServiceImpl implements TaskGroupViewService {
    @Resource
    private TaskService taskService;
    @Resource
    private TaskRewardIssuanceService rewardIssuanceService;
    @Resource
    private TaskProgressService taskProgressService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean view(GroupViewParam groupViewParam) {

        Long farmerId = groupViewParam.getFarmerId();
        Long groupId = groupViewParam.getGroupId();

        //  获取任务配置
        Task task = taskService.getTaskByType(TaskTypeEnum.GROUP_VIEW.name());
        if (Objects.isNull(task)) {
            return false;
        }

        TaskGroupViewSpecDTO viewSpecDTO = JsonUtils.fromJson(task.getSpec(), TaskGroupViewSpecDTO.class);
        Map<Integer, TaskGroupViewSpecDTO.SpecItem> specItemMap = viewSpecDTO.getItems().stream().collect(Collectors.toMap(TaskGroupViewSpecDTO.SpecItem::getProgress, e -> e));
        int progressTotal = viewSpecDTO.getProgressTotal();


        //  获取当前周期
        String period = getPeriod();
        //  计算当前周期进度
        LambdaQueryWrapper<TaskProgress> progressQuery = Wrappers.lambdaQuery();
        progressQuery.eq(TaskProgress::getTaskId, task.getId());
        progressQuery.eq(TaskProgress::getFarmerId, farmerId);
        progressQuery.eq(TaskProgress::getPeriod, period);

        int progress = taskProgressService.count(progressQuery) + 1;
        //  检查进度是否合理
        if (progress > progressTotal) {
            return false;
        }

        TaskProgress entity = new TaskProgress();
        entity.setPeriod(period);
        entity.setFarmerId(farmerId);
        entity.setProgress(progress);
        entity.setProgressTotal(viewSpecDTO.getProgressTotal());
        entity.setExecuteTime(LocalDateTime.now());
        entity.setExecuteDate(LocalDate.now());
        entity.setTaskId(task.getId());
        entity.setTaskType(task.getType());

        taskProgressService.save(entity);

        //  是否需要发放奖励
        TaskGroupViewSpecDTO.SpecItem specItem = specItemMap.get(progress);
        if (Objects.nonNull(specItem)) {
            //  构建奖励发放记录
            Long rewardId = specItem.getRewardId();
            TaskRewardIssuance rewardIssuance = new TaskRewardIssuance();
            rewardIssuance.setRewardId(rewardId);
            rewardIssuance.setFarmerId(farmerId);
            rewardIssuance.setContentId(groupId);
            rewardIssuance.setTaskId(task.getId());
            rewardIssuance.setTaskType(TaskTypeEnum.GROUP_VIEW.name());
            rewardIssuance.setDescription(TaskTypeEnum.GROUP_VIEW.getDesc());
            rewardIssuance.setStatus(0);
            rewardIssuance.setStartDate(LocalDateTime.now());
            rewardIssuance.setEndDate(LocalDateTime.of(LocalDate.now(), LocalTime.MAX));
            rewardIssuance.setCreatorId(-1L);
            rewardIssuance.setCreateDate(LocalDateTime.now());
            rewardIssuance.setUpdaterId(-1L);
            rewardIssuance.setUpdateDate(LocalDateTime.now());

            rewardIssuanceService.save(rewardIssuance);
        }

        return false;
    }

    private String getPeriod() {
        return LocalDateTime.of(LocalDate.now(), LocalTime.MIN) + "|" + LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
    }



}
