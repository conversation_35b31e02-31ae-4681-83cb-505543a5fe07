package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 页面资源
 * @date 2021/05/21 15:01
 */
@Data
public class PageResourceDTO implements Serializable {
    /**
     * 顶部资源集合
     */
    private Slide slide;
    /**
     * 资源列表
     */
    private List<Item> list;
    /**
     * 待插入的list
     */
    private Map<Integer, PageResourceDTO.ListItem> insertMap;
    /**
     * 内容列表资源总数
     */
    private Long total;
    /**
     * 当前请求页码
     */
    private Integer cursor;
    /**
     * 每页显示数量
     */
    private Integer size;
    /**
     * 是否有下一页
     */
    private boolean hasMore;

    public static PageResourceDTO empty(Integer cursor, Integer size) {
        PageResourceDTO dto = new PageResourceDTO();
        dto.setCursor(cursor);
        dto.setSize(size);
        dto.setTotal(0L);
        dto.setHasMore(false);
        PageResourceDTO.Slide slide = new PageResourceDTO.Slide();
        slide.setHeadTop(Collections.emptyList());
        slide.setHeadBottom(Collections.emptyList());
        dto.setSlide(slide);
        dto.setList(Collections.emptyList());
        return dto;
    }

    @Data
    public static class Item implements Serializable {
        /**
         * 内容列表ID
         */
        private Long listId;
        /**
         * 内容列表名称
         */
        private String listName;
        /**
         * 类型：0-资源、1-内容列表、2-H5
         */
        private Integer type;
        /**
         * 列表显示方式：1-左文右图，2-大图，3-纯文本
         */
        private Integer showType;
        /**
         * 资源ID
         */
        private Long resourceId;
        /**
         * 内容列表设置的资源显示名称
         */
        private String name;
        /**
         * 缩略图
         */
        private String cover;
        /**
         * H5页面地址
         */
        private String url;
        /**
         * H5页面创建时间
         */
        private Date createDate;
        /**
         * 切换间隔时间，单位：毫秒
         */
        private Float sliderTime;
        /**
         * 置顶顺序
         */
        private Integer stickSort;
    }

    @Data
    public static class Slide implements Serializable {
        /**
         * 切换间隔时间，单位：毫秒
         */
//        private Float sliderTime;
        /**
         * 头部上方
         */
        private List<Item> headTop;
        /**
         * 头部下方
         */
        private List<Item> headBottom;
        /**
         * 热点
         */
        private List<Item> hotNews;
    }

    @Data
    public static class ListItem implements Serializable {
        /**
         * 内容列表ID
         */
        private Long listId;
        /**
         * 布局块名称
         */
        private String blockName;
        /**
         * 类型：0-内容列表、1-顶部上、2-顶部下、3-轮播、4-滚动、5-热点
         */
        private Integer type;
        /**
         * 切换间隔时间，单位：毫秒
         */
        private Float sliderTime;
        /**
         * 插入的内容列表
         */
        private List<Item> items;
    }
}
