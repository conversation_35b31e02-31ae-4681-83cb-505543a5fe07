package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.BannerClient;
import com.jxntv.gvideo.media.client.MediaResourceRecommendClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceRecommendDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MediaResourceRecommendClientFallback implements FallbackFactory<MediaResourceRecommendClient> {
    @Override
    public MediaResourceRecommendClient create(Throwable throwable) {
        return new MediaResourceRecommendClient() {
            @Override
            public Result<List<MediaResourceRecommendDTO>> list(Long id) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> add(MediaResourceRecommendDTO mediaResourceRecommendDTO) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> update(Long id, MediaResourceRecommendDTO mediaResourceRecommendDTO) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> sort(Long[] ids) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> delete(Long id) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

        };
    }
}
