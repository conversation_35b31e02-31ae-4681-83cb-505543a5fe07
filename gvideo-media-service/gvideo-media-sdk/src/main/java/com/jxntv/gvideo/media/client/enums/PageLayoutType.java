package com.jxntv.gvideo.media.client.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2021/05/20 17:20
 */
public enum PageLayoutType {
    //类型：0-内容列表、1-顶部上、2-顶部下、3-轮播、4-滚动、5-热点
    CONTENT_LIST(0, "内容列表"),
    HEAD_TOP(1, "顶部上"),
    HEAD_BOTTOM(2, "顶部下"),
    CAROUSEL(3, "轮播"),
    ROLL(4, "滚动"),
    HOT_NEWS(5, "热点"),

    ;

    private Integer code;
    private String desc;

    PageLayoutType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescById(int code) {
        for (PageLayoutType c : PageLayoutType.values()) {
            if (c.getCode() == code) {
                return c.getDesc();
            }
        }
        return null;
    }
}
