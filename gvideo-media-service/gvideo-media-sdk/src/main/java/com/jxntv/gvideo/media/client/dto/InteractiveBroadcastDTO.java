package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> Created on 2020/12/28.
 */
@Data
public class InteractiveBroadcastDTO implements Serializable {
    private Long id;
    private Long jid;
    /**
     * 直播状态
     */
    private String broadcastStatus;
    private Integer status;
    private String title;
    private String description;
    private String liveUrl;
    /**
     * 主播名称
     */
    private String broadcasterName;
    /**
     * 推荐状态
     */
    private String recommendStatus;
    /**
     * 直播人数统计
     */
    private String viewStat;
    /**
     * 评论统计
     */
    private String commentStat;
    /**
     * 开始时间
     */
    private String beginDate;
    /**
     * 结束时间
     */
    private String endDate;
    /**
     * 是否被ban
     */
    private Integer ban;
    private Long sysUserId;

    private Long certificationId;
    private Integer playStyle;
    private Long mediaId;
    private String thumb;
    private String thumbUrl;
    private String sysUserName;
    private String certificationName;
    private Integer type;
    private Integer connectVideo;
    private Integer guestRecommendSwitch;
}
