package com.jxntv.gvideo.media.client.dto.radio;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class RadioBroadcastDTO implements Serializable {

    private Long id;

    /**
     * 信号源：0-省台，1-地市
     */
    private Integer source;

    /**
     * 广播名称
     */
    private String name;

    /**
     * 广播别名
     */
    private String alias;

    /**
     * 广播封面logo id
     */
    private String thumbId;
    /**
     * 广播封面默认URL（兼容设计），如果设置了thumbId取thumbId对应图片内容
     */
    private String thumbUrl;

    /**
     * 是否拥有播放版权
     */
    private Boolean hasCopyright;
    /**
     * 广播数据源
     */
    private List<RadioBroadcastVideoDTO> video;

    /**
     * 广播排序字段
     */
    private Integer weights;


    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

}
