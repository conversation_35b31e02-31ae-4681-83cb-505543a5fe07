package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.lottery.*;
import com.jxntv.gvideo.media.client.fallback.NewYearLotteryClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:04
 */
@FeignClient(name = "media-service",contextId = "new-year-lottery",fallbackFactory = NewYearLotteryClientFallback.class)
public interface NewYearLotteryClient {

    String PREFIX = "/api/new/year/lottery";

    @PostMapping(PREFIX+"/prize")
    Result<Boolean> saveOrUpdatePrize(@RequestBody NewYearLotteryPrizeDTO dto);

    @PostMapping(PREFIX+"/prize/page")
    Result<PageDTO<NewYearLotteryPrizeDTO>> pagePrize(@RequestBody NewYearLotteryPrizePageSearchParam dto);

    @PostMapping(PREFIX+"/prize/issue")
    Result<Long> issuePrize(@RequestBody NewYearLotteryPrizeIssueDTO dto);

    @PutMapping(PREFIX+"/prize/increase/stock")
    Result<Boolean> prizeIncreaseStock(@RequestBody NewYearLotteryPrizeIncreaseStockDTO dto);

    @GetMapping(PREFIX+"/activity/{activityId}")
    Result<NewYearLotteryActivityDTO> queryActivityById(@PathVariable("activityId") Long activityId);

    @PostMapping(PREFIX+"/log/page")
    Result<PageDTO<NewYearLotteryLogDTO>> logPage(@RequestBody NewYearLotteryLogPageSearchParam dto);

    @PostMapping(PREFIX+"/log/count")
    Result<Integer> logCount(@RequestBody NewYearLotteryLogPageSearchParam dto);

    @GetMapping(PREFIX+"/log/{logId}")
    Result<NewYearLotteryLogDTO> logInfo(@PathVariable("logId") Long logId);

    @PutMapping(PREFIX+"/log/complete")
    Result<Boolean> completeLogInfo(@RequestBody NewYearLotteryLogDTO dto);

    @PutMapping(PREFIX+"/check/winning/mobile")
    Result<Boolean> checkWinningMobile(@RequestBody NewYearLotteryCheckWinningMobileDTO dto);
}
