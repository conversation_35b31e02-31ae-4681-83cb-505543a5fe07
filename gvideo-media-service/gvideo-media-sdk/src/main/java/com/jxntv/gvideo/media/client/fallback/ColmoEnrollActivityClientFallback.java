package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.ColmoEnrollActivityClient;
import com.jxntv.gvideo.media.client.dto.ColmoEnrollActivityDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * COLMO家电折扣券活动客户端熔断降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ColmoEnrollActivityClientFallback implements FallbackFactory<ColmoEnrollActivityClient> {

    @Override
    public ColmoEnrollActivityClient create(Throwable throwable) {
        return new ColmoEnrollActivityClient() {
            @Override
            public Result<Boolean> activityEnroll(ColmoEnrollActivityDTO dto) {
                log.error("ColmoEnrollActivityClient.activityEnroll() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> getEnrollCount() {
                log.error("ColmoEnrollActivityClient.getEnrollCount() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ColmoEnrollActivityDTO> getEnrollInfoByJid(Long jid) {
                log.error("ColmoEnrollActivityClient.getEnrollInfoByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
