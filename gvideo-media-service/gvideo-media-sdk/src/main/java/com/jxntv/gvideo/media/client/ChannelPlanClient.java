package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.media.client.dto.ChannelPlanDTO;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.client.dto.PlanResourceDTO;
import com.jxntv.gvideo.media.client.dto.search.ChannelPlanSearchDTO;
import com.jxntv.gvideo.common.model.Result;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 频道投放计划管理
 *
 *
 * Created on 2020-03-23
 */
public interface ChannelPlanClient {

    @PostMapping("/api/channel-plans/page")
    Result<PageDTO<ChannelPlanDTO>> page(@RequestBody ChannelPlanSearchDTO searchDTO);

    @GetMapping("/api/channel-plans")
    Result<List<ChannelPlanDTO>> list(@RequestParam Long channelId, @RequestParam int status);

    @GetMapping("/api/channel-plans/{id}")
    Result<ChannelPlanDTO> get(@PathVariable Long id);

    @PostMapping("/api/channel-plans")
    Result<Long> create(@RequestBody ChannelPlanDTO dto);

    @PutMapping("/api/channel-plans/{id}")
    Result update(@PathVariable Long id, @RequestBody ChannelPlanDTO dto);

    @DeleteMapping("/api/channel-plans/{id}")
    Result endOrDel(@PathVariable Long id, @RequestParam Long userId, @RequestParam Integer op);

    @GetMapping("/api/channel-plans/fuzzy")
    Result<List<ChannelPlanDTO>> fuzzy(@RequestParam(required = false) Long channelId, @RequestParam(required = false) String name);

    @PostMapping("/api/channel-plans/bulk")
    Result<List<ChannelPlanDTO>> bulk(@RequestBody List<Long> ids);

    @GetMapping("/api/channel-plans/{id}/resources/count")
    Result<Integer> resourceCount(@PathVariable Long id);

    @PostMapping("/api/channel-plans/{id}/resources/page")
    Result<PageDTO<PlanResourceDTO>> resources(@PathVariable Long id,
                                               @RequestParam(required = false) Integer status,
                                               @RequestParam(required = false) Integer resourceStatus,
                                               @RequestParam(required = false, defaultValue = "1") int current,
                                               @RequestParam(required = false, defaultValue = "10") int size);

    @PostMapping("/api/channel-plans/{id}/resources")
    Result addResources(@PathVariable Long id, @RequestBody PlanResourceDTO dto);

    @PutMapping("/api/channel-plans/{id}/resources/{resource-id}")
    Result editResources(@PathVariable Long id,
                         @PathVariable("resource-id") Long resourceId,
                         @RequestBody PlanResourceDTO dto);

    @DeleteMapping("/api/channel-plans/{id}/resources/{resource-id}")
    Result removeResources(@PathVariable Long id, @PathVariable("resource-id") Long resourceId);

    @DeleteMapping("/api/channel-plans/plan-resources")
    Result removeResourcesByResourceIds(@RequestBody List<Long> ids);

    @PostMapping("/api/channel-plans/resources/update-current-weight")
    Result updateCurrentWeight(Map<Long, Integer> map);
}
