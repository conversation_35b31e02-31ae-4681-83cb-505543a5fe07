package com.jxntv.gvideo.media.client.dto.level;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
public class MediaResourceSearchLevelDTO extends SearchDTO {

    private Long resourceId;

    private String introduction;

    private String nickName; // ugc

    private List<Integer> statuses;

    private List<Integer> contentTypes;

    private List<Integer> dataTypes;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String topicContent;

    private Long contentLabel;

    private String level;

    private Integer levelDuration;
    private LocalDate levelDateStart;
    private LocalDate levelDateEnd;

    private LocalDateTime passStartTime;

    private LocalDateTime passEndTime;
}
