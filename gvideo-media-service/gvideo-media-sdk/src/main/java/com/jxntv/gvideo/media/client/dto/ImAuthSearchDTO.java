package com.jxntv.gvideo.media.client.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 私信权限表
 */
@Data
public class ImAuthSearchDTO extends SearchDTO {
    /**
     * 用户JID
     */
    private Long jid;
    /**
     * 用户类型: 0-普通用户、1-官方用户
     */
    private Integer jidType;
    /**
     * 生成类型: 0-动态规则、1-管理员
     */
    private Integer type;
    /**
     * 是否黑名单：0-否、1-是
     */
    private Integer blackFlag;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 开始时间
     */
    private LocalDateTime createDateStart;
    /**
     * 开始时间
     */
    private LocalDateTime createDateEnd;
}
