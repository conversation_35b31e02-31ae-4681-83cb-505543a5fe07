package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.dto.AiDrawPrintDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: niedamin
 * @Date: 2022/09/29 16:08
 */
@FeignClient(name = "media-service",contextId = "ai-draw")
public interface AiDrawPrintClient {

    @PostMapping("/api/ai/draw/print/page")
    Result<PageDTO<AiDrawPrintDTO>> page(@RequestBody SearchDTO searchDTO);

    @GetMapping("/api/ai/draw/print/{taskId}")
    Result<AiDrawPrintDTO> getByTaskId(@PathVariable String taskId);

    @PostMapping("/api/ai/draw/print")
    Result<Void> print(@RequestBody AiDrawPrintDTO aiDrawPrintDTO);

    @PutMapping("/api/ai/draw/print/{id}")
    Result<Void> updateById(@PathVariable Long id, @RequestBody AiDrawPrintDTO aiDrawPrintDTO);


}
