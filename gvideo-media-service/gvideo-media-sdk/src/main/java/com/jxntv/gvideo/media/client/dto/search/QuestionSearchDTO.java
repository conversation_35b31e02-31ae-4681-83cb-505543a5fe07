package com.jxntv.gvideo.media.client.dto.search;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.text.Bidi;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR> on 2020/12/3.
 */
@Getter
@Setter
@ToString
public class QuestionSearchDTO extends SearchDTO implements Serializable {
    private static final long serialVersionUID = -7347241229586270732L;

    private Long resourceId;
    private String resourceContent;
    private Integer platform;
    private Integer isQuestionClosely;
    private String nickname;
    private Long groupId;
    private LocalDateTime questionStartTime;
    private LocalDateTime questionEndTime;
    private BigDecimal amountStart;
    private BigDecimal amountEnd;

}
