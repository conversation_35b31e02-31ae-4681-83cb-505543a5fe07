package com.jxntv.gvideo.media.client.enums;

/**
 * <AUTHOR>
 * @date 2023/3/6 15:31
 */
public enum AdvertSearchMarketingStatus {

    pending(0, "未开始"),
    the_upper(1, "投放中"),
    the_lower_frame(2, "已停止");

    private Integer code;

    private String desc;

    AdvertSearchMarketingStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static AdvertSearchMarketingStatus parse(Integer code) {
        for (AdvertSearchMarketingStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
