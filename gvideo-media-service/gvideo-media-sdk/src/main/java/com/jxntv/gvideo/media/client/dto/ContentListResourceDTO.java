package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 内容列表资源关联
 * @date 2021/05/10 8:40
 */
@Data
public class ContentListResourceDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 内容列表ID
     */
    private Long listId;
    /**
     * 类型：0-资源、1-H5 2-栏目
     */
    private Integer type;
    /**
     * 列表显示方式：1-左文右图，2-大图，3-三图，4-纯文本
     */
    private Integer showType;
    /**
     * 视图序号
     */
    private Integer index;
    /**
     * 资源ID
     */
    private Long resourceId;
    /**
     * 内容类型：0-H5、1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云、12-新闻、13-专题
     */
    private Integer contentType;
    /**
     * 资源标题
     */
    private String name;
    /**
     * 缩略图
     */
    private List<String> coverList;
    /**
     * H5页面地址
     */
    private String linkUrl;
    /**
     * 置顶顺序
     */
    private Integer stickSort;
    /**
     * 锁定顺序
     */
    private Integer lockSort;
    /**
     * 顺序
     */
    private Integer sort;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 标识：0-新增、1-更新、2-删除
     */
    private Integer flag;
}
