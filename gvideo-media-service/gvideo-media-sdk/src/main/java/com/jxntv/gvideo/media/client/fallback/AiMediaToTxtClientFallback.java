package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.AiMediaToTxtClient;
import com.jxntv.gvideo.media.client.dto.ai.AiAsrParam;
import com.jxntv.gvideo.media.client.dto.ai.AiMediaToTxtResult;
import com.jxntv.gvideo.media.client.dto.ai.OverrideTxtParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * AI 媒体转文本
 */
@Slf4j
@Component
public class AiMediaToTxtClientFallback implements FallbackFactory<AiMediaToTxtClient> {


    @Override
    public AiMediaToTxtClient create(Throwable throwable) {
        return new AiMediaToTxtClient() {
            @Override
            public AiMediaToTxtResult covertTxt(AiAsrParam asrParam) {
                log.error("AiMediaToTxtClient.covertTxt() ", throwable);
                return null;
            }

            @Override
            public Result<Boolean> checkAuth(String phone) {
                log.error("AiMediaToTxtClient.checkAuth() ", throwable);
                return Result.ok(false);
            }

            @Override
            public Result<Boolean> overrideTxt(OverrideTxtParam param) {
                log.error("AiMediaToTxtClient.overrideTxt() ", throwable);
                return Result.ok(false);
            }

            @Override
            public AiMediaToTxtResult getById(Long id) {
                return null;
            }

            @Override
            public PageDTO<AiMediaToTxtResult> listHistory(Long jig, Integer page, Integer pageSize) {
                return PageDTO.empty();
           }
        };
    }
}
