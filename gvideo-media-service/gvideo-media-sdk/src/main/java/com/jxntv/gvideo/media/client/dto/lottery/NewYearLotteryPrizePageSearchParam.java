package com.jxntv.gvideo.media.client.dto.lottery;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:08
 */
@Getter
@Setter
@ToString
public class NewYearLotteryPrizePageSearchParam extends SearchDTO implements Serializable {
    private static final long serialVersionUID = -7347241229586270732L;

    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动日期
     */
    private String activityDate;
    /**
     * 类型: 1-现金 2-今豆
     */
    private Integer type;
    /**
     * 状态 0-待上架 1-已上架 2-已下架
     */
    private Integer status;
    /**
     * 奖品类型 0-优先 1-保底
     */
    private Integer prizeType;

}
