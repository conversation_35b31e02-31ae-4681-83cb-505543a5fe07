package com.jxntv.gvideo.media.client.dto.theme;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: zzh
 * @Date: 2023/01/04 14:49
 */
@Data
public class AppThemeDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     *  主题名称
     */
    private String title;
    /**
     * 0-已添加  1-预加载  2-已启用 3-已下线
     */
    private Integer status;

    /**
     * head Zip ossId
     */
    private String headZipId;

    /**
     * tab Zip ossId
     */
    private String tabZipId;

    /**
     * 0 默认 1 新年 2 两会
     */
    private Integer defaultTheme;

    /**
     * 预加载时间
     */
    private Date preLoadingTime;
    /**
     * 上线启用时间
     */
    private Date onLineTime;
    /**
     * 下线时间
     */
    private Date offLineTime;
    /**
     * 创建用户id
     */
    private Long createUserId;
    /**
     * 创建用户账号
     */
    private String createUserName;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新用户id
     */
    private Long updateUserId;
    /**
     * 更新用户账号
     */
    private String updateUserName;
    /**
     * 更新时间
     */
    private Date updateDate;
}
