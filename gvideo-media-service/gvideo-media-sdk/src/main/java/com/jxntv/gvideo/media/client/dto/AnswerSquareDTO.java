package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 提问、回答实体类
 *
 * <AUTHOR> Created on 2021/8/16.
 */
@Data
public class AnswerSquareDTO implements Serializable {
    private Long id;

    /**
     * 所属圈子id
     */
    private Long groupId;

    /**
     * 提问类型对应的资源id
     */
    private Long mediaId;
    private Long commentId;
    /**
     * 提问用户jid,主持人jid
     */
    private Long jid;

    /**
     * 1提问2回答
     */
    private Integer type;

    /**
     * 提问标题
     */
    private String title;

    /**
     * 1文字2图文3语音
     */
    private Integer answerType;

    /**
     * 内容评论
     */
    private String content;

    /**
     * 图片集合冒号隔开
     */
    private String image;

    /**
     * 语音oss id
     */
    private String soundOssId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态 0无效1有效
     */
    private Integer status;

    /**
     * 回复时间
     */
    private LocalDateTime replyTime;

    /**
     * 回复jid
     */
    private Long replyJid;

    private Long mentorJid;

    /**
     * 该提问最近一次回答id
     */
    private Long lastAnswerId;
    private String questionAuthor;
    private String answerAuthor;
    private Long authId;
}
