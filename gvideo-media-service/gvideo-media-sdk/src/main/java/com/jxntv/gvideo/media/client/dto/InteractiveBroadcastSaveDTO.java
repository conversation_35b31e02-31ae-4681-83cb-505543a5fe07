package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> Created on 2020/12/28.
 */
@Data
public class InteractiveBroadcastSaveDTO implements Serializable {
    private static final long serialVersionUID = -809767155853195761L;
    private Long id;
    private Long jid;
    private String username;
    private String title;
    private String description;
    private Long certificationId;
    private String thumb;
    private List<Long> goods;
    private String broadcastLabelId;
    private String liveUrl;
    private Integer type;
    private Integer guestRecommendSwitch;
}
