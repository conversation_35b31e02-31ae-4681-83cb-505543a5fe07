package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceExternalClient;
import com.jxntv.gvideo.media.client.dto.AddMediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class MediaResourceExternalClientFallback implements FallbackFactory<MediaResourceExternalClient> {
    @Override
    public MediaResourceExternalClient create(Throwable throwable) {
        return new MediaResourceExternalClient() {
            @Override
            public Result<MediaResourceExternalDTO> getByMediaId(Long mediaId) {
                log.error("MediaResourceExternalClient.getByMediaId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaResourceExternalDTO> getByContentId(String contentId) {
                log.error("MediaResourceExternalClient.getByContentId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addExternal(AddMediaResourceExternalDTO dto) {
                log.error("MediaResourceExternalClient.addExternal() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

        };
    }
}
