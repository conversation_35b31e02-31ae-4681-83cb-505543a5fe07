package com.jxntv.gvideo.media.client.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * <AUTHOR> Created on 2020/12/4.
 */
@Getter
public enum RecommendEnum {
    /**
     * 推荐状态
     */
    DEFAULT(0, "默认"),
    RECOMMEND(1, "推荐"),
    TOP(2, "置顶");

    @EnumValue
    @JsonValue
    private final int code;
    private final String name;

    RecommendEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 传入code获取枚举值
     *
     * @param code
     * @return
     */
    public static RecommendEnum getByCode(int code) {
        for (RecommendEnum value : RecommendEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
