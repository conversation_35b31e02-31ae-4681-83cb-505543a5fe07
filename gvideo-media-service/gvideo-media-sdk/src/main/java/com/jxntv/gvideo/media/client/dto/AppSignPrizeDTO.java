package com.jxntv.gvideo.media.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/04/18 16:01
 */
@Data
public class AppSignPrizeDTO implements Serializable {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 奖品获取类型：1-兑换奖品，2-抽奖奖品
     *
     * @see com.jxntv.gvideo.media.client.enums.AppSignPrizeCategory
     */
    private Integer category;
    /**
     * 奖品类型
     *
     * @see com.jxntv.gvideo.media.client.enums.AppSignPrizeType
     */
    private Integer type;
    /**
     * 奖品名称
     */
    private String prizeName;
    /**
     * 奖品数量
     */
    private Integer prizeCount;
    /**
     * 奖品数量
     */
    private Integer availablePrizeCount;
    /**
     * 奖品图片
     */
    private List<PrizeImageDTO> prizeImages;
    /**
     * 奖品消耗今豆
     */
    private Integer prizePrice;
    /**
     * 兑换类型
     */
    private Integer exchangeType;
    /**
     * 兑换限制周期 1-每天，2-每周，3-永久
     */
    private Integer exchangeCycleType;
    /**
     * 抽奖概率
     */
    private BigDecimal raffleRate;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 规则
     */
    private String rule;
    /**
     * 状态 0-未上架 1-上架 2-兑完 3-下架
     */
    private Integer status;
    /**
     * 微信代金券批次号
     */
    private String stockId;

    private Integer version;
    /**
     * 核销日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime writeOffDate;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateDate;

    /**
     * 兑换仅限一次的奖品是否兑换过
     */
    private boolean hasBuy;
}
