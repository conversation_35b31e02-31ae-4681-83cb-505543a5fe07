package com.jxntv.gvideo.media.client.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 群组表
 */
@Data
public class ImGroupSearchDTO extends SearchDTO {
    /**
     * 群组形态: 0-会议群、1-好友工作群、2-陌生人社交群、3-直播群
     */
    private Integer type;
    /**
     * 群组ID
     */
    private String groupId;
    /**
     * 群名称
     */
    private String name;
    /**
     * 申请加群方式: 0-需要验证、1-自由加入、2-禁止加群
     */
    private Integer applyMode;
    /**
     * 开始时间
     */
    private LocalDateTime createDateStart;
    /**
     * 开始时间
     */
    private LocalDateTime createDateEnd;
}
