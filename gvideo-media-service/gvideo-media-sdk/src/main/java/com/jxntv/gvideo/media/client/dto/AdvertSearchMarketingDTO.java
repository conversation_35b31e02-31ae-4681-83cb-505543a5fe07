package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 搜索文案投放表
 * @date 2022/02/21 15:21
 */
@Data
public class AdvertSearchMarketingDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 投放名称
     */
    private String name;
    /**
     * 开始时间
     */
    private LocalDateTime startDate;
    /**
     * 结束时间
     */
    private LocalDateTime endDate;
    /**
     * 状态：0-未开始、1-投放中、2-已停止
     */
    private Integer status;
    /**
     * 触发关键字
     */
    private String triggerKeyword;
    /**
     * banner照片ossid
     */
    private String bannerOssId;
    /**
     * 跳转类型，0-应用内跳转，1-链接
     */
    private Integer jumpType;
    /**
     * 内链接跳转类型，0-跳菜单，1-跳内容
     */
    private Integer innerJumpType;
    /**
     * 跳转菜单类型，0-新闻首页，1-推荐首页，2-我的社区，3-发现社区，4-问答广场
     */
    private Integer jumpMenu;
    /**
     * 跳转内容id
     */
    private Long jumpMediaId;
    /**
     * 跳转内容类型，0-新闻，1-圈子，2-话题，3-动态，4-直播
     */
    private Integer jumpMediaType;
    /**
     * 外链接跳转URL
     */
    private String jumpUrl;

    /**
     * 补充URL，小程序兼容H5
     */
    private String extraUrl;
    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

}