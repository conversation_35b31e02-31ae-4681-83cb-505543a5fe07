package com.jxntv.gvideo.media.client.enums;

/**
 * @Author: niedamin
 * @Date: 2022/11/08 10:27
 */
public enum DouyinPayStateEnum {

//    支付状态枚举值：SUCCESS：成功 TIMEOUT：超时未支付 PROCESSING：处理中 FAIL：失败

    SUCCESS(1, "SUCCESS"),

    TIMEOUT(2, "TIMEOUT"),

    PROCESSING(3, "PROCESSING"),

    FAIL(4, "FAIL");

    private Integer code;

    private String result;

    DouyinPayStateEnum(Integer code, String result) {
        this.code = code;
        this.result = result;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public static Integer getCode(String result) {
        Integer code = null;
        for (DouyinPayStateEnum s : values()) {
            if (s.getResult().equals(result)) {
                code = s.getCode();
                break;
            }
        }
        return code;
    }

}
