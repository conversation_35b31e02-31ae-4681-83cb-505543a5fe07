package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.hongdujuan.QueryParam;
import com.jxntv.gvideo.media.client.dto.hongdujuan.RegisterDTO;
import com.jxntv.gvideo.media.client.dto.hongdujuan.ScoringParam;
import com.jxntv.gvideo.media.client.fallback.DramaProgramClientFallback;
import com.jxntv.gvideo.media.client.fallback.HongDuJuanClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "media-service", contextId = "hongdujuan", fallbackFactory = HongDuJuanClientFallback.class)
public interface HongDuJuanClient {

    /**
     * 发送短信验证码
     * @param mobile
     * @return
     */
    @GetMapping("/api/hongdujuan/send-code")
    Result<String> sendCode(@RequestParam("mobile") String mobile);

    /**
     * 注册报名
     * @param dto
     * @return
     */
    @PostMapping("/api/hongdujuan/register")
    Result<String> register(@RequestBody RegisterDTO dto);

    @PostMapping("/api/hongdujuan/list-project")
    Result<PageDTO<RegisterDTO>> listProject(@RequestBody QueryParam queryParam);

    /**
     * 查询终审项目
     * @param queryParam
     * @return
     */
    @PostMapping("/api/hongdujuan/list-final-project")
    Result<PageDTO<RegisterDTO>> listFinalProject(@RequestBody QueryParam queryParam);

    /**
     * 查询所有作品总分
     * @param queryParam
     * @return
     */
    @PostMapping("/api/hongdujuan/list-product")
    Result<PageDTO<RegisterDTO>> listProduct(@RequestBody QueryParam queryParam);




    @PostMapping("/api/hongdujuan/query")
    Result<List<RegisterDTO>> query(@RequestBody QueryParam queryParam);

    /**
     * 评委打分
     * @param param
     * @return
     */
    @PostMapping("/api/hongdujuan/scoring")
    Result scoring(@RequestBody ScoringParam param);


    /**
     * 查询评委赛道
     * @param mobile
     * @return
     */
    @GetMapping("/api/hongdujuan/get-judge-tracks")
    Result<String> getJudgeTracks(@RequestParam String mobile,@RequestParam String type);

    /**
     * 提交打分结果
     * @param param
     * @return
     */
    @PostMapping("/api/hongdujuan/submit")
    Result submit(@RequestBody ScoringParam param);
}
