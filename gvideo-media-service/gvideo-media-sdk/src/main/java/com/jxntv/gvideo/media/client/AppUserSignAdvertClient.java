package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AppUserSignAdvertDTO;
import com.jxntv.gvideo.media.client.dto.AppUserSignAdvertSearchDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "media-service", contextId = "app-user-sign-advert")
public interface AppUserSignAdvertClient {

    @GetMapping("/api/user/sign/advert/list")
    Result<List<AppUserSignAdvertDTO>> getAdvertList();

    @PostMapping("/api/user/sign/advert")
    Result<Long> create(@RequestBody AppUserSignAdvertDTO dto);

    @GetMapping("/api/user/sign/advert/{id}")
    Result<AppUserSignAdvertDTO> getById(@PathVariable("id") Long id);

    @PostMapping("/api/user/sign/advert/page")
    Result<PageDTO<AppUserSignAdvertDTO>> page(@RequestBody AppUserSignAdvertSearchDTO searchDTO);

    @PutMapping("/api/user/sign/advert/{id}")
    Result<Boolean> updateById(@PathVariable("id") Long id, @RequestBody AppUserSignAdvertDTO dto);

    @DeleteMapping("/api/user/sign/advert/{id}")
    Result<Boolean> deleteById(@PathVariable("id") Long id);


}
