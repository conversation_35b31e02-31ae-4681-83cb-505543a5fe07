package com.jxntv.gvideo.media.client.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2021/05/10 8:26
 */
public enum ContentListFolder {
    FOLDER_0(0, "否"),
    FOLDER_1(1, "是"),
    ;

    private int code;
    private String desc;

    ContentListFolder(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (ContentListFolder value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
