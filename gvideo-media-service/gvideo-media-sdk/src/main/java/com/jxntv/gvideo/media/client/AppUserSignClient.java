package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AppUserSignDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/04/21 14:55
 */
@FeignClient(name = "media-service", contextId = "app-user-sign")
public interface AppUserSignClient {

    @PostMapping("/api/app/user/sign")
    Result<Long> create(@RequestBody AppUserSignDTO appUserSignDTO);

    @PutMapping("/api/app/user/sign")
    Result<Void> update(@RequestBody AppUserSignDTO appSignPrizeDTO);

    @GetMapping("/api/app/user/sign")
    Result<AppUserSignDTO> getByJid(@RequestParam Long jid);

    @GetMapping("/api/app/user/sign/rank")
    Result<List<AppUserSignDTO>> getRankList();

    @GetMapping("/api/app/user/sign/join/count")
    Result<Integer> getJoinUserCount();

}
