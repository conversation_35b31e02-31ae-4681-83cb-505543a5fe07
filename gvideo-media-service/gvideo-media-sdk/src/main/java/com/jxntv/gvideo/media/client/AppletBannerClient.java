package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.applet.AppletBannerDTO;
import com.jxntv.gvideo.media.client.dto.applet.AppletBannerSearchDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface AppletBannerClient {

    @PostMapping("/api/applet/banner")
    public Result<Long> create(@RequestBody AppletBannerDTO appletBannerDTO);

    @GetMapping("/api/applet/banner/{id}")
    public Result<AppletBannerDTO> getById(@PathVariable Long id);

    @PutMapping("/api/applet/banner/{id}")
    public Result<Long> updateById(@PathVariable Long id, @RequestBody AppletBannerDTO appletBannerDTO);

    @DeleteMapping("/api/applet/banner/{id}")
    public Result<Long> deleteById(@PathVariable Long id);

    @PostMapping("/api/applet/banner/page")
    public Result<PageDTO<AppletBannerDTO>> page(@RequestBody AppletBannerSearchDTO appletBannerSearchDTO);

    @GetMapping("/api/applet/banner/enable")
    Result<List<AppletBannerDTO>> queryEnableBanner();
}
