package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class OutShareWhitelistDTO implements Serializable {
    /**
     * 白名单id
     */
    private Long id;
    /**
     * 白名单名称
     */
    private String name;
    /**
     * 匹配规则
     */
    private String pattern;

    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}
