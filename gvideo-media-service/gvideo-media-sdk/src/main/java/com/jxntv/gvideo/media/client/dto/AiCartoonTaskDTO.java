package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.util.Date;

/**
 * @Author: niedamin
 * @Date: 2023/02/10 17:03
 */
@Data
public class AiCartoonTaskDTO {

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 任务ID
     */
    private String taskId;


    /**
     * 用户jid
     */
    private Long jid;

    /**
     * 任务状态1：排队中 2：绘画中 3：已完成
     */
    private Integer status;

    /**
     * 原图
     */
    private String oldImage;

    /**
     * 新图
     */
    private String newImage;

    /**
     * 提交时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 完成时间
     */
    private Date completeDate;

    /**
     * 前面排队人数
     */
    private Integer queueTaskNum;

    /**
     * 等待时间（单位秒）
     */
    private Long waitTime;

    private String oldImageId;


}
