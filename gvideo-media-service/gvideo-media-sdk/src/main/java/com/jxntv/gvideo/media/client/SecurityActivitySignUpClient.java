package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.SecurityActivitySignUpDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/07/07 15:43
 */
@FeignClient(name = "media-service")
public interface SecurityActivitySignUpClient {

    @PostMapping("/api/security/activity/sign-up")
    Result<Long> add(@RequestBody SecurityActivitySignUpDTO dto);

    @GetMapping("/api/security/activity/sign-up/list")
    Result<List<SecurityActivitySignUpDTO>> getList();

}
