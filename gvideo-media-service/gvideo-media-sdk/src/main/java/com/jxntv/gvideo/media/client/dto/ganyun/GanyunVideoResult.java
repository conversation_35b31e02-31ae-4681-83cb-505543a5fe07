package com.jxntv.gvideo.media.client.dto.ganyun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
public class GanyunVideoResult {

    @JsonProperty("state")
    private Boolean state;
    @JsonProperty("error")
    private String error;
    @JsonProperty("data")
    private DataDTO data;

    @Data
    @NoArgsConstructor
    public static class DataDTO {
        @JsonProperty("maxId")
        private Integer maxId;
        @JsonProperty("list")
        private List<ListDTO> list;


        @Data
        @NoArgsConstructor
        public static class ListDTO {
            /**
             * 赣云资源ID
             */
            private Long contentId;
            /**
             * 发布账号
             */
            private Long releaseId;
            /**
             * 标题
             */
            private String title;
            /**
             * 描述
             */
            private String desc;
            /**
             * 封面图
             */
            private String thumb;
            /**
             * 视频地址
             */
            private String url;
            /**
             * 时长
             */
            private Integer duration;
            /**
             * 标签
             */
            private String labels;
            /**
             * mcn id
             */
            private Long tenantId;
            /**
             * 可评论
             */
            private Boolean isComment;
            /**
             * TODO 来源
             */
            private String source;
            /**
             * 虚拟点击数
             */
            private Integer virtualPv;
            /**
             * 点击数
             */
            private Integer pv;
            /**
             * 是否原创：0-否、1-是
             */
            private Integer copyright;
            /**
             * 赣云资源状态：6-已发布、3-审核中、其他均为删除
             */
            private Integer status;
            /**
             * 创建时间
             */
            private String created;
            /**
             * 创建时间
             */
            private String published;
        }
    }

    @Data
    @NoArgsConstructor
    public static class Item{
        private String url;
        private String cover;
    }
}
