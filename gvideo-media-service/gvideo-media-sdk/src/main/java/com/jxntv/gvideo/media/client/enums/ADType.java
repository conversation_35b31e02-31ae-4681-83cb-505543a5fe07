package com.jxntv.gvideo.media.client.enums;

public enum ADType {
    FULL_SCREEN(1, "全屏式"),
    HALF_SCREEN(2, "半屏式");

    private int code;
    private String desc;

    ADType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) return "";
        for (ADType value : values()) {
            if (value.code == code.intValue()) {
                return value.desc;
            }
        }
        return "";
    }
}
