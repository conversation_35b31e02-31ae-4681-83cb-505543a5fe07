package com.jxntv.gvideo.media.client.dto.newscast;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JxntvContentResponse implements Serializable {

    @JsonProperty("data")
    private List<JxntvContentDTO> data;
    @JsonProperty("total")
    private Long total;
    @JsonProperty("nextpage")
    private Boolean nextpage;
    @JsonProperty("state")
    private Boolean state;

}
