package com.jxntv.gvideo.media.client.enums;

public enum ChannelPlanStatus {
    ING(1, "进行中"),
    END(2, "已结束"),
    DELETED(3, "已删除");

    private int code;
    private String desc;

    ChannelPlanStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) return "";
        for (ChannelPlanStatus value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
