package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MaterialDTO implements Serializable {
    /**
     * 运营素材id
     */
    private Long id;
    /**
     * 运营素材类型
     */
    private Integer type;
    /**
     * 广告标识
     */
    private Boolean advertising;
    /**
     * 运营素材名称
     */
    private String name;
    /**
     * 展示标题
     */
    private String title;
    /**
     * 备注
     */
    private String remark;
    /**
     * 子表id
     */
    private Long childId;
    /**
     * 角标
     */
    private Integer cornerMark;
    /**
     * 角标开始时间
     */
    private Date cornerMarkStartDate;
    /**
     * 角标结束时间
     */
    private Date cornerMarkEndDate;
    /**
     * 默认周期开始时间
     */
    private Date periodStart;
    /**
     * 默认周期结束时间
     */
    private Date periodEnd;
    /**
     * 创建人id
     */
    private Long createUserId;
    /**
     * 更新人id
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 状态
     */
    private Boolean status;

    private SeoLinkDTO seoLinkDTO;

    private OperationLinkDTO operationLinkDTO;

    private MapLocationDTO mapLocationDTO;

    private OpenAdvertDTO openAdvertDTO;

    private ActivityRegistrationDTO activityRegistrationDTO;

}
