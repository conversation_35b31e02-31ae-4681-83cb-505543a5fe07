package com.jxntv.gvideo.media.client.dto;

import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.ResourceType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MediaUgcFileResourceDTO implements Serializable {
    private Long id;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 关联文件阿里云ID
     */
    private String videoId;
    /**
     * 1视频2音频3图文4语音
     */
    private Integer fileType;
    private Long fileId;
    private Integer playStyle;
    private List<String> contentTypeLabels;
    private List<String> otherFeatureLabels;
    private String introduction;
    /**
     * 内部标签
     */
    private List<String> internalLabel;
    /**
     * 显示标题
     */
    private String showName;
    /**
     * 发布账号id
     */
    private Long releaseId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 是否公开
     */
    private Boolean isPublic;
    /**
     * 内部标题
     */
    private String internalName;
    /**
     * 封面图
     */
    private List<String> imageId;
    /**
     * 封面图
     * 后台上传区分图片类型
     */
    private List<MediaResourceImageDTO> images;
    /**
     * 图片列表
     */
    private List<String> imageList;
    /**
     * 内容
     */
    private String content;
    /**
     * 最后一次更新时间
     */
    private Date lastUpdateDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 音视频长度
     */
    private String length;
    /**
     * 客户端系统
     */
    private String clientOs;

    /**
     * 问答类型：0-默认资源、1-提问，2-回答，3-爆料
     */
    private Integer dataType;
    /**
     * 提问的导师id
     */
    private Long mentorJid;
    /**
     * 回答的提问对象
     */
    private Long questionId;
    /**
     * 回答关联的评论id
     */
    private Long commentId;
    /**
     * 问答所属社区id
     */
    private Long groupId;

    private String outShareUrl;
    private String outShareTitle;
    private MediaResourceLocationDTO location;
    /**
     * 是否匿名发帖
     */
    private Boolean isAnonymous;
    /**
     * 匿名名称
     */
    private String anonymousName;
    /**
     * 炫耀挂件ID
     */
    private Long showId;
    /**
     * ip地址
     */
    private String ip;
    /**
     * ip归属地
     */
    private String ipLocation;

    /**
     * 平台类型：0-今视频、 2-小程序
     */
    private Integer platform;


    /**
     * 被转发的资源ID
     */
    private Long relayId;

    public Integer getContentType() {
        if (fileType == 1) {
            return ContentType.VIDEO_HOMEMADE.getCode();
        } else if (fileType == 2) {
            return ContentType.AUDIO_FM.getCode();
        } else if (fileType == 3) {
            return ContentType.PIC_FONT.getCode();
        } else if (fileType == 4) {
            return ContentType.SOUND.getCode();
        }
        return null;
    }

    public Integer getResourceType() {
        if (fileType == 1) {
            return ResourceType.VIDEO.getCode();
        } else if (fileType == 2) {
            return ResourceType.AUDIO.getCode();
        } else if (fileType == 3) {
            return ResourceType.PIC_FONT.getCode();
        } else if (fileType == 4) {
            return ResourceType.SOUND.getCode();
        }
        return null;
    }
}
