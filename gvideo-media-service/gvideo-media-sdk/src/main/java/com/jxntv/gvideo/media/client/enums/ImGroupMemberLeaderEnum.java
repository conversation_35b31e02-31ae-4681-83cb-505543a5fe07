package com.jxntv.gvideo.media.client.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum ImGroupMemberLeaderEnum {
    // 是否群主：0-成员、1-群主
    MEMBER(0, "成员"),
    LEADER(1, "群主"),

    ;

    private Integer code;
    private String desc;

    ImGroupMemberLeaderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ImGroupMemberLeaderEnum parse(Integer code) {
        for (ImGroupMemberLeaderEnum value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String toStr(Integer code) {
        ImGroupMemberLeaderEnum applyEnum = parse(code);
        return Objects.isNull(applyEnum) ? "" : applyEnum.getDesc();
    }
}
