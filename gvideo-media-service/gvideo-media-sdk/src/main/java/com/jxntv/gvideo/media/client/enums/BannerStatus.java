package com.jxntv.gvideo.media.client.enums;

public enum BannerStatus {

    pending(0, "待上架"),

    the_upper(1, "已上架"),
    the_lower_frame(2, "已下架");

    private Integer code;

    private String desc;

    BannerStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static BannerStatus parse(Integer code) {
        for (BannerStatus value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }
}
