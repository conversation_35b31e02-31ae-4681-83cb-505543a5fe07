package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.GdzjAuditReportDTO;
import com.jxntv.gvideo.media.client.fallback.GdzjDataReportClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "media-service", contextId = "gdzj-data-report", fallbackFactory = GdzjDataReportClientFallback.class)
public interface GdzjDataReportClient {

    @PostMapping("/api/gdzj/audit/report")
    Result<Long> auditReport(@RequestBody GdzjAuditReportDTO dto);

}
