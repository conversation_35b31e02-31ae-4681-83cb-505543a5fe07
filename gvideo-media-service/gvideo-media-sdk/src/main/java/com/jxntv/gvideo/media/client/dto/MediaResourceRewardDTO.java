package com.jxntv.gvideo.media.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 资源奖励
 * <AUTHOR>
 */
@Data
public class MediaResourceRewardDTO implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 提问ID
     */
    private Long questionId;

    /**
     * 提问类型
     */
    private Integer questionMediaTab;

    /**
     * 资源ID media_resource.id
     */
    private Long resourceId;

    /**
     * 资源类型
     */
    private Integer resourceMediaTab;

    /**
     * 资源内容
     */
    private String resourceContent;

    /**
     * 奖励类型 1：回答
     */
    private String rewardType;

    /**
     * 发布人昵称
     */
    private String releaseNickname;

    /**
     * 发布人JID
     */
    private Long releaseJid;

    /**
     * 发布人手机号
     */
    private String releaseMobile;

    /**
     * 发布时间
     */
    private String releaseDate;

    /**
     * 社区名称
     */
    private String groupName;

    /**
     * 状态 0:待审核 1已审核:奖励 2已审核:已忽略
     */
    private String status;

    /**
     * 奖励金额
     */
    private BigDecimal amount;

    /**
     * 内容类型 1 视频自制内容 2 视频电影 3 视频节目 4 音频fm 5 视频剧集 6 活动直播 7 互动直播 8 H5页面
     */
    private Integer contentType;

    /**
     * 图片oss id集合
     */
    private String image;
    /**
     * 语音vod id
     */
    private String soundOssId;

    /**
     * 视频oss id
     */
    private String videoOssId;

    /**
     * 平台类型：0-今视频、2-小程序
     */
    private Integer platform;

    /**
     * 是否追问： 0-否 1-是
     */
    private Integer isQuestionClosely;
}
