package com.jxntv.gvideo.media.client.enums;

public enum TenantVisibleType {
    ALL(1, "全部租户可见"),
    SPECIFIED(2, "指定租户可见");
    private int code;
    private String desc;

    TenantVisibleType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) return "";
        for (TenantVisibleType value : values()) {
            if (value.getCode() == code.intValue()) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
