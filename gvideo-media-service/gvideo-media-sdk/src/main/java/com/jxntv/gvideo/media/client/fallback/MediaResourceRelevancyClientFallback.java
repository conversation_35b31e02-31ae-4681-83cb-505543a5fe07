package com.jxntv.gvideo.media.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.MediaResourceRelevancyClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceRelevancyDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceRelevancySearchDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/1/29 16:26
 */
@Slf4j
@Component
public class MediaResourceRelevancyClientFallback implements FallbackFactory<MediaResourceRelevancyClient> {
    @Override
    public MediaResourceRelevancyClient create(Throwable throwable) {

        return new MediaResourceRelevancyClient() {

            @Override
            public Result<Boolean> create(MediaResourceRelevancyDTO dto) {
                log.error("MediaResourceRelevancyClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateById(MediaResourceRelevancyDTO dto) {
                log.error("MediaResourceRelevancyClient.updateById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteById(Long id) {
                log.error("MediaResourceRelevancyClient.deleteById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceRelevancyDTO>> page(MediaResourceRelevancySearchDTO searchDTO) {
                log.error("MediaResourceRelevancyClient.page() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };

    }
}
