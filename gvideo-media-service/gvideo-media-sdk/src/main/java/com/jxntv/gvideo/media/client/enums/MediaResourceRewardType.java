package com.jxntv.gvideo.media.client.enums;

/**
 *  媒体资源奖励类型
 * <AUTHOR>
 */

public enum MediaResourceRewardType {
    /**
     * 奖励类型 1：回答、2：爆料
     */
    ANSWER(1, "回答"),
    BROKE_THE_NEWS(2, "爆料");

    ;

    private int code;
    private String desc;

    MediaResourceRewardType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) return "";
        for (MediaResourceRewardType value : values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
