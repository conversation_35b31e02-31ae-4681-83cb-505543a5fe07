package com.jxntv.gvideo.media.client.dto;

import com.jxntv.gvideo.common.model.SearchDTO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 私信权限表
 */
@Data
public class ImOfficialMessageSearchDTO extends SearchDTO {
    /**
     * 消息类型：0-官方消息、1-爆料追加奖励消息
     */
    private Integer type;
    /**
     * 用户JID
     */
    private Long jid;
    /**
     * 接收人类型：0-全体、1-社区、2-指定用户
     */
    private Integer toType;
    /**
     * 状态：0-未发送、1-已发送、2-部分发送、3-发送失败
     */
    private Integer status;
    /**
     * 开始时间
     */
    private LocalDateTime createDateStart;
    /**
     * 开始时间
     */
    private LocalDateTime createDateEnd;
}
