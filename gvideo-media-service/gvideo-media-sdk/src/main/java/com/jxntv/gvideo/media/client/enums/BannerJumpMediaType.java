package com.jxntv.gvideo.media.client.enums;

public enum BannerJumpMediaType {
    NEWS(0, "文章"),
    GROUP(1, "圈子"),
    TOPIC(2, "话题"),
    POSTS(3, "动态"),
    LIVE_BROADCAST(4, "直播"),
    ACTIVITY(5, "活动"),
    COLUMN(6, "整期节目"),
    CHANNEL(7, "电视直播"),
    ACTIVITY_GATHER(8, "活动组件"),
    INTRODUCTION(9, "个人介绍"),
    ASK_QUESTIONS(10, "提问发布"),
    GAN_YUN_VIDEO(11, "赣云-视频"),
    SPECIAL(12, "专题"),
    ;

    private Integer code;

    private String desc;

    BannerJumpMediaType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static BannerJumpMediaType parse(Integer code) {
        for (BannerJumpMediaType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }

        return null;
    }

}
