package com.jxntv.gvideo.media.client.dto.consult.qa;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: zzh
 * @Date: 2022/11/21 14:49
 */
@Data
public class ConsultQaOrderQueryDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 第三方小程序侧唯一订单编号
     */
    private String thirdOrderNo;

    /**
     * 订单状态
     * 0：待支付
     * 1：已支付
     * 2：已取消
     * 3：支付失败
     * 4：已核销（核销状态是整单核销,即一笔订单买了 3 个券，核销是指 3 个券核销的整单）
     * 5：退款中
     * 6：已退款
     * 8：退款失败
     */
    private Integer orderState;
    /**
     * 订单金额
     */
    private BigDecimal amount;
    /**
     * 支付时间
     */
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    /**
     * 资源ID media_resource.id
     */
    private Long resourceId;

    /**
     * 资源内容
     */
    private String resourceContent;

    /**
     * 内容类型 1 视频自制内容 2 视频电影 3 视频节目 4 音频fm 5 视频剧集 6 活动直播 7 互动直播 8 H5页面
     */
    private Integer contentType;

    /**
     * 提问类型
     */
    private Integer questionMediaTab;

    /**
     * 资源类型
     */
    private Integer resourceMediaTab;

    /**
     * 图片oss id集合
     */
    private String image;
    /**
     * 语音vod id
     */
    private String soundOssId;

    /**
     * 视频oss id
     */
    private String videoOssId;

    /**
     * 关联文件ID
     */
    private Long fileId;

    /**
     * 是否回答： 0-否 1-是
     */
    private Integer isAnswer;

    /**
     * 手机号
     */
    private String mobile;

    private Integer platform;

    private Long mentorJid;

    private Integer isQuestionClosely;

}
