package com.jxntv.gvideo.media.client.dto.outside.restrict;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zzh
 * @Date: 2023/01/04 14:49
 */
@Data
public class ResourceOutsideRestrictDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 资源id
     */
    private Long resourceId;
    /**
     * 内容类型 1.视频
     */
    private Integer resourceType;
    /**
     * 站外观看限制 0-不限制 1-限制
     */
    private Integer restrictType;
    /**
     * 站外观看限制秒数
     */
    private Integer restrictSeconds;

    /**
     * 操作用户id
     */
    private Long operateUserId;
    /**
     * 操作用户账号
     */
    private String operateUserName;
}
