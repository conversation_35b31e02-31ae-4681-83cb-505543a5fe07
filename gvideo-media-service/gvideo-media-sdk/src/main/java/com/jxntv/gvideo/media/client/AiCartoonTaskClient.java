package com.jxntv.gvideo.media.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AiCartoonTaskDTO;
import com.jxntv.gvideo.media.client.dto.AiCartoonTaskSearchDTO;
import com.jxntv.gvideo.media.client.fallback.AiCartoonClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Author: niedamin
 * @Date: 2023/02/10 16:01
 */
@FeignClient(name = "media-service", contextId = "ai-cartoon-task", fallbackFactory = AiCartoonClientFallback.class)
public interface AiCartoonTaskClient {

    @PostMapping("/api/ai-cartoon")
    Result<Long> create(@RequestBody AiCartoonTaskDTO dto);

    @PutMapping("/api/ai-cartoon")
    Result<Void> modify(@RequestBody AiCartoonTaskDTO dto);

    @PostMapping("/api/ai-cartoon/query")
    Result<PageDTO<AiCartoonTaskDTO>> query(@RequestBody AiCartoonTaskSearchDTO searchDTO);

    @GetMapping("/api/ai-cartoon/{id}")
    Result<AiCartoonTaskDTO> getById(@PathVariable Long id);

    @DeleteMapping("/api/ai-cartoon/task")
    Result<Void> delByJid(@RequestParam Long jid);

    @GetMapping("/api/ai-cartoon/task/failed")
    Result<Boolean> hasFailTask(@RequestParam Long jid);

    @DeleteMapping("/api/ai-cartoon/{id}")
    Result<Void> delById(@PathVariable Long id);
}
