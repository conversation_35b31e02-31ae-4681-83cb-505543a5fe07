package com.jxntv.gvideo.media.api.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.util.Map;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class Restful {

    private final MockMvc mvc;

    private final ObjectMapper objectMapper;

    public Restful(MockMvc mvc, ObjectMapper objectMapper) {
        this.mvc = mvc;
        this.objectMapper = objectMapper;
    }

    private String getStringResult(RequestBuilder builder) throws Exception {
        MockHttpServletResponse response = this.mvc.perform(builder).andExpect(status().isOk()).andReturn().getResponse();
        response.setCharacterEncoding("UTF-8");
        return response.getContentAsString();
    }

    public String get(String url) throws Exception {
        return get(url, null, new Object[]{});
    }

    public String get(String url, Map<String, String> params) throws Exception {
        return get(url, params, new Object[]{});
    }

    public String get(String urlTemplate, Object... uriVars) throws Exception {
        return get(urlTemplate, null, uriVars);
    }

    public String get(String urlTemplate, Map<String, String> params, Object... uriVars) throws Exception {
        MockHttpServletRequestBuilder builder;
        if (uriVars.length == 0) {
            builder = MockMvcRequestBuilders.get(urlTemplate);
        } else {
            builder = MockMvcRequestBuilders.get(urlTemplate, uriVars);
        }
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        return getStringResult(builder);
    }

    public String post(String url, Object entity) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.post(url);
        if (entity != null) {
            builder.content(objectMapper.writeValueAsString(entity))
                    .characterEncoding("UTF-8")
                    .contentType(MediaType.APPLICATION_JSON);
        }
        return getStringResult(builder);
    }

    public String post(String urlTemplate, Object entity, Object... uriVars) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.post(urlTemplate, uriVars);
        if (entity != null) {
            builder.content(objectMapper.writeValueAsString(entity))
                    .characterEncoding("UTF-8")
                    .contentType(MediaType.APPLICATION_JSON);
        }
        return getStringResult(builder);
    }

    public String post(String urlTemplate, Map<String, String> params, Object... uriVars) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.post(urlTemplate, uriVars);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        return getStringResult(builder);
    }

    public String put(String urlTemplate, Object entity, Object... uriVars) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.put(urlTemplate, uriVars);
        if (entity != null) {
            builder.content(objectMapper.writeValueAsString(entity))
                    .characterEncoding("UTF-8")
                    .contentType(MediaType.APPLICATION_JSON);
        }
        return getStringResult(builder);
    }

    public void put(Map<String, String> params, String urlTemplate, Object entity) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.put(urlTemplate);
        if (entity != null) {
            builder.content(objectMapper.writeValueAsString(entity))
                    .characterEncoding("UTF-8")
                    .contentType(MediaType.APPLICATION_JSON);
        }
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        this.mvc.perform(builder).andExpect(status().isOk());
    }

    public void delete(String urlTemplate, Object... uriVars) throws Exception {
        delete(null, urlTemplate, uriVars);
    }

    public void delete(Map<String, String> params, String urlTemplate, Object... uriVars) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.delete(urlTemplate, uriVars);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        this.mvc.perform(builder).andExpect(status().isOk());
    }

    public void delete(Map<String, String> params, String urlTemplate) throws Exception {
        MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.delete(urlTemplate);
        if (params != null && params.size() > 0) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.param(entry.getKey(), entry.getValue());
            }
        }
        this.mvc.perform(builder).andExpect(status().isOk());
    }
}
