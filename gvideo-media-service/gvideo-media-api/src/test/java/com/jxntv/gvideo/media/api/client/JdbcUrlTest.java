package com.jxntv.gvideo.media.api.client;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JdbcUrlTest {
    @Test
    public void test() {
        Map<String, List<Map<String, List<Map<String, List<String>>>>>> map = new HashMap<>();
        Map<String, List<String>> map1 = new HashMap<>();
        Map<String, List<String>> map2 = new HashMap<>();
        Map<String, List<String>> map3 = new HashMap<>();


        List<String> list1 = new ArrayList<>();
        list1.add("活动");
        List<String> list6 = new ArrayList<>();
        list6.add("务农");
        List<String> list2 = new ArrayList<>();
        list2.add("记忆力");
        list2.add("牙齿");
        map1.put("生活记录", list1);
        map2.put("心里健康", list2);
        map3.put("生活体验", list6);
        List<Map<String, List<String>>> list22 = new ArrayList<>();
        List<Map<String, List<String>>> list23 = new ArrayList<>();
        list22.add(map1);
        list22.add(map3);
        list23.add(map2);

        Map<String, List<Map<String, List<String>>>> map4 = new HashMap<>();
        map4.put("生活",list22);
        Map<String, List<Map<String, List<String>>>> map5 = new HashMap<>();
        map5.put("健康",list23);
        List<Map<String, List<Map<String, List<String>>>>> list4 = new ArrayList<>();

        list4.add(map4);
        list4.add(map5);
        map.put("123",list4);
        System.out.println(JSON.toJSONString(list4, true));
//
//        String s = "{\n" +
//                "\t\"生活\":[\n" +
//                "\t\t{\n" +
//                "\t\t\t\"生活体验\":[\n" +
//                "\t\t\t\t\"务农\"\n" +
//                "\t\t\t],\n" +
//                "\t\t\t\"生活记录\":[\n" +
//                "\t\t\t\t\"活动\"\n" +
//                "\t\t\t]\n" +
//                "\t\t}\n" +
//                "\t],\n" +
//                "\t\"健康\":[\n" +
//                "\t\t{\n" +
//                "\t\t\t\"心里健康\":[\n" +
//                "\t\t\t\t\"记忆力\",\n" +
//                "\t\t\t\t\"牙齿\"\n" +
//                "\t\t\t]\n" +
//                "\t\t}\n" +
//                "\t]\n" +
//                "}";
//        Map map = JSONObject.parseObject(s, Map.class);
//        System.out.println(map.toString());

    }
}
