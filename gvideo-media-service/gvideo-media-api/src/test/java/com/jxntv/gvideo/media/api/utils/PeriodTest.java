package com.jxntv.gvideo.media.api.utils;

import org.junit.Test;

import static org.junit.Assert.*;

public class PeriodTest {

    @Test
    public void conflict1() {

        Period p1 = new Period();
        p1.setStart(DateUtils.parse0("2020-01-01 10:00:00"));
        p1.setEnd(DateUtils.parse0("2020-01-10 10:00:00"));

        Period p2 = new Period();
        p2.setStart(DateUtils.parse0("2020-02-01 10:00:00"));
        p2.setEnd(DateUtils.parse0("2020-02-10 10:00:00"));

        boolean conflict = p1.conflict(p2);
        assertEquals(conflict, false);
    }

    @Test
    public void conflict2() {

        Period p1 = new Period();
        p1.setStart(DateUtils.parse0("2020-01-01 10:00:00"));
        p1.setEnd(DateUtils.parse0("2020-01-10 10:00:00"));

        Period p2 = new Period();
        p2.setStart(DateUtils.parse0("2020-01-05 10:00:00"));
        p2.setEnd(DateUtils.parse0("2020-01-08 10:00:00"));

        boolean conflict = p1.conflict(p2);
        assertEquals(conflict, true);
    }

    @Test
    public void conflict3() {

        Period p1 = new Period();
        p1.setStart(DateUtils.parse0("2020-01-01 10:00:00"));
        p1.setEnd(DateUtils.parse0("2020-01-10 10:00:00"));

        Period p2 = new Period();
        p2.setStart(DateUtils.parse0("2020-01-07 10:00:00"));
        p2.setEnd(DateUtils.parse0("2020-02-10 10:00:00"));

        boolean conflict = p1.conflict(p2);
        assertEquals(conflict, true);
    }
}