package com.jxntv.gvideo.media.api.controller;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.HashMap;
import java.util.Map;

import com.jxntv.gvideo.media.api.domain.entity.LabelType;
import com.jxntv.gvideo.media.api.service.LabelTypeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.json.AutoConfigureJsonTesters;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.json.JacksonTester;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.jxntv.gvideo.media.api.utils.Restful;
import com.jxntv.gvideo.media.api.utils.TestBeanFactory;
import com.jxntv.gvideo.media.client.dto.LabelTypeDTO;
import com.jxntv.gvideo.common.model.Result;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@AutoConfigureJsonTesters
public class LabelTypeControllerTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;

    private Restful restful;

    private LabelType testLabelType;

    @Autowired
    private LabelTypeService labelTypeService;

    @Autowired
    private JacksonTester<Result<Long>> longJacksonTester;

    @Test
    public void create() throws Exception {
        LabelTypeDTO dto = TestBeanFactory.createBean(LabelTypeDTO.class);
        String content = restful.post("/api/label/type/add", dto);
        Long id = longJacksonTester.parseObject(content).getResult();
        LabelType labelType = labelTypeService.getById(id);
        assertThat(labelType).isNotNull();
        assertThat(dto.getName()).isEqualTo(labelType.getName());
    }

    @Test
    public void update() throws Exception {
        LabelTypeDTO dto = TestBeanFactory.createBean(LabelTypeDTO.class);
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(testLabelType.getId()));
        restful.put(params, "/api/label/type/modify", dto);
        LabelType labelType = labelTypeService.getById(testLabelType.getId());

        assertThat(labelType).isNotNull();
        assertThat(dto.getName()).isEqualTo(labelType.getName());
    }

    @Test
    public void delete() throws Exception {
        Map<String, String> params = new HashMap<>();
        params.put("id", String.valueOf(testLabelType.getId()));
        restful.delete(params, "/api/label/type/delete");
        LabelType labelType = labelTypeService.getById(testLabelType.getId());
        assertThat(labelType).isNull();
    }

    @Before
    public void beforeEach() {
        if (restful == null) {
            restful = new Restful(mockMvc, objectMapper);
        }
        testLabelType = TestBeanFactory.createBean(LabelType.class);
        testLabelType.setDelFlag(false);
        labelTypeService.save(testLabelType);
    }
}