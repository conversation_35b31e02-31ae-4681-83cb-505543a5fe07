<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.media.api.repository.MediaFileMapper">
    <select id="tenantMediaFilePage" resultType="com.jxntv.gvideo.media.api.domain.entity.MediaFile">
        SELECT
            t.id,
            t.file_type,
            t.limit_view,
            t.file_name,
            t.create_user_id,
            t.resource_id,
            t.oss_id,
            t.`status`,
            t.create_date
        FROM
        (
            SELECT
                mf.id,
                mf.file_type,
                mf.limit_view,
                mf.file_name,
                mf.create_user_id,
                mf.resource_id,
                mf.oss_id,
                mf.`status`,
                mf.create_date
            FROM
            media_file mf
            WHERE
            mf.limit_view = 0
            <if test="search.status != null">
                AND mf.`status` = #{search.status}
            </if>
            <if test="search.fileType != null">
                AND mf.file_type = #{search.fileType}
            </if>
            <if test="search.start != null">
                AND mf.create_date <![CDATA[ > ]]> #{search.start}
            </if>
            <if test="search.end != null">
                AND mf.create_date <![CDATA[ < ]]> #{search.end}
            </if>
            <if test="search.fileName != null and search.fileName != ''">
                AND (mf.file_name LIKE CONCAT('%', #{search.fileName}, '%') OR mf.id = #{search.fileName})
            </if>
            <if test="search.createUserIds != null and search.createUserIds.size() > 0">
                AND mf.create_user_id IN
                <foreach collection="search.createUserIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        UNION ALL
            SELECT
                mf.id,
                mf.file_type,
                mf.limit_view,
                mf.file_name,
                mf.create_user_id,
                mf.resource_id,
                mf.oss_id,
                mf.`status`,
                mf.create_date
            FROM
            media_file mf
            INNER JOIN tenant_media_file tmf ON mf.id = tmf.media_file_id
            WHERE
            mf.limit_view = 1
            AND tmf.tenant_id = #{search.tenantId}
            <if test="search.status != null">
                AND mf.`status` = #{search.status}
            </if>
            <if test="search.fileType != null">
                AND mf.file_type = #{search.fileType}
            </if>
            <if test="search.start != null">
                AND mf.create_date <![CDATA[ > ]]> #{search.start}
            </if>
            <if test="search.end != null">
                AND mf.create_date <![CDATA[ < ]]> #{search.end}
            </if>
            <if test="search.fileName != null and search.fileName != ''">
                AND (mf.file_name LIKE CONCAT('%', #{search.fileName}, '%') OR mf.id = #{search.fileName})
            </if>
            <if test="search.createUserIds != null and search.createUserIds.size() > 0">
                AND mf.create_user_id IN
                <foreach collection="search.createUserIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        ) t
        ORDER BY
        t.create_date DESC
    </select>
</mapper>