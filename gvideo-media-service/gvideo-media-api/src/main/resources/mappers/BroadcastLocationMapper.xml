<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.media.api.repository.BroadcastLocationMapper">
    <select id="listAllByDisplayRelation" resultType="com.jxntv.gvideo.media.client.dto.BroadcastLocationDTO">
        SELECT
            t.id,
        	t.media_id,
            t.title,
        	t.broadcast_type,
            t.collection_style
        FROM
        	(
        		SELECT
					tbl.id,
        			tbl.media_id,
        			tbl.location,
                    tbl.title,
                    tbl.collection_style,
        			tbl.broadcast_type
        		FROM
        			broadcast_location tbl
        		LEFT JOIN media_resource tmr ON tbl.media_id = tmr.id
        		WHERE
        			tbl.display_relation = #{displayRelation}
        		AND tbl.broadcast_type IN (1, 2,4)
        		AND tbl.`status` = 2
        		AND tmr.`status` = 3
        		UNION ALL
        			SELECT
						tbl.id,
        				tbl.media_id,
        				tbl.location,
                        tbl.title,
                        tbl.collection_style,
        				tbl.broadcast_type
        			FROM
        				broadcast_location tbl
        			LEFT JOIN tv_channel tc ON tbl.media_id = tc.id
        			WHERE
        				tbl.display_relation = #{displayRelation}
        			AND tbl.broadcast_type = 3
        			AND tbl.`status` = 2
        			AND tc.`status` = 1
        	) t
        ORDER BY t.location ASC
    </select>

    <select id="listByDisplayRelationPage" resultType="java.lang.Long">
        SELECT
            tbl.media_id
        FROM
        	broadcast_location tbl
        LEFT JOIN media_resource tmr ON tbl.media_id = tmr.id
        WHERE
        	tbl.display_relation = #{displayRelation}
        AND tbl.`status` = 2
        AND tmr.`status` = 3
        ORDER BY tbl.location ASC
    </select>

	<update id="updateLocation">
		UPDATE broadcast_location
		SET location = location + 1
		WHERE
		display_relation = #{displayRelation}
		AND location <![CDATA[ >= ]]> #{location}
		AND `status` IN (1, 2)
		AND id <![CDATA[ <> ]]> #{id}
	</update>
</mapper>