<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.media.api.repository.ImGroupMemberMapper">
    <sql id="columnSql">
        tigm.id,
        tigm.im_group_id,
        tigm.jid,
        tigm.leader,
        tigm.apply_result,
        tigm.create_date,
        tigm.create_user_id,
        tigm.create_user_name
    </sql>

    <select id="imGroupMemberPage" resultType="com.jxntv.gvideo.media.client.dto.ImGroupMemberDTO">
        SELECT
        <include refid="columnSql"/>, cu.nickname, cu.mobile, cu.avatar
        FROM
        im_group_member tigm
        LEFT JOIN consumer_user cu ON tigm.jid = cu.jid
        <where>
            <if test="search.jid != null">
                AND tigm.jid = #{search.jid}
            </if>
            <if test="search.imGroupId != null">
                AND tigm.im_group_id = #{search.imGroupId}
            </if>
            <if test="search.mobile != null and search.mobile != ''">
                AND cu.mobile = #{search.mobile}
            </if>
            <if test="search.nickname != null and search.nickname != ''">
                AND cu.nickname LIKE CONCAT('%', #{search.nickname}, '%')
            </if>
            <if test="search.createDateStart != null">
                AND tigm.create_date <![CDATA[ >= ]]> #{search.createDateStart}
            </if>
            <if test="search.createDateEnd != null">
                AND tigm.create_date <![CDATA[ <= ]]> #{search.createDateEnd}
            </if>
        </where>
        <choose>
            <when test="search.asc">
                ORDER BY tigm.create_date ASC
            </when>
            <otherwise>
                ORDER BY tigm.create_date DESC
            </otherwise>
        </choose>
    </select>

    <select id="imGroupMemberCount" resultType="com.jxntv.gvideo.media.client.dto.ImGroupMemberCountDTO">
        SELECT
        	tigm.im_group_id, COUNT(tigm.im_group_id) AS memberCount
        FROM
        	im_group_member tigm
        WHERE
            tigm.im_group_id IN
            <foreach collection="imGroupIdList" item="imGroupId" separator="," close=")" open="(">
                 #{imGroupId}
            </foreach>
        GROUP BY
        	tigm.im_group_id
    </select>
</mapper>


