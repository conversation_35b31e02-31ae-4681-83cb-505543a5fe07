package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.jxntv.gvideo.media.client.enums.InteractiveBroadcastStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * interactive_broadcast
 *
 * <AUTHOR>
@Data
public class InteractiveBroadcast implements Serializable {
    private static final long serialVersionUID = -970287808877029628L;
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 0 未开始 1 正在直播 2 结束 3 下架
     */
    private InteractiveBroadcastStatusEnum status;
    /**
     * 直播类型
     * 1视频
     * 2语音
     */
    private Integer type;
    /**
     * 开播用户id
     */
    private Long jid;
    private Long sysUserId;
    private Long tenantId;
    private Long mediaId;
    /**
     * 点击次数
     */
    private Integer click;

    /**
     * 访问人次
     */
    private Integer view;
    /**
     * 直播标题
     */
    private String title;
    /**
     * 直播描述
     */
    private String description;
    /**
     * 直播封面
     */
    private String thumb;
    /**
     * 评论数量
     */
    private Integer commentCount;

    /**
     * 评论人数
     */
    private Integer commentPeopleCount;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 结束时间
     */
    private LocalDateTime endDate;
    private Long digg;
    private Long certificationId;
    private String liveUrl;
    /**
     * 直播嘉宾推荐开关:0-关闭，1-开启
     */
    private Integer guestRecommendSwitch;
    /**
     * 1开启连麦
     * 0关闭连麦
     */
    private Integer connectVideo;
    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Boolean delFlag;
}