package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author: niedamin
 * @Date: 2023/04/18 16:01
 */
@Data
public class AppSignPrize {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 奖品获取类型：1-兑换奖品，2-抽奖奖品
     *
     * @see com.jxntv.gvideo.media.client.enums.AppSignPrizeCategory
     */
    private Integer category;
    /**
     * 奖品类型
     *
     * @see com.jxntv.gvideo.media.client.enums.AppSignPrizeType
     */
    private Integer type;
    /**
     * 奖品名称
     */
    private String prizeName;
    /**
     * 奖品数量
     */
    private Integer prizeCount;
    /**
     * 奖品数量
     */
    private Integer availablePrizeCount;
    /**
     * 奖品图片
     */
    private String prizeImage;
    /**
     * 奖品消耗今豆
     */
    private Integer prizePrice;
    /**
     * 兑换类型 1-仅限一次 2-不限次
     */
    private Integer exchangeType;
    /**
     * 兑换限制周期 1-每天，2-每周，3-永久
     */
    private Integer exchangeCycleType;
    /**
     * 抽奖概率
     */
    private BigDecimal raffleRate;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 规则
     */
    private String rule;
    /**
     * 状态
     */
    private Integer status;

    /**
     * 微信代金券批次号
     */
    private String stockId;

    /**
     * 版本号，乐观锁实现
     */
    @Version
    private Integer version;

    /**
     * 核销日期
     */
    private LocalDateTime writeOffDate;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}
