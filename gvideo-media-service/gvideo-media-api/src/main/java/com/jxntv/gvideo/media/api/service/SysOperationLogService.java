package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.Banner;
import com.jxntv.gvideo.media.api.domain.entity.SysOperationLog;
import com.jxntv.gvideo.media.client.dto.AdvertSearchPlaceholderDTO;
import com.jxntv.gvideo.media.client.dto.SysOperationLogDTO;
import com.jxntv.gvideo.media.client.enums.AdvertSearchPlaceholderStatus;
import com.jxntv.gvideo.media.client.enums.BannerStatus;
import com.jxntv.gvideo.media.client.enums.SysOperationLogSubjectType;

/**
 * <AUTHOR>
 * @description
 * @date 2022/02/15 15:36
 */
public interface SysOperationLogService extends IService<SysOperationLog> {
    /**
     * 新增banner变更日志
     *
     * @param dbData
     * @param banner
     * @param logDTO
     */
    void addBannerLog(Banner dbData, Banner banner, SysOperationLogDTO logDTO);

    /**
     * 定时器banner状态变更日志
     *
     * @param relateId
     * @param subject
     * @param oldStatus
     * @param newStatus
     */
    void addBannerJobLog(Long relateId, String subject, SysOperationLogSubjectType subjectType, BannerStatus oldStatus, BannerStatus newStatus);

    /**
     * 新增搜索文案变更日志
     *
     * @param dbData
     * @param dto
     * @param logDTO
     */
    void addSearchPlaceholderLog(AdvertSearchPlaceholderDTO dbData, AdvertSearchPlaceholderDTO dto, SysOperationLogDTO logDTO);

    /**
     * 搜索文案状态变更日志
     *
     * @param relateId
     * @param subject
     * @param oldStatus
     * @param newStatus
     * @param logDTO
     */
    void addSearchPlaceholderStatusLog(Long relateId, String subject, AdvertSearchPlaceholderStatus oldStatus, AdvertSearchPlaceholderStatus newStatus, SysOperationLogDTO logDTO);
}
