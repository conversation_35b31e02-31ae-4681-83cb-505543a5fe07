package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.entity.BroadcastLocation;
import com.jxntv.gvideo.media.api.service.BroadcastLocationService;
import com.jxntv.gvideo.media.api.repository.BroadcastLocationMapper;
import com.jxntv.gvideo.media.client.dto.BroadcastLocationDTO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 *
 */
@Service
public class BroadcastLocationServiceImpl extends ServiceImpl<BroadcastLocationMapper, BroadcastLocation>
implements BroadcastLocationService{

    @Override
    public List<BroadcastLocationDTO> listAllByDisplayRelation(Long displayRelation) {
        return this.baseMapper.listAllByDisplayRelation(displayRelation);
    }

    @Override
    public Page<Long> listByDisplayRelationPage(Long displayRelation, Integer cursor, Integer size) {
        return this.baseMapper.listByDisplayRelationPage(new Page<>(cursor, size), displayRelation);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateLocation(Long id, Long displayRelation, Integer location) {
        return this.baseMapper.updateLocation(id, displayRelation, location);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editTitle(BroadcastLocationDTO dto) {
        LambdaQueryWrapper<BroadcastLocation> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(BroadcastLocation::getBroadcastType, dto.getBroadcastType());
        queryWrapper.eq(BroadcastLocation::getBroadcastId, dto.getBroadcastId());
        if (this.count(queryWrapper) > 0) {
            LambdaUpdateWrapper<BroadcastLocation> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.set(BroadcastLocation::getTitle, dto.getTitle());
            updateWrapper.eq(BroadcastLocation::getBroadcastType, dto.getBroadcastType());
            updateWrapper.eq(BroadcastLocation::getBroadcastId, dto.getBroadcastId());
            this.update(updateWrapper);
        }
    }
}




