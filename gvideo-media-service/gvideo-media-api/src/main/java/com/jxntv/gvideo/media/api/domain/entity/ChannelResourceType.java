package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * 频道资源类型(ChannelResourceType)实体类
 *
 *
 * @since 2020-03-23 18:38:16
 */
public class ChannelResourceType implements Serializable {
    private static final long serialVersionUID = 935296953973151398L;
    /**
    * 自增ID
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 频道ID
    */
    private Long channelId;
    /**
    * 内容类型
    */
    private Integer contentType;
    /**
    * 播放样式
    */
    private Integer playStyle;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public Integer getPlayStyle() {
        return playStyle;
    }

    public void setPlayStyle(Integer playStyle) {
        this.playStyle = playStyle;
    }

}