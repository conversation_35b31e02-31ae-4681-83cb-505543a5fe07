package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.ImGroupMember;
import com.jxntv.gvideo.media.client.dto.ImGroupMemberCountDTO;
import com.jxntv.gvideo.media.client.dto.ImGroupMemberDTO;
import com.jxntv.gvideo.media.client.dto.ImGroupMemberSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description IM群成员表
 * @date 2021/10/20 11:28
 */
public interface ImGroupMemberMapper extends BaseMapper<ImGroupMember> {
    IPage<ImGroupMemberDTO> imGroupMemberPage(@Param("page") Page<ImGroupMemberDTO> page, @Param("search") ImGroupMemberSearchDTO searchDTO);

    List<ImGroupMemberCountDTO> imGroupMemberCount(@Param("imGroupIdList") List<Long> imGroupIdList);
}
