package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.entity.TvColumnUser;
import com.jxntv.gvideo.media.api.repository.TvColumnUserMapper;
import com.jxntv.gvideo.media.api.service.TvColumnUserService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class TvColumnUserServiceImpl extends ServiceImpl<TvColumnUserMapper, TvColumnUser> implements TvColumnUserService {
    @Override
    public boolean removeByColumnId(Long columnId) {
        return this.remove(Wrappers.<TvColumnUser>lambdaQuery().eq(TvColumnUser::getColumnId, columnId));
    }

    @Override
    public List<TvColumnUser> listByColumnId(Long columnId) {
        return this.list(Wrappers.<TvColumnUser>lambdaQuery().eq(TvColumnUser::getColumnId, columnId));
    }
}
