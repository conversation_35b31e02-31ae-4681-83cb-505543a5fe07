package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.converter.MediaFileConverter;
import com.jxntv.gvideo.media.api.domain.entity.MediaFile;
import com.jxntv.gvideo.media.api.domain.entity.TenantMediaFile;
import com.jxntv.gvideo.media.api.service.MediaFileService;
import com.jxntv.gvideo.media.api.service.TenantMediaFileService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.MediaFileClient;
import com.jxntv.gvideo.media.client.dto.MediaFileDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaFileSearchDTO;
import com.jxntv.gvideo.media.client.enums.OrderBy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 影音文件管理
 * <p>
 * <p>
 * Created on 2020-02-118
 */
@RestController
public class MediaFileController implements MediaFileClient {

    @Autowired
    private MediaFileService mediaFileService;
    @Autowired
    private TenantMediaFileService tenantMediaFileService;

    @Override
    public Result<PageDTO<MediaFileDTO>> page(MediaFileSearchDTO searchDTO) {
        LambdaQueryWrapper<MediaFile> query = new LambdaQueryWrapper<>();
        if (searchDTO.getStatus() != null) {
            query.eq(MediaFile::getStatus, searchDTO.getStatus());
        }
        if (searchDTO.getFileType() != null) {
            query.eq(MediaFile::getFileType, searchDTO.getFileType());
        }
        if (StringUtils.hasText(searchDTO.getFileName())) {
            query.and(i -> i.eq(MediaFile::getId, searchDTO.getFileName()).or().like(MediaFile::getFileName, searchDTO.getFileName()));
        }
        if (!CollectionUtils.isEmpty(searchDTO.getCreateUserIds())) {
            query.in(MediaFile::getCreateUserId, searchDTO.getCreateUserIds());
        }
        if (searchDTO.getResourceId() != null) {
            query.eq(MediaFile::getResourceId, searchDTO.getResourceId());
        }
        if (searchDTO.getStart() != null) {
            query.ge(MediaFile::getCreateDate, searchDTO.getStart());
        }
        if (searchDTO.getEnd() != null) {
            query.le(MediaFile::getCreateDate, searchDTO.getEnd());
        }
        if (searchDTO.getOrderBy().equals(OrderBy.CREATE_DATE.name())) {
            if (searchDTO.isAsc()) {
                query.orderByAsc(MediaFile::getCreateDate);
            } else {
                query.orderByDesc(MediaFile::getCreateDate);
            }
        }
        IPage<MediaFile> page = mediaFileService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), query);
        return Result.ok(PageUtils.pageOf(page, MediaFileConverter::convert));
    }

    @Override
    public Result<PageDTO<MediaFileDTO>> tenantMediaFilePage(MediaFileSearchDTO searchDTO) {
        IPage<MediaFile> page = mediaFileService.tenantMediaFilePage(searchDTO);
        return Result.ok(PageUtils.pageOf(page, MediaFileConverter::convert));
    }

    @Override
    public Result<Long> create(MediaFileDTO mediaFileDTO) {
        return Result.ok(mediaFileService.saveOrUpdate(mediaFileDTO));
    }

    @Override
    public Result<Long> update(Long id, MediaFileDTO mediaFileDTO) {
        mediaFileDTO.setId(id);
        return Result.ok(mediaFileService.saveOrUpdate(mediaFileDTO));
    }

    @Override
    public Result<Void> updateFileName(Long id, String fileName) {
        mediaFileService.update(Wrappers.<MediaFile>lambdaUpdate().set(MediaFile::getFileName, fileName).eq(MediaFile::getId, id));
        return Result.ok();
    }

    @Override
    public Result<MediaFileDTO> get(Long id) {
        MediaFile mediaFile = mediaFileService.getById(id);
        if (mediaFile == null) {
            return Result.fail("文件不存在");
        } else {
            return Result.ok(MediaFileConverter.convert(mediaFile));
        }
    }

    @Override
    public Result<List<MediaFileDTO>> listByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Result.ok(Collections.emptyList());
        }
        Collection<MediaFile> mediaFiles = mediaFileService.listByIds(ids);
        return Result.ok(mediaFiles.stream().map(MediaFileConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<List<Long>> getVisibleTenantIds(Long id) {
        return Result.ok(tenantMediaFileService.list(Wrappers.<TenantMediaFile>lambdaQuery()
                .eq(TenantMediaFile::getMediaFileId, id)).stream()
                .map(TenantMediaFile::getTenantId).collect(Collectors.toList()));
    }

    @Override
    public Result limitView(Long id, Boolean limitView, List<Long> tenantIds) {
        MediaFile mediaFile = new MediaFile();
        mediaFile.setId(id);
        mediaFile.setLimitView(limitView);
        if (!mediaFileService.updateById(mediaFile)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
        tenantMediaFileService.remove(Wrappers.<TenantMediaFile>lambdaQuery().eq(TenantMediaFile::getMediaFileId, id));
        if (limitView) {
            for (Long tenantId : tenantIds) {
                TenantMediaFile tenantMediaFile = new TenantMediaFile();
                tenantMediaFile.setMediaFileId(id);
                tenantMediaFile.setTenantId(tenantId);
                if (!tenantMediaFileService.save(tenantMediaFile)) {
                    throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
                }
            }
        }
        return Result.ok();
    }

    @Override
    public Result<List<MediaFileDTO>> fuzzy(Long tenantId, String fileName, Integer fileType, Integer fileStatus) {
        if (StringUtils.isEmpty(fileName)) {
            return Result.fail("关键字不能为空");
        }
        LambdaQueryWrapper<MediaFile> query = Wrappers.lambdaQuery();
        query.like(MediaFile::getFileName, fileName);
        if (fileType != null) {
            query.eq(MediaFile::getFileType, fileType);
        }
        if (fileStatus != null) {
            query.eq(MediaFile::getStatus, fileStatus);
        }
        List<MediaFile> list = mediaFileService.list(query);
        List<MediaFile> filterList = new ArrayList<>();
        if (tenantId != null) {
            for (MediaFile mediaFile : list) {
                if (mediaFile.getLimitView()) {
                    if (tenantMediaFileService.canView(tenantId, mediaFile.getId())) {
                        filterList.add(mediaFile);
                    }
                } else {
                    filterList.add(mediaFile);
                }
            }
        }
        return Result.ok(filterList.stream().map(MediaFileConverter::convert).collect(Collectors.toList()));
    }
}
