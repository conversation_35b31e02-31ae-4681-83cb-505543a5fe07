package com.jxntv.gvideo.media.api.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.event.CharityActivityEnrollAuditMsgEvent;
import com.jxntv.gvideo.media.api.service.CharityActivityEnrollService;
import com.jxntv.gvideo.media.api.service.CharityActivityService;
import com.jxntv.gvideo.media.client.CharityActivityClient;
import com.jxntv.gvideo.media.client.dto.charity.*;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 公益活动控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(value = "公益活动相关接口", tags = {"公益活动相关接口"})
public class CharityActivityController implements CharityActivityClient {

    @Resource
    private CharityActivityService charityActivityService;

    @Resource
    private CharityActivityEnrollService charityActivityEnrollService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public Result<PageDTO<CharityActivityDTO>> page(CharityActivitySearchDTO searchDTO) {
        return Result.ok(charityActivityService.page(searchDTO));
    }

    @Override
    public Result<CharityActivityDTO> getActivityById(Long id) {
        return Result.ok(charityActivityService.getActivityById(id));
    }

    @Override
    public Result<PageDTO<CharityActivityEnrollDTO>> enrollPage(CharityActivityEnrollSearchDTO searchDTO) {
        return Result.ok(charityActivityEnrollService.page(searchDTO));
    }

    @Override
    public Result<Boolean> activityEnroll(CharityActivityEnrollDTO dto) {
        return Result.ok(charityActivityEnrollService.activityEnroll(dto));
    }

    @Override
    public Result<List<CharityActivityEnrollDTO>> queryEnrollListByJid(Long activityId, Long jid) {
        return Result.ok(charityActivityEnrollService.queryEnrollListByJid(activityId, jid));
    }

    @Override
    public Result<Boolean> enrollAudit(CharityActivityEnrollAuditDTO dto) {
        boolean status = charityActivityEnrollService.enrollAudit(dto);
        if (status){
            applicationEventPublisher.publishEvent(new CharityActivityEnrollAuditMsgEvent(dto.getId()));
        }
        return Result.ok(status);
    }

    @Override
    public Result<CharityActivityEnrollDTO> queryEnrollInfoById(Long id) {
        return Result.ok(charityActivityEnrollService.queryEnrollInfoById(id));
    }
}
