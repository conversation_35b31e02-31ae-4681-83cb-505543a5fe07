package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

@TableName(value = "hongdujuan_judge_score")
public class HongdujuanJudgeScore {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 评委 id
     */
    @TableField(value = "judge_id")
    private Integer judgeId;

    /**
     * 作品 id
     */
    @TableField(value = "product_id")
    private Integer productId;

    /**
     * 打分状态：1：未打分，2：已打分，3：锁定打分
     */
    @TableField(value = "`state`")
    private Integer state;

    /**
     * 分数
     */
    @TableField(value = "score")
    private Integer score;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private Date updateTime;

    /**
     * 获取主键
     *
     * @return id - 主键
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置主键
     *
     * @param id 主键
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取评委 id
     *
     * @return judge_id - 评委 id
     */
    public Integer getJudgeId() {
        return judgeId;
    }

    /**
     * 设置评委 id
     *
     * @param judgeId 评委 id
     */
    public void setJudgeId(Integer judgeId) {
        this.judgeId = judgeId;
    }

    /**
     * 获取作品 id
     *
     * @return product_id - 作品 id
     */
    public Integer getProductId() {
        return productId;
    }

    /**
     * 设置作品 id
     *
     * @param productId 作品 id
     */
    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    /**
     * 获取打分状态：1：未打分，2：已打分，3：锁定打分
     *
     * @return state - 打分状态：1：未打分，2：已打分，3：锁定打分
     */
    public Integer getState() {
        return state;
    }

    /**
     * 设置打分状态：1：未打分，2：已打分，3：锁定打分
     *
     * @param state 打分状态：1：未打分，2：已打分，3：锁定打分
     */
    public void setState(Integer state) {
        this.state = state;
    }

    /**
     * 获取分数
     *
     * @return score - 分数
     */
    public Integer getScore() {
        return score;
    }

    /**
     * 设置分数
     *
     * @param score 分数
     */
    public void setScore(Integer score) {
        this.score = score;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}