package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.client.ConsumerUserService;
import com.jxntv.gvideo.media.api.domain.entity.InteractiveBroadcast;
import com.jxntv.gvideo.media.api.domain.entity.InteractiveBroadcastBan;
import com.jxntv.gvideo.media.api.repository.InteractiveBroadcastBanMapper;
import com.jxntv.gvideo.media.api.repository.InteractiveBroadcastMapper;
import com.jxntv.gvideo.media.api.service.InteractiveBroadcastService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.api.utils.LocalDateTimeUtils;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.InteractiveBroadcastStatusEnum;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerInfoDTO;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR> Created on 2020/12/28.
 */
@Service
public class InteractiveBroadcastServiceImpl extends ServiceImpl<InteractiveBroadcastMapper, InteractiveBroadcast> implements InteractiveBroadcastService {
    @Autowired
    private InteractiveBroadcastMapper broadcastMapper;
    @Autowired
    private InteractiveBroadcastBanMapper broadcastBanMapper;
    @Autowired
    private ConsumerUserService consumerUserService;
    @Resource
    private SysUserClient sysUserService;
    @Autowired
    private MediaResourceService mediaResourceService;
    @Autowired
    private InteractiveBroadcastBanMapper banMapper;

    @Override
    public PageDTO<InteractiveBroadcastDTO> pageQuery(InteractiveBroadcastSearchDTO searchDTO) {
        Long tenantId = searchDTO.getTenantId();
        Integer broadcastStatus = searchDTO.getBroadcastStatus();
        LambdaQueryWrapper<InteractiveBroadcast> query = Wrappers.lambdaQuery();
        query.eq(Objects.nonNull(tenantId), InteractiveBroadcast::getTenantId, tenantId);
        query.eq(Objects.nonNull(broadcastStatus), InteractiveBroadcast::getStatus, broadcastStatus);
        query.like(StringUtils.isNotEmpty(searchDTO.getTitle()), InteractiveBroadcast::getTitle, searchDTO.getTitle());
        IPage<InteractiveBroadcast> rst = broadcastMapper.selectPage(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), query.orderByDesc(InteractiveBroadcast::getCreatedDate));
        return PageUtils.pageOf(rst, (item) -> {
            InteractiveBroadcastDTO dto = new InteractiveBroadcastDTO();
            dto.setBeginDate(LocalDateTimeUtils.format(item.getCreatedDate()));
            dto.setEndDate(LocalDateTimeUtils.format(item.getEndDate()));
            Result<ConsumerInfoDTO> consumerUser = consumerUserService.getInfoByJid(item.getJid());
            if (consumerUser.callSuccess()) {
                dto.setBroadcasterName(consumerUser.getResult().getNickname());
            }
            dto.setTitle(item.getTitle());
            dto.setBroadcastStatus(item.getStatus().getName());
            dto.setThumb(item.getThumb());
            dto.setViewStat(item.getClick() + "/" + item.getView());
            dto.setCommentStat(item.getCommentPeopleCount() + "/" + item.getCommentCount());
            dto.setId(item.getId());
            dto.setStatus(item.getStatus().getCode());
            Integer banCount = banMapper.selectCount(Wrappers.<InteractiveBroadcastBan>lambdaQuery().eq(InteractiveBroadcastBan::getSysUserId, item.getSysUserId()));
            dto.setBan(banCount);
            dto.setMediaId(item.getMediaId());
            return dto;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdate(InteractiveBroadcastSaveDTO broadcastDTO) throws Exception {
        InteractiveBroadcast broadcast = new InteractiveBroadcast();
        broadcast.setJid(broadcastDTO.getJid());
        broadcast.setType(broadcastDTO.getType());
        broadcast.setStatus(InteractiveBroadcastStatusEnum.LIVING);
        broadcast.setTitle(broadcastDTO.getTitle());
        broadcast.setDescription(broadcastDTO.getDescription());
        broadcast.setThumb(broadcastDTO.getThumb());
        broadcast.setLiveUrl(broadcastDTO.getLiveUrl());
        broadcast.setGuestRecommendSwitch(broadcastDTO.getGuestRecommendSwitch());
        String username = broadcastDTO.getUsername();
        Result<SysUserDTO> userRst = sysUserService.getUserInfo(username);
        Result<Long> tenantRst = sysUserService.getPrimary(username);
        if (!userRst.callSuccess()) {
            throw new Exception(userRst.getMessage());
        }
        if (!tenantRst.callSuccess()) {
            throw new Exception(tenantRst.getMessage());
        }
        broadcast.setSysUserId(userRst.getResult().getId());
        broadcast.setTenantId(tenantRst.getResult());
        broadcast.setCertificationId(broadcastDTO.getCertificationId());

        //将直播归入media resource contentType为6
        MediaResourceDTO mediaResourceDTO = new MediaResourceDTO();
        mediaResourceDTO.setContentType(ContentType.INTERACTIVE_BROADCAST.getCode());
        mediaResourceDTO.setPlayStyle(broadcastDTO.getType());
        mediaResourceDTO.setShowName(broadcastDTO.getTitle());
        mediaResourceDTO.setInternalName(broadcastDTO.getTitle() + "-" + LocalDateTimeUtils.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
        mediaResourceDTO.setReleaseId(broadcastDTO.getCertificationId());
        mediaResourceDTO.setReleaseType(0);
        SysUserDTO userInfo = sysUserService.getUserInfo(broadcastDTO.getUsername()).getResult();
        if (Objects.nonNull(userInfo)) {
            mediaResourceDTO.setTenantId(Objects.isNull(userInfo.getTenantId()) ? -1 : userInfo.getTenantId());
            mediaResourceDTO.setCreateUserId(userInfo.getId());
            mediaResourceDTO.setUpdateUserId(userInfo.getId());
        } else {
            mediaResourceDTO.setTenantId(-1L);
            mediaResourceDTO.setCreateUserId(-1L);
        }
        mediaResourceDTO.setIsComment(true);
        mediaResourceDTO.setIsSearch(true);
        mediaResourceDTO.setStatus(MediaResourceStatus.ENABLE.getCode());
        mediaResourceDTO.setContentTypeLabel(Collections.singletonList(broadcastDTO.getBroadcastLabelId()));
        MediaResourceImageDTO imageDTO = new MediaResourceImageDTO();
        imageDTO.setOssId(broadcastDTO.getThumb());
        imageDTO.setType(-1);
        mediaResourceDTO.setImages(Collections.singletonList(imageDTO));
        // 插入内容资源表
        Long mediaId = mediaResourceService.save(mediaResourceDTO);
        broadcast.setMediaId(mediaId);
        broadcastMapper.insert(broadcast);

        return broadcast.getMediaId();
    }

    @Override
    public InteractiveBroadcast findByMediaId(Long mediaId) {
        return this.getOne(Wrappers.<InteractiveBroadcast>lambdaQuery()
                .eq(InteractiveBroadcast::getMediaId, mediaId)
                .eq(InteractiveBroadcast::getDelFlag, false)
                .last("limit 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void statusModify(Long id, Integer status) {
        InteractiveBroadcast entity = broadcastMapper.selectById(id);
        InteractiveBroadcastStatusEnum code = InteractiveBroadcastStatusEnum.getByCode(status);
        if (Objects.isNull(code)) {
            return;
        }
        entity.setStatus(code);
        if (code.equals(InteractiveBroadcastStatusEnum.OFF_SHELF)) {
            entity.setEndDate(LocalDateTime.now());
        }
        broadcastMapper.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ban(Long id) {
        Integer count = broadcastBanMapper.selectCount(Wrappers.<InteractiveBroadcastBan>lambdaQuery().eq(InteractiveBroadcastBan::getSysUserId, id));
        if (count == 0) {
            InteractiveBroadcastBan broadcastBan = new InteractiveBroadcastBan();
            broadcastBan.setSysUserId(id);
            broadcastBan.setCreateTime(LocalDateTime.now());
            broadcastBanMapper.insert(broadcastBan);
        }
    }
}
