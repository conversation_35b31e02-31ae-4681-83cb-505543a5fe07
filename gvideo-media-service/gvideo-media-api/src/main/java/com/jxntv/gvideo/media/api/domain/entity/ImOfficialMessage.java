package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * IM官方私信表
 */
@Data
public class ImOfficialMessage implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户JID
     */
    private Long jid;
    /**
     * 消息类型：0-官方消息、1-爆料追加奖励消息
     */
    private Integer type;
    /**
     * 关联表ID，只有type = 1 有效
     */
    private Long relateId;
    /**
     * 内容
     */
    private String content;
    /**
     * 图片ID，多个英文逗号隔开
     */
    private String imageIds;
    /**
     * 视频ID
     */
    private String vodId;
    /**
     * 接收人类型：0-全体、1-社区、2-指定用户
     */
    private Integer toType;
    /**
     * 接收人ID，不为全体时使用对应表主键ID
     */
    private String toIds;

    /**
     * 状态：0-未发送、1-已发送、2-部分发送、3-发送失败
     */
    private Integer status;

    /**
     * 入库时间
     */
    private LocalDateTime createDate;
    /**
     * 创建人ID，默认动态规则生成
     */
    private Long createUserId;
    /**
     * 创建人
     */
    private String createUserName;
}
