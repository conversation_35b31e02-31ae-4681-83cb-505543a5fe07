package com.jxntv.gvideo.media.api.converter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.media.api.domain.entity.AppRecommendList;
import com.jxntv.gvideo.media.api.domain.entity.AppRecommendListResource;
import com.jxntv.gvideo.media.api.service.AppRecommendListResourceService;
import com.jxntv.gvideo.media.client.dto.recommend.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/18 9:42
 */
@Component
public class AppRecommendConverter {

    @Resource
    private AppRecommendListResourceService appRecommendListResourceService;


    public AppRecommendDTO convertConfig(AppRecommendList entity) {
        if (Objects.isNull(entity)){
            return null;
        }
        AppRecommendDTO dto = new AppRecommendDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setBgColor(entity.getBgColor());
        dto.setIcon(entity.getIcon());
        dto.setDataType(entity.getDataType());
        dto.setLinkUrl(entity.getLinkUrl());
        dto.setSort(entity.getSort());
        dto.setRemarks(entity.getRemarks());
        dto.setHomeDefaultNum(entity.getHomeDefaultNum());

        if (Objects.equals(0,entity.getDataType())){
            LambdaQueryWrapper<AppRecommendListResource> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(AppRecommendListResource::getListId, entity.getId());
            queryWrapper.eq(AppRecommendListResource::getDelFlag, 0);
            queryWrapper.orderByAsc(AppRecommendListResource::getSort);
            queryWrapper.orderByDesc(AppRecommendListResource::getUpdateDate);
            queryWrapper.last(" limit " + entity.getHomeDefaultNum());
            List<AppRecommendListResource> list = appRecommendListResourceService.list(queryWrapper);
            if (!CollectionUtils.isEmpty(list)){
               dto.setResourceList(list.stream().map(this::convert).collect(Collectors.toList()));
            }
        }

        return dto;
    }

    public AppRecommendListResourceDTO convert(AppRecommendListResource entity) {
        if (Objects.isNull(entity)){
            return null;
        }
        AppRecommendListResourceDTO dto = new AppRecommendListResourceDTO();
        dto.setId(entity.getId());
        dto.setListId(entity.getListId());
        dto.setResourceId(entity.getResourceId());
        dto.setContentType(entity.getContentType());
        dto.setName(entity.getName());
        dto.setIcon(entity.getIcon());
        dto.setContent(entity.getContent());
        dto.setLinkUrl(entity.getLinkUrl());
        dto.setSort(entity.getSort());
        dto.setBgColor(entity.getBgColor());
        return dto;
    }

    public AppRecommendListDTO convert(AppRecommendList entity) {
        if (Objects.isNull(entity)){
            return null;
        }
        AppRecommendListDTO dto = new AppRecommendListDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setBgColor(entity.getBgColor());
        dto.setIcon(entity.getIcon());
        dto.setDataType(entity.getDataType());
        dto.setLinkUrl(entity.getLinkUrl());
        dto.setSort(entity.getSort());
        dto.setRemarks(entity.getRemarks());
        dto.setHomeDefaultNum(entity.getHomeDefaultNum());
        return dto;
    }


    public AppRecommendListResource convert(AppRecommendListResourceEditDTO dto) {
        if (Objects.isNull(dto)){
            return null;
        }
        AppRecommendListResource entity = new AppRecommendListResource();
        entity.setId(dto.getId());
        entity.setListId(dto.getListId());
        entity.setResourceId(dto.getResourceId());
        entity.setContentType(dto.getContentType());
        entity.setName(dto.getName());
        entity.setContent(dto.getContent());
        entity.setIcon(dto.getIcon());
        entity.setBgColor(dto.getBgColor());
        entity.setLinkUrl(dto.getLinkUrl());
        entity.setSort(dto.getSort());
        entity.setDelFlag(0);
        if (Objects.isNull(entity.getId())){
            entity.setCreateUserId(dto.getOperateUserId());
            entity.setCreateDate(new Date());
        }else{
            entity.setUpdateUserId(dto.getOperateUserId());
            entity.setUpdateDate(new Date());
        }

        return entity;
    }

    public AppRecommendList convert(AppRecommendListEditDTO dto) {
        if (Objects.isNull(dto)){
            return null;
        }
        AppRecommendList entity = new AppRecommendList();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setType(dto.getType());
        entity.setBgColor(dto.getBgColor());
        entity.setIcon(dto.getIcon());
        entity.setDataType(dto.getDataType());
        entity.setLinkUrl(dto.getLinkUrl());
        entity.setSort(dto.getSort());
        entity.setHomeDefaultNum(dto.getHomeDefaultNum());
        entity.setRemarks(dto.getRemarks());
        entity.setStatus(1);
        entity.setDelFlag(0);
        if (Objects.isNull(entity.getId())){
            entity.setCreateUserId(dto.getOperateUserId());
            entity.setCreateDate(new Date());
        }else{
            entity.setUpdateUserId(dto.getOperateUserId());
            entity.setUpdateDate(new Date());
        }

        return entity;
    }
}
