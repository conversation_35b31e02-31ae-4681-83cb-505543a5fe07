package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.converter.MediaResourceExternalConverter;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternalCategory;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceExternal;
import com.jxntv.gvideo.media.api.domain.entity.Source;
import com.jxntv.gvideo.media.api.repository.MediaResourceExternalCategoryMapper;
import com.jxntv.gvideo.media.api.repository.MediaResourceExternalMapper;
import com.jxntv.gvideo.media.api.repository.MediaResourceMapper;
import com.jxntv.gvideo.media.api.service.MediaResourceExternalService;
import com.jxntv.gvideo.media.api.service.SourceService;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalCategoryDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceExternalDTO;
import com.jxntv.gvideo.media.client.dto.SourceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2021/11/17 10:12
 */
@Slf4j
@Service
public class MediaResourceExternalServiceImpl extends ServiceImpl<MediaResourceExternalMapper, MediaResourceExternal> implements MediaResourceExternalService {


    @Resource
    private SourceService sourceService;
    @Resource
    private MediaResourceExternalConverter mediaResourceExternalConverter;
    @Resource
    private MediaResourceExternalCategoryMapper mediaResourceExternalCategoryMapper;


    @Override
    public MediaResourceExternal geExternal(String platform, String contentId) {
        LambdaQueryWrapper<MediaResourceExternal> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MediaResourceExternal::getPlatform, platform);
        queryWrapper.eq(MediaResourceExternal::getContentId, contentId);
        queryWrapper.last("limit 1");

        return this.getOne(queryWrapper);
    }


    @Override
    public MediaResourceExternal getByMediaId(Long mediaId) {
        LambdaQueryWrapper<MediaResourceExternal> queryWrapper = Wrappers.lambdaQuery();

        queryWrapper.eq(MediaResourceExternal::getMediaId, mediaId);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean create(MediaResourceExternalDTO dto) {
        Long sourceId = findOrCreateSource(dto.getSourceName());
        dto.setSourceId(sourceId);

        //  插入关联关系
        MediaResourceExternal entity = mediaResourceExternalConverter.convert(dto);
        boolean result = this.save(entity);

        //  插入分类信息
        List<MediaResourceExternalCategoryDTO> categoryArr = dto.getCategoryArr();
        if (!CollectionUtils.isEmpty(categoryArr)) {
            for (MediaResourceExternalCategoryDTO categoryDTO : categoryArr) {
                MediaResourceExternalCategory category = mediaResourceExternalConverter.convert(categoryDTO);
                category.setMediaId(dto.getMediaId());
                mediaResourceExternalCategoryMapper.insert(category);
            }
        }

        return result;
    }

    @Override
    public boolean update(MediaResourceExternalDTO dto) {
        Long sourceId = findOrCreateSource(dto.getSourceName());
        dto.setSourceId(sourceId);
        //  更新关联关系
        MediaResourceExternal entity = mediaResourceExternalConverter.convert(dto);
        boolean result = this.updateById(entity);

        //  更新分类信息
        List<MediaResourceExternalCategoryDTO> categoryArr = dto.getCategoryArr();
        if (!CollectionUtils.isEmpty(categoryArr)) {
            //  清理原有的分类信息
            LambdaQueryWrapper<MediaResourceExternalCategory> deleteQuery = Wrappers.lambdaQuery();
            deleteQuery.eq(MediaResourceExternalCategory::getMediaId, dto.getMediaId());

            mediaResourceExternalCategoryMapper.delete(deleteQuery);

            //  插入新的分类信息
            for (MediaResourceExternalCategoryDTO categoryDTO : categoryArr) {
                MediaResourceExternalCategory category = mediaResourceExternalConverter.convert(categoryDTO);
                category.setMediaId(dto.getMediaId());
                mediaResourceExternalCategoryMapper.insert(category);
            }
        }

        return result;
    }


    private Long findOrCreateSource(String sourceName) {
        if (StringUtils.isEmpty(sourceName)) {
            return 0L;
        }

        Source source = sourceService.getByName(sourceName);
        if (Objects.nonNull(source)) {
            return source.getId();
        }
        //新增来源
        SourceDTO sourceDTO = new SourceDTO();
        sourceDTO.setName(sourceName);
        sourceDTO.setCreateDate(new Date());
        sourceDTO.setUpdateDate(new Date());
        sourceDTO.setCreateUserId(1L);
        sourceDTO.setUpdateUserId(1L);
        return sourceService.create(sourceDTO);
    }
}
