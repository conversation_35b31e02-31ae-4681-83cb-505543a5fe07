package com.jxntv.gvideo.media.api.domain.entity.handler;


import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.media.client.dto.lottery.PrizeConfigDTO;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2025/2/11 11:25
 */
public class NewYearLotteryPrizeConfigHandler extends BaseTypeHandler<PrizeConfigDTO> {


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, PrizeConfigDTO parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public PrizeConfigDTO getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return JSON.parseObject(columnValue, PrizeConfigDTO.class);
    }

    @Override
    public PrizeConfigDTO getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        return JSON.parseObject(columnValue, PrizeConfigDTO.class);
    }

    @Override
    public PrizeConfigDTO getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        return JSON.parseObject(columnValue, PrizeConfigDTO.class);
    }
}
