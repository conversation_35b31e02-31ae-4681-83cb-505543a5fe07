package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.converter.MaterialConverter;
import com.jxntv.gvideo.media.api.elasticsearch.suggest.MaterialSuggest;
import com.jxntv.gvideo.media.api.domain.entity.Material;
import com.jxntv.gvideo.media.api.repository.MaterialMapper;
import com.jxntv.gvideo.media.api.service.MaterialService;
import com.jxntv.gvideo.media.client.dto.MaterialDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    @Autowired
    private List<PartialDataService> partialDataServiceList;
    @Autowired
    private MaterialSuggest materialSuggest;

    @Override
    public PartialDataService support(Integer type) {
        if (type == null)
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "没有素材类型");
        Optional<PartialDataService> first = partialDataServiceList.stream().filter(partialDataService -> partialDataService.getType().getCode() == type.intValue()).findFirst();
        if (first.isPresent()) {
            return first.get();
        } else {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "素材类型错误");
        }
    }

    @Override
    public MaterialDTO get(Long id) {
        Material material = getById(id);
        if (material != null) {
            MaterialDTO dto = MaterialConverter.convert(material);
            support(dto.getType()).fill(dto);
            return dto;
        }
        return null;
    }

    @Override
    public Long create(MaterialDTO dto) {
        Material material = MaterialConverter.convert(dto);
        if (!save(material)) {
            throw new CodeMessageException(CodeMessage.CREATE_FAIL);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("suggest", material.getName());
        map.put("meta_data", material.getType().toString());
        materialSuggest.index(material.getId().toString(), map);
        return material.getId();
    }

    @Transactional
    @Override
    public void update(MaterialDTO dto) {
        Material old = getById(dto.getId());
        if (old == null)
            throw new CodeMessageException(CodeMessage.NOT_FOUND);

        Long childId = old.getChildId();
        if (old.getType().equals(dto.getType())) {
            //类型没变就直接更新原来的子数据
            support(old.getType()).update(childId, dto);
        } else {
            //变了的话先把之前类型的子数据删除
            support(old.getType()).delete(childId);
            //插入新类型子数据
            childId = support(dto.getType()).create(dto);
        }

        //更新下主数据
        Material newM = MaterialConverter.convert(dto);
        newM.setChildId(childId);
        if (!updateById(newM)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
    }
}
