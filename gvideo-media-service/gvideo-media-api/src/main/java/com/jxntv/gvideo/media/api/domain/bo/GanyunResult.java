package com.jxntv.gvideo.media.api.domain.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
public class GanyunResult {

    @JsonProperty("state")
    private Boolean state;
    @JsonProperty("error")
    private String error;
    @JsonProperty("data")
    private DataDTO data;

    @Data
    @NoArgsConstructor
    public static class DataDTO {
        @JsonProperty("maxId")
        private Integer maxId;
        @JsonProperty("list")
        private List<ListDTO> list;


        @Data
        @NoArgsConstructor
        public static class ListDTO {
            @JsonProperty("contentId")
            private Integer contentId;
            @JsonProperty("title")
            private String title;
            @JsonProperty("status")
            private Integer status;
            @JsonProperty("audit_step")
            private Integer auditStep;
            @JsonProperty("createTime")
            private String createTime;
            @JsonProperty("publishTime")
            private String publishTime;
            @JsonProperty("categoryId")
            private Integer categoryId;
            @JsonProperty("cName")
            private String cName;
            @JsonProperty("desc")
            private String desc;
            @JsonProperty("url")
            private String url;
            @JsonProperty("duration")
            private Integer duration;
            @JsonProperty("thumb")
            private String thumb;
            @JsonProperty("labels")
            private String labels;
        }
    }
}
