package com.jxntv.gvideo.media.api.domain.entity.handler;


import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.media.client.dto.quiz.QuizActivityConfig;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 * @date 2025/2/11 11:25
 */
public class QuizActivityConfigHandler extends BaseTypeHandler<QuizActivityConfig> {


    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, QuizActivityConfig parameter, JdbcType jdbcType) throws SQLException {
        ps.setString(i, JSON.toJSONString(parameter));
    }

    @Override
    public QuizActivityConfig getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String columnValue = rs.getString(columnName);
        return JSON.parseObject(columnValue, QuizActivityConfig.class);
    }

    @Override
    public QuizActivityConfig getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String columnValue = rs.getString(columnIndex);
        return JSON.parseObject(columnValue, QuizActivityConfig.class);
    }

    @Override
    public QuizActivityConfig getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String columnValue = cs.getString(columnIndex);
        return JSON.parseObject(columnValue, QuizActivityConfig.class);
    }
}
