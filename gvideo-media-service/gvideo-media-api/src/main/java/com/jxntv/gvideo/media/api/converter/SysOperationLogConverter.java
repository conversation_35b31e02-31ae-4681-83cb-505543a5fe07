package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.media.api.domain.entity.SysOperationLog;
import com.jxntv.gvideo.media.client.dto.SysOperationLogDTO;
import com.jxntv.gvideo.media.client.dto.SysOperationLogPageDTO;
import com.jxntv.gvideo.media.client.enums.SysOperationLogSubjectType;
import com.jxntv.gvideo.media.client.enums.SysOperationLogField;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SysOperationLogConverter {
    @Resource
    private OssClient ossClient;

    public SysOperationLogPageDTO toDTO(SysOperationLog entity) {
        SysOperationLogPageDTO sysOperationLogPageDTO = new SysOperationLogPageDTO();
        sysOperationLogPageDTO.setSubjectId(entity.getSubjectId());
        sysOperationLogPageDTO.setSubject(entity.getSubject());
        sysOperationLogPageDTO.setAction(entity.getAction().getValue());
        if (SysOperationLogField.BANNER_IMAGE.equals(entity.getChangeField())) {
            List<String> ossIds = new ArrayList<>(2);
            ossIds.add(entity.getBeforeValue());
            ossIds.add(entity.getAfterValue());
            if (!CollectionUtils.isEmpty(ossIds)) {
                List<OssDTO> ossDTOList = ossClient.listOriFileByIds(ossIds).orElse(Collections.EMPTY_LIST);
                Map<String, OssDTO> ossDTOMap = ossDTOList.stream().collect(Collectors.toMap(OssDTO::getUuid, e -> e, (a, b) -> a));
                OssDTO oldOssDTO = ossDTOMap.get(entity.getBeforeValue());
                if (Objects.nonNull(oldOssDTO)) {
                    sysOperationLogPageDTO.setBeforeValue(oldOssDTO.getUrl());
                }
                OssDTO newOssDTO = ossDTOMap.get(entity.getAfterValue());
                if (Objects.nonNull(newOssDTO)) {
                    sysOperationLogPageDTO.setAfterValue(newOssDTO.getUrl());
                }
            }
        } else {
            sysOperationLogPageDTO.setBeforeValue(entity.getBeforeValue());
            sysOperationLogPageDTO.setAfterValue(entity.getAfterValue());
        }
        sysOperationLogPageDTO.setCreateDate(entity.getCreateDate());
        sysOperationLogPageDTO.setCreateUserName(entity.getCreateUserName());
        sysOperationLogPageDTO.setSubjectType(entity.getSubjectType().getValue());
        sysOperationLogPageDTO.setChangeField(entity.getChangeField().getValue());
        return sysOperationLogPageDTO;
    }

    public SysOperationLog buildLog(SysOperationLogDTO dto, String subject, SysOperationLogSubjectType subjectType) {
        SysOperationLog optionLog = new SysOperationLog();
        optionLog.setCreateTenantId(dto.getTenantId());
        optionLog.setCreateTenantName(dto.getOptionTenantName());
        optionLog.setCreateUserId(dto.getOptionUserId());
        optionLog.setCreateUserName(dto.getOptionUserName());
        optionLog.setCreateDate(LocalDateTime.now());
        optionLog.setSubject(subject);
        optionLog.setSubjectType(subjectType);
        return optionLog;
    }

}
