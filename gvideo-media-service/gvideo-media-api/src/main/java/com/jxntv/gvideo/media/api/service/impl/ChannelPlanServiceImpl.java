package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.domain.entity.ChannelPlan;
import com.jxntv.gvideo.media.api.repository.ChannelPlanMapper;
import com.jxntv.gvideo.media.api.repository.PlanResourceMapper;
import com.jxntv.gvideo.media.api.service.ChannelPlanService;
import com.jxntv.gvideo.media.api.service.ChannelPlanUpdateService;
import com.jxntv.gvideo.media.api.service.PlanResourceService;
import com.jxntv.gvideo.media.client.dto.ChannelPlanDTO;
import com.jxntv.gvideo.media.client.enums.ChannelPlanStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;

@Service
public class ChannelPlanServiceImpl extends ServiceImpl<ChannelPlanMapper, ChannelPlan>
        implements ChannelPlanService {

    @Autowired
    private ChannelPlanUpdateService updateService;
    @Autowired
    private PlanResourceService planResourceService;
    @Autowired
    private PlanResourceMapper planResourceMapper;

    @Override
    public List<ChannelPlan> listByChannelId(Long channelId) {
        LambdaQueryWrapper<ChannelPlan> query = Wrappers.lambdaQuery();
        query.eq(ChannelPlan::getChannelId, channelId);
        return list(query);
    }

    @Transactional
    @Override
    public void update(ChannelPlanDTO dto) {
        ChannelPlan db = getById(dto.getId());
        //是否已开始投放
        boolean isStart = updateService.isStart(db);
        //是否修改当前位置
        boolean isUpdatePos = updateService.isUpdatePos(db, dto);
        //是否修改投放周期
        boolean isUpdateDate = updateService.isUpdateDate(db, dto);

        StringBuffer sb = new StringBuffer("update");
        sb.append(isStart ? 1 : 0);
        sb.append(isUpdatePos ? 1 : 0);
        sb.append(isUpdateDate ? 1 : 0);
        String execMethodName = sb.toString();
        for (Method method : this.getClass().getDeclaredMethods()) {
            if (method.getName().equals(execMethodName)) {
                try {
                    method.invoke(this, db, dto);
                } catch (Exception ex) {
                    throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
                }
            }
        }
    }

    @Override
    public void deletePlan(Long id, Long operatorId) {
        ChannelPlan plan = getById(id);
        if (plan == null) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND);
        }
        if (plan.getStartDate().isBefore(LocalDateTime.now())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "已开始计划不能删除");
        }
        ChannelPlan channelPlan = new ChannelPlan();
        channelPlan.setId(id);
        channelPlan.setOperatorId(operatorId);
        channelPlan.setStatus(ChannelPlanStatus.DELETED.getCode());
        channelPlan.setDeleteDate(LocalDateTime.now());
        if (!updateById(channelPlan)) {
            throw new CodeMessageException(CodeMessage.DELETE_FAIL);
        }
        planResourceMapper.overByPlanId(id);
    }

    @Override
    public void overPlan(Long id, Long operatorId) {
        ChannelPlan plan = getById(id);
        if (plan == null) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND);
        }
        if (LocalDateTime.now().isBefore(plan.getStartDate())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "已开始的计划才能终止");
        }
        if (plan.getStatus().equals(ChannelPlanStatus.END.getCode())) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST, "计划已结束");
        }
        ChannelPlan channelPlan = new ChannelPlan();
        channelPlan.setId(id);
        channelPlan.setOperatorId(operatorId);
        channelPlan.setStatus(ChannelPlanStatus.END.getCode());
        channelPlan.setEndDate(LocalDateTime.now());
        if (!updateById(channelPlan)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
        planResourceMapper.overByPlanId(id);
    }

    public void update111(ChannelPlan db, ChannelPlanDTO dto) {
        updateService.oldPlanEnd(db.getId(), dto.getOperatorId());
        ChannelPlan plan = new ChannelPlan();
        plan.setName(dto.getName());
        plan.setChannelId(db.getChannelId());
        plan.setOperatorId(dto.getOperatorId());
        plan.setPos(dto.getPos());
        plan.setStartDate(dto.getStartDate());
        plan.setEndDate(dto.getEndDate());
        updateService.newPlan(plan, planResourceService.listByPlanId(db.getId()));
    }

    public void update110(ChannelPlan db, ChannelPlanDTO dto) {
        updateService.oldPlanEnd(db.getId(), dto.getOperatorId());
        ChannelPlan plan = new ChannelPlan();
        plan.setName(dto.getName());
        plan.setChannelId(db.getChannelId());
        plan.setOperatorId(dto.getOperatorId());
        plan.setPos(dto.getPos());
        plan.setStartDate(LocalDateTime.now());
        plan.setEndDate(dto.getEndDate());
        updateService.newPlan(plan, planResourceService.listByPlanId(db.getId()));
    }

    public void update101(ChannelPlan db, ChannelPlanDTO dto) {
        boolean isUpdateStartDate = updateService.isUpdateStartDate(db, dto);
        //修改开始时间（向后）
        if (isUpdateStartDate) {
            updateService.oldPlanEnd(db.getId(), dto.getOperatorId());
            ChannelPlan plan = new ChannelPlan();
            plan.setName(dto.getName());
            plan.setChannelId(db.getChannelId());
            plan.setOperatorId(dto.getOperatorId());
            plan.setPos(db.getPos());
            plan.setStartDate(dto.getStartDate());
            plan.setEndDate(dto.getEndDate());
            updateService.newPlan(plan, planResourceService.listByPlanId(db.getId()));
        } else {
            //仅修改结束时间
            updateService.oldPlanDate(db.getId(), dto.getOperatorId(), db.getStartDate(), dto.getEndDate());
        }
    }

    public void update011(ChannelPlan db, ChannelPlanDTO dto) {
        updateService.oldPlanDelete(db.getId(), dto.getOperatorId());
        ChannelPlan plan = new ChannelPlan();
        plan.setName(dto.getName());
        plan.setChannelId(db.getChannelId());
        plan.setOperatorId(dto.getOperatorId());
        plan.setPos(dto.getPos());
        plan.setStartDate(dto.getStartDate());
        plan.setEndDate(dto.getEndDate());
        updateService.newPlan(plan, planResourceService.listByPlanId(db.getId()));
    }

    public void update010(ChannelPlan db, ChannelPlanDTO dto) {
        updateService.oldPlanDelete(db.getId(), dto.getOperatorId());
        ChannelPlan plan = new ChannelPlan();
        plan.setName(dto.getName());
        plan.setChannelId(db.getChannelId());
        plan.setOperatorId(dto.getOperatorId());
        plan.setPos(dto.getPos());
        plan.setStartDate(db.getStartDate());
        plan.setEndDate(db.getEndDate());
        updateService.newPlan(plan, planResourceService.listByPlanId(db.getId()));
    }

    public void update001(ChannelPlan db, ChannelPlanDTO dto) {
        updateService.oldPlanDate(db.getId(), dto.getOperatorId(), dto.getStartDate(), dto.getEndDate());
    }

    public void update000(ChannelPlan db, ChannelPlanDTO dto) {
        ChannelPlan plan = new ChannelPlan();
        plan.setName(dto.getName());
        plan.setId(dto.getId());
        updateById(plan);
    }
}