package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.converter.AiDrawConvert;
import com.jxntv.gvideo.media.api.domain.entity.AiDrawTask;
import com.jxntv.gvideo.media.api.repository.AiDrawTaskMapper;
import com.jxntv.gvideo.media.api.service.AiDrawTaskService;
import com.jxntv.gvideo.media.client.dto.AiDrawTaskDTO;
import com.jxntv.gvideo.media.client.dto.AiDrawTaskSearchDTO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author: niedamin
 * @Date: 2022/09/29 16:29
 */
@Service
public class AiDrawTaskServiceImpl extends ServiceImpl<AiDrawTaskMapper, AiDrawTask> implements AiDrawTaskService {

    @Resource
    private AiDrawConvert aiDrawConvert;

    @Override
    public IPage<AiDrawTask> page(AiDrawTaskSearchDTO searchDTO) {
        LambdaQueryWrapper<AiDrawTask> lambdaQuery = new LambdaQueryWrapper<>();
        lambdaQuery.eq(Objects.nonNull(searchDTO.getStatus()), AiDrawTask::getStatus, searchDTO.getStatus());
        lambdaQuery.like(Objects.nonNull(searchDTO.getSubmitUser()), AiDrawTask::getSubmitUser, searchDTO.getSubmitUser());
        lambdaQuery.like(Objects.nonNull(searchDTO.getKeyWord()), AiDrawTask::getKeyWord, searchDTO.getKeyWord());
        lambdaQuery.orderByDesc(AiDrawTask::getCreateDate);
        return this.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), lambdaQuery);
    }

    @Override
    public Result<String> submit(AiDrawTaskDTO aiDrawTaskDTO) {
        AiDrawTask drawTask = aiDrawConvert.convert(aiDrawTaskDTO);
        this.save(drawTask);
        return Result.ok(aiDrawTaskDTO.getTaskId());
    }

    @Override
    public void update(AiDrawTaskDTO aiDrawTaskDTO) {
        LambdaUpdateWrapper<AiDrawTask> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(AiDrawTask::getTaskId, aiDrawTaskDTO.getTaskId());
        updateWrapper.set(Objects.nonNull(aiDrawTaskDTO.getStatus()), AiDrawTask::getStatus, aiDrawTaskDTO.getStatus());
        updateWrapper.set(Objects.nonNull(aiDrawTaskDTO.getCompleteDate()), AiDrawTask::getCompleteDate, aiDrawTaskDTO.getCompleteDate());
        updateWrapper.set(Objects.nonNull(aiDrawTaskDTO.getImageUrl()), AiDrawTask::getImageUrl, aiDrawTaskDTO.getImageUrl());
        this.update(updateWrapper);
    }
}
