package com.jxntv.gvideo.media.api.service.impl;

import com.jxntv.gvideo.media.api.domain.entity.MediaPageViewConfigFigureSetting;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.event.MediaRealPvEvent;
import com.jxntv.gvideo.media.api.event.MediaVirtualPvEvent;
import com.jxntv.gvideo.media.api.service.MediaPageViewConfigFigureSettingService;
import com.jxntv.gvideo.media.api.service.MediaPageViewService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.client.constants.RedisKeys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.Random;

/**
 * 资源浏览量服务
 */
@Slf4j
@Service
public class MediaPageViewServiceImpl implements MediaPageViewService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private MediaResourceService mediaResourceService;
    @Resource
    private MediaPageViewConfigFigureSettingService mediaPageViewConfigFigureSettingService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public Long increment(Long mediaId) {
        Boolean hasKey = stringRedisTemplate.opsForHash().hasKey(RedisKeys.Media.REAL_PV_HASH, String.valueOf((mediaId)));
        if (!Boolean.TRUE.equals(hasKey)) {
            initCache(mediaId);
        }

        Long sum = stringRedisTemplate.opsForHash().increment(RedisKeys.Media.REAL_PV_HASH, String.valueOf(mediaId), 1);
        //  发送pv增加事件
        applicationEventPublisher.publishEvent(new MediaRealPvEvent(mediaId, sum));

        return sum;
    }

    @Override
    public Long get(Long mediaId) {
        return this.getReal(mediaId) + this.getVirtual(mediaId);
    }

    @Override
    public Long getReal(Long mediaId) {
        Object value = stringRedisTemplate.opsForHash().get(RedisKeys.Media.REAL_PV_HASH, String.valueOf(mediaId));
        if (Objects.isNull(value)) {
            initCache(mediaId);
            value = stringRedisTemplate.opsForHash().get(RedisKeys.Media.REAL_PV_HASH, String.valueOf(mediaId));
        }
        return Objects.isNull(value) ? 0 : Long.parseLong(value.toString());
    }

    @Override
    public Long getVirtual(Long mediaId) {
        Object value = stringRedisTemplate.opsForHash().get(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId));
        if (Objects.isNull(value)) {
            initCache(mediaId);
            value = stringRedisTemplate.opsForHash().get(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId));
        }
        return Objects.isNull(value) ? 0 : Long.parseLong(value.toString());
    }

    @Override
    public Long addVirtual(Long mediaId, Long length) {
        Boolean hasKey = stringRedisTemplate.opsForHash().hasKey(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId));
        if (!Boolean.TRUE.equals(hasKey)) {
            initCache(mediaId);
        }
        Long sum = stringRedisTemplate.opsForHash().increment(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId), length);
        //  发送虚拟PV变更事件
        applicationEventPublisher.publishEvent(new MediaVirtualPvEvent(mediaId, sum));

        return sum;
    }

    @Override
    public Long setVirtual(Long mediaId, Long sum) {
        Boolean hasKey = stringRedisTemplate.opsForHash().hasKey(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId));
        if (!Boolean.TRUE.equals(hasKey)) {
            initCache(mediaId);
        }
        stringRedisTemplate.opsForHash().put(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId), String.valueOf(sum));
        //  发送虚拟PV变更事件
        applicationEventPublisher.publishEvent(new MediaVirtualPvEvent(mediaId, sum));

        return sum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculate(Long mediaId) {
        MediaPageViewConfigFigureSetting figureSetting = mediaPageViewConfigFigureSettingService.getByMediaId(mediaId);
        if (Objects.nonNull(figureSetting)) {
            //  重新计算虚拟值
            int random = getRandom(figureSetting.getMaxValue(), figureSetting.getMinValue());
            //  当前虚拟值
            Long virtual = this.getVirtual(mediaId);
            //  仅大于时更新
            if (random > virtual) {
                stringRedisTemplate.opsForHash().put(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId), String.valueOf(random));

                applicationEventPublisher.publishEvent(new MediaVirtualPvEvent(mediaId, (long) random));
            }
            return true;
        } else {
            return false;
        }
    }

    private int getRandom(int max, int min) {
        return min + new Random().nextInt(max - min);
    }


    /**
     * 初始化pv缓存
     *
     * @param mediaId 资源id
     */
    private void initCache(Long mediaId) {
        //  初始化value
        MediaResource mediaResource = mediaResourceService.getById(String.valueOf(mediaId));
        if (Objects.nonNull(mediaResource)) {
            String pv = String.valueOf(mediaResource.getPv());
            String virtualPv = String.valueOf(mediaResource.getVirtualPv());

            stringRedisTemplate.opsForHash().putIfAbsent(RedisKeys.Media.REAL_PV_HASH, String.valueOf(mediaId), pv);
            stringRedisTemplate.opsForHash().putIfAbsent(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId), virtualPv);
        } else {
            //  给不存在的id，设置0值，避免击穿
            stringRedisTemplate.opsForHash().putIfAbsent(RedisKeys.Media.REAL_PV_HASH, String.valueOf(mediaId), "0");
            stringRedisTemplate.opsForHash().putIfAbsent(RedisKeys.Media.VIRTUAL_PV_HASH, String.valueOf(mediaId), "0");
        }
    }


}
