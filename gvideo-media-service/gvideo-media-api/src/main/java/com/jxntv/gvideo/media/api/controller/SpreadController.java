package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.converter.SpreadConverter;
import com.jxntv.gvideo.media.api.domain.entity.Spread;
import com.jxntv.gvideo.media.api.domain.entity.SpreadLog;
import com.jxntv.gvideo.media.api.service.SpreadLogService;
import com.jxntv.gvideo.media.api.service.SpreadService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.SpreadClient;
import com.jxntv.gvideo.media.client.dto.SpreadDTO;
import com.jxntv.gvideo.media.client.dto.SpreadLogDTO;
import com.jxntv.gvideo.media.client.dto.search.SpreadSearchDTO;
import com.jxntv.gvideo.media.client.enums.SpreadStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;

/**
 * 开屏管理
 *
 *
 * Created on 2020-04-15
 */
@Slf4j
@RestController
public class SpreadController implements SpreadClient {

    @Autowired
    private SpreadService spreadService;
    @Autowired
    private SpreadLogService spreadLogService;

    @Override
    public Result<PageDTO<SpreadDTO>> page(SpreadSearchDTO dto) {
        LambdaQueryWrapper<Spread> query = Wrappers.lambdaQuery();
        if (dto.getStatus() != null) {
            query.eq(Spread::getStatus, dto.getStatus());
            if (dto.getStatus().equals(SpreadStatus.ING.getCode())) {
                query.orderByDesc(Spread::getCreateDate);
            }
            if (dto.getStatus().equals(SpreadStatus.END.getCode())) {
                query.orderByDesc(Spread::getEndDate);
            }
            if (dto.getStatus().equals(SpreadStatus.DELETED.getCode())) {
                query.orderByDesc(Spread::getDeleteDate);
            }
        }
        if (CollectionUtils.isNotEmpty(dto.getMaterialIds())) {
            query.in(Spread::getMaterialId, dto.getMaterialIds());
        }
        if (CollectionUtils.isNotEmpty(dto.getOperatorIds())) {
            query.in(Spread::getOperatorId, dto.getOperatorIds());
        }
        IPage<Spread> page = spreadService.page(new Page(dto.getCurrent(), dto.getSize()), query);
        putBaseFirst(page);
        return Result.ok(PageUtils.pageOf(page, SpreadConverter::convert));
    }

    private void putBaseFirst(IPage<Spread> page) {
        List<Spread> records = page.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            Optional<Spread> base = records.stream().filter(s -> s.getThrowType().equals(1)).findFirst();
            if (base.isPresent()) {
                Spread[] arr = new Spread[records.size()];
                arr[0] = base.get();

                int i = 1;
                for (Spread record : records) {
                    if (record == base.get()) {
                        continue;
                    }
                    arr[i++] = record;
                }

                page.setRecords(org.springframework.util.CollectionUtils.arrayToList(arr));
            }
        }

    }

    @Override
    public Result<Long> create(SpreadDTO dto) {
        return Result.ok(spreadService.saveOrUpdate(dto));
    }

    @Override
    public Result<SpreadDTO> get(Long id) {
        Spread spread = spreadService.getById(id);
        if (spread == null) {
            return Result.fail("开屏计划不存在");
        }
        return Result.ok(SpreadConverter.convert(spread));
    }

    @Override
    public Result<PageDTO<SpreadLogDTO>> logs(Long id, long current, long size) {
        IPage<SpreadLog> page = spreadLogService.page(new Page(current, size),
                Wrappers.<SpreadLog>lambdaQuery().eq(SpreadLog::getSpreadId, id));
        return Result.ok(PageUtils.pageOf(page, log -> {
            SpreadLogDTO dto = new SpreadLogDTO();
            dto.setId(log.getId());
            dto.setContent(log.getContent());
            dto.setOperateDate(log.getOperateDate());
            dto.setOperatorId(log.getOperatorId());
            dto.setLoginTenantId(log.getLoginTenantId());
            return dto;
        }));
    }

    @Override
    public Result update(Long id, SpreadDTO dto) {
        dto.setId(id);
        return Result.ok(spreadService.saveOrUpdate(dto));
    }

    @Override
    public Result reset() {
        List<Spread> list = spreadService.list(Wrappers.<Spread>lambdaQuery().eq(Spread::getThrowType, 1));
        if (list == null || list.size() > 1) {
            return Result.fail("没有基础开屏，或者出现多条基础开屏配置，请检查数据");
        }
        Spread spread = list.get(0);
        spread.setExposure(5);
        spread.setSkip(1);
        spread.setMaterialId(-1L);
        if (spreadService.updateById(spread)) {
            return Result.ok();
        } else {
            return Result.ok("重置失败");
        }
    }
}
