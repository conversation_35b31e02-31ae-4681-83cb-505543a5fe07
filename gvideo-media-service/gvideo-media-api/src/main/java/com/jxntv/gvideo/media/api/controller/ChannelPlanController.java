package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.converter.ChannelPlanConverter;
import com.jxntv.gvideo.media.api.converter.PlanResourceConverter;
import com.jxntv.gvideo.media.api.domain.entity.ChannelPlan;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.domain.entity.PlanResource;
import com.jxntv.gvideo.media.api.service.ChannelPlanService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.api.service.PlanResourceService;
import com.jxntv.gvideo.media.api.utils.DateUtils;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.api.utils.Period;
import com.jxntv.gvideo.media.client.ChannelPlanClient;
import com.jxntv.gvideo.media.client.dto.ChannelPlanDTO;
import com.jxntv.gvideo.media.client.dto.PlanResourceDTO;
import com.jxntv.gvideo.media.client.dto.search.ChannelPlanSearchDTO;
import com.jxntv.gvideo.media.client.enums.ChannelPlanDateType;
import com.jxntv.gvideo.media.client.enums.ChannelPlanStatus;
import com.jxntv.gvideo.media.client.enums.PlanResourceStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Slf4j
public class ChannelPlanController implements ChannelPlanClient {


    @Autowired
    private ChannelPlanService channelPlanService;
    @Autowired
    private PlanResourceService planResourceService;
    @Autowired
    private MediaResourceService mediaResourceService;

    @Override
    public Result<PageDTO<ChannelPlanDTO>> page(ChannelPlanSearchDTO searchDTO) {
        LambdaQueryWrapper<ChannelPlan> query = Wrappers.lambdaQuery();
        if (searchDTO.getChannelId() != null) {
            query.eq(ChannelPlan::getChannelId, searchDTO.getChannelId());
        }
        if (searchDTO.getStatus() != null) {
            query.eq(ChannelPlan::getStatus, searchDTO.getStatus());
            if (searchDTO.getStatus().equals(ChannelPlanStatus.DELETED.getCode())) {
                query.orderByDesc(ChannelPlan::getDeleteDate);
            } else {
                query.orderByAsc(ChannelPlan::getStartDate);
            }
//            int s = searchDTO.getStatus().intValue();
//            Date current = new Date();
//            if (s == ChannelPlanStatus.ING.getCode()) {
//                query.gt(ChannelPlan::getEndDate, current);
//            } else if (s == ChannelPlanStatus.END.getCode()) {
//                query.lt(ChannelPlan::getEndDate, current);
//            } else if (s == ChannelPlanStatus.DELETED.getCode()) {
//                query.eq(ChannelPlan::getStatus, ChannelPlanStatus.DELETED.getCode());
//            }
        }
        if (searchDTO.getPos() != null) {
            query.eq(ChannelPlan::getPos, searchDTO.getPos());
        }
        if (searchDTO.getDateType() != null) {
            int d = searchDTO.getDateType();
            if (d == ChannelPlanDateType.START.getCode()) {
                query.ge(ChannelPlan::getStartDate, searchDTO.getStart());
                query.lt(ChannelPlan::getStartDate, searchDTO.getEnd());
            } else if (d == ChannelPlanDateType.END.getCode()) {
                query.ge(ChannelPlan::getEndDate, searchDTO.getStart());
                query.lt(ChannelPlan::getEndDate, searchDTO.getEnd());
            } else if (d == ChannelPlanDateType.CREATE.getCode()) {
                query.ge(ChannelPlan::getCreateDate, searchDTO.getStart());
                query.lt(ChannelPlan::getCreateDate, searchDTO.getEnd());
            }
        }
        if (StringUtils.hasText(searchDTO.getOrderBy())) {
            if (searchDTO.getOrderBy().equals("pos")) {
                if (searchDTO.isAsc()) {
                    query.orderByAsc(ChannelPlan::getPos);
                } else {
                    query.orderByDesc(ChannelPlan::getPos);
                }
            }
            if (searchDTO.getOrderBy().equals("planStartDate")) {
                if (searchDTO.isAsc()) {
                    query.orderByAsc(ChannelPlan::getStartDate);
                } else {
                    query.orderByDesc(ChannelPlan::getStartDate);
                }
            }
            if (searchDTO.getOrderBy().equals("planEndDate")) {
                if (searchDTO.isAsc()) {
                    query.orderByAsc(ChannelPlan::getEndDate);
                } else {
                    query.orderByDesc(ChannelPlan::getEndDate);
                }
            }
            if (searchDTO.getOrderBy().equals("planCreateDate")) {
                if (searchDTO.isAsc()) {
                    query.orderByAsc(ChannelPlan::getCreateDate);
                } else {
                    query.orderByDesc(ChannelPlan::getCreateDate);
                }
            }
        }
        IPage<ChannelPlan> page = channelPlanService.page(new Page(searchDTO.getCurrent(), searchDTO.getSize()), query);
        return Result.ok(PageUtils.pageOf(page, ChannelPlanConverter::convert));
    }

    @Override
    public Result<List<ChannelPlanDTO>> list(Long channelId, int status) {
        LambdaQueryWrapper<ChannelPlan> query = Wrappers.lambdaQuery();
        query.eq(ChannelPlan::getChannelId, channelId);
        query.eq(ChannelPlan::getStatus, status);
        return Result.ok(channelPlanService.list(query).stream().map(ChannelPlanConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<ChannelPlanDTO> get(Long id) {
        ChannelPlan entity = channelPlanService.getById(id);
        if (entity == null) {
            return Result.fail("投放计划不存在");
        }
        return Result.ok(ChannelPlanConverter.convert(entity));
    }

    @Override
    public Result<Long> create(ChannelPlanDTO dto) {
        if (dto.getEndDate().isBefore(dto.getStartDate())) {
            return Result.fail("结束时间不能小于开始时间");
        }

        Period newPeriod = new Period(dto.getStartDate(), dto.getEndDate());
        String error = null;
        List<ChannelPlan> channelPlanList = channelPlanService.listByChannelId(dto.getChannelId());
        if (!CollectionUtils.isEmpty(channelPlanList)) {
            for (ChannelPlan plan : channelPlanList) {
                if (plan.getName().equals(dto.getName())) {
                    error = "频道下已有此投放计划";
                    break;
                }
                if (newPeriod.conflict(new Period(plan.getStartDate(), plan.getEndDate()))) {
                    error = "计划周期与已有投放计划冲突";
                    break;
                }
            }
        }
        if (StringUtils.hasText(error)) {
            return Result.fail(error);
        }
        ChannelPlan channelPlan = ChannelPlanConverter.convertAdd(dto);
        if (channelPlanService.save(channelPlan)) {
            return Result.ok(channelPlan.getId());
        } else {
            return Result.fail("添加投放计划失败");
        }
    }

    @Override
    public Result update(Long id, ChannelPlanDTO dto) {
        if (dto.getEndDate().isBefore(dto.getStartDate())) {
            return Result.fail("结束时间不能小于开始时间");
        }

        Period newPeriod = new Period(dto.getStartDate(), dto.getEndDate());
        String error = null;
        List<ChannelPlan> channelPlanList = channelPlanService.listByChannelId(dto.getChannelId());
        if (!CollectionUtils.isEmpty(channelPlanList)) {
            for (ChannelPlan plan : channelPlanList) {
                if (plan.getId().equals(id)) {
                    continue;
                }
                if (plan.getName().equals(dto.getName())) {
                    error = "频道下已有此投放计划";
                    break;
                }
                if (newPeriod.conflict(new Period(plan.getStartDate(), plan.getEndDate()))) {
                    error = "计划周期与已有投放计划冲突";
                    break;
                }
            }
        }
        if (StringUtils.hasText(error)) {
            return Result.fail(error);
        }
        dto.setId(id);
        channelPlanService.update(dto);
        return Result.ok();
    }

    @Override
    public Result endOrDel(Long id, Long userId, Integer op) {
        if (op == 1) {
            channelPlanService.overPlan(id, userId);
        } else {
            channelPlanService.deletePlan(id, userId);
        }
        return Result.ok();
    }

    @Override
    public Result<List<ChannelPlanDTO>> fuzzy(Long channelId, String keyword) {
        LambdaQueryWrapper<ChannelPlan> query = Wrappers.lambdaQuery();
        if (channelId != null) {
            query.eq(ChannelPlan::getChannelId, channelId);
        }
        if (StringUtils.hasText(keyword)) {
            query.like(ChannelPlan::getName, keyword);
        }
        List<ChannelPlanDTO> list = channelPlanService.list(query)
                .stream().map(ChannelPlanConverter::convert).collect(Collectors.toList());
        return Result.ok(list);
    }

    @Override
    public Result<List<ChannelPlanDTO>> bulk(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids))
            return Result.ok();
        List<ChannelPlanDTO> list = channelPlanService.listByIds(ids)
                .stream().map(ChannelPlanConverter::convert).collect(Collectors.toList());
        return Result.ok(list);
    }

    @Override
    public Result<Integer> resourceCount(Long id) {
        LambdaQueryWrapper<PlanResource> query = Wrappers.lambdaQuery();
        query.eq(PlanResource::getPlanId, id);
        return Result.ok(planResourceService.count(query));
    }

    @Override
    public Result<PageDTO<PlanResourceDTO>> resources(Long id, Integer status, Integer resourceStatus, int current, int size) {
        LambdaQueryWrapper<PlanResource> query = Wrappers.lambdaQuery();
        query.eq(PlanResource::getPlanId, id);
        if (status != null) {
            query.eq(PlanResource::getStatus, status);
        }
        if (resourceStatus != null) {
            query.eq(PlanResource::getResourceStatus, resourceStatus);
        }
        IPage<PlanResource> page = planResourceService.page(new Page(current, size), query);
        return Result.ok(PageUtils.pageOf(page, PlanResourceConverter::convert));
    }

    @Override
    public Result addResources(Long id, PlanResourceDTO dto) {
        if (planResourceService.count(id, dto.getResourceId()) > 0) {
            return Result.fail("资源已添加到投放计划中");
        }
        PlanResource planResource = new PlanResource();
        planResource.setPlanId(id);
        planResource.setResourceId(dto.getResourceId());
        planResource.setWeight(dto.getWeight());
        planResource.setUserId(dto.getUserId());
        planResource.setAddDate(LocalDateTime.now());

        MediaResource mediaResource = mediaResourceService.getById(dto.getResourceId());
        if (mediaResource == null) {
            throw new CodeMessageException(CodeMessage.NOT_FOUND);
        }
        planResource.setResourceContentType(mediaResource.getContentType());
        planResource.setResourcePlayStyle(mediaResource.getPlayStyle());
        planResource.setResourceInternalName(mediaResource.getInternalName());
        planResource.setResourceStatus(mediaResource.getStatus());
        if (planResourceService.save(planResource)) {
            return Result.ok();
        } else {
            return Result.fail("添加资源失败");
        }
    }

    @Override
    public Result editResources(Long id, Long resourceId, PlanResourceDTO dto) {
        PlanResource planResource = planResourceService.getOne(id, resourceId);
        planResource.setWeight(dto.getWeight());
        planResource.setUserId(dto.getUserId());
        if (planResourceService.updateById(planResource)) {
            return Result.ok();
        } else {
            return Result.fail("更新资源失败");
        }
    }

    @Override
    public Result removeResources(Long id, Long resourceId) {
        PlanResource planResource = planResourceService.getOne(id, resourceId);
        planResource.setStatus(PlanResourceStatus.END.getCode());
        ChannelPlan channelPlan = channelPlanService.getById(id);
        if (channelPlan != null) {
            planResource.setEndDate(LocalDateTime.now());
            planResource.setStartDate(DateUtils.max(channelPlan.getStartDate(), planResource.getAddDate()));
        }
        if (planResourceService.updateById(planResource)) {
            return Result.ok();
        } else {
            return Result.fail("删除资源失败");
        }
    }

    @Override
    public Result removeResourcesByResourceIds(List<Long> resourceIds) {
        LambdaQueryWrapper<PlanResource> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(PlanResource::getResourceId, resourceIds);
        if (planResourceService.count(queryWrapper) > 0) {
            LambdaUpdateWrapper<PlanResource> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.in(PlanResource::getResourceId, resourceIds);
            updateWrapper.set(PlanResource::getStatus, PlanResourceStatus.END.getCode());
            if (planResourceService.update(updateWrapper)) {
                return Result.ok();
            } else {
                return Result.fail("删除频道关联资源失败");
            }
        }
        return Result.ok();
    }

    @Override
    public Result updateCurrentWeight(@RequestBody Map<Long, Integer> map) {
        for (Map.Entry<Long, Integer> entry : map.entrySet()) {
            PlanResource planResource = new PlanResource();
            planResource.setId(entry.getKey());
            planResource.setCurrentWeight(entry.getValue());
            planResourceService.updateById(planResource);
        }
        return Result.ok();
    }
}
