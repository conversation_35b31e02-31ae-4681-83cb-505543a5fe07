package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.AdvertSearchMarketing;
import com.jxntv.gvideo.media.client.dto.AdvertSearchMarketingDTO;
import com.jxntv.gvideo.media.client.dto.search.AdvertSearchMarketingSearchDTO;
/**
 * <AUTHOR>
 * @date 2023/3/6 15:44
 */
public interface AdvertSearchMarketingService extends IService<AdvertSearchMarketing> {

    Long create(AdvertSearchMarketingDTO dto);

    void updateById(Long id, AdvertSearchMarketingDTO dto);

    void updateStatus(Long id, AdvertSearchMarketingDTO dto);

    void deleteById(Long id);

    IPage<AdvertSearchMarketing> listPage(AdvertSearchMarketingSearchDTO searchDTO);

    AdvertSearchMarketing queryTriggerService(String keyword);


}
