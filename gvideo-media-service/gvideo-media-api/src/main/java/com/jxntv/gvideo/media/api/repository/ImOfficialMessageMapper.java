package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.media.api.domain.entity.ImOfficialMessage;
import com.jxntv.gvideo.media.client.dto.ImOfficialMessageCountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description IM官方私信表
 * @date 2021/10/20 11:30
 */
public interface ImOfficialMessageMapper extends BaseMapper<ImOfficialMessage> {
    List<ImOfficialMessageCountDTO> imOfficialMessageCount(@Param("jidList") List<Long> jidList);
}
