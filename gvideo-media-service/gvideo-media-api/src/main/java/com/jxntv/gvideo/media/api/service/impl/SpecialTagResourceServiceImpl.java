package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.converter.SpecialTagConverter;
import com.jxntv.gvideo.media.api.domain.entity.SpecialTag;
import com.jxntv.gvideo.media.api.domain.entity.SpecialTagResource;
import com.jxntv.gvideo.media.api.repository.SpecialTagResourceMapper;
import com.jxntv.gvideo.media.api.service.SpecialTagResourceService;
import com.jxntv.gvideo.media.api.service.SpecialTagService;
import com.jxntv.gvideo.media.client.dto.SpecialTagResourceDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 专题栏目资源关联表
 * @date 2021/05/06 9:24
 */
@Service
public class SpecialTagResourceServiceImpl extends ServiceImpl<SpecialTagResourceMapper, SpecialTagResource> implements SpecialTagResourceService {
    @Autowired
    private SpecialTagService specialTagService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void publish(List<SpecialTagResourceDTO> dtoList, Long userId) {
        //校验栏目是否存在
        SpecialTag specialTag = specialTagService.getById(dtoList.get(0).getTagId());
        List<SpecialTagResourceDTO> insertList = new ArrayList<>();
        List<SpecialTagResourceDTO> updateList = new ArrayList<>();
        List<Long> deleteList = new ArrayList<>();
        dtoList.forEach(dto -> {
            if (dto.getFlag().equals(0)) {
                dto.setId(null);
                dto.setSpecialId(specialTag.getResourceId());
                dto.setUpdateUserId(userId);
                dto.setCreateUserId(userId);
                insertList.add(dto);
            } else if (dto.getFlag().equals(1)) {
                dto.setUpdateUserId(userId);
                updateList.add(dto);
            } else if (dto.getFlag().equals(2)) {
                //-1代表新增后删除的内容，实际未入库
                if (dto.getId() != -1) {
                    deleteList.add(dto.getId());
                }
            }
        });
        if (!CollectionUtils.isEmpty(insertList)) {
            List<SpecialTagResource> tmpList = insertList.stream().map(dto -> SpecialTagConverter.convert(dto)).collect(Collectors.toList());
            if (!saveBatch(tmpList)) {
                throw new CodeMessageException(CodeMessage.ERROR);
            }
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            List<SpecialTagResource> tmpList = updateList.stream().map(dto -> SpecialTagConverter.convert(dto)).collect(Collectors.toList());
            if (!updateBatchById(tmpList)) {
                throw new CodeMessageException(CodeMessage.ERROR);
            }
        }
        if (!CollectionUtils.isEmpty(deleteList)) {
            if (!removeByIds(deleteList)) {
                throw new CodeMessageException(CodeMessage.ERROR);
            }
        }
    }

    @Override
    public IPage<SpecialTagResource> tagResourcePage(Page<SpecialTagResource> page, Long tagId) {
        return this.baseMapper.tagResourcePage(page, tagId);
    }

    @Override
    public List<SpecialTagResource> tagResourceDuplicate(Long tagId) {
        List<Long> resourceIds = this.baseMapper.tagResourceDuplicate(tagId);
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<SpecialTagResource> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpecialTagResource::getTagId, tagId);
        queryWrapper.in(SpecialTagResource::getResourceId, resourceIds);
        queryWrapper.orderByDesc(SpecialTagResource::getResourceId);
        queryWrapper.orderByAsc(SpecialTagResource::getSort);
        return this.list(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void tagResourceSort(Long tagId, Boolean asc) {
        List<Long> relateIds = this.baseMapper.tagResourceSort(tagId, asc);
        if (CollectionUtils.isEmpty(relateIds)) {
            return;
        }
        List<SpecialTagResource> specialTagResourceList = new ArrayList<>(relateIds.size());
        int total = relateIds.size();
        for (int i = 0; i < total; i++) {
            SpecialTagResource specialTagResource = new SpecialTagResource();
            specialTagResource.setId(relateIds.get(i));
            specialTagResource.setSort(total - i);
            specialTagResourceList.add(specialTagResource);
        }
        this.updateBatchById(specialTagResourceList);
    }
}
