package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.media.api.domain.entity.QuestionAnswer;
import com.jxntv.gvideo.media.client.dto.qa.QuestionAnswerDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/2/16
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class QuestionAnswerConverter {

    public QuestionAnswerDTO convert(QuestionAnswer entity) {
        QuestionAnswerDTO dto = new QuestionAnswerDTO();
        dto.setId(entity.getId());
        dto.setType(entity.getType());
        dto.setGroupId(entity.getGroupId());
        dto.setMediaId(entity.getMediaId());
        dto.setCommentId(entity.getCommentId());
        dto.setAnswerUserId(entity.getAnswerUserId());
        dto.setMentorJid(entity.getMentorJid());
        dto.setQuestionId(entity.getQuestionId());
        dto.setQuestionUserId(entity.getQuestionUserId());
        dto.setIsRead(entity.getIsRead());
        return dto;
    }

}
