package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 内容列表
 * @date 2021/04/30 17:21
 */
@Data
public class ContentList {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 封面图
     */
    private String cover;
    /**
     * 父级ID
     */
    private Long parent;
    /**
     * 是否目录：0-否、1-是
     */
    private Integer folder;
    /**
     * 限制条数
     */
    private Integer limitNum;
    /**
     * 0-手动、1-自动、2-H5页面
     */
    private Integer type;
    /**
     * H5页面地址
     */
    private String url;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 状态：0-禁用、1-启用
     */
    private Integer status;
    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Boolean delFlag;
}
