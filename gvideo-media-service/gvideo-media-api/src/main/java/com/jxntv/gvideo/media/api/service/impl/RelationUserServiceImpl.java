package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.service.RelationUserService;
import com.jxntv.gvideo.media.api.utils.StringUtils;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.List;

public abstract class RelationUserServiceImpl<M extends BaseMapper<T>, T>
        extends ServiceImpl<M, T>
        implements RelationUserService<T>, InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        ParameterizedType ptype = (ParameterizedType) this.getClass().getGenericSuperclass();
        clazz = (Class<T>) ptype.getActualTypeArguments()[1];
        setMethodName = StringUtils.getSetMethod(relationColumn());
    }

    @Autowired
    private SysUserClient userClient;

    private Class clazz;

    private String setMethodName;

    @Override
    public IPage<T> page(Long relationId, Long tenantId, long current, long size) {
        QueryWrapper<T> query = new QueryWrapper<>();
        query.eq(relationColumn(), relationId);
        if (tenantId != null) {
            query.eq("tenant_id", tenantId);
        }
        query.orderByDesc("user_create_date");
        return page(new Page(current, size), query);
    }

    @Override
    public List<T> list(Long tenantId, Long userId) {
        QueryWrapper<T> query = new QueryWrapper<>();
        query.eq("user_id", userId);
        query.eq("tenant_id", tenantId);
        return list(query);
    }

    private T newInstance() {
        if (clazz == null) {
            ParameterizedType ptype = (ParameterizedType) this.getClass().getGenericSuperclass();
            clazz = (Class<T>) ptype.getActualTypeArguments()[1];
        }
        try {
            return (T) clazz.newInstance();
        } catch (InstantiationException e) {
            throw new CodeMessageException(CodeMessage.ERROR);
        } catch (IllegalAccessException e) {
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }

    @Transactional
    @Override
    public boolean add(Long relationId, Long tenantId, Long userId) throws AuthorizedException, UserNotExistException {
        if (isAuthorized(relationId, tenantId, userId)) {
            throw new AuthorizedException();
        }
        SysUserDTO user = userClient.getUserInfoById(userId).getResult();
        if (user == null) {
            throw new UserNotExistException();
        }

        try {
            T t = newInstance();
            for (Method method : clazz.getDeclaredMethods()) {
                if (method.getName().equals("setTenantId")) {
                    method.invoke(t, tenantId);
                }
                if (method.getName().equals("setUserId")) {
                    method.invoke(t, userId);
                }
                if (method.getName().equals("setUserCreateDate")) {
                    method.invoke(t, user.getCreateDate());
                }
                if (method.getName().equals(setMethodName)) {
                    method.invoke(t, relationId);
                }
            }
            return save(t);
        } catch (Exception ex) {
            throw new CodeMessageException(CodeMessage.ERROR);
        }
    }

    @Override
    public boolean remove(Long relationId, Long tenantId, Long userId) throws UnAuthorizedException {
        QueryWrapper<T> query = new QueryWrapper<>();
        query.eq(relationColumn(), relationId);
        query.eq("tenant_id", tenantId);
        query.eq("user_id", userId);
        List<T> list = list(query);
        if (CollectionUtils.isEmpty(list)) {
            throw new UnAuthorizedException();
        }
        return remove(query);
    }

    @Override
    public boolean isAuthorized(Long relationId, Long tenantId, Long userId) {
        QueryWrapper<T> query = new QueryWrapper<>();
        query.eq(relationColumn(), relationId);
        query.eq("tenant_id", tenantId);
        query.eq("user_id", userId);
        return count(query) > 0;
    }

    @Override
    public int count(Long relationId, Long tenantId) {
        QueryWrapper<T> query = new QueryWrapper<>();
        query.eq(relationColumn(), relationId);
        query.eq("tenant_id", tenantId);
        return count(query);
    }
}
