package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.MediaPageViewConfigFigureSetting;

import java.util.List;

public interface MediaPageViewConfigFigureSettingService extends IService<MediaPageViewConfigFigureSetting> {

    Integer getMaxTime();

    MediaPageViewConfigFigureSetting getByMediaId(Long mediaId);

    List<MediaPageViewConfigFigureSetting> listByConfigId(Long configId);

    boolean removeByConfigId(Long configId);


}
