package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxntv.gvideo.media.client.enums.SysOperationLogSubjectType;
import com.jxntv.gvideo.media.client.enums.SysOperationLogField;
import com.jxntv.gvideo.media.client.enums.SysOperationLogAction;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 系统操作日志
 * @date 2021/06/11 8:59
 */
@TableName(value = "sys_operation_log")
@Data
public class SysOperationLog implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作类型：1-新增、2-修改、3-删除
     */
    private SysOperationLogAction action;

    /**
     * 主体类型: 1-弹窗/Banner投放
     */
    private SysOperationLogSubjectType subjectType;

    /**
     * 关联ID，根据类型决定关联表主键ID
     */
    private Long subjectId;

    /**
     * 变更字段
     */
    private SysOperationLogField changeField;

    /**
     * 主体内容
     */
    private String subject;

    /**
     * 变更前
     */
    private String beforeValue;

    /**
     * 变更后
     */
    private String afterValue;

    /**
     * 操作时间
     */
    private LocalDateTime createDate;

    /**
     * 操作人id
     */
    private Long createUserId;

    /**
     * 操作人
     */
    private String createUserName;

    /**
     * 租户id
     */
    private Long createTenantId;

    /**
     * 租户名称
     */
    private String createTenantName;
}