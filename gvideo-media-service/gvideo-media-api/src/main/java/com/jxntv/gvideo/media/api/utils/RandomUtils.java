package com.jxntv.gvideo.media.api.utils;

import java.util.Random;

/**
 * <AUTHOR>
 * @description
 * @date 2022/04/18 9:45
 */
public class RandomUtils {
    /**
     * 获取指定范围随机数
     *
     * @param min 最小值
     * @param max 最大值
     * @return
     */
    public static Integer getRandomNum(Integer min, Integer max) {
        min = (min == null || min < 0) ? 0 : min;
        max = (max == null || max < 0) ? 0 : max;
        if (min.equals(max)) {
            return min;
        }
        if (min > max) {
            Integer tmp = min;
            min = max;
            max = tmp;
        }
        return new Random().nextInt(max) % (max - min + 1) + min;
    }
}
