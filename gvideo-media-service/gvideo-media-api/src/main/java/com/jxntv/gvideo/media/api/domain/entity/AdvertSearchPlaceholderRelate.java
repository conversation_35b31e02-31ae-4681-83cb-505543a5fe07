package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 搜索文案投放表
 * @date 2022/02/21 15:21
 */
@Data
@TableName(value = "advert_search_placeholder_relate")
public class AdvertSearchPlaceholderRelate implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 搜索文案投放ID
     */
    private Long placeholderId;
    /**
     * open install id
     */
    private Long openInstallId;
    /**
     * open install 名称
     */
    private String openInstallName;
}