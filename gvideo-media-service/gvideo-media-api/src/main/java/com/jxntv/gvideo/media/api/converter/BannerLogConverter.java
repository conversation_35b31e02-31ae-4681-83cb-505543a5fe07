package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.media.api.domain.entity.Banner;
import com.jxntv.gvideo.media.api.domain.entity.BannerLog;
import com.jxntv.gvideo.media.client.dto.BannerLogDTO;
import com.jxntv.gvideo.user.client.SysUserClient;
import com.jxntv.gvideo.user.client.dto.SysUserDTO;
import com.jxntv.gvideo.common.model.Result;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class BannerLogConverter {

    @Resource
    private SysUserClient sysUserClient;

    public BannerLog toLog(Banner banner) {
        BannerLog log = new BannerLog();
        log.setBannerId(banner.getId());
        log.setBannerName(banner.getName());
        log.setLocationId(banner.getLocationId());
        log.setLocationName(banner.getLocationName());
        log.setEditUserId(banner.getUpdateUserId());
        log.setEditDate(LocalDateTime.now());

        Result<SysUserDTO> userInfoById = sysUserClient.getUserInfoById(banner.getUpdateUserId());
        userInfoById.ifPresent(user -> log.setEditUserName(user.getName()));

        return log;
    }






    public List<BannerLogDTO> convert(List<BannerLog> bannerLogs) {
        return bannerLogs.stream().map(this::convert).collect(Collectors.toList());
    }

    public BannerLogDTO convert(BannerLog entity) {
        BannerLogDTO bannerLogDTO = new BannerLogDTO();
        bannerLogDTO.setId(entity.getId());
        bannerLogDTO.setOriStatus(entity.getOriStatus());
        bannerLogDTO.setCurStatus(entity.getCurStatus());
        bannerLogDTO.setBannerId(entity.getBannerId());
        bannerLogDTO.setBannerName(entity.getBannerName());
        bannerLogDTO.setLocationId(entity.getLocationId());
        bannerLogDTO.setLocationName(entity.getLocationName());
        bannerLogDTO.setEditUserId(entity.getEditUserId());
        bannerLogDTO.setEditUserName(entity.getEditUserName());
        bannerLogDTO.setEditDate(entity.getEditDate());
        return bannerLogDTO;
    }
}
