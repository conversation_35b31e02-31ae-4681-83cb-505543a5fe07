package com.jxntv.gvideo.media.api.service.impl;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.domain.entity.ChannelPlan;
import com.jxntv.gvideo.media.api.domain.entity.PlanResource;
import com.jxntv.gvideo.media.api.service.ChannelPlanService;
import com.jxntv.gvideo.media.api.service.ChannelPlanUpdateService;
import com.jxntv.gvideo.media.api.service.PlanResourceService;
import com.jxntv.gvideo.media.client.dto.ChannelPlanDTO;
import com.jxntv.gvideo.media.client.enums.ChannelPlanStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class ChannelPlanUpdateServiceImpl implements ChannelPlanUpdateService {

    @Autowired
    private ChannelPlanService channelPlanService;
    @Autowired
    private PlanResourceService planResourceService;

    @Override
    public boolean isStart(ChannelPlan old) {
        return old.getStartDate().isAfter(LocalDateTime.now());
    }

    @Override
    public boolean isUpdatePos(ChannelPlan old, ChannelPlanDTO dto) {
        return !old.getPos().equals(dto.getPos());
    }

    @Override
    public boolean isUpdateDate(ChannelPlan old, ChannelPlanDTO dto) {
        if (!old.getStartDate().equals(dto.getStartDate()))
            return true;
        if (!old.getEndDate().equals(dto.getEndDate()))
            return true;
        return false;
    }

    @Override
    public boolean isUpdateStartDate(ChannelPlan old, ChannelPlanDTO dto) {
        return !old.getStartDate().equals(dto.getStartDate());
    }

    @Override
    public void oldPlanEnd(Long oldPlanId, Long operatorId) {
        ChannelPlan old = new ChannelPlan();
        old.setId(oldPlanId);
        old.setOperatorId(operatorId);
        old.setEndDate(LocalDateTime.now());
        old.setStatus(ChannelPlanStatus.END.getCode());
        if (!channelPlanService.updateById(old)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
    }

    @Override
    public void oldPlanDelete(Long oldPlanId, Long operatorId) {
        ChannelPlan old = new ChannelPlan();
        old.setId(oldPlanId);
        old.setOperatorId(operatorId);
        old.setDeleteDate(LocalDateTime.now());
        old.setStatus(ChannelPlanStatus.DELETED.getCode());
        if (!channelPlanService.updateById(old)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
    }

    @Override
    public void oldPlanDate(Long oldPlanId, Long operatorId, LocalDateTime start, LocalDateTime end) {
        ChannelPlan old = new ChannelPlan();
        old.setId(oldPlanId);
        old.setOperatorId(operatorId);
        old.setStartDate(start);
        old.setEndDate(end);
        if (!channelPlanService.updateById(old)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL);
        }
    }

    @Override
    public void newPlan(ChannelPlan newPlan, List<PlanResource> planResourceList) {
        newPlan.setStatus(ChannelPlanStatus.ING.getCode());
        if (!channelPlanService.save(newPlan)) {
            throw new CodeMessageException(CodeMessage.UPDATE_FAIL, "创建新计划失败");
        }

        if (!CollectionUtils.isEmpty(planResourceList)) {
            for (PlanResource planResource : planResourceList) {
                planResource.setId(null);
                planResource.setPlanId(newPlan.getId());
                if (!planResourceService.save(planResource)) {
                    throw new CodeMessageException(CodeMessage.UPDATE_FAIL, "复制资源失败");
                }
            }
        }
    }
}
