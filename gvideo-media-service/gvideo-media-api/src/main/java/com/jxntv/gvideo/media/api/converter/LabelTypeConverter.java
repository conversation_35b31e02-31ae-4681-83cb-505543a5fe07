package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.media.api.domain.entity.LabelType;
import com.jxntv.gvideo.media.client.dto.LabelTypeDTO;

public class LabelTypeConverter {
    public static LabelTypeDTO convert(LabelType entity) {
        LabelTypeDTO dto = new LabelTypeDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setIsSystem(entity.getIsSystem());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    public static LabelType convert(LabelTypeDTO dto) {
        LabelType entity = new LabelType();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setIsSystem(dto.getIsSystem());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }
}
