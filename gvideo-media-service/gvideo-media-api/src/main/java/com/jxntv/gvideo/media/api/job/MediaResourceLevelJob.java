package com.jxntv.gvideo.media.api.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceLevel;
import com.jxntv.gvideo.media.api.service.MediaResourceLevelService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 资源等级定时任务
 * @date 2021/12/1 9:07
 */
@Slf4j
@Component
public class MediaResourceLevelJob {

    @Autowired
    private MediaResourceLevelService mediaResourceLevelService;

    /**
     * 定时过期内容等级
     * 根据设置的等级有效时间，将到期的数据设置为 C级别 + 永久
     *  0 0 1 * * ?
     *  每天的 凌晨1点扫描数据表
     */
    @XxlJob("expiredMediaResourceLevel")
    public void expiredMediaResourceLevelByEffectiveTime() {
        LocalDate now = LocalDate.now();
        log.info("expiredMediaResourceLevelByEffectiveTime now is {}", now);
        LambdaQueryWrapper<MediaResourceLevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(MediaResourceLevel::getDuration, -1);
        queryWrapper.le(MediaResourceLevel::getEndTime, now);
        MediaResourceLevel level = new MediaResourceLevel();
        level.setLevel("C");
        level.setDuration(-1);
        level.setStartTime(now);
        level.setEndTime(now);
        mediaResourceLevelService.update(level, queryWrapper);
    }

}
