package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.MediaFile;
import com.jxntv.gvideo.media.client.dto.search.MediaFileSearchDTO;
import org.apache.ibatis.annotations.Param;

public interface MediaFileMapper extends BaseMapper<MediaFile> {
    Page<MediaFile> tenantMediaFilePage(@Param("page") Page<MediaFile> page, @Param("search") MediaFileSearchDTO searchDTO);
}
