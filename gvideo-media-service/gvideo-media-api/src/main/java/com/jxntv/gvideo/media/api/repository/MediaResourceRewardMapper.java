package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceReward;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceRewardSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @desc 资源奖励
 */
@Repository
public interface MediaResourceRewardMapper extends BaseMapper<MediaResourceReward> {

    IPage<MediaResourceReward> queryPage(@Param("page") Page<MediaResourceReward> page, @Param("search") MediaResourceRewardSearchDTO search);

}