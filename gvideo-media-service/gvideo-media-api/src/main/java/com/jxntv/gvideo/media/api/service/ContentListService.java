package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.ContentList;
import com.jxntv.gvideo.media.api.domain.entity.ContentListResource;
import com.jxntv.gvideo.media.client.dto.ContentListDTO;
import com.jxntv.gvideo.media.client.dto.ContentListResourceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description 内容列表
 * @date 2021/05/06 9:17
 */
public interface ContentListService extends IService<ContentList> {
    /**
     * 新增内容列表
     *
     * @param dto
     * @return
     */
    Long saveOrUpdateDTO(ContentListDTO dto);

    /**
     * 移除内容列表
     *
     * @param id
     */
    void remove(Long id);

    /**
     * 内容列表排序/插入
     *
     * @param targetId 当前排序的内容列表
     * @param referId  参照内容列表
     * @param insert   是否插入(拖拽效果涉及到排序、插入)
     */
    void drag(Long targetId, Long referId, Boolean insert);

    /**
     * 内容列表关联资源
     *
     * @param listResourceList
     */
    void relateResource(Long listId, List<ContentListResourceDTO> listResourceList);

    /**
     * 编辑资源
     *
     * @param dto
     */
    void updateResource(ContentListResourceDTO dto);

    /**
     * 移除资源
     *
     * @param id
     */
    void removeResource(Long id);

    /**
     * 资源置顶
     *
     * @param id
     */
    void resourceTop(Long id);

    /**
     * 资源取消置顶
     *
     * @param id
     */
    void resourceCancelTop(Long id);

    /**
     * 资源锁定
     *
     * @param id
     */
    void resourceLock(Long id);

    /**
     * 资源取消锁定
     *
     * @param id
     */
    void resourceUnlock(Long id);

    /**
     * 资源排序
     *
     * @param targetId
     * @param referId
     */
    void resourceSort(Long targetId, Long referId);

    /**
     * 分页查询内容列表资源
     *
     * @param listId  内容列表ID
     * @param keyword
     * @param current
     * @param size
     * @return
     */
    IPage<ContentListResource> resourcePage(Long listId, String keyword, Integer mediaStatus, Integer current, Integer size);

    /**
     * 分页查询内容列表资源
     *
     * @param listId    内容列表ID
     * @param keyword
     * @param current
     * @param isFilterTop 是否过滤top
     * @param size
     * @return
     */
    IPage<ContentListResource> resourcePage(Long listId, String keyword, Integer mediaStatus, Boolean isFilterTop, Integer current, Integer size);
}
