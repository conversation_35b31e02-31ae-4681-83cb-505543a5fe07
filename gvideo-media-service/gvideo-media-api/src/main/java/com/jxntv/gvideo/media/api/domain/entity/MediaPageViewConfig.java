package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class MediaPageViewConfig implements Serializable {

    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 配置名称
     */
    private String name;
    /**
     * 0-全局配置，1-内容匹配配置，2-单个配置
     */
    private Integer type;
    /**
     * 规则状态：0-禁用，1-启用
     */
    private Integer status;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建用户ID
     */
    private Long createUserId;
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 更新用户ID
     */
    private Long updateUserId;
}
