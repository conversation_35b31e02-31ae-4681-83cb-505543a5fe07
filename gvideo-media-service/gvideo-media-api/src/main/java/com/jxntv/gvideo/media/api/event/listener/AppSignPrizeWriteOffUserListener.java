package com.jxntv.gvideo.media.api.event.listener;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.canal.client.dto.ConsumerUserBinlogEvent;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.media.api.domain.entity.AppSignPrizeWriteOffUser;
import com.jxntv.gvideo.media.api.service.AppSignPrizeWriteOffUserService;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class AppSignPrizeWriteOffUserListener {


    @Resource
    private ConsumerUserClient consumerUserClient;

    @Resource
    private AppSignPrizeWriteOffUserService appSignPrizeWriteOffUserService;


    @Async
    @EventListener
    public void onEvent(ConsumerUserBinlogEvent event) {

        //  删除事件
        if (BinlogEventType.DELETE.equals(event.getEventType())) {
            ConsumerUserBinlogEvent.EventValue before = event.getBefore();
            if (Objects.nonNull(before)) {
                LambdaQueryWrapper<AppSignPrizeWriteOffUser> userQuery = Wrappers.lambdaQuery();
                userQuery.eq(AppSignPrizeWriteOffUser::getJid, before.getJid());
                List<AppSignPrizeWriteOffUser> list = appSignPrizeWriteOffUserService.list(userQuery);

                list.forEach(e -> appSignPrizeWriteOffUserService.removeById(e.getId()));
            }

        }

        //  更新事件
        if (BinlogEventType.UPDATE.equals(event.getEventType())) {
            ConsumerUserBinlogEvent.EventValue after = event.getAfter();
            if (Objects.nonNull(after)) {
                if (Objects.equals(after.getDelFlag(), 1)) {
                    LambdaQueryWrapper<AppSignPrizeWriteOffUser> userQuery = Wrappers.lambdaQuery();
                    userQuery.eq(AppSignPrizeWriteOffUser::getJid, after.getJid());
                    List<AppSignPrizeWriteOffUser> list = appSignPrizeWriteOffUserService.list(userQuery);

                    list.forEach(e -> appSignPrizeWriteOffUserService.removeById(e.getId()));
                }else {
                    LambdaQueryWrapper<AppSignPrizeWriteOffUser> userQuery = Wrappers.lambdaQuery();
                    userQuery.eq(AppSignPrizeWriteOffUser::getJid, after.getJid());
                    List<AppSignPrizeWriteOffUser> list = appSignPrizeWriteOffUserService.list(userQuery);

                    for (AppSignPrizeWriteOffUser user : list) {
                        ConsumerUserDTO consumerUserDTO = consumerUserClient.getBaseUserByJid(user.getJid()).orElse(null);
                        if (Objects.nonNull(consumerUserDTO)) {
                            user.setMobile(consumerUserDTO.getMobile());
                            user.setNickname(consumerUserDTO.getNickname());

                            appSignPrizeWriteOffUserService.updateById(user);
                        }

                    }
                }
            }
        }


    }

}
