package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.converter.LabelConverter;
import com.jxntv.gvideo.media.api.elasticsearch.suggest.AbstractSuggestElasticsearchIndex;
import com.jxntv.gvideo.media.api.elasticsearch.suggest.LabelSuggest;
import com.jxntv.gvideo.media.api.domain.entity.Label;
import com.jxntv.gvideo.media.api.domain.entity.LabelAndTypeName;
import com.jxntv.gvideo.media.api.service.LabelService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.LabelClient;
import com.jxntv.gvideo.media.client.dto.LabelDTO;
import com.jxntv.gvideo.media.client.dto.SuggestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 标签管理
 * <p>
 * <p>
 * Created on 2020-02-14
 */
@Slf4j
@RestController
public class LabelController implements LabelClient {

    @Autowired
    private LabelService labelService;
    @Autowired
    private List<LabelSuggest> suggestList;

    @Override
    public Result<List<LabelDTO>> all() {
        return Result.ok(labelService.list().stream().map(LabelConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<PageDTO<LabelDTO>> page(long current, long size, Long typeId, String queryName) {
        Page<LabelAndTypeName> page = labelService.getPagedLabels(new Page<>(current, size), typeId, queryName);
        return Result.ok(PageUtils.pageOf(page, LabelConverter::convert));
    }

    @Override
    public Result<Long> create(LabelDTO dto) {
        Label entity = LabelConverter.convert(dto);
        if (labelService.save(entity)) {
            return Result.ok(entity.getId());
        } else {
            return Result.fail("创建标签失败");
        }
    }

    @Override
    public Result update(Long id, LabelDTO dto) {
        Label entity = LabelConverter.convert(dto);
        entity.setId(id);
        if (labelService.updateById(entity)) {
            return Result.ok();
        } else {
            return Result.fail("更新标签失败");
        }
    }

    @Override
    public Result delete(Long id) {
        if (labelService.removeById(id)) {
            return Result.ok();
        } else {
            return Result.fail("删除标签失败");
        }
    }

    @Override
    public Result<List<LabelDTO>> fuzzy(String keyword) {
        LambdaQueryWrapper<Label> query = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(keyword)) {
            query.like(Label::getName, keyword);
        }
        return Result.ok(labelService.list(query).stream().map(LabelConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<Integer> count(Long id) {
        LambdaQueryWrapper<Label> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Label::getTypeId, id);
        Integer count = labelService.count(queryWrapper);
        return Result.ok(count);
    }

    @Override
    public Result<LabelDTO> getLabelById(Long id) {
        Label entity = labelService.getById(id);
        if (entity == null) {
            return Result.fail("获取标签内容失败");
        }
        return Result.ok(LabelConverter.convert(entity));
    }

    @Override
    public Result<List<SuggestDTO>> suggest(String prefix, String labelCategory) {
        Optional<LabelSuggest> first = suggestList.stream().filter(s -> s.category().name().equals(labelCategory)).findFirst();
        if (first.isPresent()) {
            AbstractSuggestElasticsearchIndex index = (AbstractSuggestElasticsearchIndex) first.get();
            return Result.ok(index.suggest(prefix));
        } else {
            return Result.ok();
        }
    }

    @Override
    public Result<List<LabelDTO>> getByIds(Collection<Long> labelList) {
        Collection<Label> labels = labelService.listByIds(labelList);
        return Result.ok(labels.stream().map(LabelConverter::convert).collect(Collectors.toList()));
    }

    @Override
    public Result<List<LabelDTO>> fetchByName(String prefix, int type) {
        Collection<Label> labels = labelService.list(Wrappers.<Label>lambdaQuery().eq(Label::getTypeId, type).like(Label::getName, prefix));
        return Result.ok(labels.stream().map(LabelConverter::convert).collect(Collectors.toList()));
    }
}
