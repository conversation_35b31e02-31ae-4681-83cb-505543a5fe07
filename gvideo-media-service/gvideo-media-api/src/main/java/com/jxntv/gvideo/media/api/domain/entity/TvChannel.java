package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 电视频道
 */
@Data
public class TvChannel implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 赣云频道id
     */
    private Long gid;

    /**
     * 信号源：0-省台信号源，1-地方信号源
     */
    private Integer source;
    /**
     * 鉴权方式：0-不鉴权，1-AES加密鉴权
     */
    private Integer authType;
    /**
     * 频道类型 0.电视频道 1.自建频道-直播 2-自建频道-其他
     */
    private Integer type;
    /**
     * 频道名称
     */
    private String channelName;
    /**
     * 简介
     */
    private String introduction;
    /**
     * 频道图标
     */
    private String iconId;

    /**
     * 频道图标链接，兼容赣云同步过来数据，如果iconId为null时候，取iconUrl内容
     */
    private String iconUrl;
    /**
     * 频道封面id
     */
    private String coverId;
    /**
     * 频道封面图URL
     */
    private String coverUrl;
    /**
     * 频道播放信号源鉴权URL
     */
    private String authUrl;
    /**
     * 频道播放信号源URL
     */
    private String playUrl;

    /**
     * 播放器类型：0-默认系统播放器，1-腾讯播放器
     */
    private Integer playerType;

    /**
     * 频道推流地址
     */
    private String forwardUrl;

    /**
     * 是否拥有版权
     */
    private Boolean hasCopyright;

    /**
     * 频道状态：0-禁用，1-启用,频道创建默认禁用
     */
    private Integer status;

    /**
     * 排序权重
     */
    private Integer weight;
    /**
     * 入库时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    private Integer replay;
}
