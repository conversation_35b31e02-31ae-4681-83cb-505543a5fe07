package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.media.api.domain.entity.MediaPageViewConfig;
import com.jxntv.gvideo.media.api.domain.entity.MediaPageViewConfigContentSetting;
import com.jxntv.gvideo.media.api.domain.entity.MediaPageViewConfigFigureSetting;
import com.jxntv.gvideo.media.api.service.MediaPageViewConfigFigureSettingService;
import com.jxntv.gvideo.media.api.service.MediaPageViewConfigContentSettingService;
import com.jxntv.gvideo.media.client.dto.pv.MediaPageViewConfigContentSettingDTO;
import com.jxntv.gvideo.media.client.dto.pv.MediaPageViewConfigDTO;
import com.jxntv.gvideo.media.client.dto.pv.MediaPageViewConfigFigureSettingDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class MediaPageViewConfigConverter {

    @Resource
    private MediaPageViewConfigFigureSettingService mediaPageViewConfigFigureSettingService;
    @Resource
    private MediaPageViewConfigContentSettingService mediaPageViewConfigContentSettingService;


    public MediaPageViewConfigDTO convert(MediaPageViewConfig entity) {
        List<MediaPageViewConfigFigureSettingDTO> figureSettings = mediaPageViewConfigFigureSettingService.listByConfigId(entity.getId()).stream().map(this::convert).collect(Collectors.toList());
        List<MediaPageViewConfigContentSettingDTO> contentSettings = mediaPageViewConfigContentSettingService.listByConfigId(entity.getId()).stream().map(this::convert).collect(Collectors.toList());

        MediaPageViewConfigDTO dto = new MediaPageViewConfigDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setType(entity.getType());
        dto.setStatus(entity.getStatus());
        dto.setCreateTime(entity.getCreateTime());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setUpdateTime(entity.getUpdateTime());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setFigureSettings(figureSettings);
        dto.setContentSettings(contentSettings);
        return dto;
    }

    public MediaPageViewConfig convert(MediaPageViewConfigDTO dto) {
        MediaPageViewConfig mediaPageViewConfig = new MediaPageViewConfig();
        mediaPageViewConfig.setId(dto.getId());
        mediaPageViewConfig.setName(dto.getName());
        mediaPageViewConfig.setType(dto.getType());
        mediaPageViewConfig.setStatus(dto.getStatus());
        mediaPageViewConfig.setCreateTime(dto.getCreateTime());
        mediaPageViewConfig.setCreateUserId(dto.getCreateUserId());
        mediaPageViewConfig.setUpdateTime(dto.getUpdateTime());
        mediaPageViewConfig.setUpdateUserId(dto.getUpdateUserId());
        return mediaPageViewConfig;

    }


    public MediaPageViewConfigFigureSettingDTO convert(MediaPageViewConfigFigureSetting entity) {
        MediaPageViewConfigFigureSettingDTO dto = new MediaPageViewConfigFigureSettingDTO();
        dto.setId(entity.getId());
        dto.setConfigId(entity.getConfigId());
        dto.setTime(entity.getTime());
        dto.setMaxValue(entity.getMaxValue());
        dto.setMinValue(entity.getMinValue());
        return dto;
    }


    public MediaPageViewConfigFigureSetting convert(MediaPageViewConfigFigureSettingDTO dto) {
        MediaPageViewConfigFigureSetting entity = new MediaPageViewConfigFigureSetting();
        entity.setId(dto.getId());
        entity.setConfigId(dto.getConfigId());
        entity.setTime(dto.getTime());
        entity.setMaxValue(dto.getMaxValue());
        entity.setMinValue(dto.getMinValue());
        return entity;

    }

    public MediaPageViewConfigContentSettingDTO convert(MediaPageViewConfigContentSetting entity) {
        MediaPageViewConfigContentSettingDTO dto = new MediaPageViewConfigContentSettingDTO();
        dto.setId(entity.getId());
        dto.setConfigId(entity.getConfigId());
        dto.setType(entity.getType());
        dto.setContentId(entity.getContentId());
        return dto;
    }

    public MediaPageViewConfigContentSetting convert(MediaPageViewConfigContentSettingDTO dto) {
        MediaPageViewConfigContentSetting entity = new MediaPageViewConfigContentSetting();
        entity.setId(dto.getId());
        entity.setConfigId(dto.getConfigId());
        entity.setType(dto.getType());
        entity.setContentId(dto.getContentId());
        return entity;
    }

}
