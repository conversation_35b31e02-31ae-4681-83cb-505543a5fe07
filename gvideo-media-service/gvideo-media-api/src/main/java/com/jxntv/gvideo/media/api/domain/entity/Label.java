package com.jxntv.gvideo.media.api.domain.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;

import lombok.Data;

@Data
public class Label {
    /**
     * 标签ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 标签名称
     */
    private String name;
    /**
     * 标签类型ID
     */
    private Long typeId;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Boolean delFlag;
}
