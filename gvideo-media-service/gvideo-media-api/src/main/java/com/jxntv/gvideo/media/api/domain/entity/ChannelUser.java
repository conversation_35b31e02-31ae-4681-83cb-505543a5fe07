package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class ChannelUser {
    /**
     * 关系ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 认证号ID
     */
    private Long channelId;
    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 账号ID
     */
    private Long userId;
    /**
     * 账号创建时间
     */
    private Date userCreateDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}
