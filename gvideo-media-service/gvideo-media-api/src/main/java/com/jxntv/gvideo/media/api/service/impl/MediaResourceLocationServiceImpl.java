package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.converter.MediaResourceLocationConverter;
import com.jxntv.gvideo.media.api.domain.entity.MediaResourceLocation;
import com.jxntv.gvideo.media.api.repository.MediaResourceLocationMapper;
import com.jxntv.gvideo.media.api.service.MediaResourceLocationService;
import com.jxntv.gvideo.media.client.dto.MediaResourceLocationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/2/14
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class MediaResourceLocationServiceImpl extends ServiceImpl<MediaResourceLocationMapper, MediaResourceLocation> implements MediaResourceLocationService {
    @Resource
    private MediaResourceLocationConverter mediaResourceLocationConverter;

    @Override
    public MediaResourceLocation getByMediaId(Long mediaId) {
        return this.getOne(Wrappers.<MediaResourceLocation>lambdaQuery().eq(MediaResourceLocation::getMediaId, mediaId));
    }

    @Override
    public Long save(MediaResourceLocationDTO dto) {
        MediaResourceLocation entity = mediaResourceLocationConverter.convert(dto);
        this.save(entity);
        return entity.getId();
    }
}
