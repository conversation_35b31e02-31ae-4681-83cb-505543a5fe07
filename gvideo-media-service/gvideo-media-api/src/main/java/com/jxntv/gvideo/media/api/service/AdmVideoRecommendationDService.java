package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.api.domain.entity.AdmVideoRecommendationD;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;

import java.util.List;

/**
 * <AUTHOR>
 * @description 内容推荐表-视听模块推荐信息
 * @date 2021/06/23 15:34
 */
public interface AdmVideoRecommendationDService extends IService<AdmVideoRecommendationD> {
    /**
     * 获取最大排名列表
     *
     * @param maxRankNum
     * @return
     */
    Result<List<Long>> queryContentMaxRank(Integer maxRankNum);

    /**
     * 查询视听推荐内容列表
     *
     * @param device
     * @param pageNum
     * @return
     */
    PageDTO<Long> queryContentSearch(String device, int pageNum);
}
