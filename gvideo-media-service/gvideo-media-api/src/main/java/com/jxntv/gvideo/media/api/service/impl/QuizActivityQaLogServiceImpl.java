package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.media.api.converter.QuizActivityConverter;
import com.jxntv.gvideo.media.api.domain.entity.QuizActivity;
import com.jxntv.gvideo.media.api.domain.entity.QuizActivityQaLog;
import com.jxntv.gvideo.media.api.repository.QuizActivityMapper;
import com.jxntv.gvideo.media.api.repository.QuizActivityQaLogMapper;
import com.jxntv.gvideo.media.api.service.QuizActivityEnrollService;
import com.jxntv.gvideo.media.api.service.QuizActivityQaLogService;
import com.jxntv.gvideo.media.client.dto.quiz.QuizActivityConfig;
import com.jxntv.gvideo.media.client.dto.quiz.QuizActivityQaLogDTO;
import com.jxntv.gvideo.media.client.dto.quiz.QuizActivityRule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Objects;

/**
 * 答题活动答题日志服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class QuizActivityQaLogServiceImpl extends ServiceImpl<QuizActivityQaLogMapper, QuizActivityQaLog> implements QuizActivityQaLogService {

    @Resource
    private QuizActivityConverter quizActivityConverter;

    @Resource
    private QuizActivityMapper quizActivityMapper;

    @Resource
    private QuizActivityEnrollService quizActivityEnrollService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitAnswer(QuizActivityQaLogDTO dto) {
        // 校验活动是否存在
        QuizActivity activity = quizActivityMapper.selectById(dto.getActivityId());
        if (activity == null || activity.getDelFlag() == 1) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动不存在");
        }

        // 校验活动状态
        if (activity.getStatus() != 1) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "活动未开始或已结束");
        }

        dto.setStatus(1);
        QuizActivityQaLog entity = quizActivityConverter.convertToEntity(dto);

        // 保存答题日志
        boolean isUpdate = this.updateById(entity);

        //  需要积分的类型累积积分，并重新排序
        if (isUpdate && dto.getScore() > 0 && shouldScore(activity.getConfig(), dto.getType())) {
            // 更新用户分数
            quizActivityEnrollService.updateScore(dto.getActivityId(), dto.getJid(), dto.getScore());
            // 重新对活动参与用户进行排名
            quizActivityEnrollService.recalculateRank(dto.getActivityId());
        }

        return isUpdate;
    }

    /**
     * 判断是否需要累积得分
     * 活动配置里面是否设置了需要积分
     *
     * @param config 活动配置
     * @param type   答题类型
     * @return 是否需要累积得分
     */
    private boolean shouldScore(QuizActivityConfig config, Integer type) {
        if (Objects.nonNull(config)) {
            List<QuizActivityRule> rules = config.getRules();
            return Objects.nonNull(rules) && rules.stream().anyMatch(e -> Objects.equals(e.getType(), type) && Objects.equals(e.getScoreFlag(), 1));
        }
        return false;
    }


    @Override
    public Integer getDailyQuizCount(Long activityId, Long jid, Integer type) {
        // 获取当天的开始时间和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay =LocalDateTime.of(today, LocalTime.MIN);
        LocalDateTime endOfDay = LocalDateTime.of(today, LocalTime.MAX);

        // 构建查询条件
        LambdaQueryWrapper<QuizActivityQaLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuizActivityQaLog::getActivityId, activityId)
                .eq(QuizActivityQaLog::getJid, jid)
                .eq(QuizActivityQaLog::getType, type)
                .between(QuizActivityQaLog::getCreateDate, startOfDay, endOfDay);

        // 统计当日答题次数
        return this.count(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createQa(QuizActivityQaLogDTO dto) {
        // 校验必要字段
        if (dto.getActivityId() == null || dto.getJid() == null || dto.getQuestionId() == null) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "缺少必要参数");
        }

        // 设置创建和更新时间
        LocalDateTime now = LocalDateTime.now();
        dto.setCreateDate(now);
        dto.setUpdateDate(now);

        // 转换为实体
        QuizActivityQaLog entity = quizActivityConverter.convertToEntity(dto);

        // 保存答题日志
        save(entity);

        // 返回新记录的ID
        return entity.getId();
    }

    @Override
    public QuizActivityQaLogDTO getQaByQuestionId(String questionId) {
        // 构建查询条件
        LambdaQueryWrapper<QuizActivityQaLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuizActivityQaLog::getQuestionId, questionId);

        // 查询答题记录
        QuizActivityQaLog qaLog = getOne(queryWrapper);

        // 转换为DTO并返回
        return quizActivityConverter.convertToDTO(qaLog);
    }
}
