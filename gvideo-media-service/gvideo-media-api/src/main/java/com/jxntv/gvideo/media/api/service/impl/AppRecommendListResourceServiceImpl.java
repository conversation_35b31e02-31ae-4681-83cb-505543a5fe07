package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.entity.AppRecommendListResource;
import com.jxntv.gvideo.media.api.repository.AppRecommendListResourceMapper;
import com.jxntv.gvideo.media.api.service.AppRecommendListResourceService;
import org.springframework.stereotype.Service;


/**
 * app推荐-列-资源-配置业务实现类
 *
 * <AUTHOR>
 * @date 2024-12-17 11:26:28
 */
@Service("appRecommendListResourceService")
public class AppRecommendListResourceServiceImpl extends ServiceImpl<AppRecommendListResourceMapper, AppRecommendListResource> implements AppRecommendListResourceService {


}