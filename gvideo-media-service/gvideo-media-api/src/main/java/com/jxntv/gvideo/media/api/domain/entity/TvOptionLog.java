package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 看电视操作日志
 * @date 2021/10/13 16:01
 */
@Data
@TableName("tv_option_log")
public class TvOptionLog implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主体类型: 1-频道、2-栏目、3-节目
     */
    private Integer subjectType;

    /**
     * 主体ID
     */
    private Long subjectId;

    /**
     * 变更主体
     */
    private String subject;

    /**
     * 动作类型: 1-新增、2-修改、3-删除
     */
    private Integer action;

    /**
     * 变更字段
     */
    private Integer changeField;

    /**
     * 变更前
     */
    private String beforeValue;

    /**
     * 变更后
     */
    private String afterValue;

    /**
     * 频道图标链接，兼容赣云同步过来数据，如果iconId为null时候，取iconUrl内容
     */
    private String iconUrl;

    /**
     * 频道/栏目/节目封面图URL
     */
    private String coverUrl;

    /**
     * 操作时间
     */
    private LocalDateTime createDate;

    /**
     * 操作人id
     */
    private Long createUserId;

    /**
     * 操作人
     */
    private String createUserName;
}
