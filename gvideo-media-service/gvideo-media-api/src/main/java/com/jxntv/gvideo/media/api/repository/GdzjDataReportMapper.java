package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.GdzjDataReport;
import com.jxntv.gvideo.media.client.dto.GdzjStatReportDTO;
import org.apache.ibatis.annotations.Param;

public interface GdzjDataReportMapper extends BaseMapper<GdzjDataReport> {

   IPage<GdzjStatReportDTO> stat(@Param("page")Page<GdzjStatReportDTO> page);


}
