package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.entity.BannerLog;
import com.jxntv.gvideo.media.api.repository.BannerLogMapper;
import com.jxntv.gvideo.media.api.service.BannerLogService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BannerLogServiceImpl extends ServiceImpl<BannerLogMapper, BannerLog> implements BannerLogService {


    @Override
    public List<BannerLog> listByBannerId(Long bannerId) {
        return this.list(Wrappers.<BannerLog>lambdaQuery().eq(BannerLog::getBannerId, bannerId));

    }

    @Override
    public boolean removeByBannerId(Long bannerId) {
        return this.remove(Wrappers.<BannerLog>lambdaQuery().eq(BannerLog::getBannerId, bannerId));
    }
}