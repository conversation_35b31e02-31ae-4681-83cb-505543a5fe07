package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

import com.jxntv.gvideo.media.client.dto.BroadcastLogDTO;
import lombok.Data;
import org.springframework.cglib.beans.BeanCopier;

/**
 * null
 *
 * @TableName broadcast_operator_log
 */
@TableName(value = "broadcast_operator_log")
@Data
public class BroadcastOperatorLog {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 操作
     */
    private String operator;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 操作人
     */
    private String createUser;

    /**
     * 操作时间
     */
    private LocalDateTime createDate;

    public static BroadcastLogDTO build(BroadcastOperatorLog log) {
        BroadcastLogDTO logDTO = new BroadcastLogDTO();
        BeanCopier.create(BroadcastOperatorLog.class, BroadcastLogDTO.class, false)
                .copy(log, logDTO, null);
        return logDTO;
    }
}