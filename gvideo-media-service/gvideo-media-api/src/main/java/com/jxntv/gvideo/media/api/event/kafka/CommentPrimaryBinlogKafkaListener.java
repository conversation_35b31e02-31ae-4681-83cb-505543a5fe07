package com.jxntv.gvideo.media.api.event.kafka;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.aliyun.sdk.param.PushDouyinMsgParam;
import com.jxntv.gvideo.canal.client.constants.Topics;
import com.jxntv.gvideo.canal.client.dto.CommentPrimaryBinlogEvent;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.consulatation.api.client.AppletSysUserClient;
import com.jxntv.gvideo.consulatation.api.constants.enums.ConsultPlatformEnum;
import com.jxntv.gvideo.consulatation.api.dto.AppletSysUserDTO;
import com.jxntv.gvideo.consulatation.api.param.SendMsgParam;
import com.jxntv.gvideo.group.sdk.client.GroupContentClient;
import com.jxntv.gvideo.group.sdk.dto.GroupContentDTO;
import com.jxntv.gvideo.group.sdk.dto.TopicMediaStatus;
import com.jxntv.gvideo.media.api.client.ConsumerUserService;
import com.jxntv.gvideo.media.api.client.DouyinAppletService;
import com.jxntv.gvideo.media.api.client.MentorInfoService;
import com.jxntv.gvideo.media.api.domain.entity.*;
import com.jxntv.gvideo.media.api.event.QuestionMsgEvent;
import com.jxntv.gvideo.media.api.service.*;
import com.jxntv.gvideo.media.client.enums.*;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
public class CommentPrimaryBinlogKafkaListener {

    @Resource
    private QuestionAnswerService questionAnswerService;
    @Resource
    private MediaResourceService mediaResourceService;
    @Resource
    private GroupContentClient groupContentClient;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;
    @Resource
    private MentorInfoService mentorInfoService;
    @Resource
    private MediaResourceRewardService mediaResourceRewardService;

    @Resource
    private QuestionAnswerMentorService questionAnswerMentorService;

    @Resource
    private ConsumerUserService consumerUserService;

    @Resource
    private DouyinAppletService douyinAppletService;

    @Resource
    private AppletSysUserClient appletSysUserClient;

    @Resource
    private ConsultQaOrderService consultQaOrderService;

    @Value("${wx.msg.templateId}")
    private String templateId;

    @Value("${douyin.order.course.path}")
    private String path;


    /**
     * 接收kafka事件源，转换成内部事件
     */
    @Transactional(rollbackFor = Exception.class)
    @KafkaListener(topics = {Topics.COMMENT_PRIMARY_TOPIC})
    public void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String value = record.value();
            //  审核问答评论的时候，同时更新对应的问答资源的状态
            CommentPrimaryBinlogEvent event = JsonUtils.fromJson(value, CommentPrimaryBinlogEvent.class);
            log.info("接收comment_primary消息:{}", event);
            LambdaQueryWrapper<QuestionAnswer> answerQuery = Wrappers.lambdaQuery();
            answerQuery.eq(QuestionAnswer::getCommentId, event.getId());
            answerQuery.eq(QuestionAnswer::getType, DataType.ANSWER.getCode());
            QuestionAnswer answer = questionAnswerService.getOne(answerQuery);
            log.info("comment_primary回答answer:{}", answer);
            if (Objects.nonNull(answer)) {
                //  如果评论被删除，需要删除对应资源信息
                if (Integer.valueOf(1).equals(event.getDelFlag())) {
                    //  移除评论关联的资源
                    mediaResourceService.removeById(answer.getMediaId());
                    //  移除社区回答关联关系
                    groupContentClient.deleteContentByMediaId(answer.getMediaId());

                    //根据发布用户id查该人员是否是导师
                    Result<Boolean> booleanResult = mentorInfoService.queryIsMentorByJid(event.getFromJid());
                    if (booleanResult.callSuccess() && booleanResult.getResult()) {
                        LambdaQueryWrapper<MediaResourceReward> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(MediaResourceReward::getResourceId, answer.getMediaId());
                        queryWrapper.eq(MediaResourceReward::getResourceType, MediaResourceRewardResourceType.RESOURCE.getCode());
                        queryWrapper.eq(MediaResourceReward::getStatus, MediaResourceRewardStatus.AUDIT.getCode());
                        mediaResourceRewardService.remove(queryWrapper);
                    }

                    QuestionAnswerMentor questionAnswerMentor = questionAnswerMentorService.getByMediaId(answer.getQuestionId());
                    if (Objects.nonNull(questionAnswerMentor) && Objects.equals(questionAnswerMentor.getMentorJid(), event.getFromJid()) && Objects.equals(questionAnswerMentor.getAnswerId(), event.getId()) && WhetherEnum.NO.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        questionAnswerMentor.setIsAnswer(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setIsQuestionClosely(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setIsQuestionRemind(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setUpdateTime(LocalDateTime.now());
                        questionAnswerMentorService.updateById(questionAnswerMentor);
                    }

                } else if (Objects.equals(EnableEnum.ENABLE.getCode(), event.getCheckstatus())) {
                    //  回答审核通过之后，对应的资源状态改为启用
                    MediaResource media = mediaResourceService.getById(answer.getMediaId());

                    if (Objects.nonNull(media) && Objects.equals(MediaResourceStatus.AUDIT.getCode(), media.getStatus())) {
                        //  回答关联资源改为启用状态
                        media.setStatus(MediaResourceStatus.ENABLE.getCode());
                        mediaResourceService.updateById(media);
                        //  社区关联关系也改为启用
                        groupContentClient.listContentByMediaId(media.getId()).ifPresent(list -> {
                            for (GroupContentDTO link : list) {
                                Long relateId = link.getId();
                                Result<Void> statusModify = groupContentClient.statusModify(relateId, TopicMediaStatus.ENABLE);
                                if (!statusModify.callSuccess()) {
                                    throw new CodeMessageException(statusModify.getCode(), statusModify.getMessage());
                                }
                            }
                        });

                    }

                    QuestionAnswerMentor questionAnswerMentor = questionAnswerMentorService.getByMediaId(answer.getQuestionId());
                    if (Objects.nonNull(questionAnswerMentor) && Objects.equals(questionAnswerMentor.getMentorJid(), event.getFromJid()) && WhetherEnum.NO.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        questionAnswerMentor.setIsAnswer(WhetherEnum.YES.getCode());
                        questionAnswerMentor.setIsAnswerRead(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setAnswerId(event.getId());
                        questionAnswerMentor.setAnswerTime(LocalDateTime.ofInstant(event.getCreateDate().toInstant(), ZoneId.systemDefault()));
                        questionAnswerMentor.setUpdateTime(LocalDateTime.now());
                        questionAnswerMentorService.updateById(questionAnswerMentor);
                        if (PlatformEnum.APPLET.getCode().equals(questionAnswerMentor.getPlatform())) {
                            pushSubscriptionMsg(questionAnswerMentor,media);
                        }
                    }
                } else if (event.getCheckstatus() == -1) {

                    QuestionAnswerMentor questionAnswerMentor = questionAnswerMentorService.getByMediaId(answer.getQuestionId());
                    if (Objects.nonNull(questionAnswerMentor) && Objects.equals(questionAnswerMentor.getMentorJid(), event.getFromJid()) && WhetherEnum.NO.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        if (WhetherEnum.NULL.getCode().equals(questionAnswerMentor.getIsAnswer())) {
                            questionAnswerMentor.setIsAnswer(WhetherEnum.NO.getCode());
                            questionAnswerMentor.setUpdateTime(LocalDateTime.now());
                            questionAnswerMentorService.updateById(questionAnswerMentor);
                        }
                    }
                }
            }
            /*if (Objects.nonNull(event)) {
                applicationEventPublisher.publishEvent(event);
            }*/


            //  手动提交ack
            ack.acknowledge();

        } catch (Exception e) {
            log.error("接收日志消费失败", e);
        }

    }


    private ConsultQaOrder getOrderByQuestionId(Long questionId) {
        LambdaQueryWrapper<ConsultQaOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(ConsultQaOrder::getMediaId, questionId);
        return consultQaOrderService.getOne(lambdaQuery);
    }

    /**
     * 推送订阅消息
     * @param questionAnswerMentor
     * @param media
     */
    void pushSubscriptionMsg(QuestionAnswerMentor questionAnswerMentor,MediaResource media){
        log.info("导师回复发送短信以及订阅消息");
        ConsumerUserDTO mentorUserDTO = consumerUserService.getBaseUserByJid(questionAnswerMentor.getMentorJid()).orElse(null);
        if (Objects.isNull(mentorUserDTO)) {
            return;
        }
        ConsultQaOrder order = getOrderByQuestionId(questionAnswerMentor.getQuestionId());
        if (Objects.isNull(order)) {
            log.error("QuestionId: {} 无关联订单。", questionAnswerMentor.getQuestionId());
            return;
        }
        //发送导师回复短信
        applicationEventPublisher.publishEvent(new QuestionMsgEvent(questionAnswerMentor.getJid(), QuestionMsgType.MENTOR_ANSWER_SUCCESS.getCode(), questionAnswerMentor.getMentorJid(), order.getPlatform()));

        //发送订阅模板消息
        AppletSysUserDTO dto = appletSysUserClient.queryUserByJidAndPlatform(questionAnswerMentor.getJid(), order.getPlatform()).orElse(null);
        if (Objects.isNull(dto)) {
            log.error("无此"+ConsultPlatformEnum.toStr(order.getPlatform())+"用户");
            return;
        }

        if (ConsultPlatformEnum.DOUYIN.getCode() == order.getPlatform()) {
            // 导师回复向抖音用户推送订阅消息
            PushDouyinMsgParam param = new PushDouyinMsgParam();
            Map<String, Object> data = new HashMap<>();
            data.put("导师", mentorUserDTO.getNickname());
            if (ContentType.SOUND.getCode() == media.getContentType()) {
                // 语音回复
                data.put("回复内容", "语音回复");
            } else {
                data.put("回复内容", media.getContent());
            }
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String dateStr = questionAnswerMentor.getAnswerTime().format(fmt);
            data.put("回复时间", dateStr);
            param.setData(data);
            param.setOpenId(dto.getOpenId());
            log.info("发送订阅消息，param：{}", JSON.toJSONString(param));
            Result result = douyinAppletService.pushDouyinMsg(param);
            log.info("订阅消息resp：{}", JSON.toJSONString(result));
            if (!result.callSuccess()) {
                log.error("发送抖音订阅消息失败");
            }
        } else if (ConsultPlatformEnum.WEIXIN.getCode() == order.getPlatform()) {
            log.info("导师回答微信订阅消息发送,order:{},dto:{}",JsonUtils.toJson(order),JsonUtils.toJson(dto));
            SendMsgParam param = new SendMsgParam();
            param.setTemplateId(templateId);
            param.setOpenId(dto.getOpenId());
            param.setPage(path);
            Map<String, String> data = new HashMap<>();
            data.put("name2", mentorUserDTO.getNickname());
            if (ContentType.SOUND.getCode() == media.getContentType()) {
                // 语音回复
                data.put("thing1", "语音回复");
            } else {
                data.put("thing1", media.getContent());
            }
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String dateStr = questionAnswerMentor.getAnswerTime().format(fmt);
            data.put("date3", dateStr);
            param.setData(data);
            Result<Boolean> result = appletSysUserClient.sendMsgToWxUser(param);
            if (!result.callSuccess()) {
                log.error("发送微信抖订阅消息失败");
            }
        }
    }
}
