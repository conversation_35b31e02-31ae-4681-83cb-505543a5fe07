package com.jxntv.gvideo.media.api.converter;

import com.jxntv.gvideo.media.api.domain.entity.AppSignPrizeWriteOffUser;
import com.jxntv.gvideo.media.client.dto.AppSignPrizeWriteOffUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppSignPrizeWriteOffUserConverter {

    public AppSignPrizeWriteOffUserDTO convert(AppSignPrizeWriteOffUser entity) {
        AppSignPrizeWriteOffUserDTO dto = new AppSignPrizeWriteOffUserDTO();
        dto.setId(entity.getId());
        dto.setJid(entity.getJid());
        dto.setNickname(entity.getNickname());
        dto.setMobile(entity.getMobile());
        dto.setExchangedCount(entity.getExchangedCount());
        dto.setMaxExchangeCount(entity.getMaxExchangeCount());
        dto.setStatus(entity.getStatus());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }


    public AppSignPrizeWriteOffUser convert(AppSignPrizeWriteOffUserDTO dto) {
        AppSignPrizeWriteOffUser entity = new AppSignPrizeWriteOffUser();
        entity.setId(dto.getId());
        entity.setJid(dto.getJid());
        entity.setNickname(dto.getNickname());
        entity.setMobile(dto.getMobile());
        entity.setExchangedCount(dto.getExchangedCount());
        entity.setMaxExchangeCount(dto.getMaxExchangeCount());
        entity.setStatus(dto.getStatus());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }
}
