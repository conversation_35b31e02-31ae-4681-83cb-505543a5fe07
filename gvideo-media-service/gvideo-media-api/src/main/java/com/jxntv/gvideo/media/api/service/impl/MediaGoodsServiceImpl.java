package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.media.api.converter.MediaGoodsConverter;
import com.jxntv.gvideo.media.api.domain.entity.MediaGoods;
import com.jxntv.gvideo.media.api.repository.MediaGoodsMapper;
import com.jxntv.gvideo.media.api.service.MediaGoodsService;
import com.jxntv.gvideo.media.client.dto.shop.MediaGoodsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/6/1
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class MediaGoodsServiceImpl extends ServiceImpl<MediaGoodsMapper, MediaGoods> implements MediaGoodsService {

    @Resource
    private MediaGoodsConverter mediaGoodsConverter;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(MediaGoodsDTO dto) {
        //  检查数据是否重复
        LambdaQueryWrapper<MediaGoods> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(MediaGoods::getMediaId, dto.getMediaId());
        lambdaQuery.eq(MediaGoods::getGoodsId, dto.getGoodsId());
        AssertUtil.assertOrThrow(this.count(lambdaQuery) == 0, CodeMessage.BAD_REQUEST.getCode(), "同一个直播间不能添加相同的商品");

        MediaGoods entity = mediaGoodsConverter.convert(dto);
        this.save(entity);
        return entity.getId();
    }

}
