package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.api.domain.bo.PublicityDepartmentDataPushLogDTO;
import com.jxntv.gvideo.media.api.domain.entity.PublicityDepartmentDataPushLog;
import com.jxntv.gvideo.media.api.domain.enums.PublicityDepartmentDataOperationType;
import com.jxntv.gvideo.media.api.domain.enums.PublicityDepartmentDataReportType;
import com.jxntv.gvideo.media.api.repository.PublicityDepartmentDataPushLogMapper;
import com.jxntv.gvideo.media.api.service.PublicityDepartmentDataPushLogService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 省宣-数据上报业务实现类
 *
 * <AUTHOR>
 * @date 2022-04-18 09:11:25
 */
@Service("publicityDepartmentDataPushLogService")
public class PublicityDepartmentDataPushLogServiceImpl extends ServiceImpl<PublicityDepartmentDataPushLogMapper, PublicityDepartmentDataPushLog> implements PublicityDepartmentDataPushLogService {


    @Override
    public void saveLog(PublicityDepartmentDataPushLogDTO dto) {
        if (Objects.equals(PublicityDepartmentDataReportType.UGC_DYNAMIC.getCode(),dto.getDataType()) && Objects.equals(PublicityDepartmentDataOperationType.INSERT.getCode(),dto.getOperationType()) ) {
            LambdaQueryWrapper<PublicityDepartmentDataPushLog> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(PublicityDepartmentDataPushLog::getDataType, dto.getDataType());
            queryWrapper.eq(PublicityDepartmentDataPushLog::getDataId, dto.getDataId());
            queryWrapper.eq(PublicityDepartmentDataPushLog::getOperationType, dto.getOperationType());
            Integer count = this.baseMapper.selectCount(queryWrapper);
            if (count > 0) {
                dto.setOperationType(PublicityDepartmentDataOperationType.UPDATE.getCode());
            }
        }

        if (Objects.equals(PublicityDepartmentDataOperationType.DELETE.getCode(),dto.getOperationType())){
            LambdaQueryWrapper<PublicityDepartmentDataPushLog> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.in(PublicityDepartmentDataPushLog::getDataType, dto.getDataType());
            queryWrapper.eq(PublicityDepartmentDataPushLog::getDataId, dto.getDataId());
            queryWrapper.eq(PublicityDepartmentDataPushLog::getOperationType, PublicityDepartmentDataOperationType.INSERT.getCode());
            Integer count = this.baseMapper.selectCount(queryWrapper);
            if (count <=0) {
                return;
            }
        }

        PublicityDepartmentDataPushLog entity = new PublicityDepartmentDataPushLog();
        entity.setDataType(dto.getDataType());
        entity.setDataId(dto.getDataId());
        entity.setOperationType(dto.getOperationType());
        entity.setPushFlag(0);
        entity.setCreateDate(new Date());
        this.baseMapper.insert(entity);

    }

    @Override
    public void updatePushFlag(Long id, Integer flag) {
        PublicityDepartmentDataPushLog entity = this.baseMapper.selectById(id);
        if (Objects.nonNull(entity)){
            Date current = new Date();
            entity.setPushFlag(flag);
            entity.setPushTime(current);
            entity.setUpdateDate(current);
            this.baseMapper.updateById(entity);
        }
    }
}