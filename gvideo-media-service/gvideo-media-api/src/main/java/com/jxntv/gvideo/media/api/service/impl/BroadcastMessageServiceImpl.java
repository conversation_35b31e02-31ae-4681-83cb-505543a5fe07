package com.jxntv.gvideo.media.api.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.media.client.dto.BanDTO;
import com.jxntv.gvideo.media.client.dto.BroadcastMessageDTO;
import com.jxntv.gvideo.media.client.enums.MessageStatusEnum;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.domain.entity.LiveBroadcastBan;
import com.jxntv.gvideo.media.api.domain.entity.LiveBroadcastMessage;
import com.jxntv.gvideo.media.api.domain.entity.Vest;
import com.jxntv.gvideo.media.api.repository.BroadcastBanMapper;
import com.jxntv.gvideo.media.api.repository.BroadcastMessageMapper;
import com.jxntv.gvideo.media.api.service.BroadcastMessageService;
import com.jxntv.gvideo.media.api.service.VestService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * <AUTHOR> Created on 2020/12/7.
 */
@Service
public class BroadcastMessageServiceImpl extends ServiceImpl<BroadcastMessageMapper, LiveBroadcastMessage> implements BroadcastMessageService {
    @Autowired
    private VestService vestService;
    @Autowired
    private BroadcastBanMapper broadcastBanMapper;

    @Override
    public Result<Void> pushMessage(BroadcastMessageDTO dto) {
        Long broadcastId = dto.getBroadcastId();
        if (Objects.isNull(broadcastId)) {
            return Result.fail("直播id不能为空");
        }
        String name = dto.getName();
        int count = vestService.count(Wrappers.<Vest>lambdaQuery().eq(Vest::getName, name));
        if (count == 0) {
            return Result.fail("该马甲名称不存在");
        }
        String content = dto.getMessage();
        if (StringUtils.isEmpty(content) || StringUtils.isEmpty(content.trim())) {
            return Result.fail("内容不能为空");
        }
        LiveBroadcastMessage message = new LiveBroadcastMessage();
        message.setCreatedDate(LocalDateTime.now());
        message.setLiveBroadcastId(broadcastId);
        message.setMessage(content);
        message.setTop(dto.getTop());
        message.setSystem(1);
        message.setUserName(dto.getName());
        message.setUserId(dto.getUserId());
        message.setImages(dto.getImages());
        message.setLinks(dto.getLinks());
        message.setPublishTime(LocalDateTime.now());
        message.setStatus(MessageStatusEnum.PASS);
        save(message);
        return Result.ok();
    }

    @Override
    public Result<Void> ban(BanDTO banDTO) {
        LiveBroadcastBan broadcastBan = new LiveBroadcastBan();
        broadcastBan.setConsumerUser(banDTO.getUserId());
        broadcastBan.setSysUser(banDTO.getSysUser());
        broadcastBan.setLiveBroadcastId(banDTO.getBroadcastId());
        broadcastBanMapper.insert(broadcastBan);
        return Result.ok();
    }

    @Override
    public Boolean banStatus(Long jid, Long liveBroadcastId) {
        return broadcastBanMapper.selectCount(Wrappers.<LiveBroadcastBan>lambdaQuery().eq(LiveBroadcastBan::getConsumerUser, jid).eq(LiveBroadcastBan::getLiveBroadcastId, liveBroadcastId)) > 0;
    }

    @Override
    public Result<Void> restore(BanDTO banDTO) {
        LiveBroadcastBan dbEntity = broadcastBanMapper.selectOne(Wrappers.<LiveBroadcastBan>lambdaQuery()
                .eq(LiveBroadcastBan::getLiveBroadcastId, banDTO.getBroadcastId())
                .eq(LiveBroadcastBan::getConsumerUser, banDTO.getUserId())
                .eq(LiveBroadcastBan::getSysUser, banDTO.getSysUser()));
        if (Objects.isNull(dbEntity)) {
            return Result.fail("恢复失败,该用户未被禁用");
        }
        broadcastBanMapper.deleteById(dbEntity.getId());
        return Result.ok();
    }

    @Override
    public Long findLastAuditTime(Long id) {
        LocalDateTime lastAuditTime = baseMapper.findLastAuditTime(id);
        if (Objects.isNull(lastAuditTime)) {
            lastAuditTime = LocalDateTime.now();
        }
        return lastAuditTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    @Override
    public PageDTO<BroadcastMessageDTO> selectPage(Long broadcastId, Integer page, Integer size) {
        IPage<LiveBroadcastMessage> pageResult = page(new Page<>(page, size),
                Wrappers.<LiveBroadcastMessage>lambdaQuery()
                        .eq(LiveBroadcastMessage::getLiveBroadcastId, broadcastId)
                        .ne(LiveBroadcastMessage::getStatus, MessageStatusEnum.DELETE)
                        .orderByDesc(LiveBroadcastMessage::getTop)
                        .orderByDesc(LiveBroadcastMessage::getCreatedDate));
        return PageUtils.pageOf(pageResult, LiveBroadcastMessage::transDTO);
    }
}
