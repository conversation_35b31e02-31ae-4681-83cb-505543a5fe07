package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.client.dto.FindByIdsAndContentTypeDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceStatisticReqDTO;
import com.jxntv.gvideo.media.client.dto.NewsQueryParam;
import com.jxntv.gvideo.media.client.dto.search.LiveBroadcastSearchDTO;
import com.jxntv.gvideo.media.client.dto.search.MediaResourceSearchDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MediaResourceMapper extends BaseMapper<MediaResource> {

    IPage<MediaResource> followList(@Param("page") Page<MediaResource> page, @Param("jid") Long jid);

    List<MediaResource> listRecommendContent(@Param("search") FindByIdsAndContentTypeDTO dto);

    IPage<MediaResource> listAuthorContentPage(@Param("page") Page<MediaResource> page, @Param("search") MediaResourceSearchDTO searchDTO);

    IPage<MediaResource> queryNewsPage(@Param("page") Page<MediaResource> page, @Param("search") NewsQueryParam search);

    IPage<Long> listResourcePage(@Param("page") Page<MediaResource> page, @Param("search") MediaResourceSearchDTO searchDTO);

    Integer countMediaResource(@Param("level") String level, @Param("params") MediaResourceStatisticReqDTO params);

    IPage<MediaResource> queryMediaResourceByLevel(@Param("page") Page<MediaResource> page, @Param("level") String level, @Param("params") MediaResourceStatisticReqDTO params);

    List<Long> listReleaseIdByType(@Param("type") Integer type);

    IPage<Long> listLiveBroadcastPage(@Param("page") Page<Long> page, @Param("search") LiveBroadcastSearchDTO searchDTO);

    Long getQuestionId(@Param("mediaResourceId") Long mediaResourceId);

    IPage<MediaResource> pageJuniorPosts(@Param("page") Page<MediaResource> page, @Param("jid") Long jid, @Param("self") Boolean self, @Param("platform") Integer platform);

}
