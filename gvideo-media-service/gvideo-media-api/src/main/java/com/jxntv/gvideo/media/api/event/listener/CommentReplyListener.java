package com.jxntv.gvideo.media.api.event.listener;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.aliyun.sdk.param.PushDouyinMsgParam;
import com.jxntv.gvideo.canal.client.dto.CommentReplyBinlogEvent;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.consulatation.api.client.AppletSysUserClient;
import com.jxntv.gvideo.consulatation.api.constants.enums.ConsultPlatformEnum;
import com.jxntv.gvideo.consulatation.api.dto.AppletSysUserDTO;
import com.jxntv.gvideo.consulatation.api.param.SendMsgParam;
import com.jxntv.gvideo.media.api.client.ConsumerUserService;
import com.jxntv.gvideo.media.api.client.DouyinAppletService;
import com.jxntv.gvideo.media.api.domain.entity.ConsultQaOrder;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.domain.entity.QuestionAnswerMentor;
import com.jxntv.gvideo.media.api.event.QuestionMsgEvent;
import com.jxntv.gvideo.media.api.service.ConsultQaOrderService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.api.service.QuestionAnswerMentorService;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.PlatformEnum;
import com.jxntv.gvideo.media.client.enums.QuestionMsgType;
import com.jxntv.gvideo.media.client.enums.WhetherEnum;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 监听CommentReply表生成奖励表
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class CommentReplyListener {

    @Resource
    private QuestionAnswerMentorService questionAnswerMentorService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ConsumerUserService consumerUserService;

    @Resource
    private DouyinAppletService douyinAppletService;

    @Resource
    private MediaResourceService mediaResourceService;

    @Resource
    private AppletSysUserClient appletSysUserClient;

    @Resource
    private ConsultQaOrderService consultQaOrderService;

    @Value("${wx.msg.templateId}")
    private String templateId;

    @Value("${douyin.order.course.path}")
    private String path;


    @EventListener
    public void onEvent(CommentReplyBinlogEvent event) {
        log.info("处理comment_reply消息事件:{}", event);

        QuestionAnswerMentor questionAnswerMentor = questionAnswerMentorService.getByMediaId(event.getMediaId());
        if (Objects.nonNull(questionAnswerMentor)) {

            //追问信息被审核通过

            if (Objects.equals(event.getId(), questionAnswerMentor.getQuestionCloselyId())) {
                if (event.getStatus() == 1 && event.getCheckstatus() == 1 && event.getDelFlag() == 0) {
                    if (WhetherEnum.NULL.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        questionAnswerMentor.setIsQuestionClosely(WhetherEnum.YES.getCode());
                        questionAnswerMentor.setIsAnswer(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setIsQuestionRemind(WhetherEnum.NO.getCode());
                        questionAnswerMentorService.updateById(questionAnswerMentor);
                    }
                } else if (event.getStatus() == 0 || event.getCheckstatus() == -1 || event.getDelFlag() == 1) {
                    if (WhetherEnum.NULL.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        questionAnswerMentor.setIsQuestionClosely(WhetherEnum.NO.getCode());
                        questionAnswerMentorService.updateById(questionAnswerMentor);
                    }
                }
            }


            if (Objects.equals(event.getFromJid(), questionAnswerMentor.getMentorJid())
                    && Objects.equals(event.getPrimaryId(), questionAnswerMentor.getAnswerId())
                    && Objects.equals(event.getToJid(), questionAnswerMentor.getJid())
            ) {
                log.info("导师追问回复，event:{}", event);
                if (event.getStatus() == 1 && event.getCheckstatus() == 1 && event.getDelFlag() == 0) {
                    if (WhetherEnum.NULL.getCode().equals(questionAnswerMentor.getIsAnswer())) {
                        questionAnswerMentor.setIsAnswer(WhetherEnum.YES.getCode());
                        questionAnswerMentor.setIsAnswerRead(WhetherEnum.NO.getCode());
                        questionAnswerMentor.setQuestionCloselyAnswerTime(LocalDateTime.ofInstant(event.getCreateDate().toInstant(), ZoneId.systemDefault()));
                        questionAnswerMentor.setQuestionCloselyAnswerId(event.getId());
                        questionAnswerMentorService.updateById(questionAnswerMentor);

                        if (PlatformEnum.APPLET.getCode().equals(questionAnswerMentor.getPlatform())) {
                            MediaResource media = mediaResourceService.getById(questionAnswerMentor.getQuestionCloselyAnswerId());
                            log.info("导师追问回复发送短信以及订阅消息");
                            ConsumerUserDTO mentorUserDTO = consumerUserService.getBaseUserByJid(questionAnswerMentor.getMentorJid()).orElse(null);
                            if (Objects.isNull(mentorUserDTO) || Objects.isNull(mentorUserDTO.getMobile())) {
                                return;
                            }

                            ConsultQaOrder order = getOrderByQuestionId(questionAnswerMentor.getQuestionId());
                            if (Objects.isNull(order)) {
                                log.error("QuestionId {} 无关联订单。", questionAnswerMentor.getQuestionId());
                                return;
                            }
                            if (ConsultPlatformEnum.DOUYIN.getCode() == order.getPlatform()) {
                                applicationEventPublisher.publishEvent(new QuestionMsgEvent(questionAnswerMentor.getJid(), QuestionMsgType.MENTOR_ANSWER_SUCCESS.getCode(), questionAnswerMentor.getMentorJid(), ConsultPlatformEnum.DOUYIN.getCode()));

                                AppletSysUserDTO dto = appletSysUserClient.queryUserByJidAndPlatform(questionAnswerMentor.getJid(), ConsultPlatformEnum.DOUYIN.getCode()).orElse(null);
                                if (Objects.isNull(dto)) {
                                    log.error("无此抖音小程序用户");
                                    return;
                                }
                                // 导师回复向抖音用户推送订阅消息
                                PushDouyinMsgParam param = new PushDouyinMsgParam();
                                Map<String, Object> data = new HashMap<>();
                                data.put("导师", mentorUserDTO.getNickname());
                                if (ContentType.SOUND.getCode() == media.getContentType()) {
                                    // 语音回复
                                    data.put("回复内容", "语音回复");
                                } else {
                                    data.put("回复内容", media.getContent());
                                }
                                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String dateStr = questionAnswerMentor.getAnswerTime().format(fmt);
                                data.put("回复时间", dateStr);
                                param.setData(data);
                                param.setOpenId(dto.getOpenId());
                                log.info("导师追问发送订阅消息，param:{}", JSON.toJSONString(param));
                                Result result = douyinAppletService.pushDouyinMsg(param);
                                log.info("订阅消息追问resp：{}", JSON.toJSONString(result));
                                if (!result.callSuccess()) {
                                    log.error("发送抖音订阅消息失败");
                                }
                            } else if (ConsultPlatformEnum.WEIXIN.getCode() == order.getPlatform()) {
                                applicationEventPublisher.publishEvent(new QuestionMsgEvent(questionAnswerMentor.getJid(), QuestionMsgType.MENTOR_ANSWER_SUCCESS.getCode(), questionAnswerMentor.getMentorJid(), ConsultPlatformEnum.WEIXIN.getCode()));
                                AppletSysUserDTO dto = appletSysUserClient.queryUserByJidAndPlatform(questionAnswerMentor.getJid(), ConsultPlatformEnum.WEIXIN.getCode()).orElse(null);
                                if (Objects.isNull(dto)) {
                                    log.error("无此微信小程序用户");
                                    return;
                                }
                                log.info("导师回答微信订阅消息发送,order:{},dto:{}",JsonUtils.toJson(order),JsonUtils.toJson(dto));
                                SendMsgParam param = new SendMsgParam();
                                param.setTemplateId(templateId);
                                param.setOpenId(dto.getOpenId());
                                Map<String, String> data = new HashMap<>();
                                data.put("name2", mentorUserDTO.getNickname());
                                if (ContentType.SOUND.getCode() == media.getContentType()) {
                                    // 语音回复
                                    data.put("thing1", "语音回复");
                                } else {
                                    data.put("thing1", media.getContent());
                                }
                                DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String dateStr = questionAnswerMentor.getAnswerTime().format(fmt);
                                data.put("date3", dateStr);
                                param.setPage(path);
                                param.setData(data);
                                Result<Boolean> result = appletSysUserClient.sendMsgToWxUser(param);
                                if (!result.callSuccess()) {
                                    log.error("发送微信抖订阅消息失败");
                                }
                            }
                        }
                    }
                } else if (Objects.equals(event.getEventType(), BinlogEventType.INSERT)) {
                    log.info("导师追问新增回复，event:{},questionAnswerMentor:{}", event, questionAnswerMentor);
                    if (WhetherEnum.YES.getCode().equals(questionAnswerMentor.getIsQuestionClosely())) {
                        if (WhetherEnum.NO.getCode().equals(questionAnswerMentor.getIsAnswer())) {
                            questionAnswerMentor.setIsAnswer(WhetherEnum.NULL.getCode());
                        }
                        questionAnswerMentor.setQuestionCloselyAnswerTime(LocalDateTime.ofInstant(event.getCreateDate().toInstant(), ZoneId.systemDefault()));
                        questionAnswerMentorService.updateById(questionAnswerMentor);
                    }

                }
            }
        }

    }

    private ConsultQaOrder getOrderByQuestionId(Long questionId) {
        LambdaQueryWrapper<ConsultQaOrder> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(ConsultQaOrder::getMediaId, questionId);
        return consultQaOrderService.getOne(lambdaQuery);
    }
}
