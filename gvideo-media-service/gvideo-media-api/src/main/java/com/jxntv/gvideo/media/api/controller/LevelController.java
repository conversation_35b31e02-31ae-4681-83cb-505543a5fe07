package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.domain.entity.MediaResource;
import com.jxntv.gvideo.media.api.service.MediaResourceLevelService;
import com.jxntv.gvideo.media.api.service.MediaResourceService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.LevelClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceStatisticRespDTO;
import com.jxntv.gvideo.media.client.dto.level.LevelReqDTO;
import com.jxntv.gvideo.media.client.dto.level.LevelRespDTO;
import com.jxntv.gvideo.media.client.dto.level.MediaResourceSearchLevelDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/29 10:40
 */
@Slf4j
@RestController
public class LevelController implements LevelClient {

    @Autowired
    private MediaResourceService mediaResourceService;

    @Autowired
    private MediaResourceLevelService mediaResourceLevelService;

    @Override
    public Result updateMediaResourceLevel(String id, LevelReqDTO updateDTO) {
        log.info("updateMediaResourceLevel start, id is {}, updateDTO is {}", updateDTO.toString());

        MediaResource mediaResource = mediaResourceService.queryMediaResourceById(Long.valueOf(id));
        if (null == mediaResource) {
            return Result.fail("资源不存在");
        }
        LocalDate now = LocalDate.now();
        // 永久
        if (updateDTO.getDuration() == -1) {
            updateDTO.setStartTime(now);
            updateDTO.setEndTime(now);
        }
        // 自定义
        if (updateDTO.getDuration() == 0) {
            if (updateDTO.getStartTime() != null && updateDTO.getStartTime().isBefore(now)) {
                return Result.fail("开始时间参数错误");
            }
            if (updateDTO.getEndTime() != null && updateDTO.getEndTime().isBefore(now)) {
                return Result.fail("结束时间参数错误");
            }
            if (updateDTO.getStartTime() != null && updateDTO.getEndTime() != null) {
                if (updateDTO.getEndTime().isBefore(updateDTO.getStartTime())) {
                    return Result.fail("起止时间参数错误");
                }
            }
        }

        if (updateDTO.getDuration() != -1 && updateDTO.getDuration() != 0) {
            updateDTO.setStartTime(now);
            updateDTO.setEndTime(now.plusDays(updateDTO.getDuration()));
        }

        boolean rest = mediaResourceLevelService.updateMediaResourceLevelInfo(updateDTO);
        if (!rest) {
            return Result.fail("设置等级失败");
        }
        return Result.ok();
    }

    /**
     * 查询内容列表
     *
     * @param reqDTO
     * @return
     */
    @Override
    public Result<PageDTO<Long>> queryMediaResourceByLevel(MediaResourceSearchLevelDTO reqDTO) {
        log.info("queryMediaResourceByLevel start, reqDTO is {}", reqDTO.toString());
        Page idList = mediaResourceLevelService.queryMediaIdByConditions(reqDTO);
        return Result.ok(PageUtils.pageOf(idList));
    }


    /**
     * 根据内容Id查询内容等级接口，如果在media_resource_level表中没有查询结果
     * 表示该数据的等级为默认的 B 永久
     *
     * @param id
     * @return
     */
    @Override
    public Result<LevelRespDTO> queryMediaResourceLevel(String id) {
        MediaResource mediaResource = mediaResourceService.queryMediaResourceById(Long.valueOf(id));
        if (mediaResource == null) {
            log.error("queryMediaResourceLevel err: mediaResource is not exists");
            return Result.fail("资源不存在");
        }
        LevelRespDTO respDTO = mediaResourceLevelService.queryLevelInfoById(Long.valueOf(id));
        return Result.ok(respDTO);
    }


    /**
     * @description: 内容资源统计
     * <AUTHOR>
     * @date 2021/11/12 15:46
     * @version 1.0
     */
    @Override
    public Result<MediaResourceStatisticRespDTO> statisticMediaResourcesByLevel(MediaResourceSearchLevelDTO reqDTO) {
        log.info("start statisticMediaResources reqDTO is {}", reqDTO.toString());

        MediaResourceStatisticRespDTO resp = mediaResourceLevelService.statisticMediaResource(reqDTO);
        if (null == resp) {
            return Result.fail("统计失败!");
        }

        return Result.ok(resp);
    }
}
