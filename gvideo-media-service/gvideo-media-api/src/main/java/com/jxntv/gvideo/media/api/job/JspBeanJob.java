package com.jxntv.gvideo.media.api.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.media.api.domain.entity.UserJspBeanDetail;
import com.jxntv.gvideo.media.api.service.UserJspBeanDetailService;
import com.jxntv.gvideo.media.api.service.UserJspBeanService;
import com.jxntv.gvideo.media.client.dto.UserJspBeanDetailDTO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 金豆任务
 */
@Slf4j
@Component
public class JspBeanJob {


    @Resource
    private UserJspBeanService userJspBeanService;

    @Resource
    private UserJspBeanDetailService userJspBeanDetailService;


    /**
     * 金豆过期任务
     */
    @XxlJob("expiredJspBeanJob")
    public void expiredJspBeanJob() {
        //  当前时间
        LocalDateTime now = LocalDateTime.now();
        //  批处理数量
        int batchSize = 100;

        while (true) {
            //  查询未结算的过期今豆获得记录
            LambdaQueryWrapper<UserJspBeanDetail> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(UserJspBeanDetail::getType, 1);
            wrapper.eq(UserJspBeanDetail::getStatus, 0);
            wrapper.lt(UserJspBeanDetail::getExpireDate, now);
            wrapper.last(" limit " + batchSize);

            List<UserJspBeanDetail> userJspBeans = userJspBeanDetailService.list(wrapper);
            if (CollectionUtils.isEmpty(userJspBeans)) {
                break;
            } else {
                List<Long> jidList = userJspBeans.stream().map(UserJspBeanDetail::getJid).distinct().collect(Collectors.toList());
                for (Long jid : jidList) {
                    int expireJspBeanCount = userJspBeanService.getExpireJspBeanCount(jid, now);
                    if (expireJspBeanCount > 0) {
                        //  扣除过期今豆
                        UserJspBeanDetailDTO userJspBeanDetailDTO = new UserJspBeanDetailDTO();
                        userJspBeanDetailDTO.setJspBeanCount(expireJspBeanCount);
                        userJspBeanDetailDTO.setOperationDate(LocalDateTime.now());
                        userJspBeanDetailDTO.setJid(jid);
                        userJspBeanDetailDTO.setType(2);
                        userJspBeanDetailDTO.setOperationType(10);
                        userJspBeanDetailDTO.setDetail("今豆过期");

                        userJspBeanService.operate(userJspBeanDetailDTO);
                    }


                    //  标记过期的详情数据列表
                    LambdaUpdateWrapper<UserJspBeanDetail> detailUpdate = Wrappers.lambdaUpdate();
                    detailUpdate.set(UserJspBeanDetail::getStatus, 1);
                    detailUpdate.eq(UserJspBeanDetail::getJid, jid);
                    detailUpdate.eq(UserJspBeanDetail::getType, 1);
                    detailUpdate.eq(UserJspBeanDetail::getStatus, 0);
                    detailUpdate.lt(UserJspBeanDetail::getExpireDate, now);

                    userJspBeanDetailService.update(detailUpdate);


                }


            }


        }


    }


}
