package com.jxntv.gvideo.media.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.VodWatermarkImageCreateDTO;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.domain.entity.DramaColumn;
import com.jxntv.gvideo.media.api.domain.entity.DramaProgram;
import com.jxntv.gvideo.media.api.repository.DramaColumnMapper;
import com.jxntv.gvideo.media.api.service.DramaColumnService;
import com.jxntv.gvideo.media.api.service.DramaProgramService;
import com.jxntv.gvideo.media.api.utils.PageUtils;
import com.jxntv.gvideo.media.client.DramaColumnClient;
import com.jxntv.gvideo.media.client.dto.DramaColumnDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @Author: niedamin
 * @Date: 2023/06/30 10:49
 */
@RestController
public class DramaColumnController implements DramaColumnClient {

    @Resource
    private DramaColumnService dramaColumnService;

    @Resource
    private DramaProgramService dramaProgramService;

    @Resource
    private DramaColumnMapper dramaColumnMapper;
    @Resource
    private OssClient ossClient;

    @Override
    public Result<PageDTO<DramaColumnDTO>> page(Integer status, String name, String category, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<DramaColumn> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(status), DramaColumn::getStatus, status);
        queryWrapper.eq(Objects.nonNull(category), DramaColumn::getCategory, category);
        queryWrapper.like(StringUtils.isNotBlank(name), DramaColumn::getColumnName, name);
        queryWrapper.orderByDesc(DramaColumn::getWeight);
        Page<DramaColumn> page = dramaColumnService.page(new Page<>(pageNum, pageSize), queryWrapper);
        return Result.ok(PageUtils.pageOf(page, (drama) -> {
            DramaColumnDTO dto = new DramaColumnDTO();
            BeanUtils.copyProperties(drama, dto);
            return dto;
        }));
    }

    @Override
    public Result<Long> create(DramaColumnDTO dto) {
        DramaColumn dramaColumn = new DramaColumn();
        BeanUtils.copyProperties(dto, dramaColumn);
        if (Objects.nonNull(dto.getWeight()) && isWeightDuplicate(dto.getWeight())) {
            return Result.fail("权重值重复");
        }

        //  创建水印图片
        if (StringUtils.isEmpty(dto.getBeian())) {
            return Result.fail("备案号为空");
        }

        VodWatermarkImageCreateDTO createDTO = new VodWatermarkImageCreateDTO();
        createDTO.setContent(dto.getBeian());
        String watermarkImageUrl = ossClient.createWatermarkImage(createDTO).orElseThrow(() -> new CodeMessageException(CodeMessage.ERROR, "水印图片创建失败"));
        dramaColumn.setWatermarkImageUrl(watermarkImageUrl);

        dramaColumnService.save(dramaColumn);
        return Result.ok(dramaColumn.getId());
    }

    @Override
    public Result<Void> update(Long id, DramaColumnDTO dto) {
        dto.setId(id);
        DramaColumn dramaColumn = new DramaColumn();
        if (dto.getStatus() == 1 && !canUp(id)) {
            return Result.fail("当前节目没有审核通过的视频");
        }
        if (Objects.nonNull(dto.getWeight())) {
            DramaColumn oldColumn = dramaColumnService.getById(id);
            if (!Objects.equals(oldColumn.getWeight(), dto.getWeight()) && isWeightDuplicate(dto.getWeight())) {
                return Result.fail("权重值重复");
            }
        }

        BeanUtils.copyProperties(dto, dramaColumn);

        //  创建水印图片
        if (StringUtils.isNotEmpty(dto.getBeian())) {
            VodWatermarkImageCreateDTO createDTO = new VodWatermarkImageCreateDTO();
            createDTO.setContent(dto.getBeian());
            String watermarkImageUrl = ossClient.createWatermarkImage(createDTO).orElseThrow(() -> new CodeMessageException(CodeMessage.ERROR, "水印图片创建失败"));
            dramaColumn.setWatermarkImageUrl(watermarkImageUrl);
        }

        dramaColumnService.updateById(dramaColumn);
        return Result.ok();
    }

    private boolean canUp(Long columnId) {
        LambdaQueryWrapper<DramaProgram> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DramaProgram::getColumnId, columnId);
        queryWrapper.eq(DramaProgram::getStatus, 1);
        int count = dramaProgramService.count(queryWrapper);
        return count > 0;
    }

    @Override
    public Result<DramaColumnDTO> getById(Long id) {
        DramaColumnDTO dto = new DramaColumnDTO();
        DramaColumn dramaColumn = dramaColumnService.getById(id);
        if (Objects.isNull(dramaColumn)) {
            return Result.fail("无此节目");
        }
        BeanUtils.copyProperties(dramaColumn, dto);
        return Result.ok(dto);
    }

    @Override
    public Result<List<String>> getColumnCategory() {
        List<String> columnCategoryList = dramaColumnMapper.getColumnCategory();
        return Result.ok(columnCategoryList);
    }

    private boolean isWeightDuplicate(Long weight) {
        LambdaQueryWrapper<DramaColumn> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(DramaColumn::getWeight, weight);
        int count = dramaColumnService.count(queryWrapper);
        return count > 0;
    }
}
