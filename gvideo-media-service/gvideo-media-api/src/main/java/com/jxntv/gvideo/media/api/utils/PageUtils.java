package com.jxntv.gvideo.media.api.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxntv.gvideo.common.model.PageDTO;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHit;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PageUtils {

    public static <T> PageDTO<T> empty(Integer pageNum, Integer pageSize) {
        PageDTO<T> page = new PageDTO<>();
        page.setList(Collections.emptyList());
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setTotal(0);
        return page;
    }

    public static <T> PageDTO<T> pageOf(IPage<T> page) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords());
        pageDTO.setPageNum((int) page.getCurrent());
        pageDTO.setPageSize((int) page.getSize());
        pageDTO.setTotal((int) page.getTotal());
        return pageDTO;
    }

    public static <T, E> PageDTO<T> pageOf(IPage<E> page, Function<E, T> mapper) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords().stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toList()));
        pageDTO.setPageNum((int) page.getCurrent());
        pageDTO.setPageSize((int) page.getSize());
        pageDTO.setTotal((int) page.getTotal());
        return pageDTO;
    }

    public static <T> PageDTO<T> pageOf(SearchResponse response, Function<SearchHit, T> mapper, long pn, long size) {
        if (response.getHits().getHits().length == 0) {
            PageDTO<T> pageDTO = new PageDTO<>();
            pageDTO.setList(new ArrayList<>());
            return pageDTO;
        }
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(Arrays.stream(response.getHits().getHits()).map(mapper).collect(Collectors.toList()));
        pageDTO.setPageNum((int) pn);
        pageDTO.setPageSize((int) size);
        pageDTO.setTotal((int) response.getHits().getTotalHits().value);
        return pageDTO;
    }

}
