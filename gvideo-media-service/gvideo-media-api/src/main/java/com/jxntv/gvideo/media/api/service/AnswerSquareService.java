package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.media.api.domain.entity.AnswerSquare;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.media.client.dto.AnswerSquareDTO;
import com.jxntv.gvideo.media.client.dto.AnswerSquareSearchDTO;
import com.jxntv.gvideo.media.client.dto.MediaUgcFileResourceDTO;

import java.util.List;

/**
 *
 */
public interface AnswerSquareService extends IService<AnswerSquare> {

    IPage<AnswerSquare> page(AnswerSquareSearchDTO searchDTO);

    Long createByMedia(MediaUgcFileResourceDTO mediaUgcFileResourceDto);

    void statusModify(Integer status, List<Long> ids, Integer type);

    IPage<Long> questionList(Page<Object> page, Long jid, Integer status);

    Integer questionCount(Long jid, Integer status);

    IPage<AnswerSquare> answerList(Page<Object> page, Long jid, Integer answerStatus, Integer checkStatus);

    Integer answerCount(Long jid, Integer answerStatus, Integer checkStatus);

    List<AnswerSquareDTO> listAuthAnswer(Long groupId);
}
