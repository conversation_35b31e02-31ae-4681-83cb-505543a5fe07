package com.jxntv.gvideo.media.api.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.api.domain.entity.HongdujuanActive;
import com.jxntv.gvideo.media.client.dto.hongdujuan.QueryParam;
import com.jxntv.gvideo.media.client.dto.hongdujuan.RegisterDTO;
import com.jxntv.gvideo.media.client.dto.hongdujuan.ScoringParam;

import java.util.List;

/**
 * 红杜鹃报名活动
 */
public interface HongDuJuanService {

    /**
     * 发送短信验证码
     * @param mobile
     * @return
     */
    Result<String> sendCode(String mobile);

    /**
     * 注册报名
     * @param dto
     * @return
     */
    Result<String> register(RegisterDTO dto);


    /**
     * 查看所有项目
     * @param queryParam
     * @return
     */
    Result<PageDTO<RegisterDTO>> listProject(QueryParam queryParam);


    /**
     * 查看所有项目
     * @param queryParam
     * @return
     */
    Result<PageDTO<RegisterDTO>> listFinalProject(QueryParam queryParam);


    /**
     * 查询所有产品总分
     * @param queryParam
     * @return
     */
    Result<PageDTO<RegisterDTO>> listProduct(QueryParam queryParam);
    /**
     * 通过手机号查询已报名信息
     * @param queryParam
     * @return
     */
    Result<List<RegisterDTO>> query(QueryParam queryParam);


    /**
     * 打分
     * @param param
     * @return
     */
    Result<String> scoring(ScoringParam param);

    /**
     * 提交打分结果
     * @param param
     * @return
     */
    Result<String> submit(ScoringParam param);

    /**
     * 查询评委赛道
     * @param mobile
     * @return
     */
    Result<String> getJudgeTracks(String mobile,String type);

}
