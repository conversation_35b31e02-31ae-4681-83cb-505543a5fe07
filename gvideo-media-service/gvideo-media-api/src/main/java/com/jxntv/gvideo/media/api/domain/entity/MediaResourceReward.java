package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jxntv.gvideo.common.mybatis.AesEncryptTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * media_resource_reward 资源奖励表
 *
 * <AUTHOR>
 */
@Data
@TableName(autoResultMap = true)
public class MediaResourceReward implements Serializable {
    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源类型  1 回答 2评论 3回复评论
     */
    private Integer resourceType;

    /**
     * 资源ID media_resource.id
     */
    private Long resourceId;
    /**
     * 资源内容
     */
    private String resourceContent;
    /**
     * 奖励类型 1：回答
     */
    private Integer rewardType;
    /**
     * 发布账号id（认证号）
     */
    private Long releaseId;
    /**
     * 发布人昵称
     */
    private String releaseNickname;
    /**
     * 发布人手机号
     */
    @TableField(typeHandler = AesEncryptTypeHandler.class)
    private String releaseMobile;
    /**
     * 脱敏发布人手机号
     */
    private String maskReleaseMobile;
    /**
     * 发布时间
     */
    private Date releaseDate;

    /**
     * 社区ID
     */
    private Long groupId;

    /**
     * 社区名称
     */
    private String groupName;

    /**
     * 状态 0:待审核 1已审核:奖励 2已审核:已忽略
     */
    private Integer status;

    /**
     * 奖励金额
     */
    private BigDecimal amount;

    /**
     * 审核人ID
     */
    private Long auditUserId;

    /**
     * 审核人姓名
     */
    private String auditUserName;

    /**
     * 审核时间
     */
    private Date auditDate;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 提问ID
     */
    private Long questionId;

    /**
     * 内容类型 1 视频自制内容 2 视频电影 3 视频节目 4 音频fm 5 视频剧集 6 活动直播 7 互动直播 8 H5页面
     */
    private Integer contentType;

    /**
     * 图片oss id集合
     */
    private String image;
    /**
     * 语音oss id
     */
    private String soundOssId;

    /**
     * 视频oss id
     */
    private String videoOssId;

    private static final long serialVersionUID = 1L;

    /**
     * 平台类型：0-今视频、 2-小程序
     */
    @TableField(exist = false)
    private Integer platform;

    /**
     * 是否追问： 0-否 1-是
     */
    @TableField(exist = false)
    private Integer isQuestionClosely;
}