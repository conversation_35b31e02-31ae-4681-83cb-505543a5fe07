package com.jxntv.gvideo.media.api.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.media.api.domain.entity.PlanResource;
import org.apache.ibatis.annotations.Update;

public interface PlanResourceMapper extends BaseMapper<PlanResource> {

    @Update("UPDATE plan_resource SET status = 2,end_date = now() WHERE plan_id = #{planId}")
    void overByPlanId(Long planId);
}