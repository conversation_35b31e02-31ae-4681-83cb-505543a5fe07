package com.jxntv.gvideo.media.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * 频道的标签(ChannelLabel)实体类
 *
 *
 * @since 2020-03-21 13:41:44
 */
public class ChannelLabel implements Serializable {
    private static final long serialVersionUID = -39246358356010217L;
    /**
    * 自增ID
    */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
    * 频道ID
    */
    private Long channelId;
    /**
    * 标签ID
    */
    private Long labelId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getChannelId() {
        return channelId;
    }

    public void setChannelId(Long channelId) {
        this.channelId = channelId;
    }

    public Long getLabelId() {
        return labelId;
    }

    public void setLabelId(Long labelId) {
        this.labelId = labelId;
    }

}