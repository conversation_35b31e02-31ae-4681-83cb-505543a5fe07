package com.jxntv.gvideo.common.utils;

/**
 * Unicode编码工具类
 */
public class UnicodeUtils {
    
    /**
     * 将Unicode编码字符串转换为UTF-8字符串
     * @param unicodeStr 包含Unicode转义序列的字符串
     * @return 转换后的UTF-8字符串
     */
    public static String decodeUnicode(String unicodeStr) {
        if (unicodeStr == null) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        int len = unicodeStr.length();
        for (int i = 0; i < len; i++) {
            char c = unicodeStr.charAt(i);
            if (c == '\\' && i + 1 < len && unicodeStr.charAt(i + 1) == 'u') {
                try {
                    String hex = unicodeStr.substring(i + 2, i + 6);
                    sb.append((char) Integer.parseInt(hex, 16));
                    i += 5;
                } catch (Exception e) {
                    sb.append(c);
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}