package com.jxntv.gvideo.common.utils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;


/**
 * 调用API接口判断日期是否是工作日 周末还是节假日
 * 工作日对应结果为 0, 休息日对应结果为 1, 节假日对应的结果为 2；
 * <AUTHOR>
 */
public class HolidayUtil {

    public static int request( String dateStr) {
        String httpUrl = "https://tool.bitefu.net/jiari/";
        BufferedReader reader = null;
        String result = null;
        StringBuffer sbf = new StringBuffer();
        httpUrl = httpUrl + "?d=" + dateStr;
        //工作日对应结果为 0, 休息日对应结果为 1, 节假日对应的结果为 2
        int d=0;

        try {
            URL url = new URL(httpUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.connect();
            InputStream is = connection.getInputStream();
            reader = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            String strRead = null;
            while ((strRead = reader.readLine()) != null) {
                sbf.append(strRead);
            }
            reader.close();
            result = sbf.toString();
            if(result!=null && !"".equals(result)){
                try {
                    d=Integer.parseInt(result);
                } catch (NumberFormatException e) {
                    e.printStackTrace();
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return d;
    }

    public static void main(String[] args) {
        //判断今天是否是工作日 周末 还是节假日
        String httpArg="20230423";//f.format(new Date());
        System.out.println(httpArg);
        int n = request(httpArg);
        //工作日对应结果为 0, 休息日对应结果为 1, 节假日对应的结果为 2
        System.out.println(n);
    }
}
