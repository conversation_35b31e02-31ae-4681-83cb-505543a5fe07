package com.jxntv.gvideo.aliyun.sdk.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: niedamin
 * @Date: 2022/11/03 15:42
 */
@Data
@NoArgsConstructor
public class DouyinRefundQueryParam implements Serializable {

    /**
     * appid
     */
    @JSONField(name = "app_id")
    private String appId;

    /**
     * 商户分配支付单号，标识进行退款的订单
     */
    @JSONField(name = "out_refund_no")
    @NotNull
    private String outRefundNo;

    /**
     * 商户分配支付单号，标识进行退款的订单
     */
    @JSONField(name = "sign")
    private String sign;

    /**
     * 第三方平台服务商 id，非服务商模式留空
     */
    @JSONField(name = "thirdparty_id")
    private String thirdpartyId;
}
