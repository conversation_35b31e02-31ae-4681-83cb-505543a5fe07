package com.jxntv.gvideo.aliyun.sdk.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: niedamin
 * @Date: 2022/11/07 15:05
 */
@Data
@NoArgsConstructor
public class DouyinSettleParam implements Serializable {

    /**
     * appid
     */
    @JSONField(name = "app_id")
    private String appId;

    /**
     * 开发者侧的结算单号，相同结算单号小程序平台会进行幂等处理。 只能使用数字、大小写字母_-*。
     */
    @JSONField(name = "out_settle_no")
    @NotNull
    private String outSettleNo;

    /**
     * 该笔分账单关联的商户订单单号，标识进行结算的订单（即对应支付预下单接口的"开发者侧的订单号out_order_no"参数）
     */
    @JSONField(name = "out_order_no")
    @NotNull
    private String outOrderNo;

    /**
     * 结算描述，长度限制 80 个字符
     */
    @JSONField(name = "settle_desc")
    @NotNull
    private String settleDesc;

    /**
     * 其他分账方信息，分账分配参数 SettleParameter 数组序列化后生成的 json 格式字符串
     */
    @JSONField(name = "settle_params")
    private String settleParams;

    /**
     * sign
     */
    @JSONField(name = "sign")
    private String sign;

    /**
     * 开发者自定义字段，回调原样回传
     */
    @JSONField(name = "cp_extra")
    private String cpExtra;

    /**
     * 商户自定义分账回调地址（要求https
     */
    @JSONField(name = "notify_url")
    private String notifyUrl;

    /**
     * 第三方平台服务商 id，非服务商模式留空
     */
    @JSONField(name = "thirdparty_id")
    private String thirdpartyId;

    /**
     * 特别注意：该参数类型为string而非bool。 1. 如果为'true'（默认值为'true'，即不传该字段等价于'true'），则该笔订单剩余未分账金额会一并结算给商户；
     * 2. 如果为'false'，该笔订单剩余未分账的金额不会一并结算给商户，可以对该笔订单再次进行分账；
     */
    @JSONField(name = "finish")
    private String finish;
}
