package com.jxntv.gvideo.aliyun.sdk.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: niedamin
 * @Date: 2022/11/07 17:34
 */
@Data
@NoArgsConstructor
public class PushDouyinMsgParam implements Serializable {

    /**
     * 服务端 API 调用标识，通过 getAccessToken 获取
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * 服务端 API 调用标识，通过 getAccessToken 获取
     */
    @JSONField(name = "app_id")
    private String appId;

    /**
     * 模板的 id
     */
    @JSONField(name = "tpl_id")
    private String tplId;
    /**
     * 接收消息目标用户的 open_id
     */
    @JSONField(name = "open_id")
    @NotNull
    private String openId;
    /**
     * 模板内容，格式形如 { "key1": "value1", "key2": "value2" }，具体使用方式参考下文请求示例
     */
    @JSONField(name = "data")
    @NotNull
    private Object data;
    /**
     * 跳转的页面
     */
    @JSONField(name = "page")
    private String page;


}
