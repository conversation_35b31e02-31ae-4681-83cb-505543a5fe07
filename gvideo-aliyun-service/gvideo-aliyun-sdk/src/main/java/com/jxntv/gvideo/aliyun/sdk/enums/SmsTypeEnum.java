package com.jxntv.gvideo.aliyun.sdk.enums;

/**
 * Created on 2020-03-04
 */
public enum SmsTypeEnum {

    LOGIN(1, "登录"),
    CHANGE_PASSWORD(2, "修改密码"),
    CHANGE_MOBILE(3, "换绑手机号"),
    SWITCH_LOGIN(4, "切换登录"),
    INVITE_USER(5, "邀请用户"),

    ;

    private int value;
    private String desc;

    SmsTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static SmsTypeEnum getTypeByValue(int value) {
        for (SmsTypeEnum enums : SmsTypeEnum.values()) {
            if (enums.getValue() == value) {
                return enums;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
