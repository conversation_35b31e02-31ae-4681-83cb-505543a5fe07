package com.jxntv.gvideo.aliyun.sdk.fallback;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.*;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class OssClientFallback implements FallbackFactory<OssClient> {
    @Override
    public OssClient create(Throwable throwable) {

        return new OssClient() {
            @Override
            public Result<OssDTO> getAvatar(String fileId) {

                log.error("OssClient.getAvatar() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getLiveRecord(String fileId) {

                log.error("OssClient.getLiveRecord() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getFuzzy(String fileId) {

                log.error("OssClient.getFuzzy() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listAvatarByIds(List<String> fileIds) {

                log.error("OssClient.listAvatarByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listLiveRecordByIds(List<String> fileIds) {

                log.error("OssClient.listLiveRecordByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> generateOssFile(Integer bizType, String extraId) {

                log.error("OssClient.generateOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> saveLiveRecordOssFile(String ossName) {

                log.error("OssClient.saveLiveRecordOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> confirmOssFile(String fileId) {

                log.error("OssClient.confirmOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> existOssFile(String name) {

                log.error("OssClient.existOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteOssFile(String fileId) {
                log.error("OssClient.deleteOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> changeUpload() {

                log.error("OssClient.changeUpload() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteFile() {

                log.error("OssClient.deleteFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> getForeverOssUrl(String fileId) {
                log.error("OssClient.getForeverOssUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getOssFile(String fileId) {

                log.error("OssClient.getOssFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getOssResize(String fileId) {

                log.error("OssClient.getOssResize() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getOriOss(String fileId) {

                log.error("OssClient.getOriOss() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getOriFile(String fileId) {

                log.error("OssClient.getOriFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listOriFileByIds(List<String> fileIds) {

                log.error("OssClient.listOriFileByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listByIds(List<String> fileIds) {

                log.error("OssClient.listByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listResizeByIds(List<String> fileIds) {

                log.error("OssClient.listResizeByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<OssDTO>> listSelfResizeByIds(List<OssResizeDTO> resizeDTOList) {

                log.error("OssClient.listSelfResizeByIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssImgMetaData> getOssImgMetaData(String ossId) {
                log.error("OssClient.getOssImgMetaData() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodVideoDTO> generateVideoFile(String fileName) {

                log.error("OssClient.generateVideoFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodVideoDTO> refreshVideoFile(String videoId) {

                log.error("OssClient.refreshVideoFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodVideoDTO> generateUgcVideoFile(String fileName) {

                log.error("OssClient.generateUgcVideoFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodImageDTO> generateImageFile(String fileName, String ext) {

                log.error("OssClient.generateImageFile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodVideoPlayDTO> getVideoFileUrl(String videoId) {

                log.error("OssClient.getVideoFileUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<VodVideoPlayDTO>> listVideoFileUrl(List<String> videoIds) {

                log.error("OssClient.listVideoFileUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodAudioPlayDTO> getAudioFileUrl(String audioId) {

                log.error("OssClient.getAudioFileUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<VodAudioPlayDTO>> listAudioFileUrl(List<String> audioIds) {

                log.error("OssClient.listAudioFileUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodImagePlayDTO> getImageFileUrl(String imageId) {

                log.error("OssClient.getImageFileUrl() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> isVideoExist(String fileName) {

                log.error("OssClient.isVideoExist() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> confirmVideoUpload(String videoId) {

                log.error("OssClient.confirmVideoUpload() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> createWatermarkImage(VodWatermarkImageCreateDTO dto) {
                log.error("OssClient.getVideoStatus() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> addVideoWatermark(VodWatermarkAddDTO dto) {
                log.error("OssClient.addVideoWatermark() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<SoundDTO> getContentBySoundId(String uuid) {

                log.error("OssClient.getContentBySoundId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<SoundDTO>> listContentBySoundIds(List<String> uuidList) {

                log.error("OssClient.listContentBySoundIds() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> saveSoundContent(SoundDTO soundDTO) {

                log.error("OssClient.saveSoundContent() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<VodVideoInfoDTO> getVideoInfo(String ossId) {

                log.error("OssClient.getVideoInfo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<NlpDTO> getToken() {

                log.error("OssClient.getToken() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> addLiveRecordVodConfig(AddLiveRecordVodConfigDTO dto) {

                log.error("OssClient.addLiveRecordVodConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> delLiveRecordVodConfig(DelLiveRecordVodConfigDTO dto) {

                log.error("OssClient.delLiveRecordVodConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> batchDelLiveRecordVodConfig(List<DelLiveRecordVodConfigDTO> dtoList) {

                log.error("OssClient.batchDelLiveRecordVodConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> batchAddLiveRecordVodConfig(List<AddLiveRecordVodConfigDTO> dtoList) {

                log.error("OssClient.batchAddLiveRecordVodConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> realTimeRecordCommand(RealTimeRecordCommandDTO dto) {

                log.error("OssClient.realTimeRecordCommand() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<LiveRecordVideoDTO>> listLiveRecordVideo(ListLiveRecordVideoSearchDTO searchDTO) {

                log.error("OssClient.listLiveRecordVideo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> addLiveRecordOssConfig(AddLiveRecordOssConfigDTO dto) {

                log.error("OssClient.addLiveRecordOssConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> delLiveRecordOssConfig(DeleteLiveAppRecordConfigDTO dto) {

                log.error("OssClient.delLiveRecordOssConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> addLiveRecordOssNotifyConfig(AddLiveRecordOssNotifyConfigDTO dto) {

                log.error("OssClient.addLiveRecordOssNotifyConfig() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> fileUpload(OssFileUploadDTO dto) {
                log.error("OssClient.fileUpload() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<OssDTO> getAiImage(String fileName, String taskId) {
                log.error("OssClient.getAiImage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };

    }
}
