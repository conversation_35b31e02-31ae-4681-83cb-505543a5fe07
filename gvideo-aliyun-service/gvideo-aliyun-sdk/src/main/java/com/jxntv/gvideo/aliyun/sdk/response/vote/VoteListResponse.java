package com.jxntv.gvideo.aliyun.sdk.response.vote;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class VoteListResponse implements Serializable {
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("total")
    private long total;
    @JsonProperty("hasMore")
    private boolean hasMore;
    @JsonProperty("rows")
    private List<ResultDTO> rows;



    @NoArgsConstructor
    @Data
    public static class ResultDTO {

        @JsonProperty("id")
        private Long id;

        /**
         * 投票名称
         */
        @JsonProperty("title")
        private String title;

        /**
         * 图片URL
         */
        @JsonProperty("bannerUrl")
        private String bannerUrl;

        /**
         * 开启/关闭投票，1关闭，0开启
         */
        @JsonProperty("closed")
        private Integer closed;

        /**
         * 活动状态  0-待开始，1-进行中，2-已结束
         */
        @JsonProperty("status")
        private Integer status;

        /**
         * 活动链接
         */
        @JsonProperty("activityUrl")
        private String activityUrl;

    }
}
