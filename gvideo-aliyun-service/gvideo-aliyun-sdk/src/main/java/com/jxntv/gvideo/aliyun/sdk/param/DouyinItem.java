package com.jxntv.gvideo.aliyun.sdk.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: niedamin
 * @Date: 2022/11/02 17:53
 */
@Data
public class DouyinItem implements Serializable {

    /**
     * 开发者侧商品 ID，长度 <= 64 byte
     */
    @JSONField(name = "item_code")
    private String itemCode;

    /**
     * 子订单商品图片 URL，长度 <= 512 byte
     *
     */
    @JSONField(name = "img")
    private String img;

    /**
     * 子订单商品介绍标题，长度 <= 256 byte
     */
    @JSONField(name = "title")
    private String title;

    /**
     * 子订单商品介绍副标题，长度 <= 256 byte
     */
    @JSONField(name = "sub_title")
    private String subTitle;

    /**
     * 单类商品的数目
     */
    @JSONField(name = "amount")
    private Integer amount = 1;

    /**
     * 单类商品的总价，单位为分
     */
    @JSONField(name = "price")
    private Integer price;

}
