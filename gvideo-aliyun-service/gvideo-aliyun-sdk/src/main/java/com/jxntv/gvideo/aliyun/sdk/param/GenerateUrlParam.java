package com.jxntv.gvideo.aliyun.sdk.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: niedamin
 * @Date: 2022/11/14 9:25
 */
@Data
@NoArgsConstructor
public class GenerateUrlParam {

    /**
     * 服务端 API 调用标识，通过 getAccessToken 获取
     */
    @JSONField(name = "access_token")
    private String accessToken;

    /**
     * 小程序ID
     */
    @JSONField(name = "ma_app_id")
    private String maAppId;

    /**
     * 宿主名称，可选 douyin，douyinlite
     */
    @JSONField(name = "app_name")
    private String appName = "douyin";

    /**
     * 到期失效的URL Link的失效时间。为 Unix 时间戳，实际失效时间为距离当前时间小时数，向上取整。最长间隔天数为180天。
     */
    @JSONField(name = "expire_time")
    private Integer expireTime;

    /**
     * 通过URL Link进入小程序时的 query（json形式），若无请填{}。最大1024个字符，只支持数字，大小写英文以及部分特殊字符：`{}!#$&'()*+,/:;=?@-._~%``。
     */
    @JSONField(name = "query")
    private String query = "{}";

}
