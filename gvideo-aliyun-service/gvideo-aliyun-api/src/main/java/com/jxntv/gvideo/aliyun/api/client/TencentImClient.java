package com.jxntv.gvideo.aliyun.api.client;

import com.jxntv.gvideo.aliyun.api.client.dto.tencent.*;
import com.jxntv.gvideo.aliyun.api.client.fallback.TencentImClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "tencentImClient", url = "${tencent.im.domain}", contextId = "im", path = "/v4", fallbackFactory = TencentImClientFallback.class)
public interface TencentImClient {

    @PostMapping("/sns/black_list_add")
    BlacklistResponse addBlacklist(@SpringQueryMap Sign sign, @RequestBody BlacklistRequest request);

    @PostMapping("/sns/black_list_delete")
    BlacklistResponse deleteBlacklist(@SpringQueryMap Sign sign, @RequestBody BlacklistRequest request);

    @PostMapping("/group_open_http_svc/create_group")
    GroupAddResponse addGroup(@SpringQueryMap Sign sign, @RequestBody GroupAddRequest request);

    @PostMapping("/group_open_http_svc/modify_group_base_info")
    GroupEditResponse editGroupBaseInfo(@SpringQueryMap Sign sign, @RequestBody GroupEditRequest request);

    @PostMapping("/group_open_http_svc/change_group_owner")
    GroupOwnerChangeResponse changeGroupOwner(@SpringQueryMap Sign sign, @RequestBody GroupOwnerChangeRequest request);

    @PostMapping("/group_open_http_svc/destroy_group")
    GroupDestroyResponse destroyGroup(@SpringQueryMap Sign sign, @RequestBody GroupDestroyRequest request);

    @PostMapping("/group_open_http_svc/add_group_member")
    GroupMemberAddResponse addGroupMember(@SpringQueryMap Sign sign, @RequestBody GroupMemberAddRequest request);

    @PostMapping("/group_open_http_svc/delete_group_member")
    GroupMemberDeleteResponse deleteGroupMember(@SpringQueryMap Sign sign, @RequestBody GroupMemberDeleteRequest request);

    @PostMapping("/group_open_http_svc/get_group_member_info")
    GroupMemberResponse getGroupMemberInfo(@SpringQueryMap Sign sign, @RequestBody GroupMemberRequest request);

    @PostMapping("/group_open_http_svc/send_group_msg")
    SendGroupMessageResponse sendGroupMessage(@SpringQueryMap Sign sign, @RequestBody SendGroupMessageRequest request);

    @PostMapping("/group_open_http_svc/forbid_send_msg")
    ForbidSendGroupMessageResponse forbidSendGroupMessage(@SpringQueryMap Sign sign, @RequestBody ForbidSendGroupMessageRequest request);

    @PostMapping("/im_open_login_svc/account_import")
    AccountAddResponse addAccount(@SpringQueryMap Sign sign, @RequestBody AccountAddRequest request);

    @PostMapping("/openim/sendmsg")
    SendC2CMessageResponse sendC2CMessage(@SpringQueryMap Sign sign, @RequestBody SendC2CMessageRequest request);

    @PostMapping("/openim/batchsendmsg")
    BatchSendC2CMessageResponse batchSendC2CMessage(@SpringQueryMap Sign sign, @RequestBody BatchSendC2CMessageRequest request);

    @PostMapping("/openconfigsvr/setnospeaking")
    SetNoSpeakingResponse setNoSpeaking(@SpringQueryMap Sign sign, @RequestBody SetNoSpeakingRequest request);

    @PostMapping("/profile/portrait_set")
    SetProfileResponse setUserProfile(@SpringQueryMap Sign sign, @RequestBody SetProfileRequest request);

    @PostMapping("/im_open_login_svc/account_check")
    CheckAccountResponse checkAccount(@SpringQueryMap Sign sign, @RequestBody CheckAccountRequest request);
}
