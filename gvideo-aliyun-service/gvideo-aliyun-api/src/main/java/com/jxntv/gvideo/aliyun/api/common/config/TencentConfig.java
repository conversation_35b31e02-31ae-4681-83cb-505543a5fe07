package com.jxntv.gvideo.aliyun.api.common.config;

import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.live.v20180801.LiveClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/7/26
 * Email: <EMAIL>
 */
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "tencent")
public class TencentConfig {
    /**
     * IM配置
     */
    private Im im;
    /**
     * 直播配置
     */
    private Live live;


    @Data
    public static class Im {
        /**
         * 应用id
         */
        private Long appId = 1400738155L;

        /**
         * 应用秘钥
         */
        private String appKey = "198736cddc600277e79078b40452be3e7c2d8abb2c5c9a3bd33c9a6bab7e1085";
        /**
         * 管理员账号
         */
        private String admin = "administrator";
        /**
         * 签名过期时间
         */
        private long expire = 86400L;
        /**
         * IM API域名
         */
        private String domain = "https://console.tim.qq.com";
        /**
         * IM头像格式化参数
         */
        private String avatarFormat;


    }


    @Data
    public static class Live {
        /**
         * 直播openID
         */
        private String openId = "AKIDpdeQzHFGXAX8Ur58qJniMCwZgYR75NnS";
        /**
         * 直播openKey
         */
        private String openKey = "0LPCu5WhcazYru2K0RhjZCZgD7kgwBBv";
        /**
         * 直播api端点
         */
        private String endPoint = "live.tencentcloudapi.com";

        /**
         * 直播域名
         */
        private String domain = "https://jsp-act-live.jxtvcn.com.cn";

        /**
         * 直播证书key
         */
        private String licenseKey = "aed25d9dadd9c14b18dae8a33ab886ba";
        /**
         * 直播证书url
         */
        private String licenseUrl = "http://license.vod2.myqcloud.com/license/v1/e1f05164645379c2150caad07f816f0d/TXLiveSDK.licence";

        /**
         * 云API密钥 上申请的标识身份的 SecretId
         */
        private String secretId = "AKIDpZDetuhR0MWXO3d3JUUJxQiDVaUBf9Xn";
        /**
         * 云API密钥 上申请的标识身份的 SecretKey
         */
        private String secretKey  = "BAwa5VklfR2SglfLrNkx0ud9TddS6av6";

        public LiveClient getClient() {
            if (Objects.isNull(ClientHolder.client)) {
                Credential cred = new Credential(this.openId, this.openKey);
                HttpProfile httpProfile = new HttpProfile();
                httpProfile.setEndpoint(this.endPoint);
                ClientProfile clientProfile = new ClientProfile();
                clientProfile.setHttpProfile(httpProfile);
                ClientHolder.client = new LiveClient(cred, "", clientProfile);
            }
            return ClientHolder.client;
        }

        //  重写部分set方法，使得client更新

        public void setOpenId(String openId) {
            this.openId = openId;
            ClientHolder.client=null;
        }

        public void setOpenKey(String openKey) {
            this.openKey = openKey;
            ClientHolder.client=null;
        }

        public void setEndPoint(String endPoint) {
            this.endPoint = endPoint;
            ClientHolder.client=null;
        }

        public static class ClientHolder {
            public static LiveClient client = null;
        }
    }


}
