package com.jxntv.gvideo.aliyun.api.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.aliyun.api.entity.OssFile;
import com.jxntv.gvideo.aliyun.sdk.dto.OssImgMetaData;

/**
 * Created on 2020-03-03
 */
public interface OssFileService extends IService<OssFile> {

    /**
     * 更新图片属性信息
     *
     * @param uuid 图片文件主键ID
     * @return 更新对象
     */
    OssFile completeProperties(String uuid);

    /**
     * 获取OSS图片元数据
     *
     * @param ossId
     * @return
     */
    OssImgMetaData getOssImgMetaData(String ossId);
}
