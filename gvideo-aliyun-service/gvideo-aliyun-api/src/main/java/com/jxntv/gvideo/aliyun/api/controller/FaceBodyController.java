package com.jxntv.gvideo.aliyun.api.controller;

import com.aliyun.facebody20191230.models.*;
import com.jxntv.gvideo.aliyun.api.service.OssService;
import com.jxntv.gvideo.aliyun.sdk.FaceBodyClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.param.FaceBodyDetectParam;
import com.jxntv.gvideo.aliyun.sdk.response.AliFaceBodyResp;
import com.jxntv.gvideo.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RefreshScope
public class FaceBodyController implements FaceBodyClient {

    @Resource(name = "facebodyClient")
    private com.aliyun.facebody20191230.Client client;

    @Resource
    private OssService ossService;

    @Value("${aliyun.facebody.baseErrorMsg}")
    private String errorMsg;

    /**
     * 人脸检测
     *
     * @param param
     * @return
     */
    @Override
    public Result<AliFaceBodyResp> detectFace(FaceBodyDetectParam param) {
        AliFaceBodyResp resp = new AliFaceBodyResp();
        OssDTO ossDTO = ossService.getOssResize(param.getOssId());
        if (Objects.isNull(ossDTO) || StringUtils.isEmpty(ossDTO.getUrl())) {
            return Result.fail("照片不存在");
        }

        try {

            // 人脸检测
            AliFaceBodyResp detectFaceResp = detectFace(ossDTO.getUrl(),param.getOssId());
            if (!detectFaceResp.isSuccess()) {
                return Result.ok(detectFaceResp);
            }

            // 公众人物检测
            AliFaceBodyResp recognizePublicFaceResp = recognizePublicFace(ossDTO.getUrl(),param.getOssId());
            if (param.getRecognizePublicFace().equals(true) && !recognizePublicFaceResp.isSuccess()) {
                return Result.ok(recognizePublicFaceResp);
            }

            // 明星检测
            AliFaceBodyResp detectCelebrityResp = detectCelebrity(ossDTO.getUrl(),param.getOssId());
            if (param.getDetectCelebrity().equals(true) && !detectCelebrityResp.isSuccess()) {
                return Result.ok(detectCelebrityResp);
            }

            resp.setSuccess(true);
            return Result.ok(resp);
        } catch (Exception error) {
            log.error("人脸综合检测ossId:{},error:{}", param.getOssId(), error.getMessage());
            resp.setSuccess(false);
            resp.setErrMsg(errorMsg);
            return Result.ok(resp);
        }
    }

    /**
     * 人脸检测
     *
     * @param imageUrl
     * @return
     */
    public AliFaceBodyResp detectFace(String imageUrl,String ossId) {
        AliFaceBodyResp resp = new AliFaceBodyResp();
        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openConnection().getInputStream();
            com.aliyun.facebody20191230.models.DetectFaceAdvanceRequest detectFaceRequest = new com.aliyun.facebody20191230.models.DetectFaceAdvanceRequest()
                    .setImageURLObject(inputStream);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();
            // 复制代码运行请自行打印 API 的返回值
            DetectFaceResponse detectFaceResponse = client.detectFaceAdvance(detectFaceRequest, runtime);
            resp.setSuccess(true);
        } catch (Exception error) {
            resp.setSuccess(false);
            resp.setErrMsg(errorMsg);
            log.error("人脸检测ossId:{},error:{}", ossId, error.getMessage());
        }
        return resp;
    }

    /**
     * 公众人物检测
     *
     * @param imageUrl
     */
    public AliFaceBodyResp recognizePublicFace(String imageUrl,String ossId) {
        AliFaceBodyResp resp = new AliFaceBodyResp();
        resp.setSuccess(true);
        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openConnection().getInputStream();
            com.aliyun.facebody20191230.models.RecognizePublicFaceAdvanceRequest.RecognizePublicFaceAdvanceRequestTask task1 = new com.aliyun.facebody20191230.models.RecognizePublicFaceAdvanceRequest.RecognizePublicFaceAdvanceRequestTask()
                    .setImageURLObject(inputStream);
            com.aliyun.facebody20191230.models.RecognizePublicFaceAdvanceRequest recognizePublicFaceAdvanceRequest = new com.aliyun.facebody20191230.models.RecognizePublicFaceAdvanceRequest()
                    .setTask(Collections.singletonList(
                            task1
                    ));
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

            RecognizePublicFaceResponse recognizePublicFaceResponse = client.recognizePublicFaceAdvance(recognizePublicFaceAdvanceRequest, runtime);
            RecognizePublicFaceResponseBody.RecognizePublicFaceResponseBodyData data = recognizePublicFaceResponse.getBody().getData();
            List<RecognizePublicFaceResponseBody.RecognizePublicFaceResponseBodyDataElements> elements = data.getElements();
            if (CollectionUtils.isNotEmpty(elements)) {
                elements.forEach(e -> {
                    List<RecognizePublicFaceResponseBody.RecognizePublicFaceResponseBodyDataElementsResults> results = e.getResults();
                    if (CollectionUtils.isNotEmpty(results)) {
                        results.forEach(res -> {
                            if ("sface".equals(res.getLabel())) {
                                // 图片中包含敏感人脸
                                resp.setSuccess(false);
                                /*Float rate = res.getRate();
                                resp.setErrMsg(rate + "%的概率图片中含有敏感人脸");*/
                                resp.setErrMsg("图片中含有敏感人脸");
                            }
                        });
                    }
                });
            }
        } catch (Exception error) {
            resp.setSuccess(false);
            resp.setErrMsg(errorMsg);
            log.error("公众人物检测ossId:{},error:{}", ossId, error.getMessage());
        }
        return resp;
    }

    /**
     * 明星检测
     *
     * @param imageUrl
     */
    public AliFaceBodyResp detectCelebrity(String imageUrl,String ossId) {
        AliFaceBodyResp resp = new AliFaceBodyResp();
        try {
            URL url = new URL(imageUrl);
            InputStream inputStream = url.openConnection().getInputStream();
            com.aliyun.facebody20191230.models.DetectCelebrityAdvanceRequest detectCelebrityRequest = new com.aliyun.facebody20191230.models.DetectCelebrityAdvanceRequest()
                    .setImageURLObject(inputStream);
            com.aliyun.teautil.models.RuntimeOptions runtime = new com.aliyun.teautil.models.RuntimeOptions();

            DetectCelebrityResponse detectCelebrityResponse = client.detectCelebrityAdvance(detectCelebrityRequest, runtime);
            DetectCelebrityResponseBody body = detectCelebrityResponse.getBody();
            List<DetectCelebrityResponseBody.DetectCelebrityResponseBodyDataFaceRecognizeResults> faceRecognizeResults = body.getData().getFaceRecognizeResults();
            // 没有检测出明星
            boolean success = CollectionUtils.isEmpty(faceRecognizeResults);
            resp.setSuccess(success);
            if (!success) {
                List<String> list = faceRecognizeResults.stream().map(DetectCelebrityResponseBody.DetectCelebrityResponseBodyDataFaceRecognizeResults::getName).collect(Collectors.toList());
                String starStr = String.join("，", list);
                resp.setErrMsg("照片检测出明星：" + starStr);
            }
        } catch (Exception error) {
            resp.setSuccess(false);
            resp.setErrMsg(errorMsg);
            log.error("明星检测ossId:{},error:{}", ossId, error.getMessage());
        }
        return resp;
    }

}
