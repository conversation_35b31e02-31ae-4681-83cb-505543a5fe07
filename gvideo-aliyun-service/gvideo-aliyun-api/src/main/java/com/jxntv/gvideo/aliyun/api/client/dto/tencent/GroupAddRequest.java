package com.jxntv.gvideo.aliyun.api.client.dto.tencent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Data
public class GroupAddRequest implements Serializable {

    @JsonProperty("Owner_Account")
    private String ownerAccount;
    @JsonProperty("Type")
    private String type;
    @JsonProperty("GroupId")
    private String groupId;
    @JsonProperty("Name")
    private String name;
    @JsonProperty("Introduction")
    private String introduction;
    @JsonProperty("Notification")
    private String notification;
    @JsonProperty("FaceUrl")
    private String faceUrl;
}
