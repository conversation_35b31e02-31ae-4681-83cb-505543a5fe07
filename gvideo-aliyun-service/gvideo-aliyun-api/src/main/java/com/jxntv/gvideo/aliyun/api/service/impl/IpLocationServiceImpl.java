package com.jxntv.gvideo.aliyun.api.service.impl;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.jthinking.common.util.ip.IPInfo;
import com.jthinking.common.util.ip.IPInfoUtils;
import com.jxntv.gvideo.aliyun.api.service.IpLocationService;
import com.jxntv.gvideo.aliyun.sdk.dto.IpLocationDTO;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.common.utils.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Service
public class IpLocationServiceImpl implements IpLocationService {

    @Resource
    private RestTemplate restTemplate;

    private final static String IP_138_API = "https://api.ip138.com/ip/?ip=%s&datatype=jsonp&token=51e4e24562b35fb5f620d82ea0a2d801";

    private final static String IP_VORE_API = "https://api.vore.top/api/IPdata?ip=%s";

    static{
        IPInfoUtils.init();
    }



    @Override
    public IpLocationDTO getLocation(String ip) {
        return localSdk(ip);
//        return ip138Api(ip);
    }

    /**
     * ip地址查询
     * @return
     */
    private IpLocationDTO localSdk(String ip){
        IPInfo ipInfo = IPInfoUtils.getIpInfo(ip);
        if ("未知".equals(ipInfo.getCountry())) {
            return ip138Api(ip);
        }
        IpLocationDTO ipLocationDTO = new IpLocationDTO();
        ipLocationDTO.setCountry(ipInfo.getCountry());
        String city = ipInfo.getAddress();
        if (city.equals(ipInfo.getCountry())) {
            city ="";
        }
        if (city.equals(ipInfo.getProvince())) {
            city = "";
        }
        if (city.startsWith("中国")){
            city =  city.replace("中国","");
        }
        if (city.lastIndexOf("市") > -1) {
            int provanceIndex = city.indexOf("省");
            city = city.substring(provanceIndex > -1 ? provanceIndex + 1 : 0, city.lastIndexOf("市")+1);
        }

        ipLocationDTO.setRegion(ipInfo.getProvince());
        ipLocationDTO.setCity(city);
        ipLocationDTO.setIp(ip);
        return ipLocationDTO;
    }
    /**
     * ip地址查询
     * @param ip
     * @see https://user.ip138.com/ip/doc/
     */
    private IpLocationDTO ip138Api(String ip) {
        try {
            AssertUtil.notEmpty(ip, CodeMessage.BAD_REQUEST.getCode(), "ip is empty");
            String result = HttpUtil.get(String.format(IP_138_API, ip));
            log.info("【调用138api查询ip归属地】追踪id:{},入参:{},结果:{}", TraceContext.traceId(), ip, result);
            if (StringUtils.isNotBlank(result)) {
                JSONObject jsonObject = JSON.parseObject(result);
                if ("ok".equals(jsonObject.getString("ret"))) {
                    JSONArray jsonArray = jsonObject.getJSONArray("data");
                    IpLocationDTO ipLocationDTO = new IpLocationDTO();
                    // 国家
                    ipLocationDTO.setCountry(jsonArray.getString(0));
                    // 省
                    ipLocationDTO.setRegion(jsonArray.getString(1));
                    // 城市
                    ipLocationDTO.setCity(jsonArray.getString(2));
                    ipLocationDTO.setIp(ip);
                    return ipLocationDTO;
                }
            }
        }catch (Exception e) {
            log.error("追踪id:{},调用138查询ip归属地接口失败:{}",e.getMessage(),e);
        }

        return null;
    }

    private IpLocationDTO voreApi(String ip){
        AssertUtil.notEmpty(ip, CodeMessage.BAD_REQUEST.getCode(), "ip is empty");
        IpLocationDTO result = null;
        IpQueryResponse ipData = null;
        String url = String.format(IP_VORE_API, ip);
        try {
            log.info("查询ip归属地:request{}", url);
            ResponseEntity<String> forEntity = restTemplate.getForEntity(url, String.class);
            String body = forEntity.getBody();

            log.info("查询ip归属地:response{}", JsonUtils.toJson(body));
            ipData = JsonUtils.fromJson(body, IpQueryResponse.class);
        } catch (Exception e) {
            log.error("查询ip归属地:", e);
        }
        log.info("【调用vore-api查询ip归属地】追踪id:{},入参:{},结果:{}", TraceContext.traceId(),ip,ipData);

        if (Objects.nonNull(ipData)) {
            result = new IpLocationDTO();
            IpQueryResponse.IpinfoDTO ipinfo = ipData.getIpinfo();
            IpQueryResponse.AdcodeDTO adcode = ipData.getAdcode();
            String country = Boolean.TRUE.equals(ipinfo.getCnip()) ? "中国" : adcode.getP();
            result.setIp(ip);
            result.setCountry(country);
            result.setCity(adcode.getC());
            result.setRegion(adcode.getP());
        }
        return result;

    }


    /**
     * IP归属地查询返回实体
     */
    @Data
    @NoArgsConstructor
    public static class IpQueryResponse {

        @JsonProperty("code")
        private Integer code;
        @JsonProperty("msg")
        private String msg;
        @JsonProperty("ipinfo")
        private IpinfoDTO ipinfo;
        @JsonProperty("ipdata")
        private IpdataDTO ipdata;
        @JsonProperty("adcode")
        private AdcodeDTO adcode;
        @JsonProperty("tips")
        private String tips;
        @JsonProperty("time")
        private Integer time;

        @NoArgsConstructor
        @Data
        public static class IpinfoDTO {
            @JsonProperty("type")
            private String type;
            @JsonProperty("text")
            private String text;
            @JsonProperty("cnip")
            private Boolean cnip;
        }

        @NoArgsConstructor
        @Data
        public static class IpdataDTO {
            @JsonProperty("info1")
            private String info1;
            @JsonProperty("info2")
            private String info2;
            @JsonProperty("info3")
            private String info3;
            @JsonProperty("isp")
            private String isp;
        }

        @NoArgsConstructor
        @Data
        public static class AdcodeDTO {
            @JsonProperty("o")
            private String o;
            @JsonProperty("p")
            private String p;
            @JsonProperty("c")
            private String c;
            @JsonProperty("n")
            private String n;
            @JsonProperty("r")
            private String r;
            @JsonProperty("a")
            private String a;
        }
    }

}
