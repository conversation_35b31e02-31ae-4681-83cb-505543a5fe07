package com.jxntv.gvideo.aliyun.api.service.impl;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.live.model.v20161101.*;
import com.aliyuncs.profile.DefaultProfile;
import com.jxntv.gvideo.aliyun.api.service.AliYunLiveService;
import com.jxntv.gvideo.aliyun.sdk.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 阿里视频直播接口
 * @date 2021/09/07 16:09
 */
@RefreshScope
@Service
@Slf4j
public class AliYunLiveServiceImpl implements AliYunLiveService {
    private final String ADD_RECORD_VOD_ACTION = "AddLiveRecordVodConfig";
    private final String DEL_RECORD_VOD_ACTION = "DeleteLiveRecordVodConfig";
    private final String REAL_TIME_RECORD_COMMAND_ACTION = "RealTimeRecordCommand";
    private final String ADD_RECORD_OSS_ACTION = "AddLiveAppRecordConfig";
    private final String DEL_RECORD_OSS_ACTION = "DeleteLiveAppRecordConfig";
    private final String ADD_RECORD_OSS_NOTIFY_ACTION = "AddLiveRecordNotifyConfig";
    private final String AUTO_COMPOSE_ON = "ON";

    @Value("${aliyun.live.region}")
    private String regionId;

    @Value("${aliyun.live.AccessKeyID}")
    private String accessKeyId;

    @Value("${aliyun.live.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.live.vodTranscodeGroupId}")
    private String vodTranscodeGroupId;

    @Value("${aliyun.live.bucket}")
    private String bucket;

    @Value("${aliyun.live.oss-endpoint}")
    private String endpoint;

    @Override
    public AddLiveRecordVodConfigResponse addLiveRecordVodConfig(AddLiveRecordVodConfigDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        AddLiveRecordVodConfigRequest request = new AddLiveRecordVodConfigRequest();
        request.setActionName(ADD_RECORD_VOD_ACTION);
        request.setAppName(dto.getAppName());
        request.setDomainName(dto.getDomainName());
        request.setVodTranscodeGroupId(vodTranscodeGroupId);
        request.setStreamName(dto.getStreamName());
        request.setCycleDuration(dto.getCycleDuration());
        request.setStorageLocation(dto.getStorageLocation());
        if (StringUtils.isNotEmpty(dto.getAutoCompose())) {
            request.setAutoCompose(dto.getAutoCompose());
        }
        if (AUTO_COMPOSE_ON.equals(dto.getAutoCompose())) {
            request.setComposeVodTranscodeGroupId(vodTranscodeGroupId);
        }
        return client.getAcsResponse(request);
    }

    @Override
    public DeleteLiveAppRecordConfigResponse deleteLiveRecordVodConfig(DelLiveRecordVodConfigDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        DeleteLiveAppRecordConfigRequest request = new DeleteLiveAppRecordConfigRequest();
        request.setActionName(DEL_RECORD_VOD_ACTION);
        request.setAppName(dto.getAppName());
        request.setDomainName(dto.getDomainName());
        request.setStreamName(dto.getStreamName());
        return client.getAcsResponse(request);
    }

    @Override
    public RealTimeRecordCommandResponse realTimeRecordCommand(RealTimeRecordCommandDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        RealTimeRecordCommandRequest request = new RealTimeRecordCommandRequest();
        request.setActionName(REAL_TIME_RECORD_COMMAND_ACTION);
        request.setAppName(dto.getAppName());
        request.setDomainName(dto.getDomainName());
        request.setStreamName(dto.getStreamName());
        request.setCommand(dto.getCommand());
        return client.getAcsResponse(request);
    }

    @Override
    public AddLiveAppRecordConfigResponse addLiveRecordOssConfig(AddLiveRecordOssConfigDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        AddLiveAppRecordConfigRequest request = new AddLiveAppRecordConfigRequest();
        request.setActionName(ADD_RECORD_OSS_ACTION);
        request.setAppName(dto.getAppName());
        request.setDomainName(dto.getDomainName());
        request.setStreamName(dto.getStreamName());
        request.setOssBucket(bucket);
        request.setOssEndpoint(endpoint);
        if (!CollectionUtils.isEmpty(dto.getFormatDTOList())) {
            request.setRecordFormats(dto.getFormatDTOList().stream().map(formatDTO -> {
                AddLiveAppRecordConfigRequest.RecordFormat format = new AddLiveAppRecordConfigRequest.RecordFormat();
                format.setFormat(formatDTO.getFormat());
                format.setOssObjectPrefix(formatDTO.getObjectPrefix());
                format.setSliceOssObjectPrefix(formatDTO.getSliceObjectPrefix());
                format.setCycleDuration(formatDTO.getCycleDuration());
                return format;
            }).collect(Collectors.toList()));
        }
        request.setStartTime(dto.getStartTime());
        request.setEndTime(dto.getEndTime());
        request.setOnDemand(dto.getOnDemand());
        return client.getAcsResponse(request);
    }

    @Override
    public AddLiveRecordNotifyConfigResponse addLiveRecordOssNotifyConfig(AddLiveRecordOssNotifyConfigDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        AddLiveRecordNotifyConfigRequest request = new AddLiveRecordNotifyConfigRequest();
        request.setActionName(ADD_RECORD_OSS_NOTIFY_ACTION);
        request.setDomainName(dto.getDomainName());
        request.setNotifyUrl(dto.getNotifyUrl());
        request.setOnDemandUrl(dto.getOnDemandUrl());
        request.setNeedStatusNotify(dto.getNeedStatusNotify());
        return client.getAcsResponse(request);
    }

    @Override
    public DeleteLiveAppRecordConfigResponse deleteLiveRecordOssConfig(DeleteLiveAppRecordConfigDTO dto) throws ClientException {
        DefaultProfile profile = DefaultProfile.getProfile(regionId, accessKeyId, accessKeySecret);
        IAcsClient client = new DefaultAcsClient(profile);
        DeleteLiveAppRecordConfigRequest request = new DeleteLiveAppRecordConfigRequest();
        request.setActionName(DEL_RECORD_OSS_ACTION);
        request.setAppName(dto.getAppName());
        request.setDomainName(dto.getDomainName());
        request.setStreamName(dto.getStreamName());
        return client.getAcsResponse(request);
    }
}
