package com.jxntv.gvideo.aliyun.api.client.dto.tencent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Data
public class GroupOwnerChangeResponse {


    @JsonProperty("ActionStatus")
    private String actionStatus;
    @JsonProperty("ErrorInfo")
    private String errorInfo;
    @JsonProperty("ErrorCode")
    private Integer errorCode;

    public boolean isOk(){
        return "OK".equals(actionStatus);
    }

}
