package com.jxntv.gvideo.aliyun.api.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.aliyun.api.entity.OssFile;
import com.jxntv.gvideo.aliyun.api.job.param.OssFileJobParam;
import com.jxntv.gvideo.aliyun.api.service.OssFileService;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 图片刷新属性信息
 */
@Slf4j
@Component
public class OssFileJob {
    @Resource
    private OssFileService ossFileService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @XxlJob("ossFileJob")
    public ReturnT<String> execute() {
        log.info("刷新图片属性任务...");

        OssFileJobParam jobParam = getJobParam();
        //  每次抓取数据限制
        int limit = jobParam.getLimit();
        //  重试限制次数
        int retry = jobParam.getRetry();

        List<OssFile> list = getTasks(limit);

        for (OssFile ossFile : list) {
            //  检查属性是否完整，完整标记同步成功，否则，尝试补全属性
            if (isComplete(ossFile)) {
                ossFile.setSync(1);
                ossFileService.updateById(ossFile);
            } else {
                int retryTimes = getRetry(ossFile.getUuid());
                //  如果重试次数小于限制，尝试补全属性
                if (retryTimes < retry) {
                    boolean success = completeProperties(ossFile.getUuid());
                    if (success) {
                        ossFile.setSync(1);
                        ossFileService.updateById(ossFile);
                    }
                } else {
                    ossFile.setSync(2);
                    ossFileService.updateById(ossFile);
                }
            }
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 获取任务列表
     *
     * @param limit 抓取数据限制
     * @return 需要补充图片元数据的列表
     */
    private List<OssFile> getTasks(int limit) {
        LambdaQueryWrapper<OssFile> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(OssFile::getUpload, 1);
        lambdaQuery.eq(OssFile::getSync, 0);
        lambdaQuery.last(" limit " + limit);

        return ossFileService.list(lambdaQuery);
    }

    /**
     * 获取尝试次数
     *
     * @return 重试次数
     */
    private int getRetry(String ossId) {

        String key = "img::properties::sync::retry::" + ossId;

        Long times = stringRedisTemplate.opsForValue().increment(key);

        //  设置一个有效时间，避免手动清理key
        stringRedisTemplate.expire(key, 1, TimeUnit.DAYS);

        return Objects.isNull(times) ? 0 : times.intValue();
    }

    private boolean completeProperties(String uuid) {
        try {
            OssFile result = ossFileService.completeProperties(uuid);
            return isComplete(result);
        } catch (Exception e) {
            log.error("oss file properties complete failed", e);
        }
        return false;
    }

    /**
     * 判断ossFile是否图片属性完整
     *
     * @return 是否
     */
    private boolean isComplete(OssFile entity) {
        return Objects.nonNull(entity.getHeight()) && Objects.nonNull(entity.getWidth()) && Objects.nonNull(entity.getSize());
    }


    private OssFileJobParam getJobParam() {
        String jobParam = XxlJobHelper.getJobParam();
        return Optional.ofNullable(jobParam).filter(StringUtils::hasText).map(s -> JsonUtils.fromJson(jobParam, OssFileJobParam.class)).orElseGet(OssFileJobParam::new);
    }
}
