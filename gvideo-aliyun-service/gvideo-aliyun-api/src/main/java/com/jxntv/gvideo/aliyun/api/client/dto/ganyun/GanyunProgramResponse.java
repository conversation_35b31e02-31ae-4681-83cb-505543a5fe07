package com.jxntv.gvideo.aliyun.api.client.dto.ganyun;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
public class GanyunProgramResponse implements Serializable {

    @JsonProperty("state")
    private Boolean state;
    @JsonProperty("error")
    private String error;
    @JsonProperty("data")
    private DataDTO data;

    @Data
    @NoArgsConstructor
    public static class DataDTO {
        @JsonProperty("maxId")
        private Long maxId;
        @JsonProperty("list")
        private List<ListDTO> list;


        @Data
        @NoArgsConstructor
        public static class ListDTO {
            @JsonProperty("contentId")
            private Long contentId;
            @JsonProperty("title")
            private String title;
            @JsonProperty("status")
            private Integer status;
            @JsonProperty("audit_step")
            private Integer auditStep;
            @JsonProperty("createTime")
            private String createTime;
            @JsonProperty("publishTime")
            private String publishTime;
            @JsonProperty("categoryId")
            private Long categoryId;
            @JsonProperty("cName")
            private String cName;
            @JsonProperty("desc")
            private String desc;
            @JsonProperty("url")
            private String url;
            @JsonProperty("duration")
            private Integer duration;
            @JsonProperty("thumb")
            private String thumb;
            @JsonProperty("labels")
            private String labels;
            @JsonProperty("customId")
            private Long customId;
            @JsonProperty("customName")
            private String customName;
        }
    }
}
