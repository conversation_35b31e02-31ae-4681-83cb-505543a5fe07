package com.jxntv.gvideo.aliyun.api.service;

import com.jxntv.gvideo.aliyun.sdk.dto.*;

public interface OssService {
    /**
     * 获取永久有效的URL
     *
     * @param fileId 文件id
     * @return 文件读取URL
     */
    String getForeverOssUrl(String fileId);

    OssDTO getOssFile(String fileId);

    OssDTO getOssResize(String fileId);

    OssDTO getOriOss(String fileId);

    OssDTO getOriFile(String fileId);

    OssDTO getSelfResize(OssResizeDTO resizeDTO);

    OssDTO getLiveRecord(String fileId);

    OssDTO getFuzzy(String fileId);

    OssDTO getAvatar(String fileId);

    VodVideoPlayDTO getVideoFile(String videoId);

    VodAudioPlayDTO getAudioFile(String audioId);

    OssDTO generateOssFile(Integer bizType, String extraId);

    String saveLiveRecord(String ossName);

    SoundDTO getContentBySoundId(String uuid);
}
