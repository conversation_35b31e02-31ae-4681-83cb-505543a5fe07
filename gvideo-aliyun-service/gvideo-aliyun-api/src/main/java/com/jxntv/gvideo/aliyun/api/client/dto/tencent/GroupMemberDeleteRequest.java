package com.jxntv.gvideo.aliyun.api.client.dto.tencent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@Data
public class GroupMemberDeleteRequest {

    @JsonProperty("GroupId")
    private String groupId;
    @JsonProperty("Reason")
    private String reason;
    @JsonProperty("Silence")
    private Integer silence;
    @JsonProperty("MemberToDel_Account")
    private List<String> memberToDelAccount;
}
