package com.jxntv.gvideo.aliyun.api.controller;

import com.alibaba.fastjson.JSONObject;
import com.jxntv.gvideo.aliyun.api.common.config.JspVoteConfig;
import com.jxntv.gvideo.aliyun.sdk.JspVoteClient;
import com.jxntv.gvideo.aliyun.sdk.dto.vote.VoteInfoDTO;
import com.jxntv.gvideo.aliyun.sdk.param.vote.VoteSearchParam;
import com.jxntv.gvideo.aliyun.sdk.param.vote.JspVoteBaseParam;
import com.jxntv.gvideo.aliyun.sdk.response.vote.VoteListResponse;
import com.jxntv.gvideo.aliyun.sdk.response.vote.VoteUserLoginResponse;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
public class JspVoteController implements JspVoteClient {

    @Resource
    private JspVoteConfig voteConfig;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private RestTemplate restTemplate;
    private static final String ACCESS_TOKEN_KEY = "jsp-vote::token::%s::%s";

    @Override
    public  Result<PageDTO<VoteInfoDTO>> pageActivity(VoteSearchParam param) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        //headers.add("Authorization", "Bearer " + getAccessToken(param));
        headers.add("appId", voteConfig.getAppId());

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>(2);
        params.add("pageNum",String.valueOf(param.getCurrent()));
        params.add("pageSize",String.valueOf(param.getSize()));

        // 构造HttpEntity对象，将headers作为参数传入
        HttpEntity<Object> entity = new HttpEntity<>(headers);

        // 创建URI，将参数加到查询字符串中
        URI uri = URI.create(voteConfig.getBaseUrl() + voteConfig.getActivityListUrl());
        uri = UriComponentsBuilder.fromUri(uri).queryParams(params).build(true).toUri();

        // 发起GET请求，获取返回结果
        ResponseEntity<VoteListResponse> response = restTemplate.exchange(uri, HttpMethod.GET, entity, VoteListResponse.class);
        VoteListResponse body = response.getBody();
        if (Objects.isNull(body) || !Objects.equals(200, body.getCode())){
            return Result.fail("查询投票列表失败");
        }

        PageDTO<VoteInfoDTO>  pageDTO = PageDTO.empty();
        pageDTO.setPageNum(param.getCurrent());
        pageDTO.setPageSize(param.getSize());
        pageDTO.setTotal((int)body.getTotal());

        List<VoteInfoDTO> list = body.getRows().stream().map(e ->{
            VoteInfoDTO dto = new VoteInfoDTO();
            BeanCopier.create(VoteListResponse.ResultDTO.class, VoteInfoDTO.class, false).copy(e, dto, null);
            return dto;
        }).collect(Collectors.toList());
        pageDTO.setList(list);
        return Result.ok(pageDTO);
    }


    private  String  getAccessToken(JspVoteBaseParam param){
       String key = String.format(ACCESS_TOKEN_KEY, voteConfig.getAppId(),param.getUserId());
       String accessToken = stringRedisTemplate.opsForValue().get(key);
       if (StringUtils.isEmpty(accessToken)){
           HttpHeaders headers = new HttpHeaders();
           headers.setContentType(MediaType.APPLICATION_JSON);

           JSONObject jsonObject = new JSONObject();
           jsonObject.put("appId", voteConfig.getAppId());
           jsonObject.put("userId",param.getUserId());
           jsonObject.put("nickname",param.getNickname());
           jsonObject.put("avatar",param.getAvatar());
           jsonObject.put("token",param.getToken());

           // 构造HttpEntity对象，将headers作为参数传入
           HttpEntity<Object> entity = new HttpEntity<>(jsonObject.toJSONString(), headers);

           VoteUserLoginResponse loginResponse = restTemplate.postForObject(voteConfig.getBaseUrl()+ voteConfig.getLoginUrl(), entity, VoteUserLoginResponse.class);
           if (Objects.nonNull(loginResponse) && Objects.equals(200, loginResponse.getCode()) && StringUtils.hasText(loginResponse.getData().getAccessToken())){
               accessToken = loginResponse.getData().getAccessToken();
               stringRedisTemplate.opsForValue().set(key,accessToken, voteConfig.getExpireTime(), TimeUnit.MINUTES);
           }
       }
       return accessToken;
    }
}
