#请先熟读代码codebase。我现在有一个需求是做一个COLMO家电折扣券活动，里面有1张业务表，请仿照gvideo-app-be模块下的com.jxntv.gvideo.app.be.controller.CharityActivityController编写活动及报名业务（CharityActivityController 调用了com.jxntv.gvideo.media.client下的client实现了业务）。 
##现要求如下：
### 新建的文件包括但不限于controller,vo,dto,client,fallback,service,serviceImpl,mapper,entity,coverter;
### 其中client,fallback及dto放在gvideo-media-service模块下的gvideo-media-sdk目录的client目录里,其中实现client的controller,service，serviceImpl,mapper,entity,coverter(用于entity、dto的数据互转)放在gvideo-media-service模块下的gvideo-media-api目录里,并且service、serviceImpl是根据mybatis-plus实现的；
### gvideo-media-service模块下的gvideo-media-sdk里只要创建一个client，里面包含了活动报名、查询当前报名总数、通过jid查询报名信息；
### gvideo-app-be模块里新建controller,vo,coverter(用于转换dto、vo的数据互转)等文件,controller调用新增活动报名、请求题目、提交答案、获取答题分数排行榜，可以参考com.jxntv.gvideo.app.be.controller.GkmnController 及com.jxntv.gvideo.app.be.service.impl.GkmnServiceImpl；
### coverter文件仿照com.jxntv.gvideo.app.be.converter.GkmnConverter.java,不要自行引入新组件,不要使用org.mapstruct.Mapper;
### client文件仿照com.jxntv.gvideo.group.sdk.client.GroupGatherActivityClient.java,所有的api返回都是com.jxntv.gvideo.common.model.Result;
### client文件仿照com.jxntv.gvideo.media.client.CharityActivityClient.java,所有的api返回都是com.jxntv.gvideo.common.model.Result;
### 生成的mapper文件存放在com.jxntv.gvideo.media.api.repository目录下；
### 生成的entity文件存放在com.jxntv.gvideo.media.api.domain.entity目录下；
### gvideo-app-be模块下生成的controller里的入参不能是dto,需要生成对应的vo,然后coverter把vo转换成dto;
### 还有，请写好注释。特别是方法级别的注释及entity、dto、vo里面的字段注释。 
### 生成的文件不要放错目录了；不要少生成文件。需要能编译通过；
### 不要创建新目录，不要创建新目录，不要创建新目录；
### 一个jid只能报一次名，如果当前报名总数超过300，则报名失败；

## 业务表sql如下： 
CREATE TABLE `colmo_enroll_activity` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `jid` bigint(20) DEFAULT NULL COMMENT '今视频唯一JID',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `contact` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系方式',
  `unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '单位',
  `remarks` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) DEFAULT 0 COMMENT '删除标志  0-未删除 1-已删除',
  `create_date` datetime DEFAULT current_timestamp() COMMENT '创建时间',
  `update_date` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `colmo_enroll_activity_idx_1` (`jid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin COMMENT='COLMO家电折扣券活动';