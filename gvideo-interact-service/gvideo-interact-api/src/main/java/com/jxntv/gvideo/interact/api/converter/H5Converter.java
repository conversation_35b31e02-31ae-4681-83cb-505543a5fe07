package com.jxntv.gvideo.interact.api.converter;


import com.jxntv.gvideo.interact.api.entity.FeedbackQuestion;
import com.jxntv.gvideo.interact.api.entity.FeedbackQuestionDetail;
import com.jxntv.gvideo.interact.client.dto.FeedbackQuestionDTO;
import com.jxntv.gvideo.interact.client.dto.FeedbackQuestionDetailDTO;

/**
 *
 * Created on 2020-03-27
 */
public class H5Converter {

    public static FeedbackQuestionDTO convert(FeedbackQuestion entity){
        FeedbackQuestionDTO dto = new FeedbackQuestionDTO();
        dto.setId(entity.getId());
        dto.setTitle(entity.getTitle());
        dto.setFather(entity.getFather());
        return dto;
    }

    public static FeedbackQuestionDetailDTO convert(FeedbackQuestionDetail entity){
        FeedbackQuestionDetailDTO dto = new FeedbackQuestionDetailDTO();
        dto.setId(entity.getId());
        dto.setQuestionId(entity.getQuestionId());
        dto.setContent(entity.getContent());
        dto.setType(entity.getType());
        dto.setHeight(entity.getHeight());
        dto.setIsBold(entity.getIsBold());
        dto.setSort(entity.getSort());
        return dto;
    }

}
