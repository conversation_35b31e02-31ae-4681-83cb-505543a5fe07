package com.jxntv.gvideo.interact.api.event.listener;

import com.jxntv.gvideo.interact.api.converter.MessageConverter;
import com.jxntv.gvideo.interact.api.entity.Message;
import com.jxntv.gvideo.interact.api.event.AddFollowMessageEvent;
import com.jxntv.gvideo.interact.api.service.MessageService;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.PushClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.jxntv.gvideo.user.client.dto.PushMsgDTO;
import com.jxntv.gvideo.user.client.enums.PushMsgContentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/1/19
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class AddFollowMessageEventListener {

    @Resource
    private MessageService messageService;
    @Resource
    private ConsumerUserClient consumerUserClient;
    @Resource
    private PushClient pushClient;

    @Async
    @EventListener
    public void onEvent(AddFollowMessageEvent event) {
        Long jid = event.getJid();
        Long authorId = event.getAuthorId();
        Integer authorType = event.getAuthorType();

        //  只给ugc用户发送消息
        if (!Objects.equals(event.getAuthorType(), authorType)) {
            return;
        }

        ConsumerUserDTO consumerUserDTO = consumerUserClient.getBaseUserByJid(jid).getResult();
        String fromName = Objects.nonNull(consumerUserDTO) ? consumerUserDTO.getNickname() : "粉丝";
        boolean fromStatus = Objects.isNull(consumerUserDTO) || Objects.equals(consumerUserDTO.getStatus(), 1);

        //  保存站内信
        Message message = MessageConverter.convertFansMsg(authorId, jid, fromName, fromStatus);
        messageService.save(message);

        // 关注成功发送推送消息
        PushMsgDTO pushMsgDTO = new PushMsgDTO();
        pushMsgDTO.setUserType(1);
        pushMsgDTO.setContentId(authorId);
        pushMsgDTO.setContentType(PushMsgContentTypeEnum.FOLLOW.getCode());
        pushMsgDTO.setType(1);
        pushMsgDTO.setToType(1);
        pushMsgDTO.setToIds(Collections.singletonList(authorId));
        pushMsgDTO.setContent(String.format("「%s」刚刚关注了你", Objects.nonNull(consumerUserDTO) ? consumerUserDTO.getNickname() : jid));
        pushMsgDTO.setLinkUrl(String.format("jinshipin://notification?msgType=%s&title=粉丝", 3));
        pushMsgDTO.setUserId(jid);
        pushMsgDTO.setUsername(consumerUserDTO.getNickname());

//        pushClient.pushMsg(pushMsgDTO);
    }

}
