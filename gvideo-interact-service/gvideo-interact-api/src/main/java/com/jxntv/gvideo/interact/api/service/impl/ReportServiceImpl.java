package com.jxntv.gvideo.interact.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.interact.api.client.CertificationService;
import com.jxntv.gvideo.interact.api.client.ConsumerUserService;
import com.jxntv.gvideo.interact.api.converter.InteractConverter;
import com.jxntv.gvideo.interact.api.converter.MessageConverter;
import com.jxntv.gvideo.interact.api.entity.CommentPrimary;
import com.jxntv.gvideo.interact.api.entity.CommentReply;
import com.jxntv.gvideo.interact.api.entity.InteractReport;
import com.jxntv.gvideo.interact.api.entity.Message;
import com.jxntv.gvideo.interact.api.repository.ReportMapper;
import com.jxntv.gvideo.interact.api.service.CommentPrimaryService;
import com.jxntv.gvideo.interact.api.service.CommentReplyService;
import com.jxntv.gvideo.interact.api.service.MessageService;
import com.jxntv.gvideo.interact.api.service.ReportService;
import com.jxntv.gvideo.interact.api.utils.PageUtils;
import com.jxntv.gvideo.interact.client.dto.InteractReportDTO;
import com.jxntv.gvideo.interact.client.dto.ReportSearchDTO;
import com.jxntv.gvideo.interact.client.dto.ReportTab;
import com.jxntv.gvideo.interact.client.dto.enums.ReportContentTypeEnum;
import com.jxntv.gvideo.interact.client.dto.enums.ReportStatusEnum;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.user.client.dto.CertificationDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @description 举报接口
 * @date 2021/05/20 11:29
 */
@Service
public class ReportServiceImpl extends ServiceImpl<ReportMapper, InteractReport> implements ReportService {
    private final String REPORT_DETAIL_FORMAT = "<div><p>举报时间：%s</p><p>举报对象：%s</p><p>审核结果：%s</p><p>感谢您的监督，请继续支持今视频，与我们一起维护良好的网络环境!</p></div>";
    private final String BEREPORT_DETAIL_FORMAT = "<div><p>被举报时间：%s</p><p>您在%s发布的%s中含有违规行为，平台现对该%s进行强制下架。</p></div>";

    @Autowired
    private ConsumerUserService consumerUserService;
    @Autowired
    private CertificationService certificationService;
    @Autowired
    private MediaResourceClient mediaResourceClient;
    @Autowired
    private MessageService messageService;
    @Resource
    private CommentPrimaryService commentPrimaryService;
    @Resource
    private CommentReplyService commentReplyService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void createReport(InteractReportDTO dto) {
        // 生成唯一code
        String code;
        do {
            code = String.format("%s%s", LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd")),
                    generateId().substring(6));
        } while (!confirmCode(code));
        dto.setCode(code);
        InteractReport interactReport = InteractConverter.convert(dto);
        save(interactReport);
    }

    @Override
    public boolean isReport(Long jid, Integer contentType, String contentId) {
        LambdaQueryWrapper<InteractReport> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InteractReport::getJid, jid);
        queryWrapper.eq(InteractReport::getContentType, contentType);
        if (ReportContentTypeEnum.GROUP.getId().equals(contentType)) {
            queryWrapper.eq(InteractReport::getGroupId, contentId);
        } else {
            queryWrapper.eq(InteractReport::getContentId, Long.valueOf(contentId));
        }
        return count(queryWrapper) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void auditReport(InteractReportDTO dto) {
        InteractReport interactReport = InteractConverter.convert(dto);
        interactReport.setAuditDate(new Date());
        updateById(interactReport);
        // 举报暂时不用发送消息
        if (updateById(interactReport)) {
            InteractReport interactReportTmp = this.baseMapper.selectById(interactReport.getId());
            sendMessage(interactReportTmp, dto.getAuditUserName());
        }
    }

    @Override
    public PageDTO<InteractReportDTO> listReport(ReportSearchDTO dto, long current, long size) {
        List<Integer> mediaTypeList = null;
        // 内容类型：0-资源、1-评论、2-回复、3-C端用户、4-群组
        List<Integer> contentTypeList = Arrays.asList(0);
        if (Integer.valueOf(ReportTab.VIDEO_AUDIT.getCode()).equals(dto.getTab())) {
            mediaTypeList = Arrays.asList(ContentType.VIDEO_HOMEMADE.getCode(),
                    ContentType.VIDEO_FILM.getCode(),
                    ContentType.VIDEO_PROGRAM.getCode(),
                    ContentType.VIDEO_COLUMN.getCode(),
                    ContentType.GAN_YUN.getCode(),
                    ContentType.GAN_YUN_VIDEO.getCode());
        } else if (Integer.valueOf(ReportTab.SOUND_AUDIT.getCode()).equals(dto.getTab())) {
            mediaTypeList = Arrays.asList(ContentType.AUDIO_FM.getCode(), ContentType.SOUND.getCode());
        } else if (Integer.valueOf(ReportTab.PIC_AUDIT.getCode()).equals(dto.getTab())) {
            mediaTypeList = Arrays.asList(ContentType.PIC_FONT.getCode());
        } else if (Integer.valueOf(ReportTab.LIVE_AUDIT.getCode()).equals(dto.getTab())) {
            mediaTypeList = Arrays.asList(ContentType.LIVE_BROADCAST.getCode(),
                    ContentType.INTERACTIVE_BROADCAST.getCode());
        } else if (Integer.valueOf(ReportTab.COMMENT_AUDIT.getCode()).equals(dto.getTab())) {
            contentTypeList = Arrays.asList(1, 2);
            mediaTypeList = Arrays.asList(1, 2, 3, 4, 5);
        } else if (Integer.valueOf(ReportTab.NEWS_AUDIT.getCode()).equals(dto.getTab())) {
            mediaTypeList = Arrays.asList(ContentType.NEWS.getCode(), ContentType.SPECIAL.getCode());
        } else if (Integer.valueOf(ReportTab.USER_AUDIT.getCode()).equals(dto.getTab())) {
            contentTypeList = Arrays.asList(3);
        } else if (Integer.valueOf(ReportTab.GROUP_AUDIT.getCode()).equals(dto.getTab())) {
            contentTypeList = Arrays.asList(4);
        } else {
            return new PageDTO<>();
        }
        LambdaQueryWrapper<InteractReport> queryWrapper = new LambdaQueryWrapper<>();
        if (!CollectionUtils.isEmpty(contentTypeList)) {
            queryWrapper.in(InteractReport::getContentType, contentTypeList);
        }
        if (!CollectionUtils.isEmpty(mediaTypeList)) {
            queryWrapper.in(InteractReport::getMediaType, mediaTypeList);
        }
        if (StringUtils.isNotEmpty(dto.getCode())) {
            queryWrapper.eq(InteractReport::getCode, dto.getCode());
        }
        if (Objects.nonNull(dto.getType())) {
            queryWrapper.eq(InteractReport::getType, dto.getType());
        }
        if (Objects.nonNull(dto.getContentId())) {
            queryWrapper.eq(InteractReport::getContentId, dto.getContentId());
        }
        if (StringUtils.isNotEmpty(dto.getGroupId())) {
            queryWrapper.eq(InteractReport::getGroupId, dto.getGroupId());
        }
        if (Objects.nonNull(dto.getStatus())) {
            queryWrapper.eq(InteractReport::getStatus, dto.getStatus());
        }
        if (StringUtils.isNotEmpty(dto.getStart())) {
            queryWrapper.gt(InteractReport::getCreateDate, dto.getStart());
        }
        if (StringUtils.isNotEmpty(dto.getEnd())) {
            queryWrapper.lt(InteractReport::getCreateDate, dto.getEnd());
        }
        if (Objects.nonNull(dto.getAsc()) && dto.getAsc()) {
            queryWrapper.orderByAsc(InteractReport::getCreateDate);
        } else {
            queryWrapper.orderByDesc(InteractReport::getCreateDate);
        }
        IPage<InteractReport> page = page(new Page<>(current, size), queryWrapper);
        return PageUtils.pageOf(page, InteractConverter::convert);
    }

    /**
     * 生成唯一ID
     *
     * @return
     */
    private String generateId() {
        int rand = new Random().nextInt(90) + 10;
        int rnd = UUID.randomUUID().toString().hashCode();
        if (rnd < 0) {
            rnd = -rnd;
        }
        return rand + String.format("%010d", rnd);
    }

    /**
     * true code有效
     * false 已存在 数据库唯一索引保证
     *
     * @param code
     * @return
     */
    private boolean confirmCode(String code) {
        return count(Wrappers.<InteractReport>lambdaQuery().eq(InteractReport::getCode, code)) == 0;
    }

    /**
     * 获取作者名称
     *
     * @param authorId
     * @param authorType
     * @return
     */
    private String getAuthorName(Long authorId, Integer authorType) {
        String name = "";
        // 0-认证号
        if (authorType.equals(0)) {
            CertificationDTO certificationDTO = certificationService.get(authorId).orElse(null);
            if (Objects.nonNull(certificationDTO)) {
                name = certificationDTO.getName();
            }
        } else if (authorType.equals(1)) {
            ConsumerUserDTO consumerUserDTO = consumerUserService.getUserByJid(authorId).orElse(null);
            if (Objects.nonNull(consumerUserDTO)) {
                name = consumerUserDTO.getNickname();
            }
        } else {
            name = "PP视频";
        }
        return name;
    }

    /**
     * 发送系统消息
     *
     * @param interactReport
     * @param auditUserName
     */
    private void sendMessage(InteractReport interactReport, String auditUserName) {
        Long jid = interactReport.getJid();
        String contentType = ReportContentTypeEnum.RESOURCE
                .equals(ReportContentTypeEnum.getNameById(interactReport.getContentType())) ? "作品" : "评论";
        String reportTime = DateFormatUtils.format(interactReport.getCreateDate(), "yyyy-MM-dd");
        String title = interactReport.getStatus().equals(ReportStatusEnum.REJECTED.getId()) ? "举报成功反馈" : "举报失败反馈";
        String intro = interactReport.getStatus().equals(ReportStatusEnum.REJECTED.getId()) ? "举报审核通过" : "举报审核不通过";
        String detail = "";

        // 发送系统通知 to 举报人
        if (ReportContentTypeEnum.RESOURCE.equals(ReportContentTypeEnum.getNameById(interactReport.getContentType()))) {
            MediaResourceDTO tmp = mediaResourceClient.get(interactReport.getContentId()).orElse(null);
            if (Objects.nonNull(tmp)) {
                String authorName = this.getAuthorName(tmp.getReleaseId(), tmp.getReleaseType());
                String publishTime = DateFormatUtils.format(tmp.getCreateDate(), "yyyy-MM-dd HH:mm:ss");
                String reportObj = "";
                if (StringUtils.isNotEmpty(authorName) && StringUtils.isNotEmpty(tmp.getShowName())) {
                    reportObj = String.format("%s:%s", authorName, tmp.getShowName());
                } else if (StringUtils.isNotEmpty(authorName)) {
                    reportObj = authorName;
                } else if (StringUtils.isNotEmpty(tmp.getShowName())) {
                    reportObj = tmp.getShowName();
                }
                detail = String.format(REPORT_DETAIL_FORMAT, reportTime, reportObj, interactReport.getAuditNote());
                Message reportMessage = MessageConverter.convertSysMsg(jid, interactReport.getAuditUserId().intValue(),
                        auditUserName, title, intro, detail);
                messageService.save(reportMessage);

                // 发送系统通知 to 被举报人
                if (ReportStatusEnum.HAS_UNDERCARRIAGE
                        .equals(ReportStatusEnum.getNameById(interactReport.getStatus()))) {
                    detail = String.format(BEREPORT_DETAIL_FORMAT, reportTime, publishTime, contentType, contentType);
                    Message beReportMessage = MessageConverter.convertSysMsg(jid,
                            interactReport.getAuditUserId().intValue(), auditUserName, "违规处罚",
                            String.format("%s进行强制下架", contentType), detail);
                    messageService.save(beReportMessage);
                }
            }
        } else if (ReportContentTypeEnum.COMMENT
                .equals(ReportContentTypeEnum.getNameById(interactReport.getContentType()))) {
            CommentPrimary commentPrimary = commentPrimaryService.getById(interactReport.getContentId());
            if (Objects.nonNull(commentPrimary)) {
                String publishTime = DateFormatUtils.format(commentPrimary.getCreateDate(), "yyyy-MM-dd HH:mm:ss");
                String authorName = this.getAuthorName(commentPrimary.getFromJid(), 1);
                String reportObj = "";
                if (StringUtils.isNotEmpty(authorName) && StringUtils.isNotEmpty(commentPrimary.getContent())) {
                    reportObj = String.format("%s:%s", authorName, commentPrimary.getContent());
                } else if (StringUtils.isNotEmpty(authorName)) {
                    reportObj = authorName;
                } else if (StringUtils.isNotEmpty(commentPrimary.getContent())) {
                    reportObj = commentPrimary.getContent();
                }
                detail = String.format(REPORT_DETAIL_FORMAT, reportTime, reportObj, interactReport.getAuditNote());
                Message entity = MessageConverter.convertSysMsg(jid, interactReport.getAuditUserId().intValue(),
                        auditUserName, title, intro, detail);
                messageService.save(entity);

                // 发送系统通知 to 被举报人
                if (ReportStatusEnum.HAS_UNDERCARRIAGE
                        .equals(ReportStatusEnum.getNameById(interactReport.getStatus()))) {
                    detail = String.format(BEREPORT_DETAIL_FORMAT, reportTime, publishTime, contentType, contentType);
                    Message beReportMessage = MessageConverter.convertSysMsg(jid,
                            interactReport.getAuditUserId().intValue(), auditUserName, "违规处罚",
                            String.format("%s进行强制下架", contentType), detail);
                    messageService.save(beReportMessage);
                }
            }
        } else if (ReportContentTypeEnum.REPLY
                .equals(ReportContentTypeEnum.getNameById(interactReport.getContentType()))) {
            CommentReply commentReply = commentReplyService.getById(interactReport.getContentId());
            if (Objects.nonNull(commentReply)) {
                String publishTime = DateFormatUtils.format(commentReply.getCreateDate(), "yyyy-MM-dd HH:mm:ss");
                String authorName = this.getAuthorName(commentReply.getFromJid(), 1);
                String reportObj = "";
                if (StringUtils.isNotEmpty(authorName) && StringUtils.isNotEmpty(commentReply.getContent())) {
                    reportObj = String.format("%s:%s", authorName, commentReply.getContent());
                } else if (StringUtils.isNotEmpty(authorName)) {
                    reportObj = authorName;
                } else if (StringUtils.isNotEmpty(commentReply.getContent())) {
                    reportObj = commentReply.getContent();
                }
                detail = String.format(REPORT_DETAIL_FORMAT, reportTime, reportObj, interactReport.getAuditNote());
                Message entity = MessageConverter.convertSysMsg(jid, interactReport.getAuditUserId().intValue(),
                        auditUserName, title, intro, detail);
                messageService.save(entity);

                // 发送系统通知 to 被举报人
                if (ReportStatusEnum.HAS_UNDERCARRIAGE
                        .equals(ReportStatusEnum.getNameById(interactReport.getStatus()))) {
                    detail = String.format(BEREPORT_DETAIL_FORMAT, reportTime, publishTime, contentType, contentType);
                    Message beReportMessage = MessageConverter.convertSysMsg(jid,
                            interactReport.getAuditUserId().intValue(), auditUserName, "违规处罚",
                            String.format("%s进行强制下架", contentType), detail);
                    messageService.save(beReportMessage);
                }
            }
        }
    }
}
