package com.jxntv.gvideo.interact.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.api.converter.FollowPeomConvert;
import com.jxntv.gvideo.interact.api.entity.InteractFollowPeomToday;
import com.jxntv.gvideo.interact.api.entity.InteractFollowPeomWeek;
import com.jxntv.gvideo.interact.api.service.FollowPeomTodayService;
import com.jxntv.gvideo.interact.api.service.FollowPeomWeekService;
import com.jxntv.gvideo.interact.api.utils.PageUtils;
import com.jxntv.gvideo.interact.client.InteractFollowPeomClient;
import com.jxntv.gvideo.interact.client.dto.FollowPeomTodayDTO;
import com.jxntv.gvideo.interact.client.dto.FollowPeomTodayInfoDTO;
import com.jxntv.gvideo.interact.client.dto.FollowPeomWeekDTO;
import com.jxntv.gvideo.interact.client.dto.FollowPeomWeekInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 诗词大会排行榜
 */
@Slf4j
@RestController
public class InteractFollowPeomController implements InteractFollowPeomClient {

    @Autowired
    private FollowPeomTodayService followPeomTodayService;

    @Autowired
    private FollowPeomWeekService followPeomWeekService;

    @Resource
    private FollowPeomConvert followPeomConvert;

    @Override
    public Result<FollowPeomTodayDTO> getTodayByJid(Long jid) {
        FollowPeomTodayDTO dto = this.queryTodayByJid(jid);
        if (Objects.nonNull(dto)){
            return Result.ok(dto);
        }

        return Result.ok(null);
    }

    public FollowPeomTodayDTO queryTodayByJid(Long jid) {
        LambdaQueryWrapper<InteractFollowPeomToday> query = new LambdaQueryWrapper<>();
        query.eq(InteractFollowPeomToday::getAuthorId, jid);
        InteractFollowPeomToday entity = followPeomTodayService.getOne(query);
        if (Objects.nonNull(entity)){
            return followPeomConvert.convertDTO(entity);
        }
        FollowPeomTodayDTO dto = new FollowPeomTodayDTO();
        dto.setAuthorId(jid);
        dto.setCnt(0);
        return dto;

    }

    @Override
    public Result<PageDTO<FollowPeomTodayDTO>> todayPageList(long current, long size) {
        return Result.ok(this.queryTodayPageList(current,size));
    }


    public PageDTO<FollowPeomTodayDTO> queryTodayPageList(long current, long size) {
        LambdaQueryWrapper<InteractFollowPeomToday> query = new LambdaQueryWrapper<>();

        query.orderByAsc(InteractFollowPeomToday::getSerialNo);
        IPage<InteractFollowPeomToday> page = followPeomTodayService.page(new Page<>(current, size), query);
        return PageUtils.pageOf(page, followPeomConvert::convertDTO);

    }

    @Override
    public Result<FollowPeomTodayInfoDTO> getTodayInfo(Long jid, long current, long size) {
        FollowPeomTodayInfoDTO dto = new FollowPeomTodayInfoDTO();
        if (current == 1) {
            dto.setCurrentUser(this.queryTodayByJid(jid));
        }
        dto.setList(this.queryTodayPageList(current,size));
        return Result.ok(dto);
    }

    public FollowPeomWeekDTO queryWeekByJid(Long jid) {
        LambdaQueryWrapper<InteractFollowPeomWeek> query = new LambdaQueryWrapper<>();
        query.eq(InteractFollowPeomWeek::getAuthorId, jid);
        InteractFollowPeomWeek entity = followPeomWeekService.getOne(query);
        if (Objects.nonNull(entity)){
            return followPeomConvert.convertWeekDTO(entity);
        }
        FollowPeomWeekDTO dto = new FollowPeomWeekDTO();
        dto.setAuthorId(jid);
        dto.setCnt(0);
        return dto;

    }

    public PageDTO<FollowPeomWeekDTO> queryWeekPageList(long current, long size) {
        LambdaQueryWrapper<InteractFollowPeomWeek> query = new LambdaQueryWrapper<>();

        query.orderByAsc(InteractFollowPeomWeek::getSerialNo);
        IPage<InteractFollowPeomWeek> page = followPeomWeekService.page(new Page<>(current, size), query);
        return PageUtils.pageOf(page, followPeomConvert::convertWeekDTO);

    }

    @Override
    public Result<FollowPeomWeekInfoDTO> getWeekInfo(Long jid, long current, long size) {
        FollowPeomWeekInfoDTO dto = new FollowPeomWeekInfoDTO();
        if (current == 1) {
            dto.setCurrentUser(this.queryWeekByJid(jid));
        }
        dto.setList(this.queryWeekPageList(current,size));
        return Result.ok(dto);
    }
}
