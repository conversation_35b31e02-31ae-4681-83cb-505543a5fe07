package com.jxntv.gvideo.interact.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.interact.api.entity.AuditingComment;
import com.jxntv.gvideo.interact.api.entity.CommentPrimary;
import com.jxntv.gvideo.interact.api.entity.CommentReply;
import com.jxntv.gvideo.interact.api.repository.CommentPrimaryMapper;
import com.jxntv.gvideo.interact.api.repository.CommentReplyMapper;
import com.jxntv.gvideo.interact.api.service.AuditingCommentService;
import com.jxntv.gvideo.interact.api.service.CommentPrimaryService;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.interact.client.param.ListCommentJidParam;
import com.jxntv.gvideo.interact.client.param.QueryCommentParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created on 2020-01-19
 */
@Service
public class CommentPrimaryServiceImpl extends ServiceImpl<CommentPrimaryMapper, CommentPrimary> implements CommentPrimaryService {

    @Autowired
    private CommentReplyMapper commentReplyMapper;
    @Autowired
    private AuditingCommentService auditingCommentService;

    @Override
    @Transactional
    public Boolean removePrimary(Long id) {
        // 删除主评论
        int count = this.baseMapper.deleteById(id);
        // 删除主评论下的所有子评论
        LambdaUpdateWrapper<CommentReply> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(CommentReply::getPrimaryId, id);
        commentReplyMapper.delete(updateWrapper);
        return count > 0;
    }

    @Override
    @Transactional
    public Boolean savePrimary(CommentPrimary entity, Integer phase, String checkAutoNote) {
        // 新增主评论
        int count = this.baseMapper.insert(entity);
        AuditingComment auditingComment = new AuditingComment();
        auditingComment.setCommentId(entity.getId());
        auditingComment.setImage(entity.getImage());
        auditingComment.setSoundOssId(entity.getSoundOssId());
        auditingComment.setMediaId(entity.getMediaId());
        auditingComment.setDataType(entity.getDataType());
        auditingComment.setContent(entity.getContent());
        auditingComment.setType(0);
        auditingComment.setPhase(Objects.nonNull(phase) ? phase : 1);
        auditingComment.setCheckAutoNote(checkAutoNote);
        auditingComment.setFromJid(entity.getFromJid());
        auditingComment.setFromName(entity.getFromName());
        auditingComment.setPlatform(entity.getPlatform());
        auditingComment.setCategoryId(entity.getCategoryId());
        auditingComment.setShowName(entity.getShowName());
        auditingComment.setAiAuditState(1); //设置为机审中
        auditingCommentService.save(auditingComment);
        // 发送机审
        auditingCommentService.postToAiAudit(auditingComment);
        return count > 0;
    }

    @Override
    public Page<CommentPrimary> getMediaCloudCommentList(Page<CommentPrimary> page, Long jid, Long excludeId, Long categoryId, Long mediaId) {
        return page.setRecords(this.baseMapper.getMediaCloudCommentList(page, jid, excludeId, categoryId, mediaId));
    }

    @Override
    public Long getAuthenticationReplyJid(Long mediaId, Long authorId, Long commentUser) {
        return this.baseMapper.getAuthenticationReplyJid(mediaId, authorId, commentUser);
    }

    /**
     * 优质评论：
     * 1、首先取高赞认证号评论
     * 2、其次取高赞评论
     *
     * @param mediaIds
     * @return
     */
    @Override
    public List<CommentPrimary> queryQuality(List<Long> mediaIds) {
        if (CollectionUtils.isEmpty(mediaIds)) {
            return Collections.emptyList();
        }
        //  优先取认证用户的点赞最多评论
        List<CommentPrimary> commentPrimaries = listAuthedLikeMostComments(mediaIds);

        //  其次取点赞最高的回复
        Set<Long> authedMediaIdSet = commentPrimaries.stream().map(CommentPrimary::getMediaId).collect(Collectors.toSet());
        //  过滤掉已经使用的mediaId
        List<Long> validMediaIds = mediaIds.stream().filter(e -> !authedMediaIdSet.contains(e)).collect(Collectors.toList());
        List<CommentPrimary> commentWithPraise = listLikeMostComments(validMediaIds);
        commentPrimaries.addAll(commentWithPraise);

        return commentPrimaries;
    }

    /**
     * 获取高赞认证号评论
     *
     * @param mediaIds
     * @return
     */
    @Override
    public List<CommentPrimary> listAuthedLikeMostComments(List<Long> mediaIds) {
        return CollectionUtils.isEmpty(mediaIds) ? Collections.emptyList() : this.baseMapper.selectAuthedLikeMostComments(mediaIds);
    }

    /**
     * @param mediaIds
     * @return
     */
    @Override
    public List<CommentPrimary> listLikeMostComments(List<Long> mediaIds) {
        return CollectionUtils.isEmpty(mediaIds) ? Collections.emptyList() : this.baseMapper.selectLikeMostComments(mediaIds);
    }

    @Override
    public List<CommentPrimary> selectHotAuditComments() {
        return this.baseMapper.selectHotAuditComments();
    }

    @Override
    public int updateBatchHotCount(List<Long> idList) {
        return this.baseMapper.updateBatchHotCount(idList);
    }

    @Override
    public List<CommentPrimary> selectCommentsById(List<Long> idList) {
        return this.baseMapper.selectCommentsById(idList);
    }

    @Override
    public Page<CommentDTO> queryComment(Page page, QueryCommentParam primary) {
        return this.baseMapper.queryComment(page,primary);
    }

    @Override
    public Page<Long> queryCommentJidList(ListCommentJidParam param) {

        return this.baseMapper.queryCommentJidList(new Page<>(param.getCurrent(), param.getSize()),param.getMediaId(), param.getJid());
    }

}
