package com.jxntv.gvideo.interact.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.interact.api.entity.InteractFollow;
import com.jxntv.gvideo.interact.client.dto.FollowResourceSearchDTO;
import com.jxntv.gvideo.interact.client.dto.SimpleFollowDTO;

import java.util.List;

/**
 * Created on 2020-03-19
 */
public interface FollowService extends IService<InteractFollow> {

    IPage<SimpleFollowDTO> listMyFollow(Long jid, Integer pageNum, Integer pageSize);

    /**
     * 推荐用户列表
     *
     * @return
     */
    List<SimpleFollowDTO> listRecommend();

    boolean isFollow(Long jid, Long authorId);

    IPage<InteractFollow> pageList(Page<InteractFollow> objectPage, Long jid);

    IPage<Long> listMyFollowResource(FollowResourceSearchDTO dto);

    IPage<Long> listJuniorMyFollowResource(FollowResourceSearchDTO dto);

}
