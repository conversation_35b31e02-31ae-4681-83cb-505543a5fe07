package com.jxntv.gvideo.interact.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.interact.api.entity.AuditingComment;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.interact.client.dto.EditAuditingCommentDTO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *
 * Created on 2020-02-07
 */
public interface AuditingCommentService extends IService<AuditingComment> {

    Boolean batchPass(List<Long> ids, String username);

    Boolean batchRefuse(List<Long> ids, String username);

    Boolean batchRefuse(List<Long> ids,Integer code, String reason, String username);

    void editAuditingComment(@RequestBody EditAuditingCommentDTO editAuditingCommentDTO);

    boolean effectAuditingComment(Long id);

    IPage<CommentDTO> queryJuniorComment(Page<CommentDTO> page, Long jid, Integer checkStatus);


    /**
     * 将评论或回复提交机审
     * @param auditingComment
     */
    void postToAiAudit(AuditingComment auditingComment);

    boolean updateAuditProcedureId(Long id, Long procedureId);
}
