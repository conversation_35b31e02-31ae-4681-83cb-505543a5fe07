package com.jxntv.gvideo.interact.api.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.interact.api.entity.CommentPrimary;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.interact.client.param.ListCommentJidParam;
import com.jxntv.gvideo.interact.client.param.QueryCommentParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Created on 2020-02-07
 */
public interface CommentPrimaryService extends IService<CommentPrimary> {

    /**
     * 删除主评论
     *
     * @param id 主评论ID
     * @return
     */
    Boolean removePrimary(Long id);

    /**
     * 增加主评论同时增加待审核记录
     *
     * @param entity
     * @param phase         当前阶段0待机审1待人审2审核完
     * @param checkAutoNote 机器审核记录
     * @return
     */
    Boolean savePrimary(CommentPrimary entity, Integer phase, String checkAutoNote);

    /**
     * 查询电视直播主评论列表
     *
     * @param page
     * @param jid        用户ID
     * @param excludeId  排除评论ID
     * @param categoryId 栏目ID
     * @param mediaId    资源ID
     * @return
     */
    Page<CommentPrimary> getMediaCloudCommentList(Page<CommentPrimary> page, Long jid, Long excludeId, Long categoryId, Long mediaId);

    /**
     * 查询媒体资源C端认证用户回复
     *
     * @param mediaId     资源ID
     * @param authorId    资源作者ID
     * @param commentUser 评论人ID
     * @return
     */
    Long getAuthenticationReplyJid(Long mediaId, Long authorId, Long commentUser);

    /**
     * 查询优质评论
     *
     * @param mediaId
     * @return
     */
    List<CommentPrimary> queryQuality(List<Long> mediaId);

    /**
     * 高赞认证号评论
     *
     * @param mediaIds
     * @return
     */
    List<CommentPrimary> listAuthedLikeMostComments(List<Long> mediaIds);

    /**
     * 高赞评论
     *
     * @param mediaIds
     * @return
     */
    List<CommentPrimary> listLikeMostComments(List<Long> mediaIds);

    /**
     * 查询满足热度规则检验的评论
     *
     * @return
     */
    List<CommentPrimary> selectHotAuditComments();

    /**
     * 批量更新热度次数
     *
     * @param idList
     * @return
     */
    int updateBatchHotCount(List<Long> idList);

    /**
     * 根据ID获取评论数据， 不排除del_flag为0
     * @param idList
     * @return
     */
    List<CommentPrimary> selectCommentsById(List<Long> idList);

    /**
     * 查询评论列表
     * @return
     */
    Page<CommentDTO> queryComment(Page page, QueryCommentParam primary);

    /**
     * 查询评论/回复的用户列表
     * @return
     */
    Page<Long> queryCommentJidList(ListCommentJidParam param);
}
