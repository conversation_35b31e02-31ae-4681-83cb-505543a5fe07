package com.jxntv.gvideo.interact.api.utils;

import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jxntv.gvideo.common.model.PageDTO;

public class PageUtils {

    public static <T, E> PageDTO<T> pageOf(IPage<E> page, Function<E, T> mapper) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords().stream().map(mapper).collect(Collectors.toList()));
        pageDTO.setPageNum((int) page.getCurrent());
        pageDTO.setPageSize((int) page.getSize());
        pageDTO.setTotal((int) page.getTotal());
        return pageDTO;
    }

    public static <T> PageDTO<T> pageOfSelf(IPage<T> page) {
        PageDTO<T> pageDTO = new PageDTO<>();
        pageDTO.setList(page.getRecords());
        pageDTO.setPageNum(Math.toIntExact(page.getCurrent()));
        pageDTO.setPageSize(Math.toIntExact(page.getSize()));
        pageDTO.setTotal(Math.toIntExact(page.getTotal()));
        return pageDTO;
    }

}
