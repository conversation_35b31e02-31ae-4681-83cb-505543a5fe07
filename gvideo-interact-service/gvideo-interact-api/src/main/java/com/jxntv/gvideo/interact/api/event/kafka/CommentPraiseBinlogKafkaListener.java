package com.jxntv.gvideo.interact.api.event.kafka;

import com.jxntv.gvideo.canal.client.constants.Topics;
import com.jxntv.gvideo.canal.client.dto.CommentPraiseBinlogEvent;
import com.jxntv.gvideo.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CommentPraiseBinlogKafkaListener {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 接收kafka事件源，转换成内部事件
     */
    @KafkaListener(topics = {Topics.COMMENT_PRAISE_TOPIC})
    public void onMessage(ConsumerRecord<String, String> record, Acknowledgment ack) {
        try {
            String value = record.value();

            log.info("接收comment praise消息:{}", value);

            CommentPraiseBinlogEvent event = JsonUtils.fromJson(value, CommentPraiseBinlogEvent.class);

            applicationEventPublisher.publishEvent(event);

            //  手动提交ack
            ack.acknowledge();

        } catch (Exception e) {
            log.error("接收日志消费失败", e);
        }

    }


}
