package com.jxntv.gvideo.interact.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * 互动服务同步的媒体资源表，用于关联查询
 */
@Data
public class InteractSyncMedia {

    /**
     * 主键自增ID
     */
    @TableId(type = IdType.INPUT)
    private Long id;
    /**
     * 资源类型
     */
    private Integer contentType;
    /**
     * 发布作者ID
     */
    private Long releaseId;
    /**
     * 作者类型0-pgc,1-ugc
     */
    private Integer releaseType;
    /**
     * 资源数据类型0-普通帖子，1-提问，2-回答，3-爆料
     */
    private Integer dataType;
    /**
     * 资源状态
     */
    private Integer status;


}
