package com.jxntv.gvideo.interact.api.job;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jxntv.gvideo.interact.api.converter.FollowIncrementalStatisticsConvert;
import com.jxntv.gvideo.interact.api.converter.FollowPeomConvert;
import com.jxntv.gvideo.interact.api.entity.InteractFollowIncrementalStatistics;
import com.jxntv.gvideo.interact.api.entity.InteractFollowPeomToday;
import com.jxntv.gvideo.interact.api.entity.InteractFollowPeomWeek;
import com.jxntv.gvideo.interact.api.repository.FollowMapper;
import com.jxntv.gvideo.interact.api.service.FollowIncrementalStatisticsService;
import com.jxntv.gvideo.interact.api.service.FollowPeomTodayService;
import com.jxntv.gvideo.interact.api.service.FollowPeomWeekService;
import com.jxntv.gvideo.interact.client.dto.FollowIncrementalStatisticsDTO;
import com.jxntv.gvideo.interact.client.dto.TodayFollowCountDTO;
import com.jxntv.gvideo.interact.client.dto.WeekFollowCountDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 粉丝统计任务
 *
 * <AUTHOR>
 * @date 2022/4/12
 */
@Slf4j
@Component
public class FollowJob {


    @Resource
    private FollowMapper followMapper;

    @Resource
    private FollowPeomTodayService followPeomTodayService;

    @Resource
    private FollowPeomWeekService followPeomWeekService;

    @Resource
    private FollowIncrementalStatisticsService followIncrementalStatisticsService;

    @Resource
    private FollowPeomConvert followPeomConvert;

    @Resource
    private FollowIncrementalStatisticsConvert followIncrementalStatisticsConvert;

    /**
     * 统计少年诗词大会今日粉丝排行榜
     */
    @XxlJob("peomFollowTodayJob")
    public ReturnT<String> peomFollowTodayJob() {

        List<TodayFollowCountDTO> list = followMapper.queryTodayFollowCount();
        followPeomTodayService.remove(new QueryWrapper<>());

        for (TodayFollowCountDTO dto : list) {
            InteractFollowPeomToday entity = followPeomConvert.convert(dto);
            followPeomTodayService.save(entity);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 统计少年诗词大会今日粉丝排行榜
     */
    @XxlJob("peomFollowWeekJob")
    public ReturnT<String> peomFollowWeekJob() {

        List<WeekFollowCountDTO> list = followMapper.queryWeekFollowCount(getWeekStart(), getWeekEnd());
        followPeomWeekService.remove(new QueryWrapper<>());

        for (WeekFollowCountDTO dto : list) {
            InteractFollowPeomWeek entity = followPeomConvert.convertWeek(dto);
            followPeomWeekService.save(entity);
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 粉丝增量统计
     */
    @XxlJob("followIncrementalStatisticsJob")
    public ReturnT<String> followIncrementalStatisticsJob() {
        String params = XxlJobHelper.getJobParam();
        XxlJobHelper.log("获取到参数为:{}", params);
        if(StringUtils.isEmpty(params)){
            return ReturnT.SUCCESS;
        }

        List<StatisticsDTO> statisticsDTOList = JSON.parseArray(params, StatisticsDTO.class);
        if (CollectionUtils.isEmpty(statisticsDTOList)){
            return ReturnT.SUCCESS;
        }

        for (StatisticsDTO statisticsDTO : statisticsDTOList) {

            List<FollowIncrementalStatisticsDTO> list = followMapper.queryFollowIncrementalStatisticsCount(statisticsDTO.getGroupId(), statisticsDTO.getStartTime(), statisticsDTO.getEndTime());

            followIncrementalStatisticsService.remove(new QueryWrapper<InteractFollowIncrementalStatistics>().lambda().eq(InteractFollowIncrementalStatistics::getGroupId, statisticsDTO.getGroupId()));

            if (!CollectionUtils.isEmpty(list)) {
                List<InteractFollowIncrementalStatistics> entityList = new ArrayList<>(list.size());
                for (FollowIncrementalStatisticsDTO dto : list) {
                    InteractFollowIncrementalStatistics entity = followIncrementalStatisticsConvert.convert(dto);
                    entity.setGroupId(statisticsDTO.getGroupId());
                    entityList.add(entity);
                }
                followIncrementalStatisticsService.saveBatch(entityList);
            }
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 获取本周的第一天
     *
     * @return String
     **/
    public static String getWeekStart() {
        Calendar c = Calendar.getInstance();
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        c.add(Calendar.DATE, -day_of_week + 1);
        return new SimpleDateFormat("yyyy-MM-dd").format(c.getTime()) + " 00:00:00";
    }

    /**
     * 获取本周的最后一天
     *
     * @return String
     **/
    public static String getWeekEnd() {
        Calendar c = Calendar.getInstance();
        int day_of_week = c.get(Calendar.DAY_OF_WEEK) - 1;
        if (day_of_week == 0) {
            day_of_week = 7;
        }
        c.add(Calendar.DATE, -day_of_week + 7);
        return new SimpleDateFormat("yyyy-MM-dd").format(c.getTime()) + " 23:59:59";
    }



    @Data
    public static class StatisticsDTO {
        private Long groupId;
        private String startTime;
        private String endTime;
    }

    public static void main(String[] args) {
        String json =" [{\"groupId\":63,\"startTime\":\"2023-07-15 00:00:00\"},{\"groupId\":11,\"startTime\":\"2023-11-15 00:00:00\",\"endTime\":\"2023-11-20 00:00:00\"}] ";
        List<StatisticsDTO> statisticsDTOList = JSON.parseArray(json, StatisticsDTO.class);
        System.out.println(statisticsDTOList.toString());
    }
}
