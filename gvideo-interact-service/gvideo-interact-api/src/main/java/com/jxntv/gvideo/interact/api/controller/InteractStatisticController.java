package com.jxntv.gvideo.interact.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.api.entity.InteractFavorite;
import com.jxntv.gvideo.interact.api.service.FavoriteService;
import com.jxntv.gvideo.interact.client.InteractStatisticClient;
import com.jxntv.gvideo.interact.client.dto.InteractFavorStatisticDTO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

@RestController
public class InteractStatisticController implements InteractStatisticClient {

    @Resource
    private FavoriteService favoriteService;

    @Override
    public Result<InteractFavorStatisticDTO> getFavorStatistic(Long mediaId, Long jid) {
        InteractFavorStatisticDTO result = new InteractFavorStatisticDTO();
        if (Objects.nonNull(jid)) {
            LambdaQueryWrapper<InteractFavorite> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(InteractFavorite::getJid, jid);
            queryWrapper.eq(InteractFavorite::getMediaId, mediaId);
            int count = favoriteService.count(queryWrapper);
            result.setIsFavor(count > 0);
        } else {
            result.setIsFavor(false);
        }

        result.setFavors(favoriteService.count(Wrappers.<InteractFavorite>lambdaQuery().eq(InteractFavorite::getMediaId, mediaId)));

        return Result.ok(result);
    }
}
