package com.jxntv.gvideo.interact.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 私信发送记录表
 * @date 2022/03/08 14:48
 */
@Data
public class InteractImRecord implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 发送人ID
     */
    private Long jid;
    /**
     * 接收人ID
     */
    private Long toJid;
    /**
     * 发送次数
     */
    private Integer sendTimes;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}
