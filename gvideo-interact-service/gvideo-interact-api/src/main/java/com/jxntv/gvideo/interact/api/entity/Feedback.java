package com.jxntv.gvideo.interact.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

import lombok.Data;

/**
 * 直播投放日志表
 *
 * @TableName feedback
 */
@TableName(value = "feedback")
@Data
public class Feedback {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 反馈人昵称
     */
    private String nickname;

    /**
     * 操作时间
     */
    private LocalDateTime createDate;
    private Long jid;
}