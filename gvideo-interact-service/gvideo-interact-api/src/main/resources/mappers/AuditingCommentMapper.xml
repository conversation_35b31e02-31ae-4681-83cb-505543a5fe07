<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.interact.api.repository.AuditingCommentMapper">

    <select id="queryJuniorComment" resultType="com.jxntv.gvideo.interact.client.dto.CommentDTO">
        select cp.id as id,
               '主评论' as type,
               cp.content as content,
               cp.from_jid as jid,
               cp.platform as platform,
               cp.media_id as media_id,
               cp.image as image,
               cp.sound_oss_id as sound_oss_id,
               cp.status as status,
               cp.show_name as show_name,
               cp.create_date as create_date,
               cp.update_date as update_date,
               cp.checkstatus as checkstatus,
               cp.audit_date as audit_date,
               cp.data_type as data_type,
               cp.ip as ip,
               cp.ip_location as ip_location
        from comment_primary cp
             left join group_topic_content gtc on cp.media_id = gtc.media_id
             left join group_info gi on gi.id = gtc.group_id
        where cp.from_jid = #{jid} and cp.del_flag = 0
          <if test="checkStatus != null">
              and cp.checkstatus = #{checkStatus}
          </if>
          and cp.data_type not in (1, 2)
          and gi.`type` like '%2%'
        union all
        select cr.id as id,
               '回复评论' as type,
               cr.content as content,
               cr.from_jid as jid,
               cr.platform as platform,
               cr.media_id as media_id,
               cr.image as image,
               cr.sound_oss_id as sound_oss_id,
               cr.status as status,
               cr.show_name as show_name,
               cr.create_date as create_date,
               cr.update_date as update_date,
               cr.checkstatus as checkstatus,
               cr.audit_date as audit_date,
               cr.data_type as data_type,
               cr.ip as ip,
               cr.ip_location as ip_location
        from comment_reply cr
             left join group_topic_content gtc on cr.media_id = gtc.media_id
             left join group_info gi on gi.id = gtc.group_id
        where cr.from_jid = #{jid} and cr.del_flag = 0
        <if test="checkStatus != null">
            and cr.checkstatus = #{checkStatus}
        </if>
          and cr.data_type not in (1, 2)
          and gi.`type` like '%2%'
    </select>
</mapper>