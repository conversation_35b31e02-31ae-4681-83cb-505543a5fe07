<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxntv.gvideo.interact.api.repository.CommentPraiseMapper">
    <!--获取点赞数量-->
    <select id="listCommentPraiseCount" resultType="com.jxntv.gvideo.interact.client.dto.CommentPraiseCountDTO">
        SELECT
        cp.comment_id AS commentId,
        COUNT(*) AS `count`
        FROM
        comment_praise cp
        WHERE
        cp.type = #{type}
        AND cp.comment_id IN
        <foreach collection="commentIdList" item="commentId" separator="," open="(" close=")">
            #{commentId}
        </foreach>
        <if test="startDate != null">
            AND cp.create_date <![CDATA[ >= ]]> #{startDate}
        </if>
        <if test="endDate != null">
            AND cp.create_date <![CDATA[ <= ]]> #{endDate}
        </if>
        GROUP BY
        cp.comment_id
    </select>

</mapper>