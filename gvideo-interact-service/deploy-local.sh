#!/bin/bash
# 参数配置
project_name="gvideo-interact-service"
module_name="gvideo-interact-service/gvideo-interact-api"
jar_name="interact.jar"
jar_path="gvideo-interact-service/gvideo-interact-api/target/${jar_name}"
log_path="/home/<USER>/logs/${project_name}.out"
env="local"
# 更新代码
cd .. && git pull
# 打包服务
mvn clean package -T 1C -Dmaven.test.skip=true -Dmaven.compile.fork=true -am -pl ${module_name} -P ${env}
# 关闭服务
pid=$(ps ax | grep -i ${jar_name} | grep java | grep -v grep | awk '{print $1}')
if [ -z "$pid" ]; then
  echo "No ${project_name} running."
else
  echo "The ${project_name}(${pid}) is running..."
  kill -9 ${pid}
  echo "Send shutdown request to ${project_name}(${pid}) OK"
fi
# 启动服务
echo "" > ${log_path}
nohup java -jar ${jar_path} >> ${log_path} 2>&1 &
tail -f ${log_path}
