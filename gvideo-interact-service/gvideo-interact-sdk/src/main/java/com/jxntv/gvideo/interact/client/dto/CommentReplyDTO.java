package com.jxntv.gvideo.interact.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * Created on 2020-02-07
 */
@Data
public class CommentReplyDTO implements Serializable {

    private Long id;
    private String content;
    private Integer platform;
    private Long categoryId;
    private String showName;
    private Long mediaId;
    private Long primaryId;
    private Long fromJid;
    private String fromName;
    private Long toJid;
    private String toName;
    private Integer status;
    private Integer checkstatus;
    private Date createDate;
    private Date updateDate;
    /**
     * 图片oss id集合
     */
    private String image;
    /**
     * 语音vod id
     */
    private String soundOssId;
    private Boolean delFlag;
    /**
     * 资源类型：0-默认资源、1-提问，2-回答，3-爆料
     */
    private Integer dataType;
    /**
     * ip
     */
    private String ip;
    /**
     * ip归属地
     */
    private String ipLocation;
}
