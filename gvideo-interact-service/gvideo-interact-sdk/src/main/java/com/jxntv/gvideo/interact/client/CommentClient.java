package com.jxntv.gvideo.interact.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.dto.*;
import com.jxntv.gvideo.interact.client.fallback.CommentClientFallback;
import com.jxntv.gvideo.interact.client.param.ListAuditLogParam;
import com.jxntv.gvideo.interact.client.param.ListAuditingParam;
import com.jxntv.gvideo.interact.client.param.ListCommentJidParam;
import com.jxntv.gvideo.interact.client.param.QueryCommentParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created on 2020-02-17
 */
@FeignClient(name = "interact-service", contextId = "comment", fallbackFactory = CommentClientFallback.class)
public interface CommentClient {

    @GetMapping("/api/comment/list")
    Result<PageDTO<CommentDTO>> getCommentList(
            @RequestParam(value = "type", required = false) Integer type,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "createStartDate", required = false) Date createStartDate,
            @RequestParam(value = "createEndDate", required = false) Date createEndDate,
            @RequestParam(value = "consumerUser", required = false) String consumerUser,
            @RequestParam(value = "mediaId", required = false) Long mediaId,
            @RequestParam(value = "commentId", required = false) Long commentId,
            @RequestParam(value = "excludeId", required = false) Long excludeId,
            @RequestParam(value = "checkstatus", required = false) Integer checkstatus,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

    /**
     * 分页查询评论列表
     * @param param
     * @return
     */
    @PostMapping("/api/comment/query")
    Result<PageDTO<CommentDTO>> queryCommentList(@RequestBody QueryCommentParam param);

    @PutMapping("/api/comment/status")
    Result changeStatus(
            @RequestParam("id") Long id,
            @RequestParam("type") Integer type,
            @RequestParam("status") Integer status);

    @GetMapping("/api/comment/tree")
    Result<CommentTreeDTO> getReplyTree(
            @RequestParam("id") Long id,
            @RequestParam("type") Integer type,
            @RequestParam(value = "checkstatus", required = false) Integer checkstatus,
            @RequestParam(value = "status", required = false) Integer status);

    @PostMapping("/api/comment")
    Result<Long> addComment(@RequestBody AddCommentDTO addCommentDTO);

    @PostMapping("/api/comment/reply")
    Result<Long> addReply(@RequestBody AddReplyDTO addReplyDTO);

    @DeleteMapping("/api/comment")
    Result deleteComment(
            @RequestParam("id") Long id);

    @DeleteMapping("/api/comment/reply")
    Result deleteReply(
            @RequestParam("id") Long id);

    @GetMapping("/api/comment/info")
    Result<CommentPrimaryDTO> getPrimary(
            @RequestParam("id") Long id);

    @GetMapping("/api/comment/info/jid")
    Result<CommentDTO> getPrimaryByJid(@RequestParam("id") Long id, @RequestParam(value = "jid", required = false) Long jid);

    @PostMapping("/api/comment/primary/bulk")
    Result<List<CommentPrimaryDTO>> primaryBulk(@RequestBody List<Long> ids);

    @GetMapping("/api/comment/reply/info")
    Result<CommentReplyDTO> getReply(
            @RequestParam("id") Long id);

    @PostMapping("/api/comment/reply/bulk")
    Result<List<CommentReplyDTO>> replyBulk(@RequestBody List<Long> ids);

    @GetMapping("/api/comment/total")
    Result<Long> getCommentTotal(
            @RequestParam("mediaId") Long mediaId,
            @RequestParam(value = "jid", required = false) Long jid);

    @PostMapping("/api/comment/total/batch")
    Result<Map<Long, Long>> batchGetCommentTotal(
            @RequestBody List<Long> mediaIds,
            @RequestParam(value = "jid", required = false) Long jid);

    @GetMapping("/api/audit/list/comment")
    Result<PageDTO<AuditingCommentDTO>> getCommentAuditingList(
            @RequestParam(value = "consumerUser", required = false) String consumerUser,
            @RequestParam(value = "createStartDate", required = false) Date createStartDate,
            @RequestParam(value = "createEndDate", required = false) Date createEndDate,
            @RequestParam(value = "phase") Integer phase,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size,
            @RequestParam(value = "aiAuditState") Integer aiAuditState);


    @PostMapping("/api/audit/list/comment")
    Result<PageDTO<AuditingCommentDTO>> getCommentAuditingList(@RequestBody ListAuditingParam param);

    @GetMapping("/api/audit/list")
    List<AuditingCommentDTO> bulk(@RequestParam(value = "limit", defaultValue = "1000") Integer limit);

    @PutMapping("/api/audit/comment/batch/pass")
    Result<Boolean> batchPassComment(
            @RequestParam(value = "ids") List<Long> ids,
            @RequestParam(value = "username") String username);

    /**
     * 通过ids获取审核评论数据
     *
     * @param ids
     * @return
     */
    @GetMapping("/api/audit/comment/ids")
    Result<List<AuditingCommentDTO>> listByIds(@RequestParam List<Long> ids);

    @PutMapping("/api/audit/comment/batch/refuse")
    Result<Boolean> batchRefuseComment(
            @RequestParam(value = "ids") List<Long> ids,
            @RequestParam(value = "username") String username);

    @PutMapping("/api/audit/comment/batch/refuse/v2")
    Result<Boolean> batchRefuseCommentV2(
            @RequestParam(value = "ids") List<Long> ids,
            @RequestParam(value = "code", required = false) Integer code,
            @RequestParam(value = "reason", required = false) String reason,
            @RequestParam(value = "username") String username);

    @GetMapping("/api/audit/log/comment")
    Result<PageDTO<AuditlogCommentDTO>> getCommentAuditLogList(
            @RequestParam(value = "createStartDate", required = false) Date createStartDate,
            @RequestParam(value = "createEndDate", required = false) Date createEndDate,
            @RequestParam(value = "auditStartDate", required = false) Date auditStartDate,
            @RequestParam(value = "auditEndDate", required = false) Date auditEndDate,
            @RequestParam(value = "checkStatus", required = false) Integer checkStatus,
            @RequestParam(value = "adminUser", required = false) String adminUser,
            @RequestParam(value = "consumerUser", required = false) String consumerUser,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size,
            @RequestParam(value = "aiAuditState") Integer aiAuditState);

    @PostMapping("/api/audit/log/comment")
    Result<PageDTO<AuditlogCommentDTO>> getCommentAuditLogList(@RequestBody ListAuditLogParam param);

    @GetMapping("/api/comment/list/jid")
    Result<PageDTO<CommentDTO>> getCommentListByJid(
            @RequestParam(value = "mediaId", required = false) Long mediaId,
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(value = "excludeId", required = false) Long excludeId,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size);

    @GetMapping("/api/comment/user/{jid}/page")
    Result<PageDTO<CommentDTO>> getUserComments(
            @PathVariable("jid") Long jid,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer checkstatus);

    @GetMapping("/api/comment/user/{jid}/count")
    Result<Integer> getUserCommentCount(@PathVariable("jid") Long jid, @RequestParam(required = false) Integer checkstatus);

    @GetMapping("/api/comment/tree/jid")
    Result<CommentTreeDTO> getReplyTreeByJid(
            @RequestParam("primaryId") Long primaryId,
            @RequestParam(value = "jid", required = false) Long jid);

    @GetMapping("/api/comment/statistic")
    Result<List<AuditingCommentDTO>> getCommentStatistic(
            @RequestParam("statisticDate") Date statisticDate);

    @GetMapping("/api/mediacloud/comment/list")
    Result<PageDTO<CommentDTO>> getMediaCloudCommentList(
            @RequestParam(value = "categoryId", required = false) Long categoryId,
            @RequestParam(value = "mediaId", required = false) Long mediaId,
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(value = "excludeId", required = false) Long excludeId,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size);

    /**
     * 评论点赞/取消点赞
     *
     * @param dto
     * @return
     */
    @PostMapping("/api/comment/praise/add")
    Result<Boolean> praise(@RequestBody CommentPraiseDTO dto);

    /**
     * 获取评论赞数量
     *
     * @param type      类型：0-评论、1-回复
     * @param commentId 评论/回复ID
     * @return
     */
    @GetMapping("/api/comment/praise/sum")
    Result<Integer> praiseSum(@RequestParam(value = "type") Integer type, @RequestParam(value = "commentId") Long commentId);

    /**
     * 获取评论赞数量
     *
     * @param type
     * @param commentIdList
     * @return
     */
    @GetMapping("/api/comment/praise/sum/bulk")
    Result<Map<Long, Long>> praiseSumBulk(@RequestParam(value = "type") Integer type, @RequestParam(value = "commentIdList") List<Long> commentIdList);

    /**
     * 判断是否点赞
     *
     * @param jid       用户ID
     * @param type      类型：0-评论、1-回复
     * @param commentId 评论/回复ID
     * @return
     */
    @GetMapping("/api/comment/praise")
    Result isPraise(@RequestParam(value = "jid") Long jid, @RequestParam(value = "type") Integer type, @RequestParam(value = "commentId") Long commentId);

    /**
     * 批量是否点赞
     *
     * @param jid
     * @param type
     * @param commentIdList
     * @return
     */
    @GetMapping("/api/comment/praise/bulk")
    Result<Map<Long, Long>> isPraiseBulk(@RequestParam(value = "jid") Long jid, @RequestParam(value = "type") Integer type, @RequestParam(value = "commentIdList") List<Long> commentIdList);

    /**
     * 查询媒体资源C端认证用户回复
     *
     * @param mediaId  资源ID
     * @param authorId 资源作者ID
     * @return
     */
    @GetMapping("/api/comment/authentication/reply/by/media")
    Result<Long> authenticationReplyJid(@RequestParam("mediaId") Long mediaId,
                                        @RequestParam("authorId") Long authorId,
                                        @RequestParam(value = "commentUser", required = false) Long commentUser);


    /**
     * 批量获取最新的C端认证用户回复
     *
     * @param queries 资源ID
     * @return
     */
    @PostMapping("/api/comment/authentication/reply/by/media/batch")
    Result<List<AuthedUgcIdDTO>> listLastAuthedUgcReply(@RequestBody List<AuthedUgcReplyQuery> queries);

    /**
     * 查询优质评论
     *
     * @param mediaId
     * @return
     */
    @PostMapping("/api/comment/query/quality")
    Result<List<CommentDTO>> queryQuality(@RequestBody List<Long> mediaId);

    /**
     * 更新评论审核记录
     *
     * @param editAuditingCommentDTO
     * @return
     */
    @PutMapping("/api/audit/comment")
    Result editAuditingComment(@RequestBody EditAuditingCommentDTO editAuditingCommentDTO);

    /**
     * 评论热度执行
     *
     * @return
     */
    @PostMapping("/api/comment-hot/exec")
    Result commentHotExec();

    /**
     * 待审核评论数量
     *
     * @return
     */
    @GetMapping("/api/audit/comment/sum")
    Result<Integer> commentAuditSum();

    /**
     * 获取评论列表
     *
     * @param answerList
     * @return
     */
    @PostMapping("/api/audit/list/comment/ids")
    Result<List<CommentDTO>> listCommentById(@RequestBody List<Long> answerList);

    /**
     * 获取回答评论列表
     *
     * @param answerList
     * @return
     */
    @PostMapping("/api/audit/list/reply/comment/ids")
    Result<List<CommentDTO>> listReplyById(@RequestBody List<Long> answerList);

    @DeleteMapping("/api/comment/primary/from")
    Result<Void> deletePrimaryByFromJid(@RequestParam Long fromJid);

    @DeleteMapping("/api/comment/reply/from")
    Result<Void> deleteReplyByFromJid(@RequestParam Long fromJid);

    @DeleteMapping("/api/comment/praise/from")
    Result<Void> deletePraiseByFromJid(@RequestParam Long fromJid);

    @DeleteMapping("/api/audit/comment/from")
    Result<Void> deleteAuditCommentByFromJid(@RequestParam Long fromJid);

    @PostMapping("/api/comment/jid/list")
    Result<PageDTO<Long>> queryCommentJidList(@RequestBody ListCommentJidParam param);
    @PutMapping("/api/audit/comment/{id}/procedure/{procedureId}")
    Result<Boolean> updateAuditingProcedureId(@PathVariable("id") Long id, @PathVariable("procedureId") Long procedureId);

    @PostMapping("/api/audit/comment/{id}/effect")
    Result<Boolean> effectAuditingComment(@PathVariable("id") Long id);
    @GetMapping("/api/comment/junior/{jid}/page")
    Result<PageDTO<CommentDTO>> getJuniorUserComments(
            @PathVariable("jid") Long jid,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size,
            @RequestParam(required = false) Integer checkstatus);

    @GetMapping("/api/comment/primary/list/jid")
    Result<PageDTO<CommentDTO>> getPrimaryListByJid(
            @RequestParam(value = "mediaId", required = false) Long mediaId,
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(required = false, defaultValue = "1") Integer current,
            @RequestParam(required = false, defaultValue = "10") Integer size);
}
