package com.jxntv.gvideo.interact.client.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 举报表
 * @date 2021/05/20 11:09
 */
@Data
public class InteractReportDTO implements Serializable {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 唯一编码
     */
    private String code;
    /**
     * 举报用户JID
     */
    private Long jid;
    /**
     * 内容类型：0-资源、1-评论、2-回复、3-C端用户、4-群组
     */
    private Integer contentType;
    /**
     * 媒体类型：评论/回复(1-图文、2-语音、3-文字、4-文字+语音、5-文字+图片)，其他和资源表对应
     */
    private Integer mediaType;
    /**
     * 内容ID
     */
    private Long contentId;
    /**
     * 群组ID
     */
    private String groupId;
    /**
     * 资源账号类型：0-pgc、1-ugc、2-pptv、3-群组
     */
    private Integer releaseType;
    /**
     * 资源账号ID
     */
    private Long releaseId;
    /**
     * 违规类型：0-其他、1-不实信息、2-政治敏感、3-违法犯罪、4-金钱诈骗、5-侵犯未成年人、6-垃圾广告、7-抄袭侵权、8-泄露隐私
     */
    private Integer type;
    /**
     * 举报理由
     */
    private String content;
    /**
     * 上传图片：ossId集合，英文冒号隔开
     */
    private List<String> imageList;
    /**
     * 状态：0-待处理、1-已驳回、2-已下架
     */
    private Integer status;
    /**
     * 审核人
     */
    private Long auditUserId;
    private String auditUserName;
    /**
     * 审核结果
     */
    private String auditNote;
    /**
     * 审核完成时间
     */
    private Date auditDate;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
}
