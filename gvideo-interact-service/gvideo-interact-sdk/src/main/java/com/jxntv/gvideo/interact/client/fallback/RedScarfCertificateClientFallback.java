package com.jxntv.gvideo.interact.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.RedScarfCertificateClient;
import com.jxntv.gvideo.interact.client.dto.RedScarfCertificateDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/11 15:15
 */
@Slf4j
@Component
public class RedScarfCertificateClientFallback implements FallbackFactory<RedScarfCertificateClient> {
    @Override
    public RedScarfCertificateClient create(Throwable throwable) {
        return new RedScarfCertificateClient() {

            @Override
            public Result<RedScarfCertificateDTO> query(String phone, String name) {
                log.error("RedScarfCertificateClient.query() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> upload(List<RedScarfCertificateDTO> params) {
                log.error("RedScarfCertificateClient.upload() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
