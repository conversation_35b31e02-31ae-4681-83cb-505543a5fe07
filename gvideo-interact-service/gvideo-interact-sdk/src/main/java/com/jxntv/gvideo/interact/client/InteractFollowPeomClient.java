package com.jxntv.gvideo.interact.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.dto.FollowPeomTodayDTO;
import com.jxntv.gvideo.interact.client.dto.FollowPeomTodayInfoDTO;
import com.jxntv.gvideo.interact.client.dto.FollowPeomWeekInfoDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @desc 诗词大会新增粉丝数
 */
public interface InteractFollowPeomClient {


    /**
     * 查询当日当前用户排名
     *
     * @param jid
     * @return
     */
    @GetMapping("/api/follow/peom/today")
    Result<FollowPeomTodayDTO> getTodayByJid(@RequestParam(value = "jid", required = true) Long jid);


    /**
     * 分页查询排行榜
     *
     * @param current
     * @param size
     * @return
     */
    @GetMapping("/api/follow/peom/today/list")
    Result<PageDTO<FollowPeomTodayDTO>> todayPageList(
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);


    @GetMapping("/api/follow/peom/today/info")
    Result<FollowPeomTodayInfoDTO> getTodayInfo(
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

    @GetMapping("/api/follow/peom/week/info")
    Result<FollowPeomWeekInfoDTO> getWeekInfo(
            @RequestParam(value = "jid", required = false) Long jid,
            @RequestParam(required = false, defaultValue = "1") long current,
            @RequestParam(required = false, defaultValue = "10") long size);

   }
