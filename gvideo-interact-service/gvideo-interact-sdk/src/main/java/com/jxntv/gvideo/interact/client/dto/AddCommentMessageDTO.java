package com.jxntv.gvideo.interact.client.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 添加互动消息
 * @date 2021/08/04 14:47
 */
@Data
public class AddCommentMessageDTO implements Serializable {
    /**
     * 发送人类型：0-系统消息、1-认证号消息、2-UGC私信消息、3-粉丝、4-问答消息
     */
    private Integer fromType;
    /**
     * 发送人ID：系统消息默认0，认证号和UGC私信对应其主键ID
     */
    private Long fromId;
    /**
     * 资源ID
     */
    private Long mediaId;
    /**
     * 内容类型：0-资源、1-评论、2-回复、3-UGC、4-回答、5-回复回答
     */
    private Integer contentType;
    /**
     * 内容ID
     */
    private Long contentId;
    /**
     * 社区ID
     */
    private Long groupId;

}
