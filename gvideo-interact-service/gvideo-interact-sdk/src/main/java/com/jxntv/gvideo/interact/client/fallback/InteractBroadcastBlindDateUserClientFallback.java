package com.jxntv.gvideo.interact.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.InteractBroadcastBlindDateUserClient;
import com.jxntv.gvideo.interact.client.dto.InteractBroadcastBlindDateUserDTO;
import com.jxntv.gvideo.interact.client.dto.InteractBroadcastBlindDateUserHeartBeatDTO;
import com.jxntv.gvideo.interact.client.dto.InteractBroadcastBlindDateUserSearchDTO;
import com.jxntv.gvideo.interact.client.dto.InteractBroadcastCloseConnectDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InteractBroadcastBlindDateUserClientFallback implements FallbackFactory<InteractBroadcastBlindDateUserClient> {
    @Override
    public InteractBroadcastBlindDateUserClient create(Throwable throwable) {
        return new InteractBroadcastBlindDateUserClient() {

            @Override
            public Result<Long> create(InteractBroadcastBlindDateUserDTO userDTO) {
                log.error("InteractBroadcastBlindDateUserClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> onlineHeartBeat(InteractBroadcastBlindDateUserHeartBeatDTO heartBeatDTO) {
                log.error("InteractBroadcastBlindDateUserClient.onlineHeartBeat() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> onlineExit(InteractBroadcastBlindDateUserHeartBeatDTO heartBeatDTO) {
                log.error("InteractBroadcastBlindDateUserClient.onlineExit() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addConnectUser(InteractBroadcastBlindDateUserDTO userDTO) {
                log.error("InteractBroadcastBlindDateUserClient.addConnectUser() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addRecommendUser(InteractBroadcastBlindDateUserDTO userDTO) {
                log.error("InteractBroadcastBlindDateUserClient.addRecommendUser() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateById(Long id, InteractBroadcastBlindDateUserDTO userDTO) {
                log.error("InteractBroadcastBlindDateUserClient.updateById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteById(Long id) {
                log.error("InteractBroadcastBlindDateUserClient.deleteById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<InteractBroadcastBlindDateUserDTO> getById(Long id) {
                log.error("InteractBroadcastBlindDateUserClient.getById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<InteractBroadcastBlindDateUserDTO> getByMediaIdAndJid(Long mediaId, Long jid) {
                log.error("InteractBroadcastBlindDateUserClient.getByMediaIdAndJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<InteractBroadcastBlindDateUserDTO>> page(InteractBroadcastBlindDateUserSearchDTO searchDTO) {
                log.error("InteractBroadcastBlindDateUserClient.page() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> closeConnect(InteractBroadcastCloseConnectDTO connectUserCloseDTO) {
                log.error("InteractBroadcastBlindDateUserClient.closeConnect() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
