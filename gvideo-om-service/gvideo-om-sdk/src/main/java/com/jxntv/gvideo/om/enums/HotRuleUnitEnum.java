package com.jxntv.gvideo.om.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 热度规则单位
 * @date 2021/07/06 16:05
 */
@Getter
public enum HotRuleUnitEnum {
    //单位：0-分钟、1-小时、2-天
    MINUTE(0, "分钟"),
    HOUR(1, "小时"),
    DAY(2, "天")
    ;

    private Integer code;
    private String name;

    HotRuleUnitEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static HotRuleUnitEnum getNameByCode(int code) {
        return Arrays.stream(HotRuleUnitEnum.values())
                .filter(item -> code == item.getCode()).findFirst().orElse(null);
    }
}
