package com.jxntv.gvideo.om.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 热度规则状态
 * @date 2021/07/06 16:05
 */
@Getter
public enum HotRuleStatusEnum {
    //状态：0-禁用、1-启用
    DISABLE(0, "禁用"),
    ENABLE(1, "启用");

    private Integer code;
    private String name;

    HotRuleStatusEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static HotRuleStatusEnum getNameByCode(int code) {
        return Arrays.stream(HotRuleStatusEnum.values())
                .filter(item -> code == item.getCode()).findFirst().orElse(null);
    }
}
