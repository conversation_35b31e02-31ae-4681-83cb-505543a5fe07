package com.jxntv.gvideo.om.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 智能审核状态
 * @date 2021/07/06 16:10
 */
@Getter
public enum AuditAutoStatus {
    //状态：0-任务待执行、1-任务执行中、2-已通过、3-含敏感、4-不通过
    JOB_PENDING(0, "任务待执行"),
    JOB_DOING(1, "任务执行中"),
    JOB_PASS(2, "已通过"),
    JOB_REVIEW(3, "含敏感"),
    JOB_REJECT(4, "未通过");

    private Integer code;
    private String name;

    AuditAutoStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AuditAutoStatus getNameByCode(int code) {
        return Arrays.stream(AuditAutoStatus.values())
                .filter(item -> code == item.getCode()).findFirst().orElse(null);
    }

    public static AuditAutoStatus getBySuggestion(SuggestionEnum suggestionEnum) {
        if (Objects.isNull(suggestionEnum)) {
            return AuditAutoStatus.JOB_REJECT;
        }
        if (SuggestionEnum.PASS == suggestionEnum) {
            return AuditAutoStatus.JOB_PASS;
        } else if (SuggestionEnum.BLOCK == suggestionEnum) {
            return AuditAutoStatus.JOB_REJECT;
        } else if (SuggestionEnum.REVIEW == suggestionEnum) {
            return AuditAutoStatus.JOB_REVIEW;
        } else {
            return AuditAutoStatus.JOB_REJECT;
        }
    }
}
