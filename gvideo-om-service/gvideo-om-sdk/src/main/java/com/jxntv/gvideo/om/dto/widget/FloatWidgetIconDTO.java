package com.jxntv.gvideo.om.dto.widget;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 浮动组件图标
 *
 * <AUTHOR>
 * @date 2022/6/14
 * Email: <EMAIL>
 */
@Data
public class FloatWidgetIconDTO implements Serializable {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 图标名称
     */
    private String name;
    /**
     * 图标对应的图片ID
     */
    private String iconId;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

}
