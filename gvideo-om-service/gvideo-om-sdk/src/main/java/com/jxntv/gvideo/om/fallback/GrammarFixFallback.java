package com.jxntv.gvideo.om.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.GrammarFixClient;
import com.jxntv.gvideo.om.dto.grammarfix.CheckForGanYunParam;
import com.jxntv.gvideo.om.dto.grammarfix.CheckForGanYunResult;
import com.jxntv.gvideo.om.dto.grammarfix.CheckParam;
import com.jxntv.gvideo.om.dto.grammarfix.CheckResult;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class GrammarFixFallback implements FallbackFactory<GrammarFixClient> {
    @Override
    public GrammarFixClient create(Throwable throwable) {
        return new GrammarFixClient() {
            @Override
            public Result<List<CheckResult>> check(CheckParam param) {
                log.error("GrammarFixClient.check() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<CheckForGanYunResult>> checkForGanYun(CheckForGanYunParam param) {
                log.error("GrammarFixClient.checkForGanYun() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
