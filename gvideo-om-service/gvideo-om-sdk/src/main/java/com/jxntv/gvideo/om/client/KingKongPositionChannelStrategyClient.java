package com.jxntv.gvideo.om.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategyDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategySearchDTO;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
public interface KingKongPositionChannelStrategyClient {

    /**
     * 默认渠道ID
     */
    Long DEFAULT_CHANNEL_ID = -1L;

    Long DEFAULT_JUNIOR_CHANNEL_ID = -2L;

    @PostMapping("/api/king-kong/position/channel/strategy")
    Result<Long> add(@RequestBody KingKongPositionChannelStrategyDTO strategyDTO);


    @GetMapping("/api/king-kong/position/channel/strategy/{id}")
    Result<KingKongPositionChannelStrategyDTO> getById(@PathVariable Long id);

    @DeleteMapping("/api/king-kong/position/channel/strategy/{id}")
    Result<Boolean> deleteById(@PathVariable Long id);

    @PutMapping("/api/king-kong/position/channel/strategy/{id}")
    Result<Boolean> updateById(@PathVariable Long id, @RequestBody KingKongPositionChannelStrategyDTO strategyDTO);


    @PostMapping("/api/king-kong/position/channel/strategy/page")
    Result<PageDTO<KingKongPositionChannelStrategyDTO>> page(@RequestBody KingKongPositionChannelStrategySearchDTO searchDTO);

    @GetMapping("/api/king-kong/position/channel/{channelId}/strategy")
    Result<KingKongPositionChannelStrategyDTO> getByChannelId(@PathVariable("channelId") Long channelId, @RequestParam Boolean isJuniorMode);

}
