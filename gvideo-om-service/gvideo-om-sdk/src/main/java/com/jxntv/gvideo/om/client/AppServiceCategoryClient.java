package com.jxntv.gvideo.om.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.om.dto.AppServiceCategoryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 金刚位接口定义
 *
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
@FeignClient(name = "om-service")
public interface AppServiceCategoryClient {


    @PostMapping("/api/app-service/category/position")
    Result<Long> add(@RequestBody AppServiceCategoryDTO appServiceCategoryDTO);


    @GetMapping("/api/app-service/category/{id}")
    Result<AppServiceCategoryDTO> getById(@PathVariable Long id);

    @DeleteMapping("/api/app-service/category/{id}")
    Result<Boolean> deleteById(@PathVariable Long id);

    @PutMapping("/api/app-service/category/{id}")
    Result<Boolean> updateById(@PathVariable Long id, @RequestBody AppServiceCategoryDTO appServiceCategoryDTO);


    @PostMapping("/api/app-service/category/page")
    Result<PageDTO<AppServiceCategoryDTO>> page(@RequestBody SearchDTO searchDTO);

    @PostMapping("/api/app-service/category/list")
    Result<List<AppServiceCategoryDTO>> listAll();
}
