package com.jxntv.gvideo.om.enums.widget;

/**
 * <AUTHOR>
 * @date 2022/5/16
 * Email: <EMAIL>
 */
public enum FloatWidgetStatus {

    DISABLE(0, "禁用"),
    ENABLE(1, "启用"),
    ;

    private Integer code;
    private String desc;

    FloatWidgetStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String toStr(Integer code) {
        if (code == null) {
            return "";
        }
        for (FloatWidgetStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }

}
