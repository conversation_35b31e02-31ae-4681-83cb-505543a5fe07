package com.jxntv.gvideo.om.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR> niedamin
 * @date : 2022-06-29 17:21
 **/
@Data
public class AppHomePageGuideDTO implements Serializable {

    /**
     * ID
     */
    private Long id;

    /**
     * 金刚位ID
     */
    private Long kingkongId;

    /**
     * 金刚位渠道ID
     */
    private String channelId;

    /**
     * 金刚位渠道名称
     */
    private String channelName;

    /**
     * 渠道策略ID
     */
    private Long strategyId;

    /**
     * 首页引导名称
     */
    private String name;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime startDate;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime endDate;

    /**
     * 图片ID
     */
    private String imageId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 按钮文案
     */
    private String buttonWords;

    /**
     * 跳转类型，0-应用内跳转，1-链接
     */
    private Integer jumpType;
    /**
     * 外链接跳转URL
     */
    private String jumpUrl;

    /**
     * 内链接跳转类型，0-跳菜单，1-跳内容
     */
    private Integer innerJumpType;


    /**
     * 跳转菜单类型，0-新闻首页，1-推荐首页，2-我的圈子，3-发现圈子
     */
    private Integer jumpMenu;

    /**
     * 跳转内容id
     */
    private Long jumpMediaId;

    /**
     * 跳转内容类型
     */
    private Integer jumpMediaType;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime createDate;


    /**
     * 更新人id
     */
    private Long updateUserId;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm",timezone = "GMT+8")
    private LocalDateTime updateDate;

    private String jump;

}
