package com.jxntv.gvideo.om.fallback;


import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AuditMediaClient;
import com.jxntv.gvideo.om.dto.AuditLogMediaDTO;
import com.jxntv.gvideo.om.dto.AuditingMediaDTO;
import com.jxntv.gvideo.om.dto.MediaAuditSumDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class AuditMediaClientFallback implements FallbackFactory<AuditMediaClient> {
    @Override
    public AuditMediaClient create(Throwable throwable) {
        return new AuditMediaClient() {
            @Override
            public Result addAuditingMedia(AuditingMediaDTO auditingMediaDTO) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result addAuditLogMedia(AuditLogMediaDTO auditLogMediaDTO) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<AuditingMediaDTO>> getAuditingMedias(String adminUserName, Integer mediaType, Integer contentType, Integer answer, String authorName, String mediaTitle, List<Long> procedureIds, Integer checkstatus, String startDate, String endDate, long current, long size,Integer aiAuditState) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> getAuditingMediasCount(String adminUserName, Integer mediaType, Integer contentType, String authorName, String mediaTitle, List<Long> procedureIds, Integer checkstatus, String startDate, String endDate) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<AuditLogMediaDTO>> getAuditLogMedias(String adminUserName, Integer contentType, Integer answer, String authorName, String mediaTitle, Integer checkstatus, String startDate, String endDate, String auditStartDate, String auditEndDate, long current, long size,Integer aiAuditState) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result batchPass(List<Long> ids, Long adminUserId, String adminUserName) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AuditingMediaDTO>> batchQueryAnswerAuditingMedia(List<Long> ids) {
                log.error("AuditMediaClient.batchQueryAnswerAuditingMedia() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result batchRefuse(List<Long> ids, Integer opType, Integer refuseCode, String refuseNote, Long adminUserId, String adminUserName) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result fetchMoreMedias(List<Long> ids, Long adminUserId, String adminUserName) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result updateAuditingMediaProcedureId(Long id, Long procedureId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result updateAuditingMediaProcedureIdAndAnswer(Long id, Long procedureId, Integer answerType) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result deleteAuditingMedia(Long id) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AuditingMediaDTO> getAuditingMediaInfo(Long id) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AuditingMediaDTO>> bulk(List<Long> ids) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AuditLogMediaDTO>> getAuditLogDetail(Long mediaId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<String> getAuditRefuseReason(Long mediaId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AuditingMediaDTO>> getAuditingStatistic(Date statisticDate) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> getAuditingMediaInProcedures(List<Long> procedureIds) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> removeUnaudit(Long mediaId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> checkMediaInAudit(Long mediaId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AuditingMediaDTO> getById(Long id) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> fetchBackJob() {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<MediaAuditSumDTO> mediaAuditSum(Integer type, List<Long> procedureIds) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteAuditMediaByAuthor(Integer authorType, Long authorId) {
                log.error("AuditMediaClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
