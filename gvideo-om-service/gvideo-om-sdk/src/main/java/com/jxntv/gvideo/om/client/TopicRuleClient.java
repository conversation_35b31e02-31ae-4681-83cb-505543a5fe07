package com.jxntv.gvideo.om.client;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.dto.TopicRuleReqDTO;
import com.jxntv.gvideo.om.dto.TopicRuleRespDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/18 14:42
 */
public interface TopicRuleClient {

    String TOPIC_RULE_PREFIX = "/api/om/topic/{model_id}/rule";

    /**
     * @param
     * @description: 创建话题模型规则
     * @return:
     * <AUTHOR>
     * @date 2021/11/4 11:04
     * @version 1.0
     */
    @PostMapping(TOPIC_RULE_PREFIX)
    Result<Long> createTopicRule(@PathVariable("model_id") String modelId,
                                 @RequestBody TopicRuleReqDTO topicRuleReqDTO);


    /**
     * 查询话题模型规则
     * @param modelId
     * @param ruleId
     * @return
     */
    @GetMapping(TOPIC_RULE_PREFIX + "/{rule_id}")
    Result<TopicRuleRespDTO> queryTopicRuleById(@PathVariable("model_id") String modelId,
                                                @PathVariable("rule_id") String ruleId);

    /**
     * 查询话题规则列表
     * @param modelId
     * @return
     */
    @GetMapping(TOPIC_RULE_PREFIX)
    Result<List<TopicRuleRespDTO>> queryTopicRuleList(@PathVariable("model_id") String modelId);


    /**
     * 修改话题规则
     * @param modelId
     * @param ruleId
     * @param topicRuleReqDTO
     * @return
     */
    @PostMapping(TOPIC_RULE_PREFIX + "/{rule_id}")
    Result<TopicRuleRespDTO> modifyTopicRule(@PathVariable("model_id") String modelId,
                                             @PathVariable("rule_id") String ruleId,
                                             @RequestBody TopicRuleReqDTO topicRuleReqDTO);

}
