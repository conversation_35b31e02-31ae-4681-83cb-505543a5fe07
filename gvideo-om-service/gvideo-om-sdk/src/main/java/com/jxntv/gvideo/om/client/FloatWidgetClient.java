package com.jxntv.gvideo.om.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetDTO;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetSearchDTO;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface FloatWidgetClient {

    @GetMapping("/api/float-widget/list/by/media")
    Result<List<FloatWidgetDTO>> listByMediaId(@RequestParam Long mediaId);

    @PostMapping("/api/float-widget")
    Result<Long> add(@RequestBody FloatWidgetDTO dto);

    @PutMapping("/api/float-widget/{id}")
    Result<Boolean> updateById(@PathVariable("id") Long id, @RequestBody FloatWidgetDTO dto);

    @GetMapping("/api/float-widget/{id}")
    Result<FloatWidgetDTO> getById(@PathVariable("id") Long id);

    @PostMapping("/api/float-widget/page")
    Result<PageDTO<FloatWidgetDTO>> page(@RequestBody FloatWidgetSearchDTO searchDTO);
}
