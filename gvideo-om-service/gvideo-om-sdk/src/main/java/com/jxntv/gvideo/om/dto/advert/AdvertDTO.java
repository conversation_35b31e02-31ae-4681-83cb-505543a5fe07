package com.jxntv.gvideo.om.dto.advert;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AdvertDTO {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 内容池投放
     */
    private List<AdvertPoolContentDTO> poolContents;
    /**
     * 投放名称
     */
    private String name;
    /**
     * 展示场景
     */
    private Integer showScene;
    /**
     * 展示位置
     */
    private String showLocationName;

    /**
     * 展示位置类型:0-菜单，1-社区
     */
    private Integer showLocationType;
    /**
     * 展示位置对应id（eg：比如社区类型，对应社区id）
     */
    private Long showLocationId;
    /**
     * 在第几条位置展示
     */
    private Integer showIndex;
    /**
     * 每隔几条展示
     */
    private Integer showInterval;
    /**
     * 下拉后是否隐藏
     */
    private Boolean hiddenAfterRefresh;

    /**
     * 投放开始时间
     */
    private LocalDateTime showStartDate;
    /**
     * 投放结束时间
     */
    private LocalDateTime showEndDate;
    /**
     * 每日浏览总数限制
     */
    private Integer dailyViewLimit;
    /**
     * 每日点击总数限制
     */
    private Integer dailyClickLimit;
    /**
     * 浏览总数限制
     */
    private Integer totalViewLimit;
    /**
     * 点击总数限制
     */
    private Integer totalClickLimit;

    /**
     * 投放类型： 0-全部用户 1-社区用户 2-渠道来源用户 3-手机号用户
     */
    private Integer putType;

    /**
     * 投放对象，putType=0时为空，putType=1时为社区ID，putType=2时为渠道来源id，putType=3时为手机号
     */
    private String putTarget;

    /**
     * 投放状态，0-未上架，1-已上架，2-已下架
     */
    private Integer status;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;
}
