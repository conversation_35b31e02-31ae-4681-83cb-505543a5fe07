package com.jxntv.gvideo.om.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.SearchShieldClient;
import com.jxntv.gvideo.om.dto.search.SearchShieldDTO;
import com.jxntv.gvideo.om.dto.search.SearchShieldQueryParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/28
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class SearchShieldClientFallback implements FallbackFactory<SearchShieldClient> {
    @Override
    public SearchShieldClient create(Throwable throwable) {
        return new SearchShieldClient() {
            @Override
            public Result<Long> create(SearchShieldDTO shieldDTO) {
                log.error("SearchShieldClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateById(Long id, SearchShieldDTO shieldDTO) {
                log.error("SearchShieldClient.updateById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteById(Long id) {
                log.error("SearchShieldClient.deleteById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<SearchShieldDTO> getById(Long id) {
                log.error("SearchShieldClient.getById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<SearchShieldDTO>> all() {
                log.error("SearchShieldClient.all() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<SearchShieldDTO>> page(SearchShieldQueryParam queryParam) {
                return null;
            }
        };
    }
}
