package com.jxntv.gvideo.om.dto;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 模型状态枚举
 * @date 2021/11/5 10:23
 */
public enum RuleModelStatus {

    ENABLE("ENABLE", 1),
    DISABLE("DISABLE", 0);

    private String name;
    private int index;

    private RuleModelStatus(String name, int index) {
        this.name = name;
        this.index = index;
    }

    public String getName() {
        return this.name;
    }

    public Integer getIndex() {
        return this.index;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public static RuleModelStatus getRuleModelStatus(int index) {
        for (RuleModelStatus status : RuleModelStatus.values()) {
            if (index == status.getIndex()) {
                return status;
            }
        }
        return null;
    }

}
