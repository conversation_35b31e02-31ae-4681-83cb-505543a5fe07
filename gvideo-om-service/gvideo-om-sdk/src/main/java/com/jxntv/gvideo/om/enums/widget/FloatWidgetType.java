package com.jxntv.gvideo.om.enums.widget;

/**
 * <AUTHOR>
 * @date 2022/6/1
 * Email: <EMAIL>
 */

public enum FloatWidgetType {
    H5(0, "H5页面"),
    JSP_PROTOCOL(1, "今视频协议"),
    APPLET(2, "小程序"),

    ;

    private Integer code;
    private String desc;

    FloatWidgetType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static FloatWidgetType parse(Integer code) {
        for (FloatWidgetType value : FloatWidgetType.values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public String toStr(Integer code) {
        for (FloatWidgetType value : values()) {
            if (value.code.equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
