package com.jxntv.gvideo.om.dto;

import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-04-27
 */
@Data
public class StatisticAppLogDTO {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * type
     */
    private String type;
    /**
     * pid
     */
    private String pid;
    /**
     * 类型
     */
    private String sessionId;
    /**
     * 数量
     */
    private Date timestamp;
    /**
     * 事件
     */
    private String ev;
    /**
     * 事件具体信息
     */
    private String ds;
    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 网络
     */
    private String networkType;
    /**
     * 载体
     */
    private String carrier;
    /**
     * 设备ID
     */
    private String cid;
    /**
     * app名称
     */
    private String appName;
    /**
     * app版本号
     */
    private String appVersion;
    /**
     * 频道
     */
    private String channel;
    /**
     * 生产厂家
     */
    private String manufacturer;
    /**
     * model
     */
    private String model;
    /**
     * 操作系统
     */
    private String os;
    /**
     * 操作系统版本
     */
    private String osVersion;

}
