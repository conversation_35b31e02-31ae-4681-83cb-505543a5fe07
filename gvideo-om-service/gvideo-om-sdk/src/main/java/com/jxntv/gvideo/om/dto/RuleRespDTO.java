package com.jxntv.gvideo.om.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/10 17:11
 */
@Data
public class RuleRespDTO implements Serializable {

    /**
     * 规则Id
     */
    private Long ruleId;

    /**
     * 推荐模型Id
     */
    private Long modelId;

    /**
     * 规则选取内容数量
     */
    private Integer contentNum;
    /**
     * 规则创建人
     */
    private String createName;

    /**
     * 规则创建时间
     */
    private LocalDateTime createTime;

    /**
     * 规则更新时间
     */
    private LocalDateTime updateTime;
}
