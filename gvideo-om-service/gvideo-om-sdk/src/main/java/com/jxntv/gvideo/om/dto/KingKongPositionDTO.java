package com.jxntv.gvideo.om.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class KingKongPositionDTO {

    /**
     * 主键id
     */
    private Long id;
    /**
     * 金刚位名称
     */
    private String name;
    /**
     * 0-原生，1-H5链接
     */
    private Integer type;

    private Long contentId;
    /**
     * 0-文章，1-圈子，2-话题，3-动态，4-直播
     */
    private Integer contentType;
    /**
     * 原生内容名称
     */
    private String contentName;
    /**
     * type=0,原生链接，比如：jinship:xxx
     * type=1,H5链接，比如：https:www.baiduc.om
     */
    private String linkUrl;
    /**
     * 补充URL，小程序兼容H5页面
     */
    private String extraUrl;
    /**
     * 图片id
     */
    private String iconId;

    /**
     * 气泡文案
     */
    private String bubbleWords;
    /**
     * 创建人
     */
    private Long createUserId;
    /**
     * 更新人
     */
    private Long updateUserId;
    /**
     * 创建时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

    /**
     * 首页强引导ID
     */
    private Long guideId;

    /**
     * item ID
     */
    private Long itemId;

}
