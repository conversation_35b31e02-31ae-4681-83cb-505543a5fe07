package com.jxntv.gvideo.om.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description 热度类型
 * @date 2021/07/06 16:05
 */
@Getter
public enum HotTypeEnum {
    //类型：0-热度、1-举报
    HOT(0, "热度"),
    REPORT(1, "举报")
    ;

    private Integer code;
    private String name;

    HotTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static HotTypeEnum getNameByCode(int code) {
        return Arrays.stream(HotTypeEnum.values())
                .filter(item -> code == item.getCode()).findFirst().orElse(null);
    }
}
