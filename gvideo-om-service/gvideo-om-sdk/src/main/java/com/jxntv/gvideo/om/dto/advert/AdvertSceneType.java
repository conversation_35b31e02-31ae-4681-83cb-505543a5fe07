package com.jxntv.gvideo.om.dto.advert;

/**
 * 投放场景
 */
public enum AdvertSceneType {
    feed(0, "信息流场景"),
    operating(1, "运营位场景"),

    ;
    /**
     * 编码
     */
    private final Integer code;
    /**
     * 描述
     */
    private final String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AdvertSceneType(Integer code, String name) {
        this.code = code;
        this.desc = name;
    }

    public static AdvertSceneType parse(Integer code) {
        for (AdvertSceneType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

}
