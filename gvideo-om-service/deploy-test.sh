#!/bin/bash
# 参数配置
project_name="gvideo-om-service"
module_name="gvideo-om-service/gvideo-om-api"
jar_name="om.jar"
jar_path="gvideo-om-service/gvideo-om-api/target/${jar_name}"
log_path="/home/<USER>/logs/${project_name}.out"
env="test"
# skywalking参数配置
skywalking_agent_path="/home/<USER>"
skywalking_apm_backend_service="*************:11800"
skywalking_agent=" -javaagent:${skywalking_agent_path}/skywalking-agent.jar  -Dskywalking_config=${skywalking_agent_path}/config/agent.config  -Dskywalking.collector.backend_service=${skywalking_apm_backend_service}  -Dskywalking.agent.service_name=${env}::${project_name} "

# 更新代码
cd .. && git pull
# 打包服务
mvn clean package -T 1C -Dmaven.test.skip=true -Dmaven.compile.fork=true -am -pl ${module_name} -P ${env}
# 关闭服务
pid=$(ps ax | grep -i ${jar_name} | grep java | grep -v grep | awk '{print $1}')
if [ -z "$pid" ]; then
  echo "No ${project_name} running."
else
  echo "The ${project_name}(${pid}) is running..."
  kill -9 ${pid}
  echo "Send shutdown request to ${project_name}(${pid}) OK"
fi
# 启动服务
echo "" > ${log_path}
nohup java -jar -Xmx800m -Xms512m ${skywalking_agent} ${jar_path} >> ${log_path} 2>&1 &
tail -f ${log_path}
