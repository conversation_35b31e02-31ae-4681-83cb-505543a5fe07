package com.jxntv.gvideo.om.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.om.client.AppHomePageGuideClient;
import com.jxntv.gvideo.om.client.KingKongPositionChannelStrategyClient;
import com.jxntv.gvideo.om.converter.KingKongPositionChannelStrategyConverter;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategyDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategySearchDTO;
import com.jxntv.gvideo.om.entity.KingKongPositionChannelStrategy;
import com.jxntv.gvideo.om.entity.KingKongPositionChannelStrategyItem;
import com.jxntv.gvideo.om.service.KingKongPositionChannelStrategyItemService;
import com.jxntv.gvideo.om.service.KingKongPositionChannelStrategyService;
import com.jxntv.gvideo.om.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class KingKongPositionChannelStrategyController implements KingKongPositionChannelStrategyClient {

    @Resource
    private KingKongPositionChannelStrategyService kingKongPositionChannelStrategyService;
    @Resource
    private KingKongPositionChannelStrategyItemService kingKongPositionChannelStrategyItemService;
    @Resource
    private KingKongPositionChannelStrategyConverter kingKongPositionChannelStrategyConverter;

    @Resource
    private AppHomePageGuideClient appHomePageGuideClient;

    @Override
    public Result<Long> add(KingKongPositionChannelStrategyDTO strategyDTO) {
        KingKongPositionChannelStrategy entity = kingKongPositionChannelStrategyService.add(strategyDTO);
        return Result.ok(entity.getId());
    }

    @Override
    public Result<KingKongPositionChannelStrategyDTO> getById(Long id) {
        KingKongPositionChannelStrategy entity = kingKongPositionChannelStrategyService.getById(id);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);

        KingKongPositionChannelStrategyDTO dto = kingKongPositionChannelStrategyConverter.convert(entity);
        return Result.ok(dto);
    }

    @Override
    @Transactional
    public Result<Boolean> deleteById(Long id) {
        //  删除策略
        boolean remove = kingKongPositionChannelStrategyService.removeById(id);

        // 删除app首页强引导
        List<KingKongPositionChannelStrategyItem> kingKongPositionChannelStrategyItems = kingKongPositionChannelStrategyItemService.listByStrategyId(id);
        kingKongPositionChannelStrategyItems.forEach(kingKongPositionChannelStrategyItem -> {
            Long positionId = kingKongPositionChannelStrategyItem.getPositionId();
            Long strategyId = kingKongPositionChannelStrategyItem.getStrategyId();
            appHomePageGuideClient.deleteByKingkongAndStrategyId(positionId, strategyId);
        });

        //  删除策略详细
        LambdaQueryWrapper<KingKongPositionChannelStrategyItem> itemQuery = Wrappers.lambdaQuery();
        itemQuery.eq(KingKongPositionChannelStrategyItem::getStrategyId, id);
        kingKongPositionChannelStrategyItemService.remove(itemQuery);

        return Result.ok(remove);
    }

    @Override
    public Result<Boolean> updateById(Long id, KingKongPositionChannelStrategyDTO strategyDTO) {
        strategyDTO.setId(id);
        return Result.ok(kingKongPositionChannelStrategyService.updateById(strategyDTO));
    }

    @Override
    public Result<PageDTO<KingKongPositionChannelStrategyDTO>> page(KingKongPositionChannelStrategySearchDTO searchDTO) {
        Page<KingKongPositionChannelStrategy> page = kingKongPositionChannelStrategyService.page(searchDTO);
        return Result.ok(PageUtils.pageOf(page, kingKongPositionChannelStrategyConverter::convert));
    }

    @Override
    public Result<KingKongPositionChannelStrategyDTO> getByChannelId(Long channelId, Boolean isJuniorMode) {
        Integer juniorMode = isJuniorMode ? 1 : 0;
        KingKongPositionChannelStrategy entity = kingKongPositionChannelStrategyService.getByChannelId(channelId, juniorMode);
        AssertUtil.notNull(entity, CodeMessage.NOT_FOUND);
        return Result.ok(kingKongPositionChannelStrategyConverter.convert(entity));
    }
}
