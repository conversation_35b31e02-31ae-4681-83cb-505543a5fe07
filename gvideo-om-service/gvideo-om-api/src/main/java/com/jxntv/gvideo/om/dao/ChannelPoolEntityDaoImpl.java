package com.jxntv.gvideo.om.dao;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.om.entity.ChannelPoolEntity;
import com.jxntv.gvideo.om.mapper.ChannelPoolEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/26 19:46
 */
@Slf4j
@Service
public class ChannelPoolEntityDaoImpl extends ServiceImpl<ChannelPoolEntityMapper, ChannelPoolEntity> implements ChannelPoolEntityDao {

    @Override
    public ChannelPoolEntity getOneById(Long id) {
        if (null == id) {
            return null;
        }
        return this.baseMapper.getOneById(id);
    }

    @Override
    public ChannelPoolEntity getOneByName(String channelPoolName) {
        if (StringUtils.isEmpty(channelPoolName)) {
            return null;
        }
        return this.baseMapper.getOneByName(channelPoolName);
    }

    @Override
    public ChannelPoolEntity updateStatusById(Long id, int status) {
        if (null == id) {
            return null;
        }

        this.baseMapper.updateStatusById(id, status, LocalDateTime.now());
        return this.baseMapper.getOneById(id);
    }
}
