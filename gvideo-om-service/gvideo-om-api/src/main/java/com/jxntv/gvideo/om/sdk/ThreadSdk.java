package com.jxntv.gvideo.om.sdk;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @description 线程池
 * @date 2021/07/06 16:53
 */
public class ThreadSdk {
    private static final ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("audit-auto-%d").build();
    private static final ExecutorService pool = new ThreadPoolExecutor(8, 200,
            0L, TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(2048), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());

    public static ExecutorService getPool() {
        return pool;
    }
}
