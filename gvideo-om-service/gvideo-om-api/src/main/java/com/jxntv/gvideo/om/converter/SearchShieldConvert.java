package com.jxntv.gvideo.om.converter;

import com.jxntv.gvideo.om.dto.search.SearchShieldDTO;
import com.jxntv.gvideo.om.entity.SearchShield;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/5/28
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class SearchShieldConvert {

    public SearchShield convert(SearchShieldDTO dto) {
        SearchShield entity = new SearchShield();
        entity.setId(dto.getId());
        entity.setRegex(dto.getRegex());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }

    public SearchShieldDTO convert(SearchShield entity) {
        SearchShieldDTO dto = new SearchShieldDTO();
        dto.setId(entity.getId());
        dto.setRegex(entity.getRegex());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

}
