package com.jxntv.gvideo.om.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.om.entity.StatisticProduce;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * Created on 2020-04-26
 */
@Mapper
@Repository
public interface StatisticProduceMapper extends BaseMapper<StatisticProduce> {

    List<Map<String, Object>> getDayCount(Integer type, Integer pgcId, Integer mediaType, Date start, Date end);

    List<Map<String, Object>> getMonthCount(Integer type, Integer pgcId, Integer mediaType, Date start, Date end);

    List<Map<String, Object>> getWeekCount(Integer type, Integer pgcId, Integer mediaType, Date start, Date end);

    List<Map<String, Object>> getMediaCount(Integer type, Integer pgcId, Integer mediaType, Date start, Date end);

}
