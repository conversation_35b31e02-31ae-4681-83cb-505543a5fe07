package com.jxntv.gvideo.om.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 智能审核关联表
 * @date 2021/07/06 16:28
 */
@Data
public class AuditAutoType implements Serializable {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 审核类型：0-文本、1-图片、2-音频、3-视频
     */
    private Integer auditType;
    /**
     * 内容类型：0-资源、1-评论、2-回复
     */
    private Integer contentType;
    /**
     * 内容ID
     */
    private Long contentId;
    /**
     * 任务对应ID
     */
    private String taskId;
    /**
     * 阿里云图片/视频ID
     */
    private String ossId;
    /**
     * 当前审核视频/图片地址/音频地址
     */
    private String url;
    /**
     * 状态：0-任务待执行、1-任务执行中、2-已通过、3-含敏感、4-不通过
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 扩展字段
     */
    @TableField(exist = false)
    private AuditAutoTypeExt auditAutoTypeExt;
}
