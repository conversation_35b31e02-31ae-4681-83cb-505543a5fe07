package com.jxntv.gvideo.om.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BigDataContentRule implements Serializable {

    @SerializedName("group_list")
    private List<String> groupList;

    @SerializedName("tag_list")
    private List<String> labelList;

    @SerializedName("level_list")
    private List<String> levelList;

    @SerializedName("user_list")
    private List<String> userType;

    @SerializedName("answer_list")
    private List<Integer> workType;

    @SerializedName("nums")
    private Integer num;


}
