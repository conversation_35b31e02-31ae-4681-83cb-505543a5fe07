package com.jxntv.gvideo.om.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.om.converter.KingKongPositionConverter;
import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionSearchDTO;
import com.jxntv.gvideo.om.entity.KingKongPosition;
import com.jxntv.gvideo.om.mapper.KingKongPositionMapper;
import com.jxntv.gvideo.om.service.KingKongPositionService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
@Service
public class KingKongPositionServiceImpl extends ServiceImpl<KingKongPositionMapper, KingKongPosition> implements KingKongPositionService {

    @Resource
    private KingKongPositionConverter kingKongPositionConverter;

    @Override
    public KingKongPosition add(KingKongPositionDTO dto) {
        LambdaQueryWrapper<KingKongPosition> nameQuery = Wrappers.lambdaQuery();
        nameQuery.eq(KingKongPosition::getName, dto.getName());
        AssertUtil.assertOrThrow(Objects.isNull(this.getOne(nameQuery)), CodeMessage.BAD_REQUEST.getCode(), "名称重复");

        KingKongPosition entity = kingKongPositionConverter.convert(dto);
        this.save(entity);
        return entity;
    }

    @Override
    public Boolean updateById(KingKongPositionDTO dto) {
        LambdaQueryWrapper<KingKongPosition> nameQuery = Wrappers.lambdaQuery();
        nameQuery.eq(KingKongPosition::getName, dto.getName());
        nameQuery.ne(KingKongPosition::getId, dto.getId());
        AssertUtil.assertOrThrow(Objects.isNull(this.getOne(nameQuery)), CodeMessage.BAD_REQUEST.getCode(), "名称重复");

        KingKongPosition entity = kingKongPositionConverter.convert(dto);
        return this.updateById(entity);
    }

    @Override
    public Page<KingKongPosition> page(KingKongPositionSearchDTO searchDTO) {
        Page<KingKongPosition> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());
        LambdaQueryWrapper<KingKongPosition> lambdaQuery = Wrappers.lambdaQuery();
        if (Objects.nonNull(searchDTO.getId())) {
            lambdaQuery.eq(KingKongPosition::getId, searchDTO.getId());
        }
        if (StringUtils.hasText(searchDTO.getNameLike())) {
            lambdaQuery.like(KingKongPosition::getName, searchDTO.getNameLike());
        }

        return this.page(pageRequest, lambdaQuery);
    }
}
