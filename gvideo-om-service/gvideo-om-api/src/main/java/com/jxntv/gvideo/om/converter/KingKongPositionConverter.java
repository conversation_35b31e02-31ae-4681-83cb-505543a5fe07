package com.jxntv.gvideo.om.converter;

import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import com.jxntv.gvideo.om.entity.KingKongPosition;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class KingKongPositionConverter {

    public KingKongPosition convert(KingKongPositionDTO dto) {
        KingKongPosition entity = new KingKongPosition();
        entity.setId(dto.getId());
        entity.setType(dto.getType());
        entity.setLinkUrl(dto.getLinkUrl());
        entity.setExtraUrl(dto.getExtraUrl());
        entity.setIconId(dto.getIconId());
        entity.setName(dto.getName());
        entity.setContentId(dto.getContentId());
        entity.setContentType(dto.getContentType());
        entity.setContentName(dto.getContentName());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;

    }


    public KingKongPositionDTO convert(KingKongPosition entity) {
        KingKongPositionDTO dto = new KingKongPositionDTO();
        dto.setId(entity.getId());
        dto.setType(entity.getType());
        dto.setExtraUrl(entity.getExtraUrl());
        dto.setLinkUrl(entity.getLinkUrl());
        dto.setIconId(entity.getIconId());
        dto.setName(entity.getName());
        dto.setContentId(entity.getContentId());
        dto.setContentType(entity.getContentType());
        dto.setContentName(entity.getContentName());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }


}
