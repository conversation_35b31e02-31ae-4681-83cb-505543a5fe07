package com.jxntv.gvideo.om.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.om.entity.ChannelPoolEntity;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/26 19:47
 */
public interface ChannelPoolEntityMapper extends BaseMapper<ChannelPoolEntity> {

    ChannelPoolEntity getOneById(Long id);

    ChannelPoolEntity getOneByName(String channelPoolName);

    void updateStatusById(Long id, int status, LocalDateTime updateTime);
}
