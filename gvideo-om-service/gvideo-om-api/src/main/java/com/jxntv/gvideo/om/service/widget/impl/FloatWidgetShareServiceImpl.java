package com.jxntv.gvideo.om.service.widget.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.om.converter.FloatWidgetConvert;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetShareDTO;
import com.jxntv.gvideo.om.entity.widget.FloatWidgetShare;
import com.jxntv.gvideo.om.mapper.widget.FloatWidgetShareMapper;
import com.jxntv.gvideo.om.service.widget.FloatWidgetShareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/22
 * Email: <EMAIL>
 */
@Slf4j
@Service
public class FloatWidgetShareServiceImpl extends ServiceImpl<FloatWidgetShareMapper, FloatWidgetShare> implements FloatWidgetShareService {

    @Resource
    private FloatWidgetConvert floatWidgetConvert;


    @Override
    public FloatWidgetShare getByWidgetId(Long widgetId) {
        LambdaQueryWrapper<FloatWidgetShare> shareQuery = Wrappers.lambdaQuery();
        shareQuery.eq(FloatWidgetShare::getWidgetId, widgetId);
        return this.getOne(shareQuery);
    }

    @Override
    public boolean saveOrUpdateByWidgetId(Long widgetId, FloatWidgetShareDTO dto) {
        FloatWidgetShare entity = floatWidgetConvert.convert(dto);
        entity.setWidgetId(widgetId);
        FloatWidgetShare exists = getByWidgetId(widgetId);
        if (Objects.nonNull(exists)) {
            entity.setId(exists.getId());
        }
        return this.saveOrUpdate(entity);
    }
}
