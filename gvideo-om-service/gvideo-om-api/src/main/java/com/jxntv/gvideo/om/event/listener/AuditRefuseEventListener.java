package com.jxntv.gvideo.om.event.listener;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.interact.client.dto.MessageDTO;
import com.jxntv.gvideo.interact.client.dto.enums.*;
import com.jxntv.gvideo.media.client.GdzjDataReportClient;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import com.jxntv.gvideo.media.client.dto.GdzjAuditReportDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.om.client.MessageService;
import com.jxntv.gvideo.om.converter.AuditConverter;
import com.jxntv.gvideo.om.dto.AuditingMediaDTO;
import com.jxntv.gvideo.om.enums.AuditRefuseCodeEnum;
import com.jxntv.gvideo.om.event.AuditRefuseEvent;
import com.jxntv.gvideo.om.service.AuditingMediaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: niedamin
 * @Date: 2023/05/30 11:21
 */
@Component
@Slf4j
@RefreshScope
public class AuditRefuseEventListener {

    @Resource
    private AuditingMediaService auditingMediaService;

    @Resource
    private MediaResourceClient mediaResourceClient;

    @Resource
    private GdzjDataReportClient gdzjDataReportClient;

    @Resource
    private MessageService messageService;

    @EventListener
    @Async
    public void onEvent(AuditRefuseEvent event) {
        List<Long> auditMediaIds = event.getAuditMediaIds();
        if (CollectionUtils.isNotEmpty(auditMediaIds)) {
            List<AuditingMediaDTO> auditingMediaDTOS = auditingMediaService.listByIds(auditMediaIds).stream().map(AuditConverter::convert).collect(Collectors.toList());
            auditingMediaDTOS.forEach(auditingMediaDTO -> {
                MediaResourceDTO mediaResourceDTO = mediaResourceClient.get(auditingMediaDTO.getMediaId()).orElse(null);
                if (Objects.nonNull(mediaResourceDTO)) {
                    MessageDTO dto = new MessageDTO();
                    dto.setTitle("作品审核通知");
                    dto.setMsgType(MessageMsgTypeEnum.SYSTEM_MSG.getId());
                    if (AuditRefuseCodeEnum.OTHERS.getId().equals(auditingMediaDTO.getRefuseCode())) {
                        dto.setDetail(getDetailPrefix(mediaResourceDTO.getShowName()) + auditingMediaDTO.getRefuseNote());
                    } else {
                        dto.setDetail(getDetailPrefix(mediaResourceDTO.getShowName()) + AuditRefuseCodeEnum.getNameById(auditingMediaDTO.getRefuseCode()));
                    }
                    dto.setContentType(MessageContentTypeEnum.GROUP_USER_REPORT.getId());
                    dto.setContentId(auditingMediaDTO.getMediaId());
                    dto.setStatus(MessageStatusEnum.STATUS_SEND.getId());
                    dto.setType(MessageTypeEnum.TEXT.getId());
                    dto.setFromType(0);
                    dto.setFromId(0L);
                    dto.setFromStatus(true);
                    dto.setToType(MessageToTypeEnum.MESSAGE_TO_HIT.getId());
                    dto.setToIds(String.valueOf(mediaResourceDTO.getReleaseId()));
                    dto.setAdminUserId(0);
                    dto.setAdminUserName("");
                    dto.setCreateDate(new Date());
                    dto.setExecuteType(1);
                    dto.setExecuteDate(new Date());
                    Result<Long> result = messageService.addMessage(dto);
                    if (result.getCode() != CodeMessage.OK.getCode()) {
                        log.error("作品审核不通过系统消息：" + JsonUtils.toJson(result));
                    }
                }
            });
        }

    }



    @EventListener
    @Async
    public void onRefuse(AuditRefuseEvent event) {
        List<Long> auditMediaIds = event.getAuditMediaIds();
        if (CollectionUtils.isNotEmpty(auditMediaIds)) {
            List<AuditingMediaDTO> auditingMediaDTOS = auditingMediaService.listByIds(auditMediaIds).stream().map(AuditConverter::convert).collect(Collectors.toList());
            auditingMediaDTOS.forEach(auditingMediaDTO -> {

                GdzjAuditReportDTO gdzjAuditReportDTO = new GdzjAuditReportDTO();
                gdzjAuditReportDTO.setMediaId(auditingMediaDTO.getMediaId());
                gdzjAuditReportDTO.setAuditDateTime(DateFormatUtils.format(auditingMediaDTO.getAuditDate(),"yyyy-MM-dd HH:mm:ss"));
                gdzjAuditReportDTO.setAuditType(2);
                gdzjAuditReportDTO.setAuditor(auditingMediaDTO.getAdminUserName());
                gdzjAuditReportDTO.setAuditResult(2);
                gdzjAuditReportDTO.setAuditDesc(auditingMediaDTO.getRefuseNote());

                gdzjDataReportClient.auditReport(gdzjAuditReportDTO);

            });
        }

    }

    private String getDetailPrefix(String title) {
        if (StringUtils.isNotBlank(title)) {
            return "<" + title + "> 审核不通过，原因是：";
        } else {
            return "您的动态审核不通过，原因是：";
        }
    }

}
