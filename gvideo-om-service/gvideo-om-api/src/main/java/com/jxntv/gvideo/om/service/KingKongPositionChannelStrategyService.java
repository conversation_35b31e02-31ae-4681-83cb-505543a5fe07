package com.jxntv.gvideo.om.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategyDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategySearchDTO;
import com.jxntv.gvideo.om.entity.KingKongPositionChannelStrategy;

/**
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
public interface KingKongPositionChannelStrategyService extends IService<KingKongPositionChannelStrategy> {

    KingKongPositionChannelStrategy add(KingKongPositionChannelStrategyDTO strategyDTO);

    boolean updateById(KingKongPositionChannelStrategyDTO strategyDTO);

    Page<KingKongPositionChannelStrategy> page(KingKongPositionChannelStrategySearchDTO searchDTO);


    KingKongPositionChannelStrategy getByChannelId(Long deviceId, Integer isJuniorMode);
}
