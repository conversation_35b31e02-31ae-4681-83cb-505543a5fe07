package com.jxntv.gvideo.om.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.om.entity.RuleEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/8 14:34
 */
public interface RuleEntityMapper extends BaseMapper<RuleEntity> {

    RuleEntity queryRuleEntityByModelIdAndRuleId(Long modelId, Long ruleId);

    List<RuleEntity> queryRuleEntityListByModelId(Long modelId);
}
