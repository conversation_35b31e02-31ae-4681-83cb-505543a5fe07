package com.jxntv.gvideo.om.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jxntv.gvideo.om.client.StatisticProduceClient;
import com.jxntv.gvideo.om.converter.Converter;
import com.jxntv.gvideo.om.entity.StatisticProduce;
import com.jxntv.gvideo.om.service.StatisticProduceService;
import com.jxntv.gvideo.om.utils.DateUtils;
import com.jxntv.gvideo.om.utils.XAliasUtils;
import com.jxntv.gvideo.om.dto.StatisticProduceDTO;
import com.jxntv.gvideo.om.vo.SearchStatisticProduceVO;
import com.jxntv.gvideo.om.vo.StatisticVO;
import com.jxntv.gvideo.om.vo.StatisticVO.*;
import com.jxntv.gvideo.common.model.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created on 2020-04-24
 */
@RestController
public class StatisticProduceController implements StatisticProduceClient {

    @Autowired
    private StatisticProduceService statisticProduceService;

    @Override
    public Result addStatisticProduce(List<StatisticProduceDTO> dtoList) {
        List<StatisticProduce> entities = dtoList.stream().map(Converter::convert).collect(Collectors.toList());
        if (!statisticProduceService.saveBatch(entities)) {
            return Result.fail("插入失败");
        }
        return Result.ok();
    }

    /**
     * 1.累计生产量
     * 2.累计视频生产量
     * 3.累计音频生产量
     * 4.新增视频生产量
     * 5.新增音频生产量
     */
    @Override
    public Result<StatisticVO> getStatisticProduceAbilityPanel(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(5);
        List<Chart> charts = new ArrayList<>(0);
        List<Table> tables = new ArrayList<>(0);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        Integer pgcId = searchStatisticProduceVO.getPgcId();

        // 5个统计数据
        // 1.累计生产量（与日期范围无关）
        LambdaQueryWrapper<StatisticProduce> queryWrapper1 = Wrappers.lambdaQuery();
        queryWrapper1.eq(StatisticProduce::getType, 6);
        if (pgcId != null && pgcId != 0) {
            queryWrapper1.eq(StatisticProduce::getPgcId, pgcId);
        }
        List<StatisticProduce> statisticProduces = statisticProduceService.list(queryWrapper1);
        Optional<Integer> optional1 = statisticProduces.stream().map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(1).title("累计生产量").unit("条").count(optional1.orElse(0)).chain("无数据").equal("无数据").build());

        // 2.累计视频生产量（与日期范围无关）
        Optional<Integer> optional2 = statisticProduces.stream().filter(s -> s.getMediaType().equals(1)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(2).title("累计视频生产量").unit("条").count(optional2.orElse(0)).chain("无数据").equal("无数据").build());

        // 3.累计音频生产量（与日期范围无关）
        Optional<Integer> optional3 = statisticProduces.stream().filter(s -> s.getMediaType().equals(2)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(3).title("累计音频生产量").unit("条").count(optional3.orElse(0)).chain("无数据").equal("无数据").build());

        // 4.累计图文生产量
        Optional<Integer> optional4 = statisticProduces.stream().filter(s -> s.getMediaType().equals(6)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(31).title("累计图文生产量").unit("条").count(optional4.orElse(0)).chain("无数据").equal("无数据").build());

        // 5.累计语音生产量
        Optional<Integer> optional5 = statisticProduces.stream().filter(s -> s.getMediaType().equals(7)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(32).title("累计语音生产量").unit("条").count(optional5.orElse(0)).chain("无数据").equal("无数据").build());

        // 5.累计直播生产量
        Optional<Integer> optional11 = statisticProduces.stream().filter(s -> s.getMediaType().equals(5)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(33).title("累计直播生产量").unit("条").count(optional11.orElse(0)).chain("无数据").equal("无数据").build());

        LambdaQueryWrapper<StatisticProduce> queryWrapper2 = Wrappers.lambdaQuery();
        queryWrapper2.eq(StatisticProduce::getType, 6);
        if (pgcId != null && pgcId != 0) {
            queryWrapper2.eq(StatisticProduce::getPgcId, pgcId);
        }
        queryWrapper2.ge(StatisticProduce::getStatisticDate, range.get(0));
        queryWrapper2.le(StatisticProduce::getStatisticDate, range.get(1));
        List<StatisticProduce> statisticProduceList = statisticProduceService.list(queryWrapper2);
        // 6.新增视频生产量（统计某日期范围中的加和）
        Optional<Integer> optional6 = statisticProduceList.stream().filter(s -> s.getMediaType().equals(1)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(4).title("新增视频生产量").unit("条").count(optional6.orElse(0)).chain("无数据").equal("无数据").build());

        // 7.新增音频生产量（统计某日期范围中的加和）
        Optional<Integer> optional7 = statisticProduceList.stream().filter(s -> s.getMediaType().equals(2)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(5).title("新增音频生产量").unit("条").count(optional7.orElse(0)).chain("无数据").equal("无数据").build());

        // 8.新增图文生产量
        Optional<Integer> optional8 = statisticProduceList.stream().filter(s -> s.getMediaType().equals(6)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(34).title("新增图文生产量").unit("条").count(optional8.orElse(0)).chain("无数据").equal("无数据").build());

        // 9.新增语音生产量
        Optional<Integer> optional9 = statisticProduces.stream().filter(s -> s.getMediaType().equals(7)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(35).title("新增语音生产量").unit("条").count(optional9.orElse(0)).chain("无数据").equal("无数据").build());

        // 10.新增直播生产量
        Optional<Integer> optional12 = statisticProduces.stream().filter(s -> s.getMediaType().equals(5)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(36).title("新增直播生产量").unit("条").count(optional12.orElse(0)).chain("无数据").equal("无数据").build());

        return Result.ok(vo);
    }

    /**
     * 6.新增总生产量趋势
     * 7.新增音视频生产量趋势
     */
    @Override
    public Result<StatisticVO> getStatisticProduceAbilityChart(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(0);
        List<Chart> charts = new ArrayList<>(10);
        List<Table> tables = new ArrayList<>(0);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        // style是展示的月日周小时4种样式
        Integer style = searchStatisticProduceVO.getType();
        Integer pgcId = searchStatisticProduceVO.getPgcId();
        List<String> compare = searchStatisticProduceVO.getCompare();

        // 下面是2个折线图，先生成折线图的X轴。每个图都要分4种情况日/周/月/小时聚合
        Map<String, Integer> xAliasMap = XAliasUtils.xAliasGen(range, style);
        if (style == 4) {
            // 按小时
            charts.add(Chart.builder().key(6).title("新增总生产量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 6, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(7).title("新增内容生产量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 6, compare, pgcId, false)).build());
        } else if (style == 1 || style == 2 || style == 3) {
            // 按日或月一样的处理办法
            charts.add(Chart.builder().key(6).title("新增总生产量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 6, style, pgcId, true)).build());
            charts.add(Chart.builder().key(7).title("新增内容生产量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 6, style, pgcId, false)).build());
        }
        return Result.ok(vo);
    }

    private List<Integer> fillXYDataByHour(Map<String, Integer> map, List<StatisticProduce> list) {
        // 这里存在需要合并小时的情况，因为总量需要音视频相加
        for (StatisticProduce produce : list) {
            Integer current = map.get(String.valueOf(produce.getClock())) != null ? map.get(String.valueOf(produce.getClock())) : 0;
            map.put(String.valueOf(produce.getClock()), current + produce.getSum());
        }
        List<Integer> res = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            res.add(entry.getValue());
        }
        return res;
    }

    /**
     * 构造具体类型内容折线图
     *
     * @param range
     * @param type
     * @param compare
     * @param pgcId
     * @param isAll
     * @return
     */
    private List<Series> buildSeriesByHour(List<String> range, Integer type, List<String> compare, Integer pgcId, Boolean isAll) {
        List<Series> seriesList = new ArrayList<>();
        LambdaQueryWrapper<StatisticProduce> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StatisticProduce::getType, type);
        if (pgcId != null && pgcId != 0) {
            queryWrapper.eq(StatisticProduce::getPgcId, pgcId);
        }
        queryWrapper.in(StatisticProduce::getStatisticDate, compare);
        List<StatisticProduce> list = statisticProduceService.list(queryWrapper);
        if (isAll) {
            // 多个日期的，每个日期一条总和折线
            // 从list中过滤每个对比日期的数据，拼合24小时，存入series对象
            for (String dateStr : compare) {
                List<StatisticProduce> subList = list.stream().filter(s -> s.getStatisticDate().equals(DateUtils.parse(dateStr))).collect(Collectors.toList());
                Map<String, Integer> map = XAliasUtils.xAliasGen(range, 4);
                List<Integer> yData = fillXYDataByHour(map, subList);
                Series series = new Series();
                series.setType("line");
                series.setData(yData);
                series.setName(dateStr);
                seriesList.add(series);
            }
        } else {
            // 多个日期的，且是音视频两条折线
            for (String dateStr : compare) {
                seriesList.add(genSeriesByHour(list, range, dateStr, 1));
                seriesList.add(genSeriesByHour(list, range, dateStr, 2));
                seriesList.add(genSeriesByHour(list, range, dateStr, 5));
                seriesList.add(genSeriesByHour(list, range, dateStr, 6));
                seriesList.add(genSeriesByHour(list, range, dateStr, 7));
            }
        }
        return seriesList;
    }

    private Series genSeriesByHour(List<StatisticProduce> list, List<String> range, String dateStr, Integer mediaType) {
        List<StatisticProduce> subList = list.stream().filter(s -> s.getMediaType().equals(mediaType)).filter(s -> s.getStatisticDate().equals(DateUtils.parse(dateStr))).collect(Collectors.toList());
        Map<String, Integer> map = XAliasUtils.xAliasGen(range, 4);
        List<Integer> yData = fillXYDataByHour(map, subList);
        Series series = new Series();
        series.setType("line");
        series.setData(yData);
        series.setName(dateStr + " " + getNameByMediaType(mediaType));
        return series;
    }

    private String getNameByMediaType(Integer mediaType) {
        if (mediaType == 1) {
            return "视频";
        } else if (mediaType == 2) {
            return "音频";
        } else if (mediaType == 6) {
            return "图文";
        } else if (mediaType == 7) {
            return "语音";
        } else if (mediaType == 5) {
            return "直播";
        }
        return "";
    }

    private List<Integer> fillXYData(Map<String, Integer> map, List<Map<String, Object>> res) {
        for (Map<String, Object> e : res) {
            Object count = e.get("sum");
            map.put(String.valueOf(e.get("date")), Integer.parseInt(String.valueOf(count)));
        }
        List<Integer> list = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            list.add(entry.getValue());
        }
        return list;
    }

    private List<Series> buildSeries(List<String> range, Integer type, Integer style, Integer pgcId, Boolean isAll) {
        Date start = DateUtils.parse(range.get(0));
        Date end = DateUtils.parse(range.get(1));
        // 按日期聚合的
        List<Series> seriesList = new ArrayList<>();
        List<Map<String, Object>> list = new ArrayList<>();

        if (isAll) {
            if (style.equals(1)) {
                list = statisticProduceService.getDayCount(type, pgcId, 0, start, end);
            } else if (style.equals(2)) {
                list = statisticProduceService.getWeekCount(type, pgcId, 0, start, end);
            } else if (style.equals(3)) {
                list = statisticProduceService.getMonthCount(type, pgcId, 0, start, end);
            }
            // 按天聚合的，只会有一个折线图
            Map<String, Integer> map = XAliasUtils.xAliasGen(range, style);
            List<Integer> yData = fillXYData(map, list);
            Series series = new Series();
            series.setType("line");
            series.setData(yData);
            series.setName("数量");
            seriesList.add(series);
            return seriesList;
        } else {
            // 按天聚合的，只会有视频和音频一个折线图
            // 统计视频
            list = getByStyle(style, type, pgcId, start, end, 1);
            seriesList.add(genSeries(list, range, style, 1));
            // 统计音频
            list = getByStyle(style, type, pgcId, start, end, 2);
            seriesList.add(genSeries(list, range, style, 2));
            // 统计直播
            list = getByStyle(style, type, pgcId, start, end, 5);
            seriesList.add(genSeries(list, range, style, 5));
            // 统计图文
            list = getByStyle(style, type, pgcId, start, end, 6);
            seriesList.add(genSeries(list, range, style, 6));
            // 统计语音
            list = getByStyle(style, type, pgcId, start, end, 7);
            seriesList.add(genSeries(list, range, style, 7));
            return seriesList;
        }
    }

    /**
     * 通过展示方式获取list数据
     *
     * @param style
     * @return
     */
    private List<Map<String, Object>> getByStyle(Integer style, Integer type, Integer pgcId, Date start, Date end, Integer mediaType) {
        if (style.equals(1)) {
            return statisticProduceService.getDayCount(type, pgcId, mediaType, start, end);
        } else if (style.equals(2)) {
            return statisticProduceService.getWeekCount(type, pgcId, mediaType, start, end);
        } else if (style.equals(3)) {
            return statisticProduceService.getMonthCount(type, pgcId, mediaType, start, end);
        }
        return Collections.emptyList();
    }

    private Series genSeries(List<Map<String, Object>> list, List<String> range, Integer style, Integer mediaType) {
        Map<String, Integer> map = XAliasUtils.xAliasGen(range, style);
        List<Integer> yData = fillXYData(map, list);
        Series series = new Series();
        series.setType("line");
        series.setData(yData);
        series.setName(getNameByMediaType(mediaType));
        return series;
    }

    /**
     * 8.累计生产量TOP榜PGC
     * 9.新增生产量TOP榜PGC
     */
    @Override
    public Result<StatisticVO> getStatisticProduceAbilityTable(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(0);
        List<Chart> charts = new ArrayList<>(0);
        List<Table> tables = new ArrayList<>(2);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        Date start = DateUtils.parse(range.get(0));
        Date end = DateUtils.parse(range.get(1));
        Integer pgcId = searchStatisticProduceVO.getPgcId();
        Integer mediaType = searchStatisticProduceVO.getMediaType();

        // 2个统计数据
        // 1.累计生产量TOP榜PGC（累计量和范围无关）
        tables.add(Table.builder().key(8).title("累计生产量TOP榜PGC").
                list(buildTable(statisticProduceService.getMediaCount(6, pgcId, mediaType, null, null))).build());

        // 2.新增生产量TOP榜PGC（统计某日期范围中的加和）
        tables.add(Table.builder().key(9).title("新增生产量TOP榜PGC").
                list(buildTable(statisticProduceService.getMediaCount(6, pgcId, mediaType, start, end))).build());

        return Result.ok(vo);
    }

    private List<Row> buildTable(List<Map<String, Object>> list) {
        List<Row> rows = new ArrayList<>();
        int i = 1;
        for (Map<String, Object> map : list) {
            Row row = new Row();
            row.setId((Long) map.get("pgcId"));
            row.setRank(i++);
            Object count = map.get("sum");
            row.setCount(Integer.parseInt(String.valueOf(count)));
            rows.add(row);
        }
        return rows;
    }


    /**
     * 1.累计曝光量
     * 2.累计视频曝光量
     * 3.累计音频曝光量
     * <p>
     * 4.累计播放量
     * 5.累计视频播放量
     * 6.累计音频播放量
     * <p>
     * 7.累计收藏次数
     * 8.累计视频收藏次数
     * 9.累计音频收藏次数
     * <p>
     * 10.累计转发次数
     * 11.累计视频转发次数
     * 12.累计音频转发次数
     * <p>
     * 13.累计评论次数
     * 14.累计视频评论次数
     * 15.累计音频评论次数
     * <p>
     * 16.新增曝光量
     * 17.新增视频曝光量
     * 18.新增音频曝光量
     * <p>
     * 19.新增播放量
     * 20.新增视频播放量
     * 21.新增音频播放量
     * <p>
     * 22.新增收藏次数
     * 23.新增视频收藏次数
     * 24.新增音频收藏次数
     * <p>
     * 25.新增转发次数
     * 26.新增视频转发次数
     * 27.新增音频转发次数
     * <p>
     * 28.新增评论次数
     * 29.新增视频评论次数
     * 30.新增音频评论次数
     * <p>
     * 31.累计图文生产量
     * 32.累计语音生产量
     * 33.累计直播生产量
     * 34.新增图文生产量
     * 35.新增语音生产量
     * 36.新增直播生产量
     * <p>
     * 37.累计图文曝光量
     * 38.累计语音曝光量
     * 39.累计直播曝光量
     * <p>
     * 40.累计图文播放量
     * 41.累计语音播放量
     * 42.累计直播播放量
     * <p>
     * 43.累计图文收藏量
     * 44.累计语音收藏量
     * 45.累计直播收藏量
     * <p>
     * 46.累计图文转发量
     * 47.累计语音转发量
     * 48.累计直播转发量
     * <p>
     * 49.累计图文评论量
     * 50.累计语音评论量
     * 51.累计直播评论量
     * <p>
     * 52.新增图文曝光量
     * 53.新增语音曝光量
     * 54.新增直播曝光量
     * <p>
     * 55.新增图文播放量
     * 56.新增语音播放量
     * 57.新增直播播放量
     * <p>
     * 58.新增图文收藏量
     * 59.新增语音收藏量
     * 60.新增直播收藏量
     * <p>
     * 61.新增图文转发量
     * 62.新增语音转发量
     * 63.新增直播转发量
     * <p>
     * 64.新增图文评论量
     * 65.新增语音评论量
     * 66.新增直播评论量
     */
    @Override
    public Result<StatisticVO> getStatisticProduceFunnelPanel(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(30);
        List<Chart> charts = new ArrayList<>(0);
        List<Table> tables = new ArrayList<>(0);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        Integer pgcId = searchStatisticProduceVO.getPgcId();

        // 30个统计数据
        genPanels(panels, 5, null, pgcId, Arrays.asList(1, 2, 3, 37, 38, 39), Arrays.asList("累计曝光量", "累计视频曝光量", "累计音频曝光量", "累计图文曝光量", "累计语音曝光量", "累计直播曝光量"));
        genPanels(panels, 1, null, pgcId, Arrays.asList(4, 5, 6, 40, 41, 42), Arrays.asList("累计播放量", "累计视频播放量", "累计音频播放量", "累计图文播放量", "累计语音播放量", "累计直播播放量"));
        genPanels(panels, 2, null, pgcId, Arrays.asList(7, 8, 9, 43, 44, 45), Arrays.asList("累计收藏量", "累计视频收藏量", "累计音频收藏量", "累计图文收藏量", "累计语音收藏量", "累计直播收藏量"));
        genPanels(panels, 3, null, pgcId, Arrays.asList(10, 11, 12, 46, 47, 48), Arrays.asList("累计转发量", "累计视频转发量", "累计音频转发量", "累计图文转发量", "累计语音转发量", "累计直播转发量"));
        genPanels(panels, 4, null, pgcId, Arrays.asList(13, 14, 15, 49, 50, 51), Arrays.asList("累计评论量", "累计视频评论量", "累计音频评论量", "累计图文评论量", "累计语音评论量", "累计直播评论量"));

        genPanels(panels, 5, range, pgcId, Arrays.asList(16, 17, 18, 52, 53, 54), Arrays.asList("新增曝光量", "新增视频曝光量", "新增音频曝光量", "新增图文曝光量", "新增语音曝光量", "新增直播曝光量"));
        genPanels(panels, 1, range, pgcId, Arrays.asList(19, 20, 21, 55, 56, 57), Arrays.asList("新增播放量", "新增视频播放量", "新增音频播放量", "新增图文播放量", "新增语音播放量", "新增直播播放量"));
        genPanels(panels, 2, range, pgcId, Arrays.asList(22, 23, 24, 58, 59, 60), Arrays.asList("新增收藏量", "新增视频收藏量", "新增音频收藏量", "新增图文收藏量", "新增语音收藏量", "新增直播收藏量"));
        genPanels(panels, 3, range, pgcId, Arrays.asList(25, 26, 27, 61, 62, 63), Arrays.asList("新增转发量", "新增视频转发量", "新增音频转发量", "新增图文转发量", "新增语音转发量", "新增直播转发量"));
        genPanels(panels, 4, range, pgcId, Arrays.asList(28, 29, 30, 64, 65, 66), Arrays.asList("新增评论量", "新增视频评论量", "新增音频评论量", "新增图文评论量", "新增语音评论量", "新增直播评论量"));

        return Result.ok(vo);
    }

    private void genPanels(List<Panel> panels, Integer type, List<String> range, Integer pgcId, List<Integer> keys, List<String> titles) {
        LambdaQueryWrapper<StatisticProduce> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StatisticProduce::getType, type);
        if (pgcId != null && pgcId != 0) {
            queryWrapper.eq(StatisticProduce::getPgcId, pgcId);
        }
        if (!CollectionUtils.isEmpty(range)) {
            queryWrapper.ge(StatisticProduce::getStatisticDate, range.get(0));
            queryWrapper.le(StatisticProduce::getStatisticDate, range.get(1));
        }
        List<StatisticProduce> statisticProduces = statisticProduceService.list(queryWrapper);
        Optional<Integer> optional1 = statisticProduces.stream().map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(0)).title(titles.get(0)).unit("次").count(optional1.orElse(0)).chain("无数据").equal("无数据").build());

        // 2.累计视频曝光量（与日期范围无关）
        Optional<Integer> optional2 = statisticProduces.stream().filter(s -> s.getMediaType().equals(1)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(1)).title(titles.get(1)).unit("次").count(optional2.orElse(0)).chain("无数据").equal("无数据").build());

        // 3.累计音频曝光量（与日期范围无关）
        Optional<Integer> optional3 = statisticProduces.stream().filter(s -> s.getMediaType().equals(2)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(2)).title(titles.get(2)).unit("次").count(optional3.orElse(0)).chain("无数据").equal("无数据").build());

        // 4.累计图文曝光量（与日期范围无关）
        Optional<Integer> optional4 = statisticProduces.stream().filter(s -> s.getMediaType().equals(6)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(3)).title(titles.get(3)).unit("次").count(optional4.orElse(0)).chain("无数据").equal("无数据").build());

        // 4.累计语音曝光量（与日期范围无关）
        Optional<Integer> optional5 = statisticProduces.stream().filter(s -> s.getMediaType().equals(7)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(4)).title(titles.get(4)).unit("次").count(optional5.orElse(0)).chain("无数据").equal("无数据").build());

        // 4.累计直播曝光量（与日期范围无关）
        Optional<Integer> optional6 = statisticProduces.stream().filter(s -> s.getMediaType().equals(5)).map(StatisticProduce::getSum).reduce(Integer::sum);
        panels.add(Panel.builder().key(keys.get(5)).title(titles.get(5)).unit("次").count(optional6.orElse(0)).chain("无数据").equal("无数据").build());
    }

    /**
     * 1.累计曝光量TOP榜 PGC
     * 2.新增曝光量TOP榜 PGC
     * 3.累计播放量TOP榜 PGC
     * 4.新增播放量TOP榜 PGC
     * 5.累计收藏量TOP榜 PGC
     * 6.新增收藏量TOP榜 PGC
     * 7.累计转发量TOP榜 PGC
     * 8.新增转发量TOP榜 PGC
     * 9.累计评论量TOP榜 PGC
     * 10.新增评论量TOP榜 PGC
     */
    @Override
    public Result<StatisticVO> getStatisticProduceTopTable(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(0);
        List<Chart> charts = new ArrayList<>(0);
        List<Table> tables = new ArrayList<>(10);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        Date start = DateUtils.parse(range.get(0));
        Date end = DateUtils.parse(range.get(1));
        Integer pgcId = searchStatisticProduceVO.getPgcId();
        Integer mediaType = searchStatisticProduceVO.getMediaType();

        // 2个统计数据
        // 1.累计曝光量TOP榜（累计量和范围无关）
        tables.add(Table.builder().key(1).title("累计曝光量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(5, pgcId, mediaType, null, null))).build());
        // 2.新增曝光量TOP榜（统计某日期范围中的加和）
        tables.add(Table.builder().key(2).title("新增曝光量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(5, pgcId, mediaType, start, end))).build());
        // 3.累计播放量TOP榜（累计量和范围无关）
        tables.add(Table.builder().key(3).title("累计播放量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(1, pgcId, mediaType, null, null))).build());
        // 4.新增播放量TOP榜（统计某日期范围中的加和）
        tables.add(Table.builder().key(4).title("新增播放量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(1, pgcId, mediaType, start, end))).build());
        // 5.累计收藏量TOP榜（累计量和范围无关）
        tables.add(Table.builder().key(5).title("累计收藏量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(2, pgcId, mediaType, null, null))).build());
        // 6.新增收藏量TOP榜（统计某日期范围中的加和）
        tables.add(Table.builder().key(6).title("新增收藏量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(2, pgcId, mediaType, start, end))).build());
        // 7.累计转发量TOP榜（累计量和范围无关）
        tables.add(Table.builder().key(7).title("累计转发量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(3, pgcId, mediaType, null, null))).build());
        // 8.新增转发量TOP榜（统计某日期范围中的加和）
        tables.add(Table.builder().key(8).title("新增转发量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(3, pgcId, mediaType, start, end))).build());
        // 9.累计评论量TOP榜（累计量和范围无关）
        tables.add(Table.builder().key(9).title("累计评论量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(4, pgcId, mediaType, null, null))).build());
        // 10.新增评论量TOP榜（统计某日期范围中的加和）
        tables.add(Table.builder().key(10).title("新增评论量TOP榜").
                list(buildTable(statisticProduceService.getMediaCount(4, pgcId, mediaType, start, end))).build());
        return Result.ok(vo);
    }

    /**
     * 1.新增曝光量趋势
     * 2.新增分类曝光量趋势
     * 3.新增播放量趋势
     * 4.新增分类播放量趋势
     * 5.新增收藏次数趋势
     * 6.新增分类收藏次数趋势
     * 7.新增转发次数趋势
     * 8.新增分类转发次数趋势
     * 9.新增评论次数趋势
     * 10.新增分类评论次数趋势
     */
    @Override
    public Result<StatisticVO> getStatisticProduceTrendChart(SearchStatisticProduceVO searchStatisticProduceVO) {
        StatisticVO vo = new StatisticVO();
        List<Panel> panels = new ArrayList<>(0);
        List<Chart> charts = new ArrayList<>(10);
        List<Table> tables = new ArrayList<>(0);
        vo.setPanels(panels);
        vo.setCharts(charts);
        vo.setTables(tables);
        List<String> range = searchStatisticProduceVO.getRange();
        // style是展示的月日周小时4种样式
        Integer style = searchStatisticProduceVO.getType();
        Integer pgcId = searchStatisticProduceVO.getPgcId();
        List<String> compare = searchStatisticProduceVO.getCompare();

        // 下面是2个折线图，先生成折线图的X轴。每个图都要分4种情况日/周/月/小时聚合
        Map<String, Integer> xAliasMap = XAliasUtils.xAliasGen(range, style);
        if (style == 4) {
            // 按小时
            charts.add(Chart.builder().key(1).title("新增曝光量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 5, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(2).title("新增分类曝光量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 5, compare, pgcId, false)).build());
            charts.add(Chart.builder().key(3).title("新增播放量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 1, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(4).title("新增分类播放量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 1, compare, pgcId, false)).build());
            charts.add(Chart.builder().key(5).title("新增收藏量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 2, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(6).title("新增分类收藏量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 2, compare, pgcId, false)).build());
            charts.add(Chart.builder().key(7).title("新增转发量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 3, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(8).title("新增分类转发量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 3, compare, pgcId, false)).build());
            charts.add(Chart.builder().key(9).title("新增评论量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 4, compare, pgcId, true)).build());
            charts.add(Chart.builder().key(10).title("新增分类评论量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeriesByHour(range, 4, compare, pgcId, false)).build());
        } else if (style == 1 || style == 2 || style == 3) {
            // 按日或月一样的处理办法
            charts.add(Chart.builder().key(1).title("新增曝光量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 5, style, pgcId, true)).build());
            charts.add(Chart.builder().key(2).title("新增分类曝光量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 5, style, pgcId, false)).build());
            charts.add(Chart.builder().key(3).title("新增播放量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 1, style, pgcId, true)).build());
            charts.add(Chart.builder().key(4).title("新增分类播放量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 1, style, pgcId, false)).build());
            charts.add(Chart.builder().key(5).title("新增收藏量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 2, style, pgcId, true)).build());
            charts.add(Chart.builder().key(6).title("新增分类收藏量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 2, style, pgcId, false)).build());
            charts.add(Chart.builder().key(7).title("新增转发量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 3, style, pgcId, true)).build());
            charts.add(Chart.builder().key(8).title("新增分类转发量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 3, style, pgcId, false)).build());
            charts.add(Chart.builder().key(9).title("新增评论量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 4, style, pgcId, true)).build());
            charts.add(Chart.builder().key(10).title("新增分类评论量趋势").xAxis(new ArrayList<>(xAliasMap.keySet())).
                    series(buildSeries(range, 4, style, pgcId, false)).build());
        }
        return Result.ok(vo);
    }
}
