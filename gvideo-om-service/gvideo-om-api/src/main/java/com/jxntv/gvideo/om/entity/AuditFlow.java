package com.jxntv.gvideo.om.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-03-27
 */
@Data
public class AuditFlow {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 描述
     */
    private String note;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 是否系统预设
     */
    private String uuid;
    /**
     * 是否已归档的历史版本
     */
    private Boolean isFiled;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Boolean delFlag;
}
