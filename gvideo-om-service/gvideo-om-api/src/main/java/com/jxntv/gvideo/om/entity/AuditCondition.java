package com.jxntv.gvideo.om.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-04-01
 */
@Data
public class AuditCondition {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 条件ID
     */
    private Long configId;
    /**
     * 关联审核业务ID
     */
    private Long bizId;
    /**
     * 条件内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createDate;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 逻辑删除标记(0--正常 1--删除)
     */
    @TableLogic
    private Boolean delFlag;
}
