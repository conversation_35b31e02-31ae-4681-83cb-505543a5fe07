package com.jxntv.gvideo.om.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.om.entity.RuleModelEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/4 18:03
 */
public interface RuleModelEntityDao extends IService<RuleModelEntity> {

    RuleModelEntity updateRuleModelStatusById(Long modelId, int status);

    RuleModelEntity updateRuleModelStatusAndNumById(Long modelId, int status, int count);

    RuleModelEntity queryRuleModelEntityById(Long modelId);

    RuleModelEntity queryRuleModelEntityByModelName(String modelName);

    RuleModelEntity queryRuleModelEntityByNameAndType(String modelName, String modelType);

    List<Long> queryModelIdByFuzzyName(String name);

}
