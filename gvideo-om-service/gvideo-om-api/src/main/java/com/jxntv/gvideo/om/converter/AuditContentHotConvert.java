package com.jxntv.gvideo.om.converter;

import com.jxntv.gvideo.om.dto.AuditContentHotDTO;
import com.jxntv.gvideo.om.dto.AuditContentHotRuleDTO;
import com.jxntv.gvideo.om.entity.AuditContentHot;
import com.jxntv.gvideo.om.entity.AuditContentHotRule;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description 热度转换类
 * @date 2021/07/14 8:43
 */
@Component
public class AuditContentHotConvert {

    public AuditContentHotRule convert(AuditContentHotRuleDTO dto) {
        AuditContentHotRule auditContentHotRule = new AuditContentHotRule();
        BeanCopier.create(AuditContentHotRuleDTO.class, AuditContentHotRule.class, false)
                .copy(dto, auditContentHotRule, null);
        return auditContentHotRule;
    }

    public AuditContentHotRuleDTO convert(AuditContentHotRule auditContentHotRule) {
        AuditContentHotRuleDTO dto = new AuditContentHotRuleDTO();
        BeanCopier.create(AuditContentHotRule.class, AuditContentHotRuleDTO.class, false)
                .copy(auditContentHotRule, dto, null);
        return dto;
    }

    public AuditContentHot convert(AuditContentHotDTO dto) {
        AuditContentHot auditContentHot = new AuditContentHot();
        BeanCopier.create(AuditContentHotDTO.class, AuditContentHot.class, false)
                .copy(dto, auditContentHot, null);
        return auditContentHot;
    }

    public AuditContentHotDTO convert(AuditContentHot auditContentHot) {
        AuditContentHotDTO dto = new AuditContentHotDTO();
        BeanCopier.create(AuditContentHot.class, AuditContentHotDTO.class, false)
                .copy(auditContentHot, dto, null);
        return dto;
    }
}
