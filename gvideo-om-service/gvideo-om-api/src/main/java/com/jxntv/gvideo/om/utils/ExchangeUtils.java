package com.jxntv.gvideo.om.utils;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/19 18:52
 */
public class ExchangeUtils {

    public static String ConvertLongArrToStr(List<Long> arr) {
        if (CollectionUtils.isEmpty(arr)) {
            return "";
        }
        return StringUtils.join(arr, "_");
    }

    public static String ConvertStringArrToStr(List<String> arr) {
        if (CollectionUtils.isEmpty(arr)) {
            return "";
        }
        return StringUtils.join(arr, "_");
    }

    public static List<Long> ConvertStrToLongArr(String str) {
        if (StringUtils.isEmpty(str)) {
            return new ArrayList<>();
        }
        List<String> strArr = Arrays.asList(StringUtils.split(str, "_"));
        return strArr.stream().map(o -> Long.valueOf(o)).collect(Collectors.toList());
    }

    public static List<String> ConvertStrToStringArr(String str) {
        if (StringUtils.isEmpty(str)) {
            return new ArrayList<>();
        }

        return Arrays.asList(StringUtils.split(str, "_"));
    }
}
