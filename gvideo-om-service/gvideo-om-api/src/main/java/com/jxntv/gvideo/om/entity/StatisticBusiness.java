package com.jxntv.gvideo.om.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 *
 * Created on 2020-04-24
 */
@Data
public class StatisticBusiness {

    /**
     * 自增ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 统计日期
     */
    private Date statisticDate;
    /**
     * 小时
     */
    private Integer clock;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 数量
     */
    private Integer sum;
    /**
     * 创建时间
     */
    private Date createDate;

}
