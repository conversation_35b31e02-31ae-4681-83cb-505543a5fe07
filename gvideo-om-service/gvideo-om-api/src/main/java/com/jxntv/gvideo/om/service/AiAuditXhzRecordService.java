package com.jxntv.gvideo.om.service;

import com.google.gson.JsonObject;
import com.jxntv.gvideo.om.entity.AiAuditXhzRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.om.param.PostAiAuditParam;

public interface AiAuditXhzRecordService extends IService<AiAuditXhzRecord>{

    /**
     * 结果回调
     * @param result
     */
    Boolean callBack(String result);

    /**
     * 易盾结果回调
     * @param antispam
     * @return
     */
    Boolean yidunCallBack(JsonObject antispam);

    /**
     * 保存审核记录
     * @param param
     * @return
     */
    AiAuditXhzRecord saveAiAuditRecord(PostAiAuditParam param);

    /**
     * 保存审核记录
     * @param param
     * @return
     */
    AiAuditXhzRecord saveYiDunAuditRecord(PostAiAuditParam param);
}
