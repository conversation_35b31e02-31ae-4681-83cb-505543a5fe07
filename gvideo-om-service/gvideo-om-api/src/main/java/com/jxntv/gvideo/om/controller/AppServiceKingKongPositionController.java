package com.jxntv.gvideo.om.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AppServiceKingKongPositionClient;
import com.jxntv.gvideo.om.dto.AppServiceKingKongPositionDTO;
import com.jxntv.gvideo.om.dto.AppServiceKingKongPositionSearchDTO;
import com.jxntv.gvideo.om.entity.AppServiceKingKongPosition;
import com.jxntv.gvideo.om.event.service.AppServiceKingKongPositionService;
import com.jxntv.gvideo.om.utils.PageUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: niedamin
 * @Date: 2023/06/05 11:14
 */
@RestController
public class AppServiceKingKongPositionController implements AppServiceKingKongPositionClient {

    @Resource
    private AppServiceKingKongPositionService appServiceKingKongPositionService;

    @Override
    public Result<Long> add(AppServiceKingKongPositionDTO positionDTO) {
        AppServiceKingKongPosition position = new AppServiceKingKongPosition();
        BeanUtils.copyProperties(positionDTO, position);
        appServiceKingKongPositionService.save(position);
        return Result.ok(position.getId());
    }

    @Override
    public Result<AppServiceKingKongPositionDTO> getById(Long id) {
        AppServiceKingKongPosition position = appServiceKingKongPositionService.getById(id);
        AppServiceKingKongPositionDTO dto = new AppServiceKingKongPositionDTO();
        BeanUtils.copyProperties(position, dto);
        return Result.ok(dto);
    }

    @Override
    public Result<Boolean> deleteById(Long id) {
        return Result.ok(appServiceKingKongPositionService.removeById(id));
    }

    @Override
    public Result<Boolean> updateById(Long id, AppServiceKingKongPositionDTO positionDTO) {
        AppServiceKingKongPosition position = new AppServiceKingKongPosition();
        positionDTO.setId(id);
        if (StringUtils.isBlank(positionDTO.getLinkUrl())) {
            positionDTO.setLinkUrl(null);
        }
        BeanUtils.copyProperties(positionDTO, position);
        return Result.ok(appServiceKingKongPositionService.updateById(position));
    }

    @Override
    public Result<PageDTO<AppServiceKingKongPositionDTO>> page(AppServiceKingKongPositionSearchDTO searchDTO) {
        LambdaQueryWrapper<AppServiceKingKongPosition> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(Objects.nonNull(searchDTO.getCategoryId()), AppServiceKingKongPosition::getCategoryId, searchDTO.getCategoryId());
        queryWrapper.orderByAsc(AppServiceKingKongPosition::getSort);
        Page<AppServiceKingKongPosition> page = appServiceKingKongPositionService.page(new Page<>(searchDTO.getCurrent(), searchDTO.getSize()), queryWrapper);
        return Result.ok(PageUtils.pageOf(page, (appServiceKingKongPosition) -> {
            AppServiceKingKongPositionDTO dto = new AppServiceKingKongPositionDTO();
            BeanUtils.copyProperties(appServiceKingKongPosition, dto);
            return dto;
        }));
    }

    @Override
    public Result<Integer> getByCategoryId(Long categoryId) {
        LambdaQueryWrapper<AppServiceKingKongPosition> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AppServiceKingKongPosition::getCategoryId, categoryId);
        return Result.ok(appServiceKingKongPositionService.count(queryWrapper));
    }

    @Override
    public Result<List<AppServiceKingKongPositionDTO>> getListByCategoryId(Long categoryId) {
        List<AppServiceKingKongPositionDTO> res = new ArrayList<>();
        LambdaQueryWrapper<AppServiceKingKongPosition> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AppServiceKingKongPosition::getCategoryId, categoryId);
        queryWrapper.orderByAsc(AppServiceKingKongPosition::getSort);
        List<AppServiceKingKongPosition> list = appServiceKingKongPositionService.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            res = list.stream().map(appServiceKingKongPosition -> {
                AppServiceKingKongPositionDTO dto = new AppServiceKingKongPositionDTO();
                BeanUtils.copyProperties(appServiceKingKongPosition, dto);
                return dto;
            }).collect(Collectors.toList());
        }
        return Result.ok(res);
    }
}
