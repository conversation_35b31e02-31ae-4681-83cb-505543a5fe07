package com.jxntv.gvideo.om.controller;

import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.ResourceRuleClient;
import com.jxntv.gvideo.om.constant.Constant;
import com.jxntv.gvideo.om.dao.RuleEntityDao;
import com.jxntv.gvideo.om.dto.ResourceRuleReqDTO;
import com.jxntv.gvideo.om.dto.ResourceRuleRespDTO;
import com.jxntv.gvideo.om.dto.RuleRespDTO;
import com.jxntv.gvideo.om.service.RuleModelService;
import com.jxntv.gvideo.om.service.RuleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 渠道规则
 * @date 2021/11/10 14:49
 */
@Slf4j
@RestController
public class ResourceRuleController implements ResourceRuleClient {

    @Autowired
    private RuleService ruleService;

    @Autowired
    private RuleModelService ruleModelService;

    @Override
    public Result<Long> createResourceRule(String modelId, ResourceRuleReqDTO reqDTO) {
        log.info("start createChannelRule modelId is {}, modelType is {}", modelId, Constant.RESOURCE_MODEL_TYPE);

        boolean resourceRuleExist = ruleModelService.checkRuleModelIsExist(Long.valueOf(modelId), Constant.RESOURCE_MODEL_TYPE);
        if (!resourceRuleExist) {
            return Result.fail("动态规则不存在");
        }

        Long ruleId = ruleService.createResourceRule(Long.valueOf(modelId), reqDTO);
        if (ruleId <= 0L) {
            return Result.fail("创建动态规则失败");
        }
        return Result.ok(ruleId);
    }

    @Override
    public Result<ResourceRuleRespDTO> queryResourceRuleById(String modelId, String ruleId) {
        log.info("start queryChannelRuleById modelId is {}, ruleId is {}", modelId, ruleId);
        RuleRespDTO respDTO = ruleService.queryRuleById(Long.valueOf(modelId), Long.valueOf(ruleId));
        return Result.ok((ResourceRuleRespDTO) respDTO);
    }

    @Override
    public Result<List<ResourceRuleRespDTO>> queryResourceRuleList(String modelId) {
        log.info("start queryChannelRuleList modelId is {}", modelId);
        List<RuleRespDTO> respDTOList = ruleService.queryRuleListByModelId(Long.valueOf(modelId));
        List<ResourceRuleRespDTO> rest = new ArrayList<>();
        if(respDTOList == null) {
            return Result.ok(rest);
        }
        for (RuleRespDTO respDTO : respDTOList) {
            rest.add((ResourceRuleRespDTO) respDTO);
        }

        return Result.ok(rest);
    }

    @Override
    public Result<ResourceRuleRespDTO> modifyResourceRule(String modelId, String ruleId, ResourceRuleReqDTO resourceRuleReqDTO) {
        log.info("start modifyChannelRule modelId is {}, ruleId is {}", modelId, ruleId);
        RuleRespDTO respDTO = ruleService.modifyResourceRule(Long.valueOf(modelId), Long.valueOf(ruleId), resourceRuleReqDTO);
        if (null == respDTO) {
            return Result.fail("modify err.");
        }
        return Result.ok((ResourceRuleRespDTO) respDTO);
    }
}
