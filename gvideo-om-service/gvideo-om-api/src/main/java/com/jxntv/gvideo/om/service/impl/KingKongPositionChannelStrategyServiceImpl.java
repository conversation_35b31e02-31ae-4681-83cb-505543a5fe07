package com.jxntv.gvideo.om.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.om.client.AppHomePageGuideClient;
import com.jxntv.gvideo.om.converter.KingKongPositionChannelStrategyConverter;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategyDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionChannelStrategySearchDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import com.jxntv.gvideo.om.entity.KingKongPositionChannelStrategy;
import com.jxntv.gvideo.om.entity.KingKongPositionChannelStrategyItem;
import com.jxntv.gvideo.om.mapper.KingKongPositionChannelStrategyMapper;
import com.jxntv.gvideo.om.service.KingKongPositionChannelStrategyItemService;
import com.jxntv.gvideo.om.service.KingKongPositionChannelStrategyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
@Service
public class KingKongPositionChannelStrategyServiceImpl extends ServiceImpl<KingKongPositionChannelStrategyMapper, KingKongPositionChannelStrategy>
        implements KingKongPositionChannelStrategyService {

    @Resource
    private KingKongPositionChannelStrategyConverter kingKongPositionChannelStrategyConverter;
    @Resource
    private KingKongPositionChannelStrategyItemService kingKongPositionChannelStrategyItemService;

    @Resource
    private AppHomePageGuideClient appHomePageGuideClient;


    @Override
    public KingKongPositionChannelStrategy add(KingKongPositionChannelStrategyDTO dto) {
        List<KingKongPositionChannelStrategy> all = new ArrayList<>();
        LambdaQueryWrapper<KingKongPositionChannelStrategy> queryWrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(dto.getIsJuniorMode()) && dto.getIsJuniorMode().equals(1)) {
            // 青少年模式
            queryWrapper.eq(KingKongPositionChannelStrategy::getIsJuniorMode, 1);
        } else {
            queryWrapper.eq(KingKongPositionChannelStrategy::getIsJuniorMode, 0);
        }
        all = this.list(queryWrapper);

        List<Long> ids = new ArrayList<>();
        all.forEach(kingKongPositionChannelStrategy -> {
            String channelId = kingKongPositionChannelStrategy.getChannelId();
            List<String> channelIds = Arrays.asList(channelId.split(","));
            ids.addAll(channelIds.stream().map(Long::parseLong).collect(Collectors.toList()));
        });
        // 检验渠道
        dto.getChannels().forEach(channel -> {
            if (ids.contains(channel.getChannelId())) {
                throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "渠道重复");
            }
        });

        KingKongPositionChannelStrategy entity = kingKongPositionChannelStrategyConverter.convert(dto);
        this.save(entity);

        this.addItems(entity.getId(), dto.getPositions());

        return entity;
    }

    private void addItems(Long strategyId, List<KingKongPositionDTO> positions) {
        //  添加渠道不可以不添加
        AssertUtil.notEmpty(positions, CodeMessage.BAD_REQUEST.getCode(), "添加失败，金刚位没有设置");


        List<KingKongPositionChannelStrategyItem> items = new ArrayList<>();
        for (KingKongPositionDTO position : positions) {
            //  检查金刚位是否设置
            AssertUtil.notNull(position.getId(), CodeMessage.BAD_REQUEST.getCode(), "添加失败，金刚位没有设置");

            KingKongPositionChannelStrategyItem item = new KingKongPositionChannelStrategyItem();
            item.setStrategyId(strategyId);
            item.setPositionId(position.getId());
            item.setBubbleWords(position.getBubbleWords());

            items.add(item);
        }

        kingKongPositionChannelStrategyItemService.saveBatch(items);


    }


    @Override
    @Transactional
    public boolean updateById(KingKongPositionChannelStrategyDTO dto) {
        KingKongPositionChannelStrategy entity = kingKongPositionChannelStrategyConverter.convert(dto);

        // 如果此次修改删除了金刚位，并且此金刚位设置了app首页强引导，需要删除首页强引导
        deleteAppHomePageGuide(dto);

        //  删除原来的ITEM
        LambdaQueryWrapper<KingKongPositionChannelStrategyItem> itemQuery = Wrappers.<KingKongPositionChannelStrategyItem>lambdaQuery().eq(KingKongPositionChannelStrategyItem::getStrategyId, dto.getId());
        kingKongPositionChannelStrategyItemService.remove(itemQuery);

        this.addItems(entity.getId(), dto.getPositions());

        return this.updateById(entity);
    }

    @Override
    public Page<KingKongPositionChannelStrategy> page(KingKongPositionChannelStrategySearchDTO searchDTO) {
        Page<KingKongPositionChannelStrategy> pageRequest = Page.of(searchDTO.getCurrent(), searchDTO.getSize());
        LambdaQueryWrapper<KingKongPositionChannelStrategy> lambdaQuery = Wrappers.lambdaQuery();
        if (StringUtils.hasText(searchDTO.getNameLike())) {
            lambdaQuery.like(KingKongPositionChannelStrategy::getChannelName, searchDTO.getNameLike());
        }
        lambdaQuery.eq(KingKongPositionChannelStrategy::getIsJuniorMode, searchDTO.getIsJuniorMode());
        return this.page(pageRequest, lambdaQuery);
    }

    @Override
    public KingKongPositionChannelStrategy getByChannelId(Long channelId, Integer isJuniorMode) {
        LambdaQueryWrapper<KingKongPositionChannelStrategy> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(KingKongPositionChannelStrategy::getIsJuniorMode, isJuniorMode);
        lambdaQuery.and(wrapper ->  wrapper
                .eq(KingKongPositionChannelStrategy::getChannelId,channelId)
                .or().likeRight(KingKongPositionChannelStrategy::getChannelId, channelId + ",")
                .or().like(KingKongPositionChannelStrategy::getChannelId, "," + channelId + ",")
                .or().likeLeft(KingKongPositionChannelStrategy::getChannelId, "," + channelId)
        );
        return this.getOne(lambdaQuery);
    }

    private void deleteAppHomePageGuide(KingKongPositionChannelStrategyDTO dto) {
        // 如果此次修改删除了金刚位，并且此金刚位设置了app首页强引导，需要删除首页强引导
        List<Long> oldId = new ArrayList<>();
        List<Long> newId = new ArrayList<>();
        List<KingKongPositionChannelStrategyItem> kingKongPositionChannelStrategyItems = kingKongPositionChannelStrategyItemService.listByStrategyId(dto.getId());
        List<KingKongPositionDTO> positions = dto.getPositions();
        if (!CollectionUtils.isEmpty(kingKongPositionChannelStrategyItems)) {
            kingKongPositionChannelStrategyItems.forEach(kingKongPositionChannelStrategyItem -> oldId.add(kingKongPositionChannelStrategyItem.getPositionId()));
        }
        if (!CollectionUtils.isEmpty(positions)) {
            positions.forEach(position -> newId.add(position.getId()));
        }
        oldId.forEach(id -> {
            if (!newId.contains(id)) {
                // 删除此金刚位绑定的首页强引导
                appHomePageGuideClient.deleteByKingkongAndStrategyId(id, dto.getId());
            }
        });
    }
}
