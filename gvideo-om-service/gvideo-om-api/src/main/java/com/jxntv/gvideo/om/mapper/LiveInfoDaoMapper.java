package com.jxntv.gvideo.om.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.om.dto.ContentLivePoolDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/25 19:23
 */
public interface LiveInfoDaoMapper extends BaseMapper<ContentLivePoolDTO> {

    List<ContentLivePoolDTO> queryActivityLiveByConditions(int certId, int status, int nums);

    List<ContentLivePoolDTO> queryInteractiveLiveByConditions(int certId, int status, int nums);

    List<ContentLivePoolDTO> queryTVChannelByConditions(int status, int nums);


}
