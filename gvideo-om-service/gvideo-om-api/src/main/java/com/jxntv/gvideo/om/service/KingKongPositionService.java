package com.jxntv.gvideo.om.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import com.jxntv.gvideo.om.dto.KingKongPositionSearchDTO;
import com.jxntv.gvideo.om.entity.KingKongPosition;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2022/4/21
 * Email: <EMAIL>
 */
public interface KingKongPositionService extends IService<KingKongPosition> {

    KingKongPosition add(KingKongPositionDTO positionDTO);

    Boolean updateById(KingKongPositionDTO positionDTO);

    Page<KingKongPosition> page(KingKongPositionSearchDTO searchDTO);

}
