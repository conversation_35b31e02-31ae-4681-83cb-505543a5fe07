package com.jxntv.gvideo.om.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jxntv.gvideo.om.dto.ContentTopicPoolDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2021/11/19 12:44
 */
public interface TopicInfoDao extends IService<ContentTopicPoolDTO> {

    List<Long> queryTopicListByGroupAndLabel(List<Long> groupArr,
                                             List<Long> labelArr,
                                             long nums);

    List<ContentTopicPoolDTO> queryTopicListByTopicId(List<Long> topicArr);

    int countContentNumsByTopicId(long topicId);
}
