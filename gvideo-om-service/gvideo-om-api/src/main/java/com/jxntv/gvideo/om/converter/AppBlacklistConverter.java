package com.jxntv.gvideo.om.converter;


import com.jxntv.gvideo.om.dto.AppBlacklistDTO;
import com.jxntv.gvideo.om.entity.AppBlacklist;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class AppBlacklistConverter {

    public AppBlacklistDTO convert(AppBlacklist entity){
        if (Objects.isNull(entity)){
            return null;
        }
        AppBlacklistDTO dto = new AppBlacklistDTO();
        dto.setId(entity.getId());
        dto.setJid(entity.getJid());
        dto.setType(entity.getType());
        dto.setReason(entity.getReason());
        dto.setCreateUserId(entity.getCreateUserId());
        dto.setCreateUserName(entity.getCreateUserName());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateUserId(entity.getUpdateUserId());
        dto.setUpdateUserName(entity.getUpdateUserName());
        dto.setUpdateDate(entity.getUpdateDate());
        return dto;
    }

    public AppBlacklist convert(AppBlacklistDTO dto){
        AppBlacklist entity = new AppBlacklist();
        entity.setId(dto.getId());
        entity.setJid(dto.getJid());
        entity.setType(dto.getType());
        entity.setReason(dto.getReason());
        entity.setCreateUserId(dto.getCreateUserId());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setCreateDate(dto.getCreateDate());
        entity.setUpdateUserId(dto.getUpdateUserId());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateDate(dto.getUpdateDate());
        return entity;
    }
}
