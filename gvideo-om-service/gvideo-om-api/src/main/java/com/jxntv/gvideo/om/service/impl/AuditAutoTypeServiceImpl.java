package com.jxntv.gvideo.om.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jxntv.gvideo.om.converter.AuditAutoConvert;
import com.jxntv.gvideo.om.entity.AuditAutoResult;
import com.jxntv.gvideo.om.entity.AuditAutoResultDetail;
import com.jxntv.gvideo.om.entity.AuditAutoType;
import com.jxntv.gvideo.om.entity.AuditAutoTypeExt;
import com.jxntv.gvideo.om.enums.AuditAutoStatus;
import com.jxntv.gvideo.om.enums.SuggestionEnum;
import com.jxntv.gvideo.om.mapper.AuditAutoTypeMapper;
import com.jxntv.gvideo.om.sdk.AiCheckSdk;
import com.jxntv.gvideo.om.sdk.request.ImageReq;
import com.jxntv.gvideo.om.sdk.request.TextReq;
import com.jxntv.gvideo.om.sdk.request.VideoReq;
import com.jxntv.gvideo.om.sdk.request.VoiceReq;
import com.jxntv.gvideo.om.sdk.response.*;
import com.jxntv.gvideo.om.service.AuditAutoResultDetailService;
import com.jxntv.gvideo.om.service.AuditAutoResultService;
import com.jxntv.gvideo.om.service.AuditAutoTypeExtService;
import com.jxntv.gvideo.om.service.AuditAutoTypeService;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 智能审核关联表
 * @date 2021/07/07 8:31
 */
@Service
@Slf4j
public class AuditAutoTypeServiceImpl extends ServiceImpl<AuditAutoTypeMapper, AuditAutoType> implements AuditAutoTypeService {
    @Autowired
    private AuditAutoConvert auditAutoConvert;
    @Autowired
    private AuditAutoResultService auditAutoResultService;
    @Autowired
    private AuditAutoResultDetailService auditAutoResultDetailService;
    @Autowired
    private AuditAutoTypeExtService auditAutoTypeExtService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dealCheck(List<AuditAutoType> autoTypeList) {
        if (CollectionUtils.isEmpty(autoTypeList)) {
            throw new CodeMessageException(CodeMessage.AUDIT_CHECK, "无智能检测参数");
        }
        //文字检测
        TextReq textReq = auditAutoConvert.convertTextReq(autoTypeList);
        if (!CollectionUtils.isEmpty(textReq.getTasks())) {
            TextRsp textRsp = AiCheckSdk.checkText(textReq);
            if (Objects.isNull(textRsp)) {
                log.warn("图片检测结果返回异常: null, 请求数据：{}", JSON.toJSONString(textReq));
            } else {
                //入库结果
                this.saveCheckResult(textRsp);
            }
        }
        //图片检测
        ImageReq imageReq = auditAutoConvert.convertImageReq(autoTypeList);
        if (!CollectionUtils.isEmpty(imageReq.getTasks())) {
            ImageRsp imageRsp = AiCheckSdk.checkImage(imageReq);
            if (Objects.isNull(imageRsp)) {
                log.warn("图片检测结果返回异常: null, 请求数据：{}", JSON.toJSONString(imageReq));
            } else {
                //入库结果
                this.saveCheckResult(imageRsp);
            }
        }
        //视频检测
        VideoReq videoReq = auditAutoConvert.convertVideoReq(autoTypeList);
        if (!CollectionUtils.isEmpty(videoReq.getTasks())) {
            VideoRsp videoRsp = AiCheckSdk.checkVideo(videoReq);
            if (Objects.isNull(videoRsp)) {
                log.warn("视频检测上报返回异常: null, 请求数据：{}", JSON.toJSONString(videoReq));
            } else {
                //入库上报结果
                this.saveReportResult(videoRsp);
            }
        }
        //音频检测
        VoiceReq voiceReq = auditAutoConvert.convertVoiceReq(autoTypeList);
        if (!CollectionUtils.isEmpty(voiceReq.getTasks())) {
            VoiceRsp voiceRsp = AiCheckSdk.checkVoice(voiceReq);
            if (Objects.isNull(voiceRsp)) {
                log.warn("音频检测上报返回异常: null, 请求数据：{}", JSON.toJSONString(voiceReq));
            } else {
                //入库上报结果
                this.saveReportResult(voiceRsp);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveReportResult(SafeCheckRsp safeCheckRsp) {
        List<AuditAutoType> auditAutoTypeList = new ArrayList<>();
        if (safeCheckRsp instanceof VideoRsp) {
            VideoRsp videoRsp = (VideoRsp) safeCheckRsp;
            if (!CollectionUtils.isEmpty(videoRsp.getData())) {
                auditAutoTypeList.addAll(videoRsp.getData().stream().map(video -> {
                    AuditAutoType auditAutoType = new AuditAutoType();
                    auditAutoType.setId(Long.valueOf(video.getDataId()));
                    auditAutoType.setTaskId(video.getTaskId());
                    auditAutoType.setStatus(AuditAutoStatus.JOB_DOING.getCode());
                    return auditAutoType;
                }).collect(Collectors.toList()));
            }
        }
        if (safeCheckRsp instanceof VoiceRsp) {
            VoiceRsp voiceRsp = (VoiceRsp) safeCheckRsp;
            if (!CollectionUtils.isEmpty(voiceRsp.getData())) {
                auditAutoTypeList.addAll(voiceRsp.getData().stream().map(voice -> {
                    AuditAutoType auditAutoType = new AuditAutoType();
                    auditAutoType.setId(Long.valueOf(voice.getDataId()));
                    auditAutoType.setTaskId(voice.getTaskId());
                    auditAutoType.setStatus(AuditAutoStatus.JOB_DOING.getCode());
                    return auditAutoType;
                }).collect(Collectors.toList()));
            }
        }
        if (!CollectionUtils.isEmpty(auditAutoTypeList)) {
            if (!this.updateBatchById(auditAutoTypeList)) {
                throw new CodeMessageException(CodeMessage.CREATE_FAIL, "更新智能检测关联状态/任务ID失败");
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCheckResult(SafeCheckRsp safeCheckRsp) {
        List<AuditAutoResult> autoResultList = new ArrayList<>();
        if (safeCheckRsp instanceof TextRsp) {
            List<AuditAutoResult> textResultList = auditAutoConvert.convertTextRsp((TextRsp) safeCheckRsp);
            if (!CollectionUtils.isEmpty(textResultList)) {
                //保存过滤内容
                Map<Long, AuditAutoResult> auditAutoResultMap = textResultList.stream()
                        .filter(auditAutoResult -> !StringUtils.isEmpty(auditAutoResult.getFilterContent()))
                        .collect(Collectors.toMap(AuditAutoResult::getAuditAutoTypeId, auditAutoResult -> auditAutoResult));
                if (!CollectionUtils.isEmpty(auditAutoResultMap)) {
                    List<Long> auditTypeIds = auditAutoResultMap.keySet().stream().collect(Collectors.toList());
                    List<AuditAutoTypeExt> autoTypeExtList = auditAutoTypeExtService.list(Wrappers.<AuditAutoTypeExt>lambdaQuery().in(AuditAutoTypeExt::getAuditAutoTypeId, auditTypeIds));
                    if (!CollectionUtils.isEmpty(autoTypeExtList)) {
                        autoTypeExtList.forEach(auditAutoTypeExt -> {
                            if (auditAutoResultMap.containsKey(auditAutoTypeExt.getAuditAutoTypeId())) {
                                auditAutoTypeExt.setFilterContent(auditAutoResultMap.get(auditAutoTypeExt.getAuditAutoTypeId()).getFilterContent());
                            }
                        });
                        auditAutoTypeExtService.updateBatchById(autoTypeExtList);
                    }
                }
                autoResultList.addAll(textResultList);
            }
        }
        if (safeCheckRsp instanceof ImageRsp) {
            autoResultList.addAll(auditAutoConvert.convertImageRsp((ImageRsp) safeCheckRsp));
        }
        if (safeCheckRsp instanceof VideoAsyncRsp) {
            autoResultList.addAll(auditAutoConvert.convertVideoRsp((VideoAsyncRsp) safeCheckRsp));
        }
        if (safeCheckRsp instanceof VoiceAsyncRsp) {
            autoResultList.addAll(auditAutoConvert.convertVoiceRsp((VoiceAsyncRsp) safeCheckRsp));
        }
        if (!CollectionUtils.isEmpty(autoResultList)) {
            saveResult(autoResultList);
        }
    }

    /**
     * 保存结果
     *
     * @param autoResultList
     */
    private void saveResult(List<AuditAutoResult> autoResultList) {
        //保存结果
        if (!auditAutoResultService.saveBatch(autoResultList)) {
            throw new CodeMessageException(CodeMessage.CREATE_FAIL, "入库智能检测结果失败");
        }
        //保存详情
        List<AuditAutoResultDetail> auditAutoResultDetailList = autoResultList.stream()
                .filter(auditAutoResult -> !CollectionUtils.isEmpty(auditAutoResult.getDetailList()))
                .flatMap(auditAutoResult -> auditAutoResult.getDetailList().stream().map(auditAutoResultDetail -> {
                    auditAutoResultDetail.setAuditAutoResultId(auditAutoResult.getId());
                    return auditAutoResultDetail;
                }).collect(Collectors.toList()).stream())
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(auditAutoResultDetailList)) {
            auditAutoResultDetailService.saveBatch(auditAutoResultDetailList);
        }

        //按照任务分组，一个任务可能有多个场景，就会有多个结果
        Map<Long, List<AuditAutoResult>> auditAutoResultMap = autoResultList.stream().collect(Collectors.groupingBy(AuditAutoResult::getAuditAutoTypeId));
        List<AuditAutoType> auditAutoTypeList = new ArrayList<>(auditAutoResultMap.keySet().size());
        for (Long id : auditAutoResultMap.keySet()) {
            List<AuditAutoResult> auditAutoResultList = auditAutoResultMap.get(id);
            //获取列表检测结果，按照优先级 unknown > block > review > pass取第一个
            Integer suggestion = auditAutoResultList.stream()
                    .map(auditAutoResult -> auditAutoResult.getSuggestion())
                    .distinct()
                    .sorted(Comparator.reverseOrder())
                    .findFirst().orElse(SuggestionEnum.UNKNOWN.getCode());
            AuditAutoType auditAutoType = new AuditAutoType();
            auditAutoType.setId(id);
            SuggestionEnum suggestionEnum = SuggestionEnum.getByCode(suggestion);
            auditAutoType.setStatus(AuditAutoStatus.getBySuggestion(suggestionEnum).getCode());
            auditAutoTypeList.add(auditAutoType);
        }
        if (!CollectionUtils.isEmpty(auditAutoTypeList)) {
            if (!this.updateBatchById(auditAutoTypeList)) {
                throw new CodeMessageException(CodeMessage.CREATE_FAIL, "更新智能检测任务状态失败");
            }
        }
    }
}
