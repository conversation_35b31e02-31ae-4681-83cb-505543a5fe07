package com.jxntv.gvideo.om.converter.advert;

import com.jxntv.gvideo.om.dto.advert.AdvertContentLocationDTO;
import com.jxntv.gvideo.om.dto.advert.AdvertDTO;
import com.jxntv.gvideo.om.dto.advert.AdvertPoolContentDTO;
import com.jxntv.gvideo.om.entity.advert.Advert;
import com.jxntv.gvideo.om.entity.advert.AdvertContentLocation;
import com.jxntv.gvideo.om.entity.advert.AdvertPoolContent;
import com.jxntv.gvideo.om.service.advert.AdvertPoolContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AdvertConverter {

    @Resource
    private AdvertPoolContentService advertPoolContentService;


    public AdvertDTO convert(Advert entity) {

        AdvertDTO dto = new AdvertDTO();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setShowScene(entity.getShowScene());
        dto.setShowStartDate(entity.getShowStartDate());
        dto.setShowEndDate(entity.getShowEndDate());
        dto.setStatus(entity.getStatus());
        dto.setCreateDate(entity.getCreateDate());
        dto.setUpdateDate(entity.getUpdateDate());
        dto.setShowLocationName(entity.getShowLocationName());
        dto.setShowLocationType(entity.getShowLocationType());
        dto.setShowLocationId(entity.getShowLocationId());
        dto.setShowIndex(entity.getShowIndex());
        dto.setShowInterval(entity.getShowInterval());
        dto.setHiddenAfterRefresh(entity.getHiddenAfterRefresh());
        dto.setDailyViewLimit(entity.getDailyViewLimit());
        dto.setDailyClickLimit(entity.getDailyClickLimit());
        dto.setTotalViewLimit(entity.getTotalViewLimit());
        dto.setTotalClickLimit(entity.getTotalClickLimit());
        dto.setPutType(entity.getPutType());
        dto.setPutTarget(entity.getPutTarget());

        //  投放内容
        List<AdvertPoolContent> advertPoolContents = advertPoolContentService.listByAdvertId(entity.getId());
        List<AdvertPoolContentDTO> advertPoolContentDTOList = advertPoolContents.stream().map(this::convert).collect(Collectors.toList());
        dto.setPoolContents(advertPoolContentDTOList);

        return dto;
    }


    public AdvertPoolContentDTO convert(AdvertPoolContent entity) {
        AdvertPoolContentDTO dto = new AdvertPoolContentDTO();
        dto.setId(entity.getId());
        dto.setAdvertId(entity.getAdvertId());
        dto.setModelType(entity.getModelType());
        dto.setModelId(entity.getModelId());
        dto.setModelName(entity.getModelName());
        dto.setRate(entity.getRate());
        return dto;
    }


    public Advert convert(AdvertDTO dto) {
        Advert advert = new Advert();
        advert.setId(dto.getId());
        advert.setName(dto.getName());
        advert.setShowScene(dto.getShowScene());
        advert.setShowLocationName(dto.getShowLocationName());
        advert.setShowLocationType(dto.getShowLocationType());
        advert.setShowLocationId(dto.getShowLocationId());
        advert.setShowIndex(dto.getShowIndex());
        advert.setShowInterval(dto.getShowInterval());
        advert.setHiddenAfterRefresh(dto.getHiddenAfterRefresh());
        advert.setShowStartDate(dto.getShowStartDate());
        advert.setShowEndDate(dto.getShowEndDate());
        advert.setStatus(dto.getStatus());
        advert.setDailyViewLimit(dto.getDailyViewLimit());
        advert.setDailyClickLimit(dto.getDailyClickLimit());
        advert.setTotalViewLimit(dto.getTotalViewLimit());
        advert.setTotalClickLimit(dto.getTotalClickLimit());
        advert.setPutType(dto.getPutType());
        advert.setPutTarget(dto.getPutTarget());
        return advert;
    }


    public AdvertPoolContent convert(AdvertPoolContentDTO dto) {
        AdvertPoolContent entity = new AdvertPoolContent();
        entity.setId(dto.getId());
        entity.setAdvertId(dto.getAdvertId());
        entity.setModelType(dto.getModelType());
        entity.setModelName(dto.getModelName());
        entity.setRate(dto.getRate());
        entity.setModelId(dto.getModelId());
        return entity;

    }

    public AdvertContentLocationDTO convert(AdvertContentLocation entity) {
        AdvertContentLocationDTO dto = new AdvertContentLocationDTO();
        dto.setId(entity.getId());
        dto.setAdvertId(entity.getAdvertId());
        dto.setShowLocationType(entity.getShowLocationType());
        dto.setShowLocationId(entity.getShowLocationId());
        dto.setNum(entity.getNum());
        dto.setModelId(entity.getModelId());
        dto.setModelType(entity.getModelType());
        dto.setContentId(entity.getContentId());
        dto.setHiddenAfterRefresh(entity.getHiddenAfterRefresh());
        return dto;

    }

}
