<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.om.mapper.LiveInfoDaoMapper">

    <select id="queryActivityLiveByConditions" resultType="com.jxntv.gvideo.om.dto.ContentLivePoolDTO">
        SELECT a2.media_id as id, a2.title, a1.weight, a2.create_date FROM
        (SELECT id, weight FROM media_resource)a1
            JOIN
        (SELECT id, title, media_id, live_broadcast.`status`, created_date AS create_date  FROM live_broadcast WHERE certification_id=#{certId})a2
            ON a1.id = a2.media_id
        AND a2.status=#{status} LIMIT 0, #{nums};
    </select>


    <select id="queryInteractiveLiveByConditions" resultType="com.jxntv.gvideo.om.dto.ContentLivePoolDTO">
        SELECT a2.media_id as id, a2.title, a1.weight, a2.create_date FROM
        (SELECT id, weight FROM media_resource)a1
            JOIN
        (SELECT id, title, media_id, interactive_broadcast.`status`, created_date AS create_date FROM interactive_broadcast WHERE certification_id=#{certId})a2
            ON a1.id = a2.media_id AND a2.status=#{status} LIMIT 0, #{nums};

    </select>


    <select id="queryTVChannelByConditions" resultType="com.jxntv.gvideo.om.dto.ContentLivePoolDTO">
        SELECT id, channel_name, weight, create_date FROM tv_channel WHERE tv_channel.`status` = #{status} LIMIT 0, #{nums};
    </select>


</mapper>