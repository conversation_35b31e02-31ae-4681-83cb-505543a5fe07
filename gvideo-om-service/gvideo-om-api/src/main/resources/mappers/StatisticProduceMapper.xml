<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.om.mapper.StatisticProduceMapper">

    <select id = "getDayCount" resultType = "java.util.Map">
        SELECT
            DATE( statistic_date ) AS date,
            SUM(sum) AS sum
        FROM
            statistic_produce
        WHERE
            type = #{type}
        <if test="pgcId != null and pgcId != 0">
            AND pgc_id = #{pgcId}
        </if>
        <if test="mediaType != null and mediaType != 0">
            AND media_type = #{mediaType}
        </if>
          AND statistic_date BETWEEN #{start}
            AND #{end}
        GROUP BY
            date
    </select>

    <select id = "getMonthCount" resultType = "java.util.Map">
        SELECT
            DATE_FORMAT( statistic_date, '%Y-%m' ) AS date,
            SUM(sum) AS sum
        FROM
            statistic_produce
        WHERE
            type = #{type}
        <if test="pgcId != null and pgcId != 0">
            AND pgc_id = #{pgcId}
        </if>
        <if test="mediaType != null and mediaType != 0">
            AND media_type = #{mediaType}
        </if>
          AND statistic_date BETWEEN #{start}
            AND #{end}
        GROUP BY
            date
    </select>

    <select id = "getWeekCount" resultType = "java.util.Map">
        SELECT
            DATE_FORMAT( statistic_date, '%x-W%v' ) AS date,
            SUM(sum) AS sum
        FROM
            statistic_produce
        WHERE
            type = #{type}
        <if test="pgcId != null and pgcId != 0">
            AND pgc_id = #{pgcId}
        </if>
        <if test="mediaType != null and mediaType != 0">
            AND media_type = #{mediaType}
        </if>
          AND statistic_date BETWEEN #{start}
            AND #{end}
        GROUP BY
            date;
    </select>

    <select id = "getHourCount" resultType = "java.util.Map">
        SELECT
            statistic_date as date,
            clock,
            sum( sum ) as sum
        FROM
            statistic_produce
        WHERE
            type = #{type}
        <if test="pgcId != null and pgcId != 0">
            AND pgc_id = #{pgcId}
        </if>
        <if test = "mediaType != null and mediaType != 0">
            AND media_type = #{mediaType}
        </if>
          AND statistic_date IN
        <foreach collection="compare" item="i" open="(" close=")" separator=",">
            #{i}
        </foreach>
        GROUP BY
            date,
            clock
    </select>

    <select id = "getMediaCount" resultType = "java.util.Map">
        SELECT
            pgc_id,
            SUM( sum ) AS sum
        FROM
            statistic_produce
        WHERE
            type = #{type}
            <if test="pgcId != null and pgcId != 0">
                AND pgc_id = #{pgcId}
            </if>
            <if test = "mediaType != null and mediaType != 0">
                AND media_type = #{mediaType}
            </if>
            <if test = "start != null and end != null">
                AND statistic_date BETWEEN #{start} AND #{end}
            </if>
        GROUP BY
            pgc_id
        ORDER BY
            sum DESC LIMIT 10
    </select>

</mapper>