<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.om.mapper.ContentPoolEntityMapper">

    <select id="updateBlackFlag">
        UPDATE content_pool_table
        SET black_flag = #{status}, update_time = #{updateTime}
        WHERE model_id = #{modelId} AND content_id=#{contentId};
    </select>


    <select id="selectOneByModelId" resultType="com.jxntv.gvideo.om.entity.ContentPoolEntity">
        select * from content_pool_table
        where model_id=#{modelId} and black_flag = 0
        order by update_time
        limit 0,1;
    </select>

    <select id="queryContentPoolEntityByPoolIdAndContentId" resultType="com.jxntv.gvideo.om.entity.ContentPoolEntity">
        select * from content_pool_table
        where pool_id=#{poolId} and content_id= #{contentId};
    </select>

    <select id="queryContentPoolEntityByModelIdAndContentId" resultType="com.jxntv.gvideo.om.entity.ContentPoolEntity">
        select * from content_pool_table
        where model_id=#{modelId} and content_id= #{contentId};
    </select>


    <select id="deleteNonBlackEntityByModelId">
        delete from content_pool_table where model_id=#{poolId} and black_flag = 0;
    </select>


</mapper>