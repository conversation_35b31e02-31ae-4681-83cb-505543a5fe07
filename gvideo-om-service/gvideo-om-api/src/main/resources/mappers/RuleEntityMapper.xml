<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jxntv.gvideo.om.mapper.RuleEntityMapper">

   <select id="queryRuleEntityByModelIdAndRuleId" resultType="com.jxntv.gvideo.om.entity.RuleEntity">
       SELECT *
       FROM rule_table
       WHERE model_id=#{modelId}
       AND id=#{ruleId};
   </select>
    
    <select id="queryRuleEntityListByModelId" resultType="com.jxntv.gvideo.om.entity.RuleEntity">
        SELECT * FROM rule_table WHERE model_id=#{modelId} ORDER BY update_time DESC;
    </select>


</mapper>