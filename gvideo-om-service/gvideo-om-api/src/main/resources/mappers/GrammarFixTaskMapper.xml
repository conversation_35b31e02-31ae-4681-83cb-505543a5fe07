<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jxntv.gvideo.om.mapper.GrammarFixTaskMapper">
  <resultMap id="BaseResultMap" type="com.jxntv.gvideo.om.entity.GrammarFixTask">
    <!--@mbg.generated-->
    <!--@Table grammar_fix_task-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="media_id" jdbcType="BIGINT" property="mediaId" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="html_link" jdbcType="VARCHAR" property="htmlLink" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="content_date" jdbcType="TIMESTAMP" property="contentDate" />
    <result column="task_result" jdbcType="LONGVARCHAR" property="taskResult" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, media_id, show_name, html_link, content, content_date, task_result, create_time
  </sql>
</mapper>