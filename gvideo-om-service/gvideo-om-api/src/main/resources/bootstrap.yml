server:
  port: 8016  #服务端口号
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: om-service #服务名称--调用的时候根据名称来调用该服务的方法
  profiles:
    active: @profiles.active@
mybatis-plus:
  global-config:
    db-config:
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath:mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
  type-enums-package: com.jxntv.gvideo.recommend.sdk.dto

management:
  endpoint:
    health:
      show-details: always # 显示健康详情
feign:
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connect-timeout: 10000
        read-timeout: 10000
        loggerLevel: BASIC
  compression:
    request:
      enabled: true
    response:
      enabled: true
---

spring:
  profiles: local
  cloud:
    nacos:
      config:
        server-addr: 10.72.231.190:8848
        namespace: 4fb4c8f9-e8ff-44df-9c61-222899464866
        file-extension: yaml

---
spring:
  profiles: dev
  cloud:
    nacos:
      config:
        server-addr: nacos.dev-base:8848
        file-extension: yaml
---

spring:
  profiles: test
  cloud:
    nacos:
      config:
        server-addr: nacos.test-base:8848
        file-extension: yaml
---

spring:
  profiles: pre
  cloud:
    nacos:
      config:
        server-addr: nacos.pre-base:8848
        file-extension: yaml

---

spring:
  profiles: uat
  cloud:
    nacos:
      config:
        server-addr: 192.168.6.32:8848
        file-extension: yaml
---
spring:
  profiles: prod
  cloud:
    nacos:
      config:
        server-addr: mse-bd718ea6-nacos-ans.mse.aliyuncs.com:8848
        file-extension: yaml
        group: jsp
        namespace: fdfd2bd1-ccd0-47fa-ae00-3f68b99551f5
---