package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created on 2020-01-13
 */
@Data
@ApiModel(value = "CommentVO", description = "评论对象")
public class CommentVO {

    @ApiModelProperty(value = "评论唯一标识", required = true, example = "100000001")
    private Long primaryId;
    @ApiModelProperty(value = "评论用户", required = true)
    private ConsumerAuthorVO commentUser;
    @ApiModelProperty(value = "评论内容", required = true, example = "你好")
    private String content;
    @ApiModelProperty(value = "评论时间", required = true, example = "2020-01-12 20:00:00")
    private Date commentDate;
    @ApiModelProperty(value = "资源信息")
    private MediaVo media;
    @ApiModelProperty(value = "相关回复")
    private List<ReplyVO> replies;
    @ApiModelProperty(value = "评论图片")
    private List<String> imageList;
    @ApiModelProperty(value = "源评论图片")
    private List<String> oriImageList;
    @ApiModelProperty(value = "关联语音")
    private String soundUrl;
    @ApiModelProperty(value = "关联语音文字")
    private String soundContent;
    private String length;
    @ApiModelProperty(value = "是否点赞")
    private Boolean isPraise;
    @ApiModelProperty(value = "点赞数量")
    private Integer praiseTotal;
    @ApiModelProperty(value = "问答类型：0-评论回复、1-/回复回答")
    private Integer answerId;
    @ApiModelProperty(value = "1自己的回答")
    private Integer self;
    @ApiModelProperty(value = "评论IP地址")
    private String ip;
    @ApiModelProperty(value = "评论IP归属地")
    private String ipLocation;

    @Data
    @ApiModel(value = "MediaVo", description = "资源信息")
    public static class MediaVo {
        @ApiModelProperty(value = "资源栏目/分类")
        private Long categoryId;
        @ApiModelProperty(value = "资源ID")
        private Long mediaId;
        @ApiModelProperty(value = "资源标题")
        private String title;
        @ApiModelProperty(value = "资源封面")
        private String thumburl;
        @ApiModelProperty(value = "资源状态")
        private Integer status;
    }

    @Data
    @ApiModel(value = "ReplyVO", description = "回复对象")
    public static class ReplyVO {
        @ApiModelProperty(value = "回复唯一标识", required = true)
        private Long replyId;
        @ApiModelProperty(value = "回复用户", required = true)
        private ConsumerAuthorVO user;
        @ApiModelProperty(value = "被回复用户", required = true)
        private ConsumerAuthorVO toUser;
        @ApiModelProperty(value = "回复内容", required = true)
        private String replyContent;
        @ApiModelProperty(value = "回复时间", required = true)
        private Date replyDate;
        @ApiModelProperty(value = "评论图片")
        private List<String> imageList;
        @ApiModelProperty(value = "源评论图片")
        private List<String> oriImageList;
        @ApiModelProperty(value = "关联语音")
        private String soundUrl;
        @ApiModelProperty(value = "关联语音文字")
        private String soundContent;
        private String length;
        @ApiModelProperty(value = "是否点赞")
        private Boolean isPraise;
        @ApiModelProperty(value = "点赞数量")
        private Integer praiseTotal;
        @ApiModelProperty(value = "评论IP地址")
        private String ip;
        @ApiModelProperty(value = "评论IP归属地")
        private String ipLocation;

    }

    public boolean authUserReplay() {
        ConsumerAuthorVO user = this.getCommentUser();
        if (Objects.nonNull(user)) {
            return user.getIsAuthentication();
        }
        return false;
    }
}
