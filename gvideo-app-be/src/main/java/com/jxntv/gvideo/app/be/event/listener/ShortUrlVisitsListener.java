package com.jxntv.gvideo.app.be.event.listener;

import com.jxntv.gvideo.aliyun.sdk.ShortUrlClient;
import com.jxntv.gvideo.aliyun.sdk.dto.shortUrl.ShortUrlVisitsDto;
import com.jxntv.gvideo.app.be.event.ShortUrlVisitsEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 短链访问上报
 * <AUTHOR>
 * @date 2023/5/17 9:31
 */
@Slf4j
@Component
public class ShortUrlVisitsListener {

    @Resource
    private ShortUrlClient shortUrlClient;

    @Async
    @EventListener
    public void onEvent(ShortUrlVisitsEvent event) {
        ShortUrlVisitsDto dto = new ShortUrlVisitsDto();
        dto.setId(event.getId());
        dto.setAccessIp(event.getIp());
        dto.setAccessUserAgent(event.getUserAgent());
        shortUrlClient.visits(dto);

    }

}
