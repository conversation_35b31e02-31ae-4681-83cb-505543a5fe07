package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: niedamin
 * @Date: 2023/07/18 10:40
 */
@Data
@ApiModel(value = "OpeninstallChannelVO", description = "渠道设备信息")
public class OpeninstallChannelVO {

    @ApiModelProperty(value = "设备唯一特征信息，md5字符串")
    private String du;

    @ApiModelProperty(value = "内网ip特征信息，md5字符串")
    private String pu;

    @ApiModelProperty(value = "渠道码")
    private Long cd;
}
