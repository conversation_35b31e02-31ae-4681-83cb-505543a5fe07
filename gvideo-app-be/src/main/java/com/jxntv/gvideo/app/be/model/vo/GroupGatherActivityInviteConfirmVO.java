package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupGatherActivityInviteConfirmVO", description = "活动组件-约会计划-邀请-确认")
public class GroupGatherActivityInviteConfirmVO {

    @ApiModelProperty(value = "邀请ID")
    @NotNull(message = "活动ID不能为空")
    private Long inviteId;
}
