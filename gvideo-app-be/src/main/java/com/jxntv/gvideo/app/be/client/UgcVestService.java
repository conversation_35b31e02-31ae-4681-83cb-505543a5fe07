package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.UgcVestClientFallback;
import com.jxntv.gvideo.user.client.UgcVestClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> Created on 2021/9/18.
 */
@FeignClient(value = "user-service", contextId = "ugc-vest", fallbackFactory = UgcVestClientFallback.class)
public interface UgcVestService extends UgcVestClient {
}
