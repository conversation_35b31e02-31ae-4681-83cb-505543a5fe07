package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.manager.GroupManager;
import com.jxntv.gvideo.app.be.manager.GroupMedalManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.GroupTopicVO;
import com.jxntv.gvideo.app.be.utils.LocalDateTimeUtils;
import com.jxntv.gvideo.app.be.utils.ShareUrlUtils;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupMedalDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupTopicDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupMedalImageSizeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class GroupTopicConverter {

    @Resource
    private OssManager ossManager;
    @Resource
    private GroupManager groupManager;
    @Resource
    private GroupConverter groupConverter;
    @Resource
    private GroupMedalManager groupMedalManager;
    @Resource
    private GroupMedalConvert groupMedalConvert;

    public GroupTopicVO convert(GroupTopicDTO dto) {
        String groupCoverUrl = "";
        GroupInfoDTO simpleGroupInfo = groupManager.getSimpleById(dto.getGroupId());
        if (Objects.nonNull(simpleGroupInfo)) {
            groupCoverUrl = ossManager.getAvatar(simpleGroupInfo.getCover()).map(OssDTO::getUrl).orElse("");
        }

        GroupTopicVO vo = new GroupTopicVO();
        vo.setGroupId(dto.getGroupId());
        vo.setGroupCoverUrl(groupCoverUrl);
        vo.setId(dto.getId());
        vo.setCode(dto.getCode());
        vo.setType(dto.getType());
        vo.setContent(dto.getContent());

        vo.setWeight(dto.getWeight());
        vo.setShowInNewList(dto.getShowInNewList());
        vo.setStatus(dto.getStatus());
        vo.setCreateDate(LocalDateTimeUtils.ms(dto.getCreateDate()));
        vo.setLabels(dto.getLabelsStr());
        vo.setGroupName(dto.getGroupName());
        vo.setJoin(dto.getJoin());
        vo.setAuthName(dto.getAuthName());
        vo.setShareUrl(ShareUrlUtils.generateShareUrl("topic", dto.getId()));
        vo.setTenantId(dto.getTenantId());
        vo.setTenantName(dto.getTenantName());
        if (Objects.nonNull(dto.getGatherDTO())) {
            vo.setGather(groupConverter.toGatherVo(dto.getGatherDTO()));
        }
        return vo;
    }

    public GroupTopicVO convertGroupMedal(GroupTopicDTO dto) {
        List<GroupMedalDTO> list = groupMedalManager.list(dto.getAuthJid(), dto.getGroupId());
        GroupTopicVO groupTopicVO = this.convert(dto);
        try {
            groupTopicVO.setMedals(groupMedalConvert.convert(list, GroupMedalImageSizeEnum.SMALL.getCode()));
            groupTopicVO.setIsNeedShowRedName(groupMedalManager.isNeedShowRedName(dto.getAuthJid(), dto.getGroupId()));
        } catch (Exception ex) {
            log.error("convertGroupMedal error.", ex);
        }
        return groupTopicVO;
    }
}
