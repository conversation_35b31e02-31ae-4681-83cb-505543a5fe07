package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.InteractFollowPeomTodayInfoVO;
import com.jxntv.gvideo.app.be.model.vo.InteractFollowPeomTodayVO;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.dto.FollowIncrementalStatisticsDTO;
import com.jxntv.gvideo.interact.client.dto.FollowIncrementalStatisticsInfoDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: niedamin
 * @Date: 2023/07/19 11:18
 */
@Slf4j
@Component
public class FollowIncrementalStatisticsConvert {

    @Autowired
    private ConsumerUserManager consumerUserManager;

    @Autowired
    private OssManager ossManager;

    public InteractFollowPeomTodayInfoVO convertTodayInfoVO(FollowIncrementalStatisticsInfoDTO dto) {
        InteractFollowPeomTodayInfoVO vo = new InteractFollowPeomTodayInfoVO();
        if (Objects.nonNull(dto.getCurrentUser())) {
            vo.setCurrent(convertTodayVO(dto.getCurrentUser()));
        }

        vo.setRecord(this.convertPageTodayVO(dto.getList()));
        return vo;
    }

    public PageDTO<InteractFollowPeomTodayVO> convertPageTodayVO(PageDTO<FollowIncrementalStatisticsDTO> dto) {
        PageDTO<InteractFollowPeomTodayVO> vo = new PageDTO<>();
        vo.setPageNum(dto.getPageNum());
        vo.setPageSize(dto.getPageSize());
        vo.setTotal(dto.getTotal());

        if (!CollectionUtils.isEmpty(dto.getList())) {
            List<InteractFollowPeomTodayVO> list = new ArrayList<>(dto.getList().size());
            for (FollowIncrementalStatisticsDTO recordDTO : dto.getList()) {
                list.add(convertTodayVO(recordDTO));
            }
            vo.setList(list);
        }
        return vo;
    }

    public InteractFollowPeomTodayVO convertTodayVO(FollowIncrementalStatisticsDTO dto) {
        InteractFollowPeomTodayVO vo = new InteractFollowPeomTodayVO();
        vo.setJid(dto.getAuthorId());
        vo.setCnt(dto.getCnt());
        vo.setSerialNo(dto.getSerialNo());
        vo.setStatisticTime(dto.getStatisticTime());

        ConsumerUserDTO result = consumerUserManager.getByJid(dto.getAuthorId());
        if (Objects.nonNull(result)) {
            vo.setNickname(result.getNickname());
            if (StringUtils.hasText(result.getAvatar())) {
                Result<OssDTO> avatar = ossManager.getAvatar(result.getAvatar());
                if (avatar.callSuccess() && Objects.nonNull(avatar.getResult()) && StringUtils.hasText(avatar.getResult().getUrl())) {
                    vo.setAvatarUrl(avatar.getResult().getUrl());
                }
            }
        }
        return vo;
    }
}
