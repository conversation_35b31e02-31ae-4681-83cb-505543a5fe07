package com.jxntv.gvideo.app.be.model.vo.pendant;

import com.jxntv.gvideo.app.be.common.vo.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "UserPendantInfoVO", description = "用户挂件信息")
public class UserPendantInfoVO {


    @ApiModelProperty(value = "佩戴信息")
    private PendantInfoVO wearInfo;

    @ApiModelProperty(value = "挂件列表")
    private Page<PendantInfoVO> record;

    @ApiModelProperty(value = "总数")
    private long total;

}
