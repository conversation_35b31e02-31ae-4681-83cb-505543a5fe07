package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.SpreadClient;
import com.jxntv.gvideo.media.client.dto.SpreadDTO;
import com.jxntv.gvideo.media.client.dto.SpreadLogDTO;
import com.jxntv.gvideo.media.client.dto.search.SpreadSearchDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SpreadClientFallback implements FallbackFactory<SpreadClient> {
    @Override
    public SpreadClient create(Throwable throwable) {
        return new SpreadClient() {
            @Override
            public Result<PageDTO<SpreadDTO>> page(SpreadSearchDTO dto) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> create(SpreadDTO dto) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<SpreadDTO> get(Long id) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<SpreadLogDTO>> logs(Long id, long current, long size) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result update(Long id, SpreadDTO dto) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result reset() {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
