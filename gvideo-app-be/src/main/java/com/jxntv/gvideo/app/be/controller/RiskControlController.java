package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.service.RiskControlService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.track.sdk.report.AppTrack;
import com.jxntv.gvideo.track.sdk.report.AppTrackType;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 风控判定接口
 */
@RequestMapping("/api/risk")
@RestController
public class RiskControlController {
    @Resource
    private RiskControlService riskControlService;

    @GetMapping("/control")
    @ApiOperation(value = "风控查询接口", notes = "判定用户是否被风控")
    @AppTrack(type = AppTrackType.VOTE_INTERCEPTION)
    public Result<UserInfo> getUserInfo() {
        return riskControlService.getUserInfo();
    }
}
