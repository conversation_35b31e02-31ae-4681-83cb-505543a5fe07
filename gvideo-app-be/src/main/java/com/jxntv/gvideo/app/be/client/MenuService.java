package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.SysMenuClientFallback;
import com.jxntv.gvideo.user.client.SysMenuClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> Created on 2021/3/3.
 */
@FeignClient(value = "user-service", contextId = "menu", fallbackFactory = SysMenuClientFallback.class)
public interface MenuService extends SysMenuClient {
}
