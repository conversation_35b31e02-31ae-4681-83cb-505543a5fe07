package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.farm.*;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.farm.sdk.FarmClient;
import com.jxntv.gvideo.farm.sdk.FarmTaskClient;
import com.jxntv.gvideo.farm.sdk.dto.*;
import com.jxntv.gvideo.farm.sdk.enums.TaskTypeEnum;
import com.jxntv.gvideo.farm.sdk.param.TaskProgressCurrentQueryParam;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FarmConverter {

    @Resource
    private FarmClient farmClient;
    @Resource
    private FarmTaskClient farmTaskClient;
    @Autowired
    private ConsumerUserManager consumerUserManager;
    @Resource
    private OssManager ossManager;

    public FarmerVO convert(FarmerDTO dto) {
        FarmerVO vo = new FarmerVO();
        vo.setId(dto.getId());
        vo.setJid(dto.getJid());
        vo.setEggsBalance(dto.getEggsBalance());
        vo.setFeedBalance(dto.getFeedBalance());
        vo.setToBeClaimed(farmTaskClient.toBeClaimed(dto.getId()).orElse(0));
        vo.setSigned(farmTaskClient.isSign(dto.getId()).orElse(false));
        return vo;
    }

    public ChickenVO convert(ChickenDTO dto) {

        FeedProgressDTO feedProgressDTO = farmClient.getChickenFeedProgress(dto.getId()).orElseThrow();

        ChickenVO vo = new ChickenVO();
        vo.setId(dto.getId());
        vo.setName(dto.getName());
        vo.setFeedProgressCurrent(feedProgressDTO.getCurrent());
        vo.setFeedProgressTotal(feedProgressDTO.getTotal());
        vo.setFeedCost(feedProgressDTO.getFeedCost());
        return vo;
    }


    public FarmTaskVO convert(TaskDTO dto, FarmerDTO farmer) {

        FarmTaskVO vo = new FarmTaskVO();
        vo.setTaskId(dto.getId());
        vo.setStyle(dto.getStyle());
        vo.setType(dto.getType());
        vo.setTitle(dto.getName());
        vo.setDesc(dto.getDescription());
        vo.setIconUrl(ossManager.getOriFile(dto.getIconId()).map(OssDTO::getUrl).orElse(""));

        //  待领取奖励列表
        List<TaskRewardIssuanceDTO> rewardIssuanceDTOList = farmTaskClient.getUnclaimedList(farmer.getId()).orElse(Collections.emptyList());
        Map<Long, TaskRewardIssuanceDTO> rewardMap = rewardIssuanceDTOList.stream().collect(Collectors.toMap(TaskRewardIssuanceDTO::getTaskId, e -> e, (e1, e2) -> e2));

        //  当前进度信息
        TaskProgressCurrentQueryParam taskProgressCurrentQueryParam = new TaskProgressCurrentQueryParam();
        taskProgressCurrentQueryParam.setFarmerId(farmer.getId());
        taskProgressCurrentQueryParam.setTaskId(dto.getId());
        TaskProgressDTO currentProgress = farmTaskClient.getCurrentTaskProgress(taskProgressCurrentQueryParam).getResult();


        if (Objects.nonNull(currentProgress)) {
            //  判断是否完成
            int status = currentProgress.getProgress() < currentProgress.getProgressTotal() ? 0 : 2;

            vo.setProgress(currentProgress.getProgress());
            vo.setProgressTotal(currentProgress.getProgressTotal());
            vo.setStatus(status);
        } else {
            vo.setProgress(0);
            vo.setProgressTotal(1);
            vo.setStatus(0);
        }

        //  如果有对应奖励的发放ID，设置为status=1 待领取奖励状态
        if (rewardMap.containsKey(dto.getId())) {

            TaskRewardIssuanceDTO issuanceDTO = rewardMap.get(dto.getId());
            vo.setRewardIssuanceId(issuanceDTO.getId());
            vo.setStatus(1);
        }

        //  限时领取，如果不在领取时间内，屏蔽掉按钮
        if (TaskTypeEnum.TIMED_OPEN.name().equals(dto.getType())) {
            boolean matched = false;
            TaskTimedOpenSpecDTO taskTimedOpenSpecDTO = JsonUtils.fromObj(dto.getSpec(), TaskTimedOpenSpecDTO.class);
            List<TaskTimedOpenSpecDTO.SpecItem> items = taskTimedOpenSpecDTO.getItems();

            if (!CollectionUtils.isEmpty(items)) {
                for (TaskTimedOpenSpecDTO.SpecItem item : items) {
                    LocalTime startTime = item.getStartTime();
                    LocalTime endTime = item.getEndTime();

                    if (LocalTime.now().isAfter(startTime) && LocalTime.now().isBefore(endTime)) {
                        matched = true;
                        break;
                    }

                }
            }

            if (!matched) {
                vo.setStatus(-1);
            }
        }


        return vo;
    }

    public EggsCollectionVO convert(EggsCollectionDTO dto) {
        EggsCollectionVO vo = new EggsCollectionVO();
        vo.setEggs(dto.getEggs());
        vo.setCollectDate(dto.getCollectDate());
        return vo;
    }


    public GoodsExchangeHisVO convert(GoodsExchangeDTO dto) {
        GoodsExchangeHisVO eggsExchangeVO = new GoodsExchangeHisVO();
        eggsExchangeVO.setGoodsName(dto.getGoodsName());
        eggsExchangeVO.setReceiverName(dto.getReceiverName());
        eggsExchangeVO.setReceiverMobile(dto.getReceiverMobile());
        eggsExchangeVO.setReceiverAddress(dto.getReceiverAddress());
        eggsExchangeVO.setStatus(dto.getStatus());
        eggsExchangeVO.setExchangeDate(dto.getExchangeDate());
        return eggsExchangeVO;
    }

    public NewUserRewardVO convert(NewUserRewardIssuanceDTO dto) {
        if (Objects.isNull(dto)){
            return null;
        }
        NewUserRewardVO vo = new NewUserRewardVO();
        vo.setId(dto.getId());
        vo.setRewardFeed(dto.getRewardFeed());

        ConsumerUserDTO consumerDto = consumerUserManager.getByJid(dto.getInviterJid());
        if (Objects.nonNull(consumerDto)) {
            vo.setInviterNickname(consumerDto.getNickname());
            if (StringUtils.hasText(consumerDto.getAvatar())) {
                vo.setInviterAvatar(ossManager.getOssFile(consumerDto.getAvatar()).map(OssDTO::getUrl).orElse(""));
            }
        }
        return vo;
    }

    public InviteInfoVO convert(TaskInviteDTO dto) {
        if (Objects.isNull(dto)){
            return null;
        }
        InviteInfoVO vo = new InviteInfoVO();
        vo.setInviteFeed(dto.getInviteFeed());
        vo.setInviteCount(dto.getInviteCount());
        vo.setRewardFeedAmount(dto.getRewardFeedAmount());
        return vo;
    }
}
