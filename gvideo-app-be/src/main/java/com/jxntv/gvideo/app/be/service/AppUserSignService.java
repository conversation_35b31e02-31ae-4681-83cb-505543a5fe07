package com.jxntv.gvideo.app.be.service;

import com.jxntv.gvideo.app.be.config.JspBeanConfig;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AppSignPrizeDTO;
import com.jxntv.gvideo.media.client.dto.PrizeExchangeDTO;
import com.jxntv.gvideo.media.client.dto.UserJspBeanDetailDTO;

import java.util.List;
import java.util.Map;

/**
 * @Author: niedamin
 * @Date: 2023/04/20 17:42
 */
public interface AppUserSignService {

    Result<Integer> sign(Long jid,Long t);

    Result<Map<String, Boolean>> getMonthSign(Long jid, String dateStr);

    Result<AppUserSignRankListVO> getRankList(Long jid);

    Result<PageDTO<AppSignPrizeDTO>> getPrizeList(Integer pageNum, Integer pageSize,Integer orderType);

    Result<PageDTO<UserJspBeanDetailDTO>> getMyJspBeanDetail(Long jid, Integer pageNum, Integer pageSize, Integer type);

    Result<PageDTO<AppSignOrderVO>> getMyPrize(Long jid, Integer pageNum, Integer pageSize);

    Result<String> exchange(PrizeExchangeDTO dto);

    Result<AppSignOrderVO> getOrderDetail(String orderNo,boolean qrcode);

    Result<AppUserSignInfoVO> getSignInfo(Long jid);

    Result<AppSignPrizeDTO> getById(Long jid, Long id);

    Result<Boolean> qrcodeWriteOff(String code,String orderNo);

    Result<UserJspBeanInviteStatVO> inviteStat(Long jid);

    Result<AppWatchTaskDurationVO> reportWatchDuration(Long jid,Integer duration);

    Result<AppWatchTaskInfoVO> getMyWatchTaskInfo(UserInfo userInfo);

    Result<AppWatchTaskDurationV2VO> reportWatchDurationV2(Long jid,Integer duration,Long t);

    Result<AppWatchTaskPopVO> getWatchPop(Long jid);

    Result<Boolean> updateWatchPopStatus(Long jid);

    Result<AppUserSignRaffleInfoVO> myRaffleInfo(Long jid);

    Result<AppUserSignRaffleResVO> startRaffle(Long jid);

    /**
     * 补充额外抽奖信息
     * @param additionalVO
     * @return
     */
    Result<Boolean> raffleCompleteInfo(AppUserSignRaffleCompleteVO additionalVO);

    List<JspBeanConfig.WatchRule> getWatchRules(Long t);


    /**
     * 获取今日观看时长
     * @param jid
     * @return
     */
    int getDailyWatchDuration(Long jid);

}
