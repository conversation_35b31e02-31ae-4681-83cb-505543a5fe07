package com.jxntv.gvideo.app.be.service.im.callback;


import com.jxntv.gvideo.aliyun.sdk.ImClient;
import com.jxntv.gvideo.app.be.model.dto.im.IMCallbackRequestDTO;
import com.jxntv.gvideo.app.be.model.dto.im.IMCallbackResponseDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.ConsumerSettingsClient;
import com.jxntv.gvideo.user.client.PushClient;
import com.jxntv.gvideo.user.client.dto.ConsumerNotificationSettingsDTO;
import com.jxntv.gvideo.user.client.dto.PushMsgDTO;
import com.jxntv.gvideo.user.client.enums.PushMsgContentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 群里之后回调
 */
@Slf4j
@Component
public  class GroupChatAfterSendMsgCallbackHandler implements IMCallbackHandler {

    @Resource
    private PushClient pushClient;
    @Resource
    private ImClient imClient;
    @Resource
    private ConsumerSettingsClient consumerSettingsClient;

    @Override
    public String getCallbackCommand() {
        return "Group.CallbackAfterSendMsg";
    }

    @Override
    public IMCallbackResponseDTO handle(IMCallbackRequestDTO callbackBody) {
        String type = callbackBody.getType();
        if ("Public".equals(type)) {
            PushMsgDTO msgDTO = buildGroupChatPushMsg(callbackBody);
            if (!CollectionUtils.isEmpty(msgDTO.getToIds())) {
                pushClient.pushMsg(msgDTO);
            }
        }

        IMCallbackResponseDTO resultVO = new IMCallbackResponseDTO();
        resultVO.setActionStatus("OK");
        resultVO.setErrorInfo("");
        resultVO.setErrorCode(0);

        return resultVO;
    }

    /**
     * 用户是否开启是否开启私信通知功能
     *
     * @param toJid 被通知用户
     * @return 是否开启通知设置
     */
    private boolean openImNotification(Long toJid) {

        Result<ConsumerNotificationSettingsDTO> settingsResult = consumerSettingsClient.getNotificationSettingsByJid(toJid);

        return settingsResult.callSuccess() && settingsResult.map(ConsumerNotificationSettingsDTO::getImNotificationOpen).orElse(true);
    }


    private PushMsgDTO buildGroupChatPushMsg(IMCallbackRequestDTO callbackBody) {
        String groupId = callbackBody.getGroupId();
        List<String> accounts = imClient.listGroupUserIds(groupId).orElse(Collections.emptyList());

        List<Long> toIds = accounts.stream().map(Long::valueOf).filter(this::openImNotification).collect(Collectors.toList());


        //push消息
        PushMsgDTO msgDTO = new PushMsgDTO();
        msgDTO.setType(0);
        msgDTO.setUserType(1);
        msgDTO.setContentId(-1L);
        msgDTO.setContentType(PushMsgContentTypeEnum.IM_CHAT_NOTIFICATION.getCode());
        msgDTO.setToType(1);
        msgDTO.setToIds(toIds);
        msgDTO.setContent("您有未读群聊消息");

        msgDTO.setLinkUrl(String.format("jinshipin://im?type=%s&nickname=%s&groupId=%s", "group","",groupId));
        msgDTO.setUserId(0L);
        msgDTO.setUsername("系统管理员");


        return msgDTO;
    }




}



