
package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户抽奖结果
 * 
 * <AUTHOR>
 * @date 2024/1/30 11:04
 */
@Data
@ApiModel
public class ActivityLotteryResVO implements Serializable {

    @ApiModelProperty("奖品ID")
    private Long prizeId;

    @ApiModelProperty("奖金")
    private String prize;

    @ApiModelProperty("类型 1-现金 2-今豆 3-优惠券 4-门票")
    private Integer type;

    @ApiModelProperty("类型 现金 今豆 优惠券 门票")
    private String prizeName;

    @ApiModelProperty("奖品图片")
    private String prizeCover;

    @ApiModelProperty("优惠券券码（如果有）")
    private String couponCode;

    @ApiModelProperty("抽奖日志ID")
    private Long logId;

    @ApiModelProperty("奖品序号")
    private String prizeCode;
}
