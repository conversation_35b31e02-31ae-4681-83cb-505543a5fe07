package com.jxntv.gvideo.app.be.track;

import com.jxntv.gvideo.app.be.client.AppTrackReportService;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.track.sdk.report.AppTrackReportBO;
import com.jxntv.gvideo.track.sdk.vo.appbe.AppTrackReportConverter;
import com.jxntv.gvideo.track.sdk.vo.appbe.EventRecordAppbeVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/30
 */
@Service
@Slf4j
public class AppTrackAnalyticsServiceImpl implements AppTrackAnalyticsService {

    @Resource
    AppTrackReportService  trackReportService;


    @Override
    public void report(String eventName, Map<String, Object> eventProp, AppTrackReportBO reportBO) {
        EventRecordAppbeVO  vo = new EventRecordAppbeVO();
        vo.setEventName(eventName);
        vo.setEventProp(eventProp);
        vo.setEventTime(System.currentTimeMillis());
        vo.setEventCommon(AppTrackReportConverter.toCommon(reportBO));
        try {
            trackReportService.appbeTrack(vo);
            log.info("【自建埋点上报】：{}", JsonUtils.toJson(vo));
        } catch (Exception e) {
            log.error("【自建埋点上报】：{}", e.getLocalizedMessage());
        }
    }
}

