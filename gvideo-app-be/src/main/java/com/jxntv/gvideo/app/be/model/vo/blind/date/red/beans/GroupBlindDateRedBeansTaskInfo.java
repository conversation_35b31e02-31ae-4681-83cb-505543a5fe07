package com.jxntv.gvideo.app.be.model.vo.blind.date.red.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "GroupBlindDateRedBeansTaskInfo", description = "红豆-签到任务")
public class GroupBlindDateRedBeansTaskInfo {

    @ApiModelProperty(value = "任务类型")
    private Integer type;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "任务奖励红豆数")
    private Integer reward;

    @ApiModelProperty(value = "任务完成状态")
    private Boolean done;

    @ApiModelProperty(value = "任务开放状态")
    private Boolean isOpen;

}
