package com.jxntv.gvideo.app.be.service;

import com.jxntv.gvideo.app.be.model.vo.OptionsVO;
import com.jxntv.gvideo.app.be.model.vo.vocation.PersonalInfoEditVO;
import com.jxntv.gvideo.app.be.model.vo.vocation.PersonalInfoVO;
import com.jxntv.gvideo.app.be.model.vo.vocation.SchoolVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/6
 * Email: <EMAIL>
 */
public interface VocationValueService {

    PersonalInfoVO getByGroupIdAndJid(Long groupId, Long jid);

    Long addPerson(PersonalInfoEditVO editVO);

    Boolean updatePerson(Long id, PersonalInfoEditVO editVO);

    Map<String, Object> getPersonRank(Long groupId, Long jid, Integer pageNum, Integer pageSize);

    Map<String, Object> getSchoolRank(Long groupId, Long schoolId, Integer pageNum, Integer pageSize);

    Map<String, Object> getCityRank(Long groupId, Long cityId, Integer pageNum, Integer pageSize);

    List<SchoolVO> listSchoolByGroupId(Long groupId);

    List<OptionsVO> listCityByGroupId(Long groupId);

    List<OptionsVO> listSchoolByGroupIdAndRegionCode(Long groupId,Long cityId,Long districtId);


}
