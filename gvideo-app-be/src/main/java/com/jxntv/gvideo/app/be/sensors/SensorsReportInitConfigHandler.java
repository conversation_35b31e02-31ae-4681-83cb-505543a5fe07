package com.jxntv.gvideo.app.be.sensors;

import com.jxntv.gvideo.app.be.client.ConsumerUserChannelClient;
import com.jxntv.gvideo.app.be.client.GroupBlindDateLoveService;
import com.jxntv.gvideo.app.be.client.GroupBlindDateFeignClient;
import com.jxntv.gvideo.app.be.converter.GroupBlindDateConverter;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.manager.LocationManager;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.*;
import com.jxntv.gvideo.group.sdk.dto.blind.date.love.GroupBlindDateLoveActivityDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateNeedsDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateUserDTO;
import com.jxntv.gvideo.group.sdk.dto.blind.date.GroupBlindDateUserInfoDTO;
import com.jxntv.gvideo.group.sdk.enums.*;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.OldUserDeviceClient;
import com.jxntv.gvideo.user.client.UgcVestClient;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelTabDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.UserRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 获取配置文件事件上传
 *
 * <AUTHOR>
 * @date 2022/1/21
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class SensorsReportInitConfigHandler implements SensorsReportHandler {

    @Resource
    private SensorsAnalytics sensorsAnalytics;
    @Resource
    private GroupTopicClient groupTopicClient;
    @Resource
    private ConsumerUserClient consumerUserClient;
    @Resource
    private LocationManager locationManager;
    @Resource
    private ConsumerUserChannelClient consumerChannelService;
    @Resource
    private UgcVestClient ugcVestClient;

    @Resource
    private GroupBlindDateFeignClient groupBlindDateService;

    @Resource
    private GroupBlindDateConverter groupBlindDateConverter;

    @Resource
    private GroupBlindDateLoveService groupBlindDateLoveService;
    @Resource
    private OldUserDeviceClient oldUserDeviceClient;

    @Override
    public boolean support(SensorsReportBO sensorsReportBO) {
        return SensorsType.CONFIG.equals(sensorsReportBO.getType());
    }

    @Override
    public void handler(SensorsReportBO sensorsReportBO) {
        UserInfo userInfo = sensorsReportBO.getUserInfo();
        log.info("init config sensorLog start");
        if (Objects.isNull(userInfo) || Objects.isNull(userInfo.getJid())) {
            log.info("init config sensorLog jid is empty");
            return;
        }
        Long jid = userInfo.getJid();

        ConsumerUserDTO consumerUserDTO = consumerUserClient.getUserByJid(jid).getResult();
        if (Objects.isNull(consumerUserDTO)) {
            log.info("init config sensorLog ConsumerUserDTO is empty");
            return;
        }
        String ipAddress = sensorsReportBO.getIpAddress();
        String address = locationManager.getAddress(ipAddress);
        String mobile = consumerUserDTO.getMobile();
        groupTopicClient.myGroupBaseInfo(jid).ifPresent(groups -> {
            String community_name = groups.stream().map(GroupInfoDTO::getName).collect(Collectors.joining(","));
            this.setGroupProfile(jid, community_name);
        });

        //获取都市放心爱相亲信息
        GroupBlindDateUserInfoDTO userBlindDateInfo = this.getUserBlindDateInfo(jid);
        log.info("init config sensorLog userBlindDateInfo jid = {}, info = {}", jid, JsonUtils.toJson(userBlindDateInfo));
        if (Objects.nonNull(userBlindDateInfo) && Objects.nonNull(userBlindDateInfo.getUserDTO())){
            this.setBlindDateProfile(jid,userBlindDateInfo);
        }


        String deviceId = userInfo.getDeviceId();
        log.info("init config sensorLog jid = {}, deviceId = {}", jid, deviceId);

        if (StringUtils.isEmpty(deviceId)) {
            log.info("init config sensorLog deviceId is empty");
            return;
        }
        //查询渠道名称
        String yl_channel = getYlChannel(deviceId, mobile);
        boolean sockpuppet = isSockpuppet(jid);

        //更新用户属性
        String yl_institution = "";
        if (StringUtils.hasText(yl_channel)) {
            yl_institution = consumerChannelService.getConsumerChannelTabByName(yl_channel).map(ConsumerChannelTabDTO::getGroupName).orElse("");
        }
        log.info("init config sensorLog groupName is {}", yl_institution);

        String status = Integer.valueOf(1).equals(consumerUserDTO.getStatus()) ? "启用" : "禁用";
        String account = String.valueOf(jid);
        String nickname = consumerUserDTO.getNickname();
        String registerTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(consumerUserDTO.getCreateDate());
        Boolean isOld = oldUserDeviceClient.isOldDevice(deviceId).orElse(false);
        try {
            UserRecord record = UserRecord.builder()
                    .setDistinctId(account)
                    .isLoginId(Boolean.TRUE)
                    .addProperty("account_type", "ugc")
                    .addProperty("user_institution", "")
                    .addProperty("ip", ipAddress)
                    .addProperty("address", address)
                    .addProperty("is_sockpuppet", sockpuppet)
                    .addProperty("user_channel", StringUtils.hasText(yl_channel) ? yl_channel : "非渠道")
                    .addProperty("register_time", registerTime)
                    .addProperty("nick_name", nickname)
                    .addProperty("YL_channel", StringUtils.hasText(yl_channel) ? yl_channel : "")
                    .addProperty("yl_institution", StringUtils.hasText(yl_institution) ? yl_institution : "应用市场")
                    .addProperty("status", status)
                    .addProperty("is_old", isOld)
                    .build();

            sensorsAnalytics.profileSet(record);

            log.info("【埋点上报】开始设置用户属性：{}", JsonUtils.toJson(record));
        } catch (Exception e) {
            log.error("【埋点上报】设置用户属性异常：{}", e.getLocalizedMessage());
        }

    }


    public void setGroupProfile(Long jid, String community_name) {
        UserRecord record;
        try {
            record = UserRecord.builder()
                    .setDistinctId(String.valueOf(jid))
                    .isLoginId(Boolean.TRUE)
                    .addProperty("community_name", community_name)
                    .build();
            log.info("【埋点上报】开始设置用户圈子属性：jid = {}, communityNameList = {}", jid, community_name);
            sensorsAnalytics.profileSet(record);
        } catch (Exception e) {
            log.error("【埋点上报】设置用户圈子属性异常：{}", e.getLocalizedMessage());
        }
    }

    private String getYlChannel(String deviceId, String mobile) {
        return consumerUserClient.queryChannelNameByDeviceAndMobile(deviceId, mobile).orElse("");
    }

    private GroupBlindDateUserInfoDTO getUserBlindDateInfo(Long jid) {
        return groupBlindDateService.completeUserInfoByJid(jid).orElse(null);
    }

    private boolean isSockpuppet(Long jid) {
        return Objects.nonNull(jid) && ugcVestClient.queryByJid(jid).orElse(false);
    }

    private List<GroupBlindDateLoveActivityDTO> queryEnrollActivityList(Long jid) {
        return groupBlindDateLoveService.queryEnrollActivityList(jid).orElse(null);
    }

    public void setBlindDateProfile(Long jid,GroupBlindDateUserInfoDTO info) {
        GroupBlindDateUserDTO userDTO = info.getUserDTO();
        GroupBlindDateNeedsDTO needsDTO = info.getNeedsDTO();
        UserRecord record;

       String enrollActivity = "";
       List<GroupBlindDateLoveActivityDTO> enrollActivityList = this.queryEnrollActivityList(jid);
       if (!CollectionUtils.isEmpty(enrollActivityList)){
           enrollActivity =  enrollActivityList.stream().map(GroupBlindDateLoveActivityDTO::getName).collect(Collectors.joining(","));
       }

        try {
            record = UserRecord.builder()
                    .setDistinctId(String.valueOf(jid))
                    .isLoginId(Boolean.TRUE)
                    .addProperty("authentication", Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getVerifyFlag()) ? "已实名" : "未实名")
                    .addProperty("card_number", Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getVerifyFlag()) ? userDTO.getCardNo() : "")
                    .addProperty("real_name", Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getVerifyFlag()) ? userDTO.getName() : "")
                    .addProperty("Inner_monologue", Objects.equals( GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getMonologueAudit() ) && StringUtils.hasText(userDTO.getMonologue()) ? userDTO.getMonologue() : "")
                    .addProperty("gender", Objects.nonNull(userDTO.getGender()) ? GroupBlindDateGenderEnum.toStr(userDTO.getGender()) : "未知")
                    .addProperty("birthday", StringUtils.hasText(userDTO.getBirthday()) ? userDTO.getBirthday() :"")
                    .addProperty("height", Objects.nonNull(userDTO.getHeight()) ? userDTO.getHeight()+"cm" : "")
                    .addProperty("education", GroupBlindDateEducationEnum.toStr(userDTO.getEducation()))
                    .addProperty("home",   (StringUtils.isEmpty(userDTO.getProvinceName()) || StringUtils.isEmpty(userDTO.getCityName())) ? "" : userDTO.getProvinceName()+"-"+userDTO.getCityName())
                    .addProperty("unit", StringUtils.isEmpty(userDTO.getCompanyName()) ? "" : userDTO.getCompanyName())
                    .addProperty("profession_certification_status", Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getCompanyVerifyStatus()) ? "已认证" : "未认证")
                    .addProperty("professional", StringUtils.isEmpty(userDTO.getOccupationName()) ? "" : userDTO.getOccupationName())
                    .addProperty("post_properties", GroupBlindDatePostEnum.toStr(userDTO.getPostType()))
                    .addProperty("marriage",  GroupBlindDateMaritalEnum.toStr(userDTO.getMarital()))
                    .addProperty("hobby",  StringUtils.isEmpty(userDTO.getHobby()) ? "" : userDTO.getHobby())
                    .addProperty("monthly_income",  Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getSalaryAudit())  ? this.groupBlindDateConverter.formatSalaryInterval(userDTO.getSalaryFrom(),userDTO.getSalaryTo(),"") :"")
                    .addProperty("is_Have_car",  Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getCarAcqAudit())  ? GroupBlindDateAcqEnum.toStr(userDTO.getCarAcqStatus()) : "")
                    .addProperty("is_Have_room",  Objects.equals(GroupBlindDateAuditEnum.PASS.getCode(),userDTO.getHouseAcqAudit())  ? GroupBlindDateAcqEnum.toStr(userDTO.getHouseAcqStatus()) : "")
                    .addProperty("spouse_age", Objects.nonNull(needsDTO) ? this.groupBlindDateConverter.formatInterval(needsDTO.getAgeFrom(),needsDTO.getAgeTo(),"岁") : "")
                    .addProperty("spouse_height", Objects.nonNull(needsDTO) ? this.groupBlindDateConverter.formatInterval(needsDTO.getHeightFrom(),needsDTO.getHeightTo(),"cm") : "")
                    .addProperty("spouse_education", Objects.nonNull(needsDTO) ? this.groupBlindDateConverter.formatEducationInterval(needsDTO.getEducationFrom(),needsDTO.getEducationTo()) : "")
                    .addProperty("spouse_income", Objects.nonNull(needsDTO) ? this.groupBlindDateConverter.formatInterval(needsDTO.getSalaryFrom(),needsDTO.getSalaryTo(),"元") : "")
                    .addProperty("spouse_home", Objects.isNull(needsDTO) || (StringUtils.isEmpty(needsDTO.getProvinceName()) || StringUtils.isEmpty(needsDTO.getCityName())) ? "" : needsDTO.getProvinceName()+"-"+needsDTO.getCityName())
                    .addProperty("elect_record", enrollActivity)
                    .addProperty("first_community_time", info.getUserDTO().getCreateDate())
                    .build();
            log.info("【埋点上报】开始设置都市放心爱圈子相亲信息属性：jid = {}, UserRecord = {}", jid, JsonUtils.toJson(record));
            sensorsAnalytics.profileSet(record);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("【埋点上报】设置用户都市放心爱圈子相亲信息属性异常：jid = {},errorMsg: {}",jid,e.getLocalizedMessage());
        }
    }

}
