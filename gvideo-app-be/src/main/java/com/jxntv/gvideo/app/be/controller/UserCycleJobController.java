package com.jxntv.gvideo.app.be.controller;

import com.alibaba.fastjson.JSON;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.client.UserCycleJobFeignClient;
import com.jxntv.gvideo.app.be.config.JspBeanConfig;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.manager.GroupUserMarkComponentManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.UserCycleReplenishSignVO;
import com.jxntv.gvideo.app.be.model.vo.mark.UserMarkDrawInfoVO;
import com.jxntv.gvideo.app.be.model.vo.mark.UserMarkSettingVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.GroupUserMarkComponentDTO;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.UserCycleProgressDTO;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.UserCycleTaskDTO;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.UserMarkDetailDTO;
import com.jxntv.gvideo.group.sdk.dto.user.cycle.*;
import com.jxntv.gvideo.group.sdk.enums.user.cycle.UserCycleJobType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/5/15
 **/
@RestController
@Slf4j
@Api(value = "圈子相关接口", tags = {"签到/互动任务"})
@RequestMapping("/api/group/user/cycle/job/")
public class UserCycleJobController {

    @Resource
    private UserCycleJobFeignClient userCycleJobFeignClient;

    @Resource
    private GroupUserMarkComponentManager groupUserMarkComponentManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private JspBeanConfig jspBeanConfig;

    @ApiOperation("签到")
    @PostMapping({"mark"})
    public Result<Integer> mark(@RequestBody UserCycleTaskDTO userCycleTaskDTO) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        log.info("用户签到，userId={},jid={}",userCycleTaskDTO.getUserId(),jid);
        userCycleTaskDTO.setUserId(jid);
        return userCycleJobFeignClient.mark(userCycleTaskDTO);
    }

    @ApiOperation("自动签到")
    @PostMapping({"mark/auto"})
    public Result<Integer> autoMark(@RequestBody UserCycleTaskDTO taskDTO) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        GroupUserMarkComponentDTO groupUserMarkComponentDTO = groupUserMarkComponentManager.getUserMarkComponentByGroupId(taskDTO.getGroupId());
        if (Objects.isNull(groupUserMarkComponentDTO)){
            return Result.ok(0);
        }

        if ( !CollectionUtils.isEmpty(jspBeanConfig.getAutoSignGroupIds()) && jspBeanConfig.getAutoSignGroupIds().contains(taskDTO.getGroupId())) {
            Boolean isTodayMark = userCycleJobFeignClient.todayMarkStatus(jid, taskDTO.getGroupId(), groupUserMarkComponentDTO.getType()).orElse(Boolean.FALSE);
            if (Boolean.FALSE.equals(isTodayMark)) {
                UserCycleTaskDTO userCycleTaskDTO = new UserCycleTaskDTO();
                userCycleTaskDTO.setUserId(jid);
                userCycleTaskDTO.setGroupId(taskDTO.getGroupId());
                userCycleTaskDTO.setJobType(groupUserMarkComponentDTO.getType());
                return  userCycleJobFeignClient.mark(userCycleTaskDTO);
            }
        }

        return Result.ok(0);
    }


    @ApiOperation("互动任务进度")
    @GetMapping({"progress"})
    public Result<UserCycleProgressDTO> getProgress(@RequestParam Long userId, @RequestParam Long groupId, @RequestParam(value = "jobType",required = false) Integer jobType) {
        return userCycleJobFeignClient.getProgress(userId, groupId, (Objects.isNull(jobType) ? UserCycleJobType.ANSWER_JOB.getType() : jobType));
    }


    @ApiOperation("签到任务进度")
    @GetMapping({"mark"})
    public Result<UserMarkDetailDTO> getUserMarkDetail(@RequestParam Long userId, @RequestParam Long groupId, @RequestParam(value = "jobType",required = false) Integer jobType) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }

        Result<UserMarkDetailDTO> userMarkResult = userCycleJobFeignClient.getUserMarkDetail(jid, groupId, (Objects.isNull(jobType) ? UserCycleJobType.QA_MARK_JOB.getType() : jobType));
        if (!userMarkResult.callSuccess()){
            return Result.fail("签到任务进度信息查询失败");
        }
        log.info("签到任务进度，userId={},jid={},result={}",userId,jid, JSON.toJSON(userMarkResult.getResult()));
        UserMarkDetailDTO userMarkDetailDTO = userMarkResult.getResult();
        userMarkDetailDTO.setMarkStatus(Boolean.TRUE);
        GroupUserMarkComponentDTO groupUserMarkComponentDTO = groupUserMarkComponentManager.getUserMarkComponentByGroupId(groupId);
        if (Objects.nonNull(groupUserMarkComponentDTO) && Objects.nonNull(groupUserMarkComponentDTO.getEndDate())){
           userMarkDetailDTO.setMarkStatus(groupUserMarkComponentDTO.getEndDate().isAfter(LocalDateTime.now()));
        }
        return Result.ok(userMarkDetailDTO);
    }


    @ApiOperation("社区首页的签到状态")
    @GetMapping({"status"})
    public Result<Integer> getUserMarkStatus(@RequestParam Long userId, @RequestParam Long groupId, @RequestParam(value = "jobType",required = false) Integer jobType) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }

        return userCycleJobFeignClient.getUserMarkStatus(jid, groupId, (Objects.isNull(jobType) ? UserCycleJobType.QA_MARK_JOB.getType() : jobType));
    }


    @ApiOperation("用户签到配置")
    @GetMapping({"mark/setting"})
    public Result<UserMarkSettingVO> getUserMarkSetting(@RequestParam Long groupId) {

        GroupUserMarkComponentDTO groupUserMarkComponentDTO = groupUserMarkComponentManager.getUserMarkComponentByGroupId(groupId);
        if (Objects.isNull(groupUserMarkComponentDTO)){
           return Result.fail("签到组件信息查询失败");
        }
        String bannerUrl = ossManager.getOriFile(groupUserMarkComponentDTO.getBannerId()).map(OssDTO::getUrl).orElse("");

        UserMarkSettingVO userMarkSettingDTO = new UserMarkSettingVO();
        userMarkSettingDTO.setBanner(bannerUrl);
        userMarkSettingDTO.setDescription(groupUserMarkComponentDTO.getDescription());

        return Result.ok(userMarkSettingDTO);
    }

    @ApiOperation("查询用户是否有抽奖")
    @GetMapping({"draw/show"})
    public Result<UserMarkDrawInfoVO> getDrawInfo(@RequestParam Long groupId, @RequestParam(value = "jobType",required = false) Integer jobType) {

        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }

        Result<UserMarkDrawInfoDTO> result = userCycleJobFeignClient.getDrawInfo(jid, groupId, jobType);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        UserMarkDrawInfoVO vo = null;
        if (Objects.nonNull(result.getResult())){
            vo = new UserMarkDrawInfoVO();
            vo.setDrawUrl(result.getResult().getDrawUrl());
            vo.setMarkDays(result.getResult().getMarkDays());
        }
        return Result.ok(vo);
    }

    @ApiOperation("续签")
    @PostMapping({"replenish/sign"})
    public Result<Boolean> replenishSign(@RequestBody @Validated UserCycleReplenishSignVO vo) {

        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        ReplenishSignDTO  dto = new ReplenishSignDTO();
        dto.setUserId(jid);
        dto.setGroupId(vo.getGroupId());
        dto.setJobType(vo.getJobType());
        dto.setStartDate(vo.getStartDate());
        dto.setEndDate(vo.getEndDate());

        Result<Boolean> result = userCycleJobFeignClient.replenishSign(dto);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        return Result.ok(result.getResult());
    }
}
