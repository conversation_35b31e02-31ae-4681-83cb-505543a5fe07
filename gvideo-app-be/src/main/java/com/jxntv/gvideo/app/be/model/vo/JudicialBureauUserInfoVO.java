package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: niedamin
 * @Date: 2022/08/12 11:06
 */
@Data
@ApiModel("司法厅用户首页展示信息")
public class JudicialBureauUserInfoVO {

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("所在调解组织")
    private String mediationOrganization;

    @ApiModelProperty("今日是否已答题")
    private Boolean isAnswer;

    @ApiModelProperty("个人排名")
    private Integer myRank;

    @ApiModelProperty("市排名")
    private Integer cityRank;

    @ApiModelProperty("区县排名")
    private Integer countyRank;

    @ApiModelProperty("个人头像")
    private String avatar;

}
