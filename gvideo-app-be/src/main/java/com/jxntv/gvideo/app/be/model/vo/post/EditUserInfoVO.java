package com.jxntv.gvideo.app.be.model.vo.post;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created on 2020-02-28
 */
@Data
@ApiModel(value = "EditUserInfoVO", description = "编辑用户信息对象")
public class EditUserInfoVO {

    @ApiModelProperty(value = "用户昵称")
    private String nickname;
    @ApiModelProperty(value = "性别")
    private Integer gender;
    @ApiModelProperty(value = "性别可见范围")
    private Integer genderVisible;
    @ApiModelProperty(value = "生日")
    private Date birthday;
    @ApiModelProperty(value = "生日可见范围")
    private Integer birthdayVisible;
    @ApiModelProperty(value = "头像地址")
    private String avatarFileId;
    @ApiModelProperty(value = "简介")
    private String info;
    @ApiModelProperty(value = "省份id")
    private Integer provinceId;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "地市id")
    private Integer cityId;
    @ApiModelProperty(value = "地市")
    private String city;
}
