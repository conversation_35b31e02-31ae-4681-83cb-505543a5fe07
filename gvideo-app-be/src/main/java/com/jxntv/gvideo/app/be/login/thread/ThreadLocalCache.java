package com.jxntv.gvideo.app.be.login.thread;

import java.util.Objects;
import java.util.Optional;

/**
 * Created on 2020-02-13
 */
public class ThreadLocalCache {

    private final static ThreadLocal<UserInfo> threadLocalCache = new ThreadLocal<>();

    public static void add(UserInfo userInfo) {
        threadLocalCache.set(userInfo);
    }

    public static UserInfo get() {
        return threadLocalCache.get();
    }

    public static Long getJid() {
        return Optional.of(threadLocalCache).map(ThreadLocal::get).map(UserInfo::getJid).orElse(null);
    }

    public static void remove() {
        threadLocalCache.remove();
    }

    public static boolean login(UserInfo userinfo) {
        return Objects.nonNull(userinfo) && Objects.nonNull(userinfo.getJid());
    }

    public static boolean login() {
        return Objects.nonNull(ThreadLocalCache.get()) && Objects.nonNull(ThreadLocalCache.get().getJid());
    }
}
