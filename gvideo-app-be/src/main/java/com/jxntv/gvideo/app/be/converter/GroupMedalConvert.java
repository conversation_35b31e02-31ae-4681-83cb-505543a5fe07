package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalFeedVO;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalGrantDetailsVO;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.GroupMedalDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupMedalGrantDetailsDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupMedalImageSizeEnum;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.Strings;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/4/25 10:33 下午
 */
@Slf4j
@Component
public class GroupMedalConvert {

    @Resource
    private OssManager ossManager;
    @Resource
    private ConsumerUserClient consumerUserClient;

    public GroupMedalVO convert(GroupMedalDTO groupMedal,int imageSize) {
        if (groupMedal == null) {
            return null;
        }
        BeanCopier copier = BeanCopier.create(GroupMedalDTO.class, GroupMedalDTO.class, false);
        return convert(groupMedal, copier,imageSize);
    }

    public GroupMedalVO convert(GroupMedalDTO groupMedal, BeanCopier beanCopier,int imageSize) {
        if (groupMedal == null) {
            return null;
        }
        GroupMedalVO groupMedalVO = new GroupMedalVO();
        beanCopier.copy(groupMedal, groupMedalVO, null);

        String ossId = null;
        if (Objects.equals(GroupMedalImageSizeEnum.LARGE.getCode(),imageSize)){
            ossId = groupMedal.getLargeOssId();
        }else if (Objects.equals(GroupMedalImageSizeEnum.MEDIUM.getCode(),imageSize)){
            ossId = groupMedal.getMediumOssId();
        }else if (Objects.equals(GroupMedalImageSizeEnum.SMALL.getCode(),imageSize)){
            ossId = groupMedal.getSmallOssId();
        }
        if (StringUtils.hasText(ossId)){
            try {
                Result<OssDTO> result = ossManager.getOriFile(ossId);
                if (result.callSuccess()) {
                    groupMedalVO.setImageUrl(result.getResult().getUrl());
                }
            } catch (Exception ex) {
                log.error("get {} oss image url error.", groupMedalVO, ex);
            }
        }

        return groupMedalVO;
    }

    public List<GroupMedalVO> convert(List<GroupMedalDTO> groupMedals,int imageSize) {
        if (groupMedals == null) {
            return null;
        }
        BeanCopier copier = BeanCopier.create(GroupMedalDTO.class, GroupMedalVO.class, false);
        List<GroupMedalVO> list = new ArrayList<>();
        groupMedals.forEach(e -> {
            list.add(convert(e, copier,imageSize));
        });
        return list;
    }

    public List<GroupMedalFeedVO> convertFeed(List<GroupMedalDTO> groupMedals) {
        if (CollectionUtils.isEmpty(groupMedals)) {
            return null;
        }
        if (groupMedals.size() >2){
            groupMedals = groupMedals.subList(0,2);
        }


        List<GroupMedalFeedVO> list = new ArrayList<>(groupMedals.size());
        groupMedals.forEach(e -> {
            GroupMedalFeedVO vo = new GroupMedalFeedVO();
            vo.setId(e.getId());
            vo.setGroupId(e.getGroupId());
            vo.setGroupName(e.getGroupName());
            vo.setName(e.getName());
            vo.setReason(e.getReason());

            if (StringUtils.hasText(e.getSmallOssId())){
                try {
                    Result<OssDTO> result = ossManager.getOriFile(e.getSmallOssId());
                    if (result.callSuccess()) {
                        vo.setImageUrl(result.getResult().getUrl());
                    }
                } catch (Exception ex) {
                    log.error("get {} oss image url error.", e, ex);
                }
            }

            list.add(vo);
        });
        return list;
    }

    public GroupMedalGrantDetailsVO convert(GroupMedalGrantDetailsDTO detailsDTO) {
        if (detailsDTO == null) {
            return null;
        }
        GroupMedalVO groupMedalVO = new GroupMedalVO();
        BeanCopier beanCopier = BeanCopier.create(GroupMedalGrantDetailsDTO.class, GroupMedalVO.class, false);
        beanCopier.copy(detailsDTO, groupMedalVO, null);
        GroupMedalGrantDetailsVO groupMedalGrantDetailsVO = new GroupMedalGrantDetailsVO();
        groupMedalGrantDetailsVO.setMedal(groupMedalVO);
        groupMedalGrantDetailsVO.setUser(new GroupMedalGrantDetailsVO.MedalOwner());
        Long jid = detailsDTO.getJid();
        groupMedalGrantDetailsVO.getUser().setMedalOwnerJid(jid);

        ConsumerUserDTO consumerUserDTO = consumerUserClient.getBaseUserByJid(jid).getResult();
        if (consumerUserDTO != null) {
            groupMedalGrantDetailsVO.getUser().setMedalOwnerName(consumerUserDTO.getNickname());
            groupMedalGrantDetailsVO.getUser().setMedalOwnerPhoneNumber(consumerUserDTO.getMobile());
        }
        if (!Strings.isEmpty(detailsDTO.getLargeOssId())) {

            try {
                Result<OssDTO> result = ossManager.getOriFile(detailsDTO.getLargeOssId());
                if (result.callSuccess()) {
                    groupMedalVO.setImageUrl(result.getResult().getUrl());
                }
                Result<OssDTO> resize = ossManager.getResize(detailsDTO.getLargeOssId());
                if (resize.callSuccess()) {
                    groupMedalVO.setShareImageUrl(resize.getResult().getUrl());
                }

            } catch (Exception ex) {
                log.error("get {} oss image url error.", detailsDTO, ex);
            }
        }
        return groupMedalGrantDetailsVO;
    }
}
