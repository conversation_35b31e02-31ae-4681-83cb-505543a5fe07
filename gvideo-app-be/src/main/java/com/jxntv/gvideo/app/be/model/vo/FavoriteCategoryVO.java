package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-01-13
 */
@Data
@ApiModel(value="FavoriteCategoryVO", description="收藏类别对象")
public class FavoriteCategoryVO {

    @ApiModelProperty(value="分类ID", required = true)
    private Integer id;
    @ApiModelProperty(value="图片URL", required = true)
    private String coverImageUrl;
    @ApiModelProperty(value="分类名", required = true)
    private String name;
    @ApiModelProperty(value="个数", required = true)
    private Long count;
    @ApiModelProperty(value="展示信息", required = true)
    private String detailText;
    @ApiModelProperty(value="是否竖版", required = true)
    private Boolean vertical;
}
