package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.kingkong.KingKongPositionVO;
import com.jxntv.gvideo.app.be.utils.AppVersionUtils;
import com.jxntv.gvideo.om.dto.KingKongPositionDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 金刚位
 *
 * <AUTHOR>
 * @date 2022/4/25
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class KingKongPositionConverter {

    @Resource
    private OssManager ossManager;


    public KingKongPositionVO convert(KingKongPositionDTO dto) {
        String iconUrl = ossManager.getOriFile(dto.getIconId()).map(OssDTO::getUrl).orElse("");

        KingKongPositionVO vo = new KingKongPositionVO();
        vo.setId(dto.getId());
        vo.setType(dto.getType());
        vo.setLinkUrl(dto.getLinkUrl());
        vo.setIconId(dto.getIconId());
        vo.setIconUrl(iconUrl);
        vo.setTitle(dto.getName());
        vo.setBubbleWords(dto.getBubbleWords());

        //  小程序低版本兼容
        if (Objects.equals(dto.getType(), 3)) {
            String appVersion = ThreadLocalCache.get().getAppVersion();
            if (AppVersionUtils.isSupportAppletUrl(appVersion)) {
                if (dto.getLinkUrl().startsWith("jinshipin://wechatProgram")) {
                    if (AppVersionUtils.isSupportWechatApplet(appVersion)) {
                        vo.setLinkUrl(dto.getLinkUrl());
                    }else {
                        vo.setLinkUrl(dto.getExtraUrl());
                    }
                }else {
                    vo.setLinkUrl(dto.getLinkUrl());
                }
            } else {
                vo.setLinkUrl(dto.getExtraUrl());
            }

        }
        return vo;

    }
}
