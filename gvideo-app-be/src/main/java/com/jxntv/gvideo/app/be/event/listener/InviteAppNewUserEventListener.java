package com.jxntv.gvideo.app.be.event.listener;

import com.jxntv.gvideo.app.be.event.InviteAppNewUserEvent;
import com.jxntv.gvideo.app.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.app.be.redis.RedisKeyConstant;
import com.jxntv.gvideo.media.client.enums.WhetherEnum;
import com.jxntv.gvideo.user.client.InviteFriendLogClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.jxntv.gvideo.user.client.dto.InviteFriendLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * @Author: niedamin
 * @Date: 2023/08/02 16:38
 */
@Slf4j
@Component
public class InviteAppNewUserEventListener {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private InviteFriendLogClient inviteFriendLogClient;

    @Resource
    private ConsumerUserManager consumerUserManager;


    @Async
    @EventListener
    public void onEvent(InviteAppNewUserEvent event) {
        LocalDateTime now = LocalDateTime.now();
        String dayStr = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String monthStr = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        Long jid = event.getJid();
        // 查询用户是否通过app拉新进入
        ConsumerUserDTO consumerUserDTO = consumerUserManager.getByJid(jid);
        if (Objects.nonNull(consumerUserDTO)) {
            InviteFriendLogDTO inviteFriendLogDTO = inviteFriendLogClient.queryByMobileAndChannel(consumerUserDTO.getMobile(),0).orElse(null);
            if (Objects.nonNull(inviteFriendLogDTO) && Objects.equals(inviteFriendLogDTO.getRewardFlag(), 0) ) {
                Long inviteUserJid = inviteFriendLogDTO.getJid();
                Integer channel = inviteFriendLogDTO.getChannel();
                // 更新邀请人的总数据
                stringRedisTemplate.opsForValue().increment(String.format(RedisKeyConstant.PERSONAL_KEY, channel,inviteUserJid), 1);
                // 更新邀请人的当月数据
                stringRedisTemplate.opsForValue().increment(String.format(RedisKeyConstant.PERSONAL_MONTH_KEY,channel, monthStr, inviteUserJid), 1);
                // 更新日排行榜数据
                stringRedisTemplate.opsForZSet().incrementScore(String.format(RedisKeyConstant.DAY_RANK_KEY,channel, dayStr), String.valueOf(inviteUserJid), 1);
                // 更新月排行榜数据
                stringRedisTemplate.opsForZSet().incrementScore(String.format(RedisKeyConstant.MONTH_RANK_KEY,channel, monthStr), String.valueOf(inviteUserJid), 1);
                // 更新日志表
                InviteFriendLogDTO updateLogDTO = new InviteFriendLogDTO();
                updateLogDTO.setId(inviteFriendLogDTO.getId());
                updateLogDTO.setRewardFlag(WhetherEnum.YES.getCode());
                inviteFriendLogClient.updateLog(updateLogDTO);
            }
        }
    }
}
