package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.InteractStatisticClient;
import com.jxntv.gvideo.interact.client.dto.InteractFavorStatisticDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InteractStatisticClientFallback implements FallbackFactory<InteractStatisticClient> {
    @Override
    public InteractStatisticClient create(Throwable throwable) {
        return new InteractStatisticClient() {
            @Override
            public Result<InteractFavorStatisticDTO> getFavorStatistic(Long mediaId, Long jid) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
