package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.aspect.action.Action;
import com.jxntv.gvideo.app.be.aspect.action.ActionType;
import com.jxntv.gvideo.app.be.client.*;
import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.common.vo.PageC;
import com.jxntv.gvideo.app.be.config.BehaviorConfig;
import com.jxntv.gvideo.app.be.converter.GroupConverter;
import com.jxntv.gvideo.app.be.converter.GroupTopicConverter;
import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.manager.GroupGatherManager;
import com.jxntv.gvideo.app.be.manager.GroupManager;
import com.jxntv.gvideo.app.be.model.constant.FeedKeyConstant;
import com.jxntv.gvideo.app.be.model.enums.ApiInfoEnum;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.sensors.Sensors;
import com.jxntv.gvideo.app.be.sensors.SensorsType;
import com.jxntv.gvideo.app.be.service.GroupService;
import com.jxntv.gvideo.app.be.utils.FeedDeduplicate;
import com.jxntv.gvideo.app.be.utils.FeedHandle;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupContentClient;
import com.jxntv.gvideo.group.sdk.dto.*;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorSearchDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.media.client.enums.DataType;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.track.sdk.report.AppTrack;
import com.jxntv.gvideo.track.sdk.report.AppTrackType;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.jxntv.gvideo.common.utils.ListUtils.distinctByKey;

/**
 * <AUTHOR> Created on 2021/5/17.
 */
@Slf4j
@RestController
@RequestMapping("/api/group")
@Api(value = "圈子相关接口", tags = {"圈子相关接口"})
@RefreshScope
public class GroupController {
    @Autowired
    private GroupTopicService groupTopicService;
    @Autowired
    private GroupConverter groupConverter;
    @Autowired
    private GroupManageService groupManageService;
    @Autowired
    private GroupGatherFeignClient groupGatherService;
    @Autowired
    private BroadcastLocationService broadcastLocationService;
    @Resource
    private GroupManager groupManager;
    @Resource
    private GroupTopicConverter groupTopicConverter;
    @Autowired
    private MentorInfoService mentorInfoService;
    @Resource
    private GroupService groupService;
    @Autowired
    private GroupGatherManager groupGatherManager;
    @Resource
    private FeedV2Converter feedV2Converter;

    @Resource
    private GroupContentClient groupContentClient;

    @Resource
    private FeedDeduplicate feedDeduplicate;

    @Resource
    private BehaviorConfig behaviorConfig;

    @Resource
    private FeedHandle feedHandle;

    @GetMapping("top/live")
    @ApiOperation("圈子置顶直播")
    Result<List<FeedVO>> topLive(@RequestParam Long groupId) {
        Long jid = ThreadLocalCache.getJid();
        Result<List<Long>> result = broadcastLocationService.fetchTopLive(groupId);
        return result.map(list -> list.stream().map(id -> feedV2Converter.convert(FeedContext.of(id, jid))).collect(Collectors.toList()));
    }

    @GetMapping("my")
    @ApiOperation("我的圈子")
    Result<MyGroupVO> myGroup() {
        Long jid = ThreadLocalCache.get().getJid();
        MyGroupVO result = groupService.getMyGroup(jid);
        return Result.ok(result);
    }


    @GetMapping("my/content")
    @ApiOperation("我的圈子-下方内容新版本")
    @ApiImplicitParams({@ApiImplicitParam(name = "pageNum", value = "页码", dataType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "页码大小", dataType = "query")})
    public Result<PageC<FeedVO>> pageFollowedGroupContent(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                                          @RequestParam(required = false, defaultValue = "15") Integer pageSize) {
        Long jid = ThreadLocalCache.get().getJid();
        PageC<FeedVO> page = groupService.followGroupContent(jid, pageNum, pageSize);
        feedDeduplicate.deduplicate(page.getList(), pageNum, FeedKeyConstant.TOPIC_DETAIL_FLOW);
        feedHandle.handle(page.getList());
        return Result.ok(page);

    }

    @GetMapping("/option/list")
    @ApiOperation("动态发布圈子筛选列表")
    Result<Page<GroupListVO>> optionList(@RequestParam(required = false, defaultValue = "1") Integer pageNum,
                                         @RequestParam(required = false, defaultValue = "15") Integer pageSize,
                                         @RequestParam(required = false) Long firstGroupId,
                                         @RequestParam(required = false) String nameLike,
                                         @RequestParam(required = false, defaultValue = "false") Boolean isRelay) {
        Long jid = ThreadLocalCache.get().getJid();

        String relayGroupIds = null;
        if(Boolean.TRUE.equals(isRelay) && !CollectionUtils.isEmpty(behaviorConfig.getRelay().getGroupIds())){
            relayGroupIds = behaviorConfig.getRelay().getGroupIds().stream().map(String::valueOf).collect(Collectors.joining(","));
        }

        Result<PageDTO<GroupOptionDTO>> result = groupTopicService.optionPage(jid, pageNum, pageSize, firstGroupId, nameLike,relayGroupIds);

        return result.map(page -> Page.Of(page, e -> groupConverter.convertGroupListVO(e)));
    }

    @GetMapping("/check/join")
    @ApiOperation("是否加入圈子")
    Result<Boolean> isJoined(@RequestParam Long groupId) {
        Long jid = ThreadLocalCache.getJid();
        return Result.ok(groupManager.isJoined(jid, groupId));
    }

    @GetMapping("/component/permission-check")
    @ApiOperation("检查是否拥有社区组件权限")
    Result<Boolean> hasPermission(@RequestParam Long componentId) {
        boolean permission = groupService.hasComponentPermission(componentId);
        return permission ? Result.ok(true) : Result.fail(-1, "没有权限", false);
    }

    @GetMapping("discover/tags")
    @ApiOperation("发现圈子标签列表")
    Result<List<KeyValueVO>> tags() {
        Long jid = ThreadLocalCache.get().getJid();
        Result<List<KeyValueDTO>> rst = groupTopicService.tags(jid);
        if (!rst.callSuccess()) {
            return Result.fail(rst.getMessage());
        }
        return Result.ok(rst.getResult().stream().map(KeyValueVO::convert).collect(Collectors.toList()));
    }

    @GetMapping("discover/content")
    @ApiOperation("发现圈子内容列表")
    @ApiImplicitParams({@ApiImplicitParam(name = "label", value = "标签id", dataType = "query"), @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "query"), @ApiImplicitParam(name = "pageSize", value = "页码大小", dataType = "query")})
    Result<PageC<GroupListVO>> discover(@RequestParam(required = false) Long label, @RequestParam(required = false, defaultValue = "1") Integer pageNum, @RequestParam(required = false, defaultValue = "15") Integer pageSize) {
        Long jid = ThreadLocalCache.get().getJid();

        List<Long> labels = Objects.isNull(label) ? null : Collections.singletonList(label);

        GroupInfoSearchDTO searchDTO = new GroupInfoSearchDTO();
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setLabels(labels);
        searchDTO.setStatus(true);
        searchDTO.setJid(jid);
        searchDTO.setIsAppCall(true);
        return groupTopicService.page(searchDTO).map(page -> PageC.of(page, e -> groupConverter.convertGroupListVO(e)));
    }

    @GetMapping("detail")
    @ApiOperation("圈子详情")
    @Action(type = ActionType.GROUP_VIEW)
    @Sensors(type = SensorsType.GROUP_VIEW)
    @AppTrack(type = AppTrackType.GROUP_VIEW)
    Result<GroupDetailVO> detail(@RequestParam Long groupId) {
        GroupDetailVO result = groupService.getGroupDetailById(groupId);
        return Result.ok(result);
    }

    @PutMapping
    @ApiOperation("圈子编辑")
    Result<Void> modify(@RequestBody @Validated GroupModifyVO vo) {
        Long jid = ThreadLocalCache.get().getJid();
        Result<Void> permission = groupManageService.appManagePermission(jid, vo.getGroupId());
        if (!permission.callSuccess()) {
            return Result.fail(permission.getMessage());
        }
        AppGroupModifyDTO dto = new AppGroupModifyDTO();
        BeanCopier.create(GroupModifyVO.class, AppGroupModifyDTO.class, false).copy(vo, dto, null);
        return groupTopicService.appModify(dto);
    }

    @GetMapping("apply")
    @ApiOperation("申请加入圈子")
    @ApiImplicitParams({@ApiImplicitParam(name = "groupId", value = "圈子id", dataType = "query", required = true), @ApiImplicitParam(name = "reason", value = "加入理由", dataType = "query", required = true)})
    @Sensors(type = SensorsType.GROUP_FOLLOW)
    @AppTrack(type = AppTrackType.GROUP_FOLLOW)
    Result<Void> apply(@RequestParam Long groupId, @RequestParam String reason) {
        Long jid = ThreadLocalCache.get().getJid();
        return groupTopicService.apply(groupId, jid, reason);
    }

    @PutMapping("exit")
    @ApiOperation("退出圈子")
    @Sensors(type = SensorsType.GROUP_UNFOLLOW)
    @AppTrack(type = AppTrackType.GROUP_UNFOLLOW)
    Result<Void> exit(@RequestBody @Validated GroupExitVO vo) {
        Long jid = ThreadLocalCache.get().getJid();
        return groupTopicService.exit(vo.getGroupId(), jid);
    }

    @GetMapping("topic/search")
    @ApiOperation("话题检索")
    @ApiImplicitParams({@ApiImplicitParam(name = "groupId", value = "圈子id", dataType = "query", required = true), @ApiImplicitParam(name = "key", value = "检索关键字 话题id、话题内容", dataType = "query"), @ApiImplicitParam(name = "gatherId", value = "组件id", dataType = "query", required = false), @ApiImplicitParam(name = "pageNum", value = "页码", dataType = "query"), @ApiImplicitParam(name = "pageSize", value = "页码大小", dataType = "query")})
    Result<Page<GroupTopicVO>> topicSearch(@RequestParam Long groupId, @RequestParam(required = false) String key, @RequestParam(required = false) Long gatherId, @RequestParam(required = false, defaultValue = "0") Integer pageNum, @RequestParam(required = false, defaultValue = "15") Integer pageSize) {
        Long jid = ThreadLocalCache.get().getJid();
        Result<PageDTO<GroupTopicDTO>> pageRst = groupTopicService.appSearchTopic(groupId, key, gatherId, pageNum, pageSize, jid);
        if (!pageRst.callSuccess()) {
            return Result.fail(pageRst.getMessage());
        }
        PageDTO<GroupTopicDTO> page = pageRst.getResult();
        return Result.ok(Page.Of(page, groupTopicConverter::convert));
    }

    @GetMapping("topic/detail")
    @ApiOperation("话题详情")
    @ApiImplicitParams({@ApiImplicitParam(name = "topicId", value = "话题id", dataType = "query", required = true)})
    Result<GroupTopicVO> topicDetail(@RequestParam Long topicId) {
        Long jid = ThreadLocalCache.get().getJid();
        Result<GroupTopicDTO> dto = groupTopicService.topicDetail(topicId, jid);
        if (!dto.callSuccess()) {
            return Result.fail(dto.getMessage());
        }
        return Result.ok(groupTopicConverter.convertGroupMedal(dto.getResult()));
    }

    @PostMapping("topic")
    @ApiOperation("话题创建")
    Result<Void> createTopic(@RequestBody @Validated GroupTopicPostVO postVO) {
        return Result.ok();
    }

    @PutMapping("topic")
    @ApiOperation("话题编辑")
    Result<Void> modifyTopic(@RequestBody @Validated GroupTopicPostVO postVO) {
        return Result.ok();
    }

    @GetMapping("/gather/tags")
    @ApiOperation("圈子组件列表")
    Result<List<GatherVO>> gatherTags(@ApiParam(name = "groupId", value = "圈子ID") @RequestParam("groupId") Long groupId) {
        if (Objects.isNull(groupId)) {
            return Result.fail("社区ID不能为空");
        }
        List<GatherVO> result = groupService.listGatherTabsByGroupId(groupId);
        return Result.ok(result);
    }

    @GetMapping("/{group_id}/mentor")
    @ApiOperation(" 圈子问答导师成员列表")
    Result<PageC<AuthorVO>> queryMentorInfoPage(@ApiParam(name = "group_id", value = "圈子ID", required = true) @PathVariable("group_id") Long groupId, @ApiParam(name = "pageNum", value = "页码", example = "1", defaultValue = "0") @RequestParam(required = false, defaultValue = "0") Integer pageNum, @ApiParam(name = "pageSize", value = "每页记录", defaultValue = "10") @RequestParam(required = false, defaultValue = "10") Integer pageSize) {


        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.nonNull(userInfo) && "iOS".equals(userInfo.getOs())) {
            pageNum = pageNum + 1;
        }

        GroupFreeMentorSearchDTO searchDTO = new GroupFreeMentorSearchDTO();
        searchDTO.setGroupId(groupId);
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);

        Result<PageDTO<GroupFreeMentorDTO>> result = mentorInfoService.searchFreeMentor(searchDTO);
        return result.map(page -> PageC.of(page, e -> groupConverter.convertMentorInfoToUserVo(e)));
    }

    @ApiOperation("根据圈子ID获取爆料组件信息")
    @GetMapping("/{groupId}/broke-the-news")
    Result<GatherVO> getBrokeTheNewsByGroupId(@ApiParam(name = "groupId", value = "圈子ID") @PathVariable("groupId") Long groupId) {
        if (Objects.isNull(groupId)) {
            return Result.fail("社区ID不能为空");
        }
        GroupGatherDTO gatherDTO = groupGatherManager.getBrokeTheNewsByGroupId(groupId);
        if (Objects.isNull(gatherDTO)) {
            return Result.fail("爆料组件不存在");
        }
        if (Integer.valueOf(0).equals(gatherDTO.getStatus())) {
            return Result.fail("爆料组件已禁用");
        }
        return Result.ok(groupConverter.toGatherVo(gatherDTO));
    }

    @GetMapping("/mentor/list")
    @ApiOperation(" 圈子问答导师成员列表(用于心理测评)")
    Result<List<AuthorVO>> queryMentorList(@ApiParam(name = "groupId", value = "圈子ID", required = true) @RequestParam("groupId") String groupId) {

        log.info("queryMentorList groupId is {}, {}", groupId, Long.valueOf(groupId));
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            jid = 0L;
        }

        Result<List<GroupGatherDTO>> gatherResult = groupGatherService.listByType(Long.valueOf(groupId), GroupGatherTypeEnum.QUESTION_ANSWER.getCode());
        if (!gatherResult.callSuccess() || CollectionUtils.isEmpty(gatherResult.getResult())) {
            return Result.ok(null);
        }

        Long componentId = gatherResult.getResult().get(0).getId();

        List<GroupFreeMentorDTO> mentorInfoRespDTOList = mentorInfoService.listMentorByComponentId(componentId).orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(mentorInfoRespDTOList)) {
            return Result.ok(null);
        }
        Long finalJid = jid;
        List<AuthorVO> mentorVoList = mentorInfoRespDTOList.stream().limit(6).map(e -> groupConverter.convertMentor(e, finalJid)).collect(Collectors.toList());
        return Result.ok(mentorVoList);
    }

    /**
     * 圈子问答合集
     *
     * @param groupId
     * @return
     */
    @GetMapping("/{groupId}/QaCollection")
    public Result<PageC<FeedVO>> queryQaCollection(@PathVariable("groupId") Long groupId, @RequestParam(required = false, defaultValue = "1") Integer pageNum, @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        Long jid = ThreadLocalCache.getJid();
        PageDTO<MediaIdDTO> page = groupContentClient.queryQaCollection(groupId, jid, pageNum, pageSize).orElse(PageDTO.empty(pageNum, pageSize));

        if (!CollectionUtils.isEmpty(page.getList())) {
            page.setList(page.getList().stream().filter(distinctByKey(MediaIdDTO::getMediaId)).collect(Collectors.toList()));
        }

        PageC<FeedVO> feedVOPageC = PageC.of(page, i -> feedV2Converter.convert(FeedContext.builder().id(i.getMediaId()).jid(jid).showBlindDateUser(true).showTodayTalk(true).showTodayTalkDetail(false).apiInfoEnum(ApiInfoEnum.GROUP_HOME_FLOW).build()));
        processQuestion(feedVOPageC.getList());
        return Result.ok(feedVOPageC);
    }

    /**
     * 处理问答兼容问题
     * 1、过滤掉有回答的提问本体
     * 2、兼容新老版本提问的内容标题显示问题
     *
     * @param feedList 信息流列表数据
     */
    private void processQuestion(List<FeedVO> feedList) {
        if (!CollectionUtils.isEmpty(feedList)) {
            //  移除禁用的数据
            feedList.removeIf(e -> Objects.equals(MediaResourceStatus.DISABLE.getCode(), e.getMediaStatus()));


            List<FeedVO> questionList = feedList.stream().filter(e -> DataType.QUESTION.getCode().equals(e.getAnswerSquareType())).collect(Collectors.toList());
            if (isHighVersion()) {
                //  新版本，1、有标题，有内容->如果内容包含标题，标题不显示；2、有标题，无内容->不处理
                questionList.stream().filter(e -> startsWith(e.getContent(), e.getTitle())).forEach(e -> e.setTitle(""));
            } else {
                //  老版本，1、有标题，有内容->不处理；2、有标题，无内容->将标题赋值给内容
                questionList.stream().filter(e -> StringUtils.isEmpty(e.getContent())).forEach(e -> e.setContent(e.getTitle()));
            }

            //  修复回答
            List<FeedVO> answerList = feedList.stream().filter(e -> DataType.ANSWER.getCode().equals(e.getAnswerSquareType())).collect(Collectors.toList());

            if (isHighVersion()) {
                answerList.stream().map(FeedVO::getQuestionVO).filter(Objects::nonNull).filter(e -> startsWith(e.getContent(), e.getTitle())).forEach(e -> e.setTitle(""));
            } else {
                answerList.stream().map(FeedVO::getQuestionVO).filter(Objects::nonNull).filter(e -> org.apache.commons.lang.StringUtils.isEmpty(e.getContent())).forEach(e -> e.setContent(e.getTitle()));
            }

        }


    }

    private boolean startsWith(String content, String title) {
        return StringUtils.hasText(content) && StringUtils.hasText(title) && content.startsWith(title);
    }

    /**
     * 是否为高版本
     *
     * @return 是否高版本
     */
    private boolean isHighVersion() {
        UserInfo userInfo = ThreadLocalCache.get();
        String os = userInfo.getOs();
        String appVersion = userInfo.getAppVersion();
        return "ios".equalsIgnoreCase(os) && "5.03.04".compareTo(appVersion) < 0 || "android".equalsIgnoreCase(os) && "5.03.03".compareTo(appVersion) < 0;
    }

}
