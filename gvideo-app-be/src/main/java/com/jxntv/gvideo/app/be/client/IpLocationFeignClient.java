package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.aliyun.sdk.IpLocationClient;
import com.jxntv.gvideo.app.be.client.fallback.IpLocationClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "aliyun-service", contextId = "ip-location", fallbackFactory = IpLocationClientFallback.class)
public interface IpLocationFeignClient extends IpLocationClient {
}
