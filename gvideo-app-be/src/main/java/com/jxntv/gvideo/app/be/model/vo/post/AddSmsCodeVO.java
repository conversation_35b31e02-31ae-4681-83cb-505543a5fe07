package com.jxntv.gvideo.app.be.model.vo.post;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2020-02-28
 */
@Data
@ApiModel(value = "AddSmsCodeVO", description = "短信验证码对象")
public class AddSmsCodeVO {

    @ApiModelProperty(value = "国家号", required = true)
    private String countryCode;
    @ApiModelProperty(value = "手机号", required = true)
    private String phone;
    @ApiModelProperty(value = "短信业务类型:1登录注册/2换绑手机号/3切换登录", required = true)
    private Integer type;
    @ApiModelProperty(value = "时间戳", required = true)
    private Long t;
    @ApiModelProperty(value = "签名字符串", required = true)
    private String sign;

}
