package com.jxntv.gvideo.app.be.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jxntv.gvideo.aliyun.sdk.dto.kuaidi100.LogisticsDTO;
import com.jxntv.gvideo.media.client.dto.PrizeImageDTO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: niedamin
 * @Date: 2023/04/19 17:36
 */
@Data
public class AppSignOrderVO {

    /**
     * 自增ID
     */
    private Long id;
    /**
     * 兑换时间
     */
    private Long jid;
    /**
     * 唯一订单号
     */
    private String orderNo;
    /**
     * 用户昵称
     */
    private String userName;
    /**
     * 用户手机
     */
    private String mobile;
    /**
     * 用户订单预留手机号
     */
    private String orderMobile;
    /**
     * 收货手机号
     */
    private String receiverName;
    /**
     * 奖品Id
     */
    private Long prizeId;
    /**
     * 奖品Id
     */
    private String prizeName;

    /**
     * 奖品获取类型：1-兑换奖品，2-抽奖奖品
     */
    private Integer prizeCategory;
    /**
     * 物流单号
     */
    private String trackingNo;
    /**
     * 奖品图片
     */
    private List<PrizeImageDTO> prizeImages;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 奖品类型
     */
    private Integer prizeType;
    /**
     * 奖品价格
     */
    private Integer prizePrice;
    /**
     * 收货地址ID
     */
    private Long addressId;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 奖品二维码
     */
    private String prizeQrCode;
    /**
     * 是否拥有核销权限
     */
    private Boolean hasPermission;
    /**
     * 兑换时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exchangeDate;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDate;

    /**
     * 物流信息
     */
    private LogisticsDTO logisticsInfo;
}
