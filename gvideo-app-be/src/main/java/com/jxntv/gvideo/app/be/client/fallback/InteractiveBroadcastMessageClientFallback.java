package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.InteractiveBroadcastMessageClient;
import com.jxntv.gvideo.media.client.dto.InteractiveBroadcastMessageDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> Created on 2020/12/31.
 */
@Slf4j
@Component
public class InteractiveBroadcastMessageClientFallback implements FallbackFactory<InteractiveBroadcastMessageClient> {
    @Override
    public InteractiveBroadcastMessageClient create(Throwable throwable) {
        return new InteractiveBroadcastMessageClient() {
            @Override
            public Result<PageDTO<InteractiveBroadcastMessageDTO>> page(String groupId, Integer page, Integer size) {
                log.error("InteractiveBroadcastMessageClient.page() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<InteractiveBroadcastMessageDTO> statusModify(Long id, Integer status) {
                log.error("InteractiveBroadcastMessageClient.statusModify() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> saveMessage(InteractiveBroadcastMessageDTO interactiveBroadcastMessageDTO) {
                log.error("InteractiveBroadcastMessageClient.saveMessage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
