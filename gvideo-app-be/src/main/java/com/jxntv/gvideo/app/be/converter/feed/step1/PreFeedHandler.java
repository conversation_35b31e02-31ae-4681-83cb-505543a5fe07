package com.jxntv.gvideo.app.be.converter.feed.step1;

import com.jxntv.gvideo.app.be.converter.feed.FeedHandler;
import com.jxntv.gvideo.app.be.manager.MediaResourceManager;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 信息流上下文预处理
 */
@Slf4j
@Order(1)
@Component
public class PreFeedHandler implements FeedHandler {

    @Resource
    private MediaResourceManager mediaResourceManager;

    @Override
    public boolean handle(FeedContext context) {
        MediaResourceDTO data = mediaResourceManager.getById(context.getId());
        if (Objects.nonNull(data)) {
            context.setMediaResource(data);
            context.setFeed(new FeedVO());
            return true;
        }

        return false;
    }
}
