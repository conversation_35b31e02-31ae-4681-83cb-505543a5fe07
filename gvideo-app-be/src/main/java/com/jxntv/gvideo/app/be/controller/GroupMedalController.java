package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.client.GroupMedalService;
import com.jxntv.gvideo.app.be.converter.GroupMedalConvert;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalGrantDetailsVO;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalShowVO;
import com.jxntv.gvideo.app.be.model.vo.GroupMedalVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.GroupMedalClient;
import com.jxntv.gvideo.group.sdk.dto.GroupMedalDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupMedalGrantDetailsDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupMedalImageSizeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/4/25 3:20 下午
 */
@RestController
@RequestMapping(GroupMedalClient.PREFIX)
@Api(value = "勋章接口", tags = {"勋章接口"})
public class GroupMedalController {

    @Autowired
    private GroupMedalService groupMedalService;

    @Autowired
    private GroupMedalConvert groupMedalConvert;

    @GetMapping("/show")
    @ApiOperation("圈子获取显示勋章列表")
    public Result<List<GroupMedalVO>> show(@RequestParam Long groupId) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        Result<List<GroupMedalDTO>> result = groupMedalService.show(userInfo.getJid(), groupId);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        return Result.ok(groupMedalConvert.convert(result.getResult(), GroupMedalImageSizeEnum.LARGE.getCode()));
    }

    @PutMapping("/show")
    @ApiOperation("更新勋章状态")
    public Result<Void> updateShow(@RequestBody GroupMedalShowVO showVO) {
        UserInfo userInfo = ThreadLocalCache.get();
        Result<Void> result = groupMedalService.update(userInfo.getJid(), showVO.getId());
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        return Result.ok(null);
    }

    @ApiOperation("获取勋章详情")
    @GetMapping
    public Result<GroupMedalVO> getDetails(@RequestParam Long id) {
        Result<GroupMedalDTO> result = groupMedalService.getDetails(id);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        return Result.ok(groupMedalConvert.convert(result.getResult(), GroupMedalImageSizeEnum.LARGE.getCode()));
    }

    @ApiOperation("勋章列表")
    @GetMapping({"/list"})
    public Result<List<GroupMedalVO>> list(@RequestParam Long jid, @RequestParam(required = false) Long groupId) {

        Result<List<GroupMedalDTO>> result = groupMedalService.list(jid, groupId, null);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        List<GroupMedalDTO> medalList = result.getResult();
        if (!CollectionUtils.isEmpty(medalList) && medalList.size() >10){
            medalList = medalList.subList(0,10);
        }
        return Result.ok(groupMedalConvert.convert(medalList, GroupMedalImageSizeEnum.MEDIUM.getCode()));
    }

    @ApiOperation("获取用户勋章授予详情")
    @GetMapping({"/grant/details"})
    public Result<GroupMedalGrantDetailsVO> getGrantDetails(@RequestParam Long medalId,@RequestParam Long jid) {

        Result<GroupMedalGrantDetailsDTO> result = groupMedalService.getGrantDetails(medalId,jid);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }
        return Result.ok(groupMedalConvert.convert(result.getResult()));
    }
}
