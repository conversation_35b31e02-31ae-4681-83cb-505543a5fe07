package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2022/4/25 3:24 下午
 */
@Data
@ApiModel("信息流圈子勋章对象")
public class GroupMedalFeedVO implements Serializable {

    @ApiModelProperty("勋章id")
    private Long id;

    @ApiModelProperty("关联圈子ID")
    private Long groupId;

    @ApiModelProperty("关联圈子名称")
    private String groupName;

    @ApiModelProperty("勋章名称")
    private String name;

    @ApiModelProperty("勋章授予原因")
    private String reason;

    @ApiModelProperty("勋章照片")
    private String imageUrl;

}
