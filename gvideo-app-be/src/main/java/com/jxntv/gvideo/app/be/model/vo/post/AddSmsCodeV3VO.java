package com.jxntv.gvideo.app.be.model.vo.post;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2020-02-28
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AddSmsCodeVO", description = "短信验证码对象")
public class AddSmsCodeV3VO  extends  AddSmsCodeVO{

    @ApiModelProperty(value = "验证码")
    private Captcha captcha;

    @Data
    public static class Captcha {
        private String lotNumber;
        private String captchaOutput;
        private String passToken;
        private String genTime;
    }

}
