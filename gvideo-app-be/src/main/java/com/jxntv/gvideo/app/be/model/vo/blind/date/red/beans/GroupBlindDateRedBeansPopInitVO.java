package com.jxntv.gvideo.app.be.model.vo.blind.date.red.beans;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupBlindDateRedBeansPopInitVO", description = "都市放心爱-红豆初始化弹窗")
public class GroupBlindDateRedBeansPopInitVO {


    @ApiModelProperty(value = "奖励总数")
    private Integer rewardTotal;

    @ApiModelProperty(value = "tip名称")
    private String tip;

    @ApiModelProperty(value = "tip链接")
    private String tipUrl;

    @ApiModelProperty(value = "奖励列表")
    private List<RewardInfo> rewards;


    @Data
    public static class RewardInfo {

        private Integer type;

        private Integer amount;
    }
}
