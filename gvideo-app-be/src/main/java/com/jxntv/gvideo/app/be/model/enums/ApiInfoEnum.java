package com.jxntv.gvideo.app.be.model.enums;


/**
 * API接口信息枚举
 */
public enum ApiInfoEnum {

    RESOURCE_DETAIL("resource_detail", "资源详情接口", "/api/media/{id}"),
    GROUP_HOME_FLOW("group_home_flow", "圈子主页信息流接口", "/api/ai/recommend/group/content"),
    TOPIC_DETAIL_FLOW("topic_home_flow", "话题详情信息流接口", "/api/ai/recommend/topic/content"),
    HOMEPAGE_FLOW("homepage_flow", "个人主页动态/喜欢接口", "/api/user/{userId}/homepage/posts, /api/user/{userId}/homepage/favorites"),
    GROUP_GATHER_FLOW("group_gather_flow", "圈子组件信息流接口", "/api/group/content/gather/search"),
    ANSWER_SQUARE_FLOW("answer_square_flow", "问答广场信息流接口", "/api/answer/square"),
    MY_GROUP_FLOW("my_group_flow", "我的圈子信息流接口", "api/group/my/content"),
    LIVE_PLAYING_LIST("live_playing_list", "直播-直播中(预告)", "/api/media/list/live/playing"),
    LIVE_PLAY_BACK("list_play_back", "直播-直播回顾", "/api/media/list/live/playback"),
    BLIND_DATE_POST_LIST("blind_date_post_list", "放心爱社区-用户动态列表", "/api/group/blind-date/{jid}/content"),
    GROUP_TODAY_TALK("group_today_talk", "圈子-今日聊", "/api/group/today-talk"),


    ;

    private String code;
    private String name;
    private String path;

    ApiInfoEnum(String code, String name, String path) {
        this.code = code;
        this.name = name;
        this.path = path;
    }

}
