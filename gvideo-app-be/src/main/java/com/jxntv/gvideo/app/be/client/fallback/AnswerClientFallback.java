package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.AnswerClient;
import com.jxntv.gvideo.media.client.dto.AnswerSquareDTO;
import com.jxntv.gvideo.media.client.dto.AnswerSquareSearchDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Created on 2021/8/16.
 */
@Slf4j
@Component
public class AnswerClientFallback implements FallbackFactory<AnswerClient> {
    @Override
    public AnswerClient create(Throwable throwable) {
        return new AnswerClient() {
            @Override
            public Result<Void> saveOrUpdate(AnswerSquareDTO dto) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> answerSquare(AnswerSquareSearchDTO dto) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AnswerSquareDTO>> listForGroup(Long groupId) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> status(Integer status, List<Long> ids, Integer type) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<AnswerSquareDTO>> page(AnswerSquareSearchDTO dto) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Map<Long, List<AnswerSquareDTO>>> fetchAnswerByQuestion(List<Long> qaList) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<MediaResourceDTO>> questionList(Long jid, int pageNum, int pageSize, Integer status) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> questionCount(Long jid, Integer status) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<AnswerSquareDTO>> answerList(Long jid, int pageNum, int pageSize, Integer answerStatus, Integer checkStatus) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Integer> answerCount(Long jid, Integer answerStatus, Integer checkStatus) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteByJid(Long jid) {
                log.error("AnswerClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AnswerSquareDTO> queryAnswerInfoById(String id) {
                log.error("AnswerClient.queryAnswerInfoById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
