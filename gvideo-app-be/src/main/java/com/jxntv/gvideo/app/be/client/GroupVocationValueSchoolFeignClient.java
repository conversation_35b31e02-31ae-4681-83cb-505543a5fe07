package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.GroupVocationValueSchoolClientFallback;
import com.jxntv.gvideo.group.sdk.client.GroupVocationValueSchoolClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 * @date 2022/7/6
 * Email: <EMAIL>
 */
@FeignClient(name = "group-service", contextId = "group-vocation-value-school", fallbackFactory = GroupVocationValueSchoolClientFallback.class)
public interface GroupVocationValueSchoolFeignClient extends GroupVocationValueSchoolClient {
}
