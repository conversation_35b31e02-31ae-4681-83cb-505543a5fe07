package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.model.vo.shop.GoodsVO;
import com.jxntv.gvideo.app.be.model.vo.shop.ParseLinkVO;
import com.jxntv.gvideo.app.be.service.ShopService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.AppRedDotClient;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotDTO;
import com.jxntv.gvideo.group.sdk.dto.AppRedDotSearchDTO;
import com.jxntv.gvideo.group.sdk.enums.BusinessType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/6/1
 * Email: <EMAIL>
 */
@RestController
@RequestMapping("/api/shop")
@Api(value = "商城接口管理", tags = {"商城接口管理"})
public class ShopController {

    @Resource
    private ShopService shopService;

    @Resource
    private AppRedDotClient appRedDotClient;

    @PostMapping("/goods/link/parse")
    @ApiOperation(value = "解析商品链接获取详情")
    public Result<GoodsVO> parseLink(@RequestBody ParseLinkVO parseLinkVO) {
        GoodsVO goods = shopService.parseGoodsURL(parseLinkVO.getLinkUrl());
        return Result.ok(goods);
    }


    @GetMapping("/goods")
    @ApiOperation(value = "商品列表接口")
    public Result<List<GoodsVO>> listOfGoods(@ApiParam(value = "资源ID，例如直播间ID") @RequestParam Long mediaId) {
        List<GoodsVO> page = shopService.listGoodsByMediaId(mediaId);
        return Result.ok(page);
    }

    @GetMapping("/coupon/red-dot")
    @ApiOperation(value = "是否展示红点")
    public Result<AppRedDotDTO> showRedDot() {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        // 查询是否需要小红点
        AppRedDotSearchDTO searchDTO = new AppRedDotSearchDTO();
        searchDTO.setJid(jid);
        searchDTO.setBusinessType(BusinessType.COUPON.getCode());
        Result<List<AppRedDotDTO>> result = appRedDotClient.query(searchDTO);
        if (!result.callSuccess()) {
            return Result.fail("查询失败");
        }
        List<AppRedDotDTO> list = result.getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Result.ok();
        }
        return Result.ok(list.get(0));
    }

    @PostMapping("/coupon/red-dot/read")
    @ApiOperation(value = "读取卡券")
    public Result<Void> readRedDot() {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        // 修改红点状态
        AppRedDotDTO dto = new AppRedDotDTO();
        dto.setJid(jid);
        dto.setBusinessType(BusinessType.COUPON.getCode());
        dto.setIsShow(0);
        Result<Void> res = appRedDotClient.modify(dto);
        if (!res.callSuccess()) {
            return Result.fail("修改红点状态失败");
        }
        return Result.ok();
    }

}
