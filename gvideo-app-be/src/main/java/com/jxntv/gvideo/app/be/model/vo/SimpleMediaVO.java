package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 *
 * Created on 2020-01-13
 */
@Data
@ApiModel(value="SimpleMediaVO", description="资源对象简单对象，不含用户信息")
public class SimpleMediaVO {

    @ApiModelProperty(value="资源大类", required = true, example = "1", notes = "1视频2FM")
    private int mediaType;
    @ApiModelProperty(value="资源唯一标识", required = true, example = "1234567")
    private Long id;
    @ApiModelProperty(value="资源样式", required = true, example = "1横视频2竖视频3音频")
    private int mediaStyle;
    @ApiModelProperty(value="资源标题", required = false, example = "贝加尔湖")
    private String title;
    @ApiModelProperty(value="资源链接集合", required = true)
    private List<String> mediaUrls;
    @ApiModelProperty(value="资源封面", required = false, example = "http://xxx.jpg")
    private String coverUrl;
    @ApiModelProperty(value="封面图片宽度", required = false, example = "360")
    private int coverWidth;
    @ApiModelProperty(value="封面图片高度", required = false, example = "240")
    private int coverHeight;
    @ApiModelProperty(value="资源评论数量", required = false, example = "100000")
    private Long reviews;
    @ApiModelProperty(value="分享链接", required = false)
    private String shareUrl;
    @ApiModelProperty(value="是否已收藏", required = true, example = "true")
    private Boolean isFavor;
    @ApiModelProperty(value="上传时间", required = true, example = "2020-01-12 12:00:00")
    private Date createDate;

}
