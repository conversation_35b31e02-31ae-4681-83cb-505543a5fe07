package com.jxntv.gvideo.app.be.converter.feed.step100;


import com.jxntv.gvideo.app.be.converter.feed.FeedHandler;
import com.jxntv.gvideo.app.be.manager.MediaResourceManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.manager.SpecialTagManager;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceImageDTO;
import com.jxntv.gvideo.media.client.dto.SpecialTagResourceDTO;
import com.jxntv.gvideo.media.client.enums.MediaResourceImageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关联专题的内容处理器
 */
@Slf4j
@Order(102)
@Component
public class SpecialItemContentHandler implements FeedHandler {

    @Resource
    private SpecialTagManager specialTagManager;
    @Resource
    private MediaResourceManager mediaResourceManager;
    @Resource
    private OssManager ossManager;

    @Override
    public boolean handle(FeedContext context) {
        //  判断是否关联专题
        Long id = context.getId();
        FeedVO feed = context.getFeed();

        SpecialTagResourceDTO specialTagResourceDTO = specialTagManager.getRelateByMediaId(id);
        if (Objects.nonNull(specialTagResourceDTO)) {
            Long specialId = specialTagResourceDTO.getSpecialId();

            MediaResourceDTO specialDTO = mediaResourceManager.get(specialId).orElse(null);
            if (Objects.nonNull(specialDTO)) {
                //  设置专题ID
                feed.setSpecialId(specialId);
                //  设置专题相关图片
                List<MediaResourceImageDTO> images = specialDTO.getImages();
                if (!CollectionUtils.isEmpty(images)) {
                    //  设置专题图片
                    Map<Integer, MediaResourceImageDTO> imageMap = images.stream().collect(Collectors.toMap(MediaResourceImageDTO::getType, e -> e, (a, b) -> b));
                    //  通用图
                    MediaResourceImageDTO defaultPic = imageMap.get(MediaResourceImageType.RECOMMENDED_FLOW.getCode());

                    //  头图
                    MediaResourceImageDTO headPic = imageMap.getOrDefault(MediaResourceImageType.HEAD_PIC.getCode(), defaultPic);
                    if (Objects.nonNull(headPic)) {
                        ossManager.getOssFile(headPic.getOssId()).ifPresent(oss -> feed.setHeadPic(oss.getUrl()));
                    }

                    //  详情大图
                    MediaResourceImageDTO detailBigPic = imageMap.getOrDefault(MediaResourceImageType.DETAIL_BIG_PIC.getCode(), defaultPic);
                    if (Objects.nonNull(detailBigPic)) {
                        ossManager.getOssFile(detailBigPic.getOssId()).ifPresent(oss -> feed.setDetailBigPic(oss.getUrl()));
                    }

                    //  详情小图
                    MediaResourceImageDTO detailSmallPic = imageMap.getOrDefault(MediaResourceImageType.DETAIL_SMALL_PIC.getCode(), defaultPic);
                    if (Objects.nonNull(detailSmallPic)) {
                        ossManager.getOssFile(detailSmallPic.getOssId()).ifPresent(oss -> feed.setDetailSmallPic(oss.getUrl()));
                    }

                }

            }


        }


        return true;
    }
}
