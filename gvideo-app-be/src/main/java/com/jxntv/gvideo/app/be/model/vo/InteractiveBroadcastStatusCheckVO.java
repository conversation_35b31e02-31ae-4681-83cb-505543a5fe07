package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> Created on 2021/3/10.
 */
@Data
public class InteractiveBroadcastStatusCheckVO {
    @ApiModelProperty(value = "0离线 1在线")
    private Integer online;
    @ApiModelProperty(value = "媒体内容id")
    private Long mediaId;
    @ApiModelProperty(value = "直播标题")
    private String title;
    @ApiModelProperty(value = "分享链接")
    private String shareUrl;
    @ApiModelProperty(value = "封面")
    private String thumb;
}
