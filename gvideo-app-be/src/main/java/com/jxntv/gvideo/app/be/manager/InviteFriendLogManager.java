package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.UserJspBeanClient;
import com.jxntv.gvideo.media.client.dto.UserJspBeanDTO;
import com.jxntv.gvideo.media.client.dto.UserJspBeanSearchDTO;
import com.jxntv.gvideo.user.client.InviteFriendLogClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class InviteFriendLogManager {

    @Resource
    private InviteFriendLogClient inviteFriendLogClient;

    @Cached(name = "app::invite-friend-log::count", cacheType = CacheType.LOCAL, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    public Integer getInviteUserCount(Long jid) {
        return inviteFriendLogClient.queryInviteFriendCount(jid).orElse(0);
    }


}
