package com.jxntv.gvideo.app.be.model.vo;

import com.jxntv.gvideo.app.be.client.ConsumerUserService;
import com.jxntv.gvideo.app.be.client.MediaResourceService;
import com.jxntv.gvideo.app.be.utils.SpringUtils;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.AnswerSquareDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2021/8/23.
 */
@Data
@ApiModel("圈子详情-问答对象")
public class GroupDetailAnswerVO {
    @ApiModelProperty("头像")
    private String avatar;
    @ApiModelProperty("用户jid")
    private Long jid;
    @ApiModelProperty("是否认证")
    private Boolean auth;
    @ApiModelProperty("用户昵称")
    private String name;
    @ApiModelProperty("媒体id")
    private Long mediaId;
    @ApiModelProperty("回答id")
    private Long commentId;
    @ApiModelProperty("提问人昵称")
    private String questionAuthorName;
    @ApiModelProperty("提问人昵称")
    private Long replyJid;

    public static GroupDetailAnswerVO convert(AnswerSquareDTO dto) {
        GroupDetailAnswerVO vo = new GroupDetailAnswerVO();
        Long jid = dto.getJid();
        Long mediaId = dto.getMediaId();
        vo.setJid(jid);
        vo.setMediaId(mediaId);
        vo.setCommentId(dto.getCommentId());
        vo.setName(dto.getAnswerAuthor());
        vo.setQuestionAuthorName(dto.getQuestionAuthor());
        Long authId = dto.getAuthId();
        vo.setAuth(Objects.nonNull(authId) && authId != 0);
        return vo;
    }


    public static List<GroupDetailAnswerVO> convertBatch(List<AnswerSquareDTO> dtoList) {
        List<GroupDetailAnswerVO> voList = dtoList.stream().map(GroupDetailAnswerVO::convert).collect(Collectors.toList());
        List<Long> replyList = dtoList.stream().map(AnswerSquareDTO::getReplyJid).collect(Collectors.toList());
        List<Long> mediaList = dtoList.stream().map(AnswerSquareDTO::getMediaId).collect(Collectors.toList());
        Result<List<ConsumerUserDTO>> replyRst = SpringUtils.getBean(ConsumerUserService.class).listUserByJids(replyList);
        Result<List<MediaResourceDTO>> mediaRst = SpringUtils.getBean(MediaResourceService.class).bulk(mediaList);
        Map<Long, ConsumerUserDTO> replyMap = new HashMap<>();
        //jid , 对应用户
        Map<Long, ConsumerUserDTO> questionMap;
        //媒体资源id，对应用户
        Map<Long, ConsumerUserDTO> mediaMap = new HashMap<>();
        if (replyRst.callSuccess()) {
            replyMap = replyRst.getResult().stream().collect(Collectors.toMap(ConsumerUserDTO::getJid,
                    Function.identity(), (a, b) -> a));
        }
        if (mediaRst.callSuccess()) {
            List<Long> authorList = mediaRst.getResult().stream().map(MediaResourceDTO::getReleaseId).collect(Collectors.toList());
            //获取作者jid和资源id的哈希表
            Map<Long, Long> mediaReleaseMap = mediaRst.getResult().stream().collect(Collectors
                    .toMap(MediaResourceDTO::getReleaseId, MediaResourceDTO::getId, (a, b) -> a));
            Result<List<ConsumerUserDTO>> qstRst = SpringUtils.getBean(ConsumerUserService.class).listUserByJids(authorList);
            if (qstRst.callSuccess()) {
                questionMap = qstRst.getResult().stream().collect(Collectors.toMap(ConsumerUserDTO::getJid,
                        Function.identity(), (a, b) -> a));
                //将jid，用户的哈希表转为mediaId，用户的哈希表
                questionMap.forEach((k, v) -> {
                    Long mediaId = mediaReleaseMap.get(k);
                    if (Objects.nonNull(mediaId)) {
                        mediaMap.put(mediaId, v);
                    }
                });
            }
        }

        Map<Long, ConsumerUserDTO> finalReplyMap = replyMap;
        voList.forEach(item -> {
            //回答人jid
            Long jid = item.getJid();
            ConsumerUserDTO reply = finalReplyMap.get(jid);
            if (Objects.nonNull(reply)) {
                item.setName(reply.getNickname());
            }
            //媒体id
            Long mediaId = item.getMediaId();
            ConsumerUserDTO question = mediaMap.get(mediaId);
            if (Objects.nonNull(question)) {
                item.setQuestionAuthorName(question.getNickname());
            }
        });
        return voList;
    }
}
