package com.jxntv.gvideo.app.be.login.security.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.User;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created on 2020-01-15
 */
@Log4j2
@Component
@RefreshScope
public class JwtUtils {

    /**
     * JID号
     */
    private static final String JID = Claims.SUBJECT;
    /**
     * 创建时间
     */
    private static final String CREATED = "created";
    /**
     * 权限列表
     */
    private static final String AUTHORITIES = "authorities";
    /**
     * 密钥
     */
    private static final String SECRET = "abcdefgh";
    /**
     * token redis key prefix
     */
    private static final String REDIS_PREFIX = "app::token::";

    @Value("${jwt.header}")
    private String tokenHeader;

    @Value("${jwt.tokenHead}")
    private String authTokenStart;

    /**
     * 最长有效期，单位秒
     */
    @Value("${jwt.expiration}")
    private long expiration;
    /**
     * 刷新时长，单位秒
     */
    @Value("${jwt.refreshTime}")
    private long refreshTime;
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 生成令牌
     *
     * @return 令牌
     */
    public String generateToken(User userDetail) {
        Map<String, Object> claims = new HashMap<>(4);
        claims.put(JID, userDetail.getUsername());
        claims.put(CREATED, new Date());
        return generateToken(claims);
    }

    /**
     * 根据Jid和mobile生成令牌
     *
     * @return 令牌
     */
    public String generateToken(Long jid) {
        Map<String, Object> claims = new HashMap<>(4);
        claims.put(JID, String.valueOf(jid));
        claims.put(CREATED, new Date());
        return generateToken(claims);
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String generateToken(Map<String, Object> claims) {
        Date expirationDate = new Date(System.currentTimeMillis() + expiration * 1000);
        String token = Jwts.builder().setClaims(claims).setExpiration(expirationDate).signWith(SignatureAlgorithm.HS512, SECRET).compact();
        //  设置redis过期时间
        stringRedisTemplate.opsForValue().set(REDIS_PREFIX + token, token, expiration, TimeUnit.SECONDS);

        return token;
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 根据请求令牌获取登录认证信息
     *
     * @return 用户名
     */
    public User getUserFromToken(HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getToken(request);
        if (StringUtils.isNotEmpty(token)) {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                return null;
            }
            String jid = claims.getSubject();
            if (jid == null) {
                return null;
            }
            if (isTokenExpired(token)) {
                return null;
            }
            // 解析对应的权限以及用户id
            Object authors = claims.get(AUTHORITIES);
            Set<String> perms = new HashSet<>();
            if (authors instanceof List) {
                for (Object object : (List) authors) {
                    perms.add(((Map) object).get("authority").toString());
                }
            }
            Collection<? extends GrantedAuthority> authorities = AuthorityUtils.createAuthorityList(perms.toArray(new String[0]));
            if (validateToken(token, jid)) {
                // 未把密码放到jwt
                return new User(jid, "", authorities);
            }
        }
        return null;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = Jwts.parser().setSigningKey(SECRET).parseClaimsJws(token).getBody();
        } catch (Exception e) {
            claims = null;
        }
        return claims;
    }

    /**
     * 验证令牌
     *
     * @param token
     * @param username
     * @return
     */
    private Boolean validateToken(String token, String username) {
        String userName = getUsernameFromToken(token);
        return (userName.equals(username) && !isTokenExpired(token));
    }

    /**
     * 刷新令牌
     *
     * @param token
     * @return
     */
    public String refreshToken(String token) {
        String refreshedToken;
        try {
            Claims claims = getClaimsFromToken(token);
            claims.put(CREATED, new Date());
            refreshedToken = generateToken(claims);
        } catch (Exception e) {
            refreshedToken = null;
        }
        return refreshedToken;
    }

    /**
     * 判断令牌是否过期
     *
     * @param token 令牌
     * @return 是否过期
     */
    private Boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            Date expiration = claims.getExpiration();
            boolean isTokenExpired = expiration.before(new Date());
            //  判断是否过期，如果没有过期在redis续租7天
            if (!isTokenExpired) {
                //  设置redis过期时间
                stringRedisTemplate.opsForValue().set(REDIS_PREFIX + token, token, refreshTime, TimeUnit.SECONDS);
            }
            return isTokenExpired;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return
     */
    public String getToken(HttpServletRequest request) {
        //先从header中直接取值
        String token = request.getHeader(tokenHeader);
        //token未在header中，从cookie中直接取值
        if (StringUtils.isEmpty(token)) {
            Cookie[] cookies = request.getCookies();
            if (cookies != null) {
                for (Cookie cookie : cookies) {
                    if (cookie.getName().equals(tokenHeader)) {
                        token = cookie.getValue();
                        break;
                    }
                }
            }
        }
        // token去掉Bearer
        if (StringUtils.isNotEmpty(token)) {
            token = token.substring(authTokenStart.length());
            if (token.startsWith("20")) {
                token = token.substring(2);
            }
        }
        return token;
    }

    public static void main(String[] args) {
        Map<String, Object> claims = new HashMap<>(4);
        claims.put(JID, String.valueOf(571297607236L));
        claims.put(CREATED, new Date());
        Date expirationDate = new Date(System.currentTimeMillis() + 10000 * 1000);
        String token = Jwts.builder().setClaims(claims).setExpiration(expirationDate).signWith(SignatureAlgorithm.HS512, SECRET).compact();
        System.out.println( token);
    }
}
