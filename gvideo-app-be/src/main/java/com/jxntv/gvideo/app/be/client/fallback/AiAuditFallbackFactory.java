package com.jxntv.gvideo.app.be.client.fallback;


import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AiAuditClient;
import com.jxntv.gvideo.om.dto.AiYiDunAuditDTO;
import com.jxntv.gvideo.om.param.AiAuditCallbackParam;
import com.jxntv.gvideo.om.param.GetAiAuditResultParam;
import com.jxntv.gvideo.om.param.PostAiAuditParam;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AiAuditFallbackFactory implements FallbackFactory<AiAuditClient> {
    @Override
    public AiAuditClient create(Throwable throwable) {

        return new AiAuditClient() {
            @Override
            public Result<Long> postAudit(PostAiAuditParam aiAuditParam) {
                log.error("AiAuditClient.postAudit() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> aiAuditCallbackResult(AiAuditCallbackParam param) {
                log.error("AiAuditClient.aiAuditCallbackResult() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<String>> getAiAuditResult(GetAiAuditResultParam aiAuditResultParam) {
                log.error("AiAuditClient.getAiAuditResult() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<AiYiDunAuditDTO>> getYiDunAuditResult(GetAiAuditResultParam aiAuditResultParam) {
                log.error("AiAuditClient.getAiAuditResult() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
