package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.AdvertSearchPlaceholderClientFallback;
import com.jxntv.gvideo.media.client.AdvertSearchPlaceholderClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "media-service", contextId = "advert-search-placeholder", fallbackFactory = AdvertSearchPlaceholderClientFallback.class)
public interface AdvertSearchPlaceholderService extends AdvertSearchPlaceholderClient {
}
