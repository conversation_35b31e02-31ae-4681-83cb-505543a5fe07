package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.client.AiAuditService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.param.AiAuditCallbackParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/aiAudit/")
public class AiAuditCallbackController {

    @Autowired
    private AiAuditService service;

    @RequestMapping("callback")
    public void callback(String result)
    {
        AiAuditCallbackParam param = new AiAuditCallbackParam();
        param.setResult(result);
        Result callbackResult = service.aiAuditCallbackResult(param);
        if(callbackResult.callSuccess()) {
            log.info("【新华智云回调】追踪id:{},入参:{}", TraceContext.traceId(), result);
        } else {
            log.info("【新华智云回调失败】追踪id:{},入参:{}", TraceContext.traceId(), result);
        }
    }
    
    
}
