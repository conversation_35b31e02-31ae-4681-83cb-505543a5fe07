package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.MediaResourceManager;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.media.collection.LiveCollectionListVO;
import com.jxntv.gvideo.app.be.utils.AppVersionUtils;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.SpecialTagClient;
import com.jxntv.gvideo.media.client.dto.BroadcastLocationDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.SpecialTagResourceDTO;
import com.jxntv.gvideo.media.client.dto.special.SpecialResourceSearchParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class LiveCollectionConverter {

    @Resource
    private SpecialTagClient specialTagClient;
    @Resource
    private FeedV2Converter feedV2Converter;

    @Resource
    private MediaResourceManager mediaResourceManager;


    public LiveCollectionListVO convert(BroadcastLocationDTO dto) {
        //老版本不返回竖屏直播合集
        if (!AppVersionUtils.isSupportLiveCollectionVertical(ThreadLocalCache.get())){
            if(Objects.equals(dto.getCollectionStyle(), 2)){
                return null;
            }
        }

        Long jid = ThreadLocalCache.getJid();
        String title = mediaResourceManager.get(dto.getMediaId()).map(MediaResourceDTO::getShowName).orElse("");

        LiveCollectionListVO vo = new LiveCollectionListVO();
        vo.setCollectionId(dto.getMediaId());
        vo.setPositionId(dto.getId());
        vo.setCollectionTitle(title);
        vo.setCollectionStyle(dto.getCollectionStyle());

        SpecialResourceSearchParam searchParam = new SpecialResourceSearchParam();
        searchParam.setSpecialId(dto.getMediaId());
        searchParam.setCurrent(1);
        searchParam.setSize(10);

        searchParam.addSort(SearchDTO.Sort.of("tagId",true));
        searchParam.addSort(SearchDTO.Sort.of("sort",false));

        List<SpecialTagResourceDTO> dtoList = specialTagClient.resourcePage(searchParam).map(PageDTO::getList).orElse(Collections.emptyList());
        //  转换对象
        List<FeedVO> contents = dtoList.stream().map(e -> feedV2Converter.convert(FeedContext.builder().id(e.getResourceId()).jid(jid).build())).collect(Collectors.toList());

        //竖屏直播合集移除横屏直播
        /*if(Objects.equals(dto.getCollectionStyle(), 3)){
            contents.removeIf(e -> Objects.equals(e.getMediaType(), 5));
        }*/

        //  移除已经下架的直播截取前4个有效内容
        contents = contents.stream().filter(e -> !Objects.equals(e.getLiveBroadcastStatus(), 4)).limit(4).collect(Collectors.toList());

        vo.setContents(contents);

        return vo;
    }


}
