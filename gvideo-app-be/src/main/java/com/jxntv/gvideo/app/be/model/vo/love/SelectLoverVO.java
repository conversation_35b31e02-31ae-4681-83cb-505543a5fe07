package com.jxntv.gvideo.app.be.model.vo.love;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/5/16
 * Email: <EMAIL>
 */
@Data
@ApiModel(value = "嘉宾选择对象", description = "选择嘉宾")
public class SelectLoverVO {
    @ApiModelProperty("直播间ID")
    private Long mediaId;

    @ApiModelProperty("选择用户jid")
    private Long selectJid;
}
