package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.SearchShieldClient;
import com.jxntv.gvideo.om.dto.search.SearchShieldDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/5/28
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class SearchShieldManager {

    @Resource
    private SearchShieldClient searchShieldClient;


    @Cached(name = "app::search-shield::all", cacheType = CacheType.BOTH, expire = 5, timeUnit = TimeUnit.MINUTES, postCondition = "#result.code==0")
    @CacheRefresh(refresh = 5, stopRefreshAfterLastAccess = 60)
    public Result<List<SearchShieldDTO>> all() {
        return searchShieldClient.all();
    }


}
