package com.jxntv.gvideo.app.be.model.vo.lecture;

import com.jxntv.gvideo.app.be.common.vo.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupGatherInstructionListVO", description = "课堂组件-视频列表信息")
public class GroupGatherInstructionListVO {

    @ApiModelProperty(value = "分享标题", required = true)
    private String shareTitle;

    @ApiModelProperty(value = "分享链接", required = true)
    private String shareUrl;

    @ApiModelProperty(value = "分页列表信息")
    private Page<GroupGatherInstructionInfoVO> record;
}
