package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.config.JspBeanConfig;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.AppOptimizeSignVO;
import com.jxntv.gvideo.app.be.model.vo.AppWatchDurationOptimizeVO;
import com.jxntv.gvideo.app.be.model.vo.AppWatchDurationReportVO;
import com.jxntv.gvideo.app.be.redis.RedisKeyConstant;
import com.jxntv.gvideo.app.be.service.AppUserSignService;
import com.jxntv.gvideo.app.be.utils.SignUtils;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.UserJspBeanClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/28 11:38
 */
@RestController
@RequestMapping("/api/app/optimize")
@Api(value = "app优化接口", tags = {"app优化接口"})
@Slf4j
public class AppOptimizeController {

    @Resource
    private AppUserSignService appUserSignService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private UserJspBeanClient userJspBeanClient;

    @PostMapping("/sign")
    @ApiOperation(value = "用户签到(optimize)", notes = "用户签到(optimize)")
    public Result<Integer> sign(@RequestBody AppOptimizeSignVO vo, HttpServletRequest request) {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("请先登录");
        }

        return appUserSignService.sign(jid,vo.getT());
    }

    @PostMapping("/watch/duration")
    @ApiOperation(value = "上报时长(optimize)", notes = "上报时长(optimize)")
    public Result<Boolean> reportWatchDurationOptimize(@RequestBody @Validated AppWatchDurationOptimizeVO vo, HttpServletRequest request) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        /*Result result = this.checkSign(jid, vo);
        if (!result.callSuccess()) {
            return result;
        }*/
        if (vo.getEt() <= vo.getSt()) {
            return Result.ok(true);
        }
        if(vo.getSt() < 1697688000000L){
            return Result.ok(true);
        }

        long startTime = vo.getSt();
        long endTime = vo.getEt();
        long duration = endTime - startTime;
        double discount;
        Random random = new Random();
        if(vo.getSt() < 1733470200000L){
            discount = 0.1 + 0.3 * random.nextDouble();
        }else{
            discount = 0.3 + 0.4 * random.nextDouble();
        }
        // 格式化折扣到小数点后两位
        discount = Math.round(discount * 100.0) / 100.0;
        // 计算新的结束时间
        long newEndTime = startTime + (long) (duration * discount);
        // 设置新的结束时间
        vo.setEt(newEndTime);


        List<JspBeanConfig.WatchRule> rules = this.appUserSignService.getWatchRules(vo.getSt());

        if (!CollectionUtils.isEmpty(rules)) {
            int randomMilliseconds;
            int lastRuleDuration = 0;
            long t = vo.getSt();
            for (JspBeanConfig.WatchRule rule : rules){
                randomMilliseconds  =   this.getRandomMilliseconds();
                int nextDifferDuration = rule.getDuration() - lastRuleDuration;
                t = t + randomMilliseconds + (nextDifferDuration * 1000L);
                if (t <= vo.getEt()){
                    try {
                        this.appUserSignService.reportWatchDurationV2(jid,nextDifferDuration,t);
                    } catch (Exception e) {
                        log.error("reportWatchDurationOptimize error", e);
                    }
                }else {
                    break;
                }
                lastRuleDuration = rule.getDuration();
            }
           // this.appUserSignService.reportWatchDurationV2(jid, vo.getDuration(),vo.getT());
        }

        return Result.ok(true);
    }


    Result checkSign(Long jid, AppWatchDurationReportVO vo) {
        Long t = vo.getT();
        //  判断时间戳是否合法
        long diff = System.currentTimeMillis() / 1000 / 60 - t / 60;
        if (diff < -5 || diff > 20) {
            log.info("diff is {}", diff);
            return Result.fail("签名已经过期");
        }

        Map<String, Object> params = new HashMap<>(3);
        params.put("duration", vo.getDuration());
        params.put("jid", jid);
        params.put("t", t);

        String sign = SignUtils.sign(params, SignUtils.SMS_SECRET, t);
        if (!Objects.equals(sign, vo.getSign())) {
            return Result.fail("签名验证失败");
        }

        if (Boolean.FALSE.equals(stringRedisTemplate.opsForValue().setIfAbsent(String.format(RedisKeyConstant.JSP_BEAN_WATCH_REPORT_SIGN_KEY, jid, vo.getSign()), "1", 30L, TimeUnit.SECONDS))) {
            return Result.fail("签名已失效");
        }
        return Result.ok();
    }

    int getRandomMilliseconds(){
        return (int) (Math.random() * 5001) + 5000;
    }

    public static void main(String[] args) {
        Random random = new Random();
       double discount = 0.3 + 0.4 * random.nextDouble();

       discount = Math.round(discount * 100.0) / 100.0;

       System.out.println(discount);
    }
}
