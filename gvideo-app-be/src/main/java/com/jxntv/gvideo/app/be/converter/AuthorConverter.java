package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.GroupManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.app.be.utils.ShareUrlUtils;
import com.jxntv.gvideo.app.be.utils.UserAuditUtils;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.group.sdk.client.GroupGatherClient;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.client.MentorInfoClient;
import com.jxntv.gvideo.group.sdk.dto.GroupGatherDTO;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorSearchDTO;
import com.jxntv.gvideo.group.sdk.enums.GroupGatherTypeEnum;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.PendantClient;
import com.jxntv.gvideo.user.client.dto.*;
import com.jxntv.gvideo.user.client.dto.pendant.PendantWearInfoDTO;
import com.jxntv.gvideo.user.client.enums.PendantType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 用户模型转换器
 */
@Slf4j
@Component
public class AuthorConverter {

    @Resource
    private OssManager ossManager;
    @Resource
    private InteractClient interactClient;
    @Resource
    private ConsumerUserClient consumerUserClient;
    @Resource
    private GroupTopicClient groupTopicClient;
    @Resource
    private MentorInfoClient mentorInfoClient;
    @Resource
    private GroupGatherClient groupGatherClient;
    @Resource
    private GroupManager groupManager;
    @Resource
    private GroupConverter groupConverter;
    @Resource
    private PendantClient pendantClient;


    public AuthorVO convert(CertificationDTO dto) {
        Long jid = ThreadLocalCache.getJid();
        String avatarUrl = ossManager.getOssFile(dto.getAvatar()).map(OssDTO::getUrl).orElse("");
        String backgroundUrl = ossManager.getOssFile(dto.getBackgroundImage()).map(OssDTO::getUrl).orElse("");
        Boolean followed = Objects.nonNull(jid) && interactClient.isAlreadyFollow(dto.getId(), jid).orElse(false);

        AuthorVO vo = new AuthorVO();
        vo.setId(dto.getId());
        vo.setShareUrl(ShareUrlUtils.generateShareUrl("pgc", dto.getId()));
        vo.setType(0);
        vo.setName(dto.getName());
        vo.setAvatar(avatarUrl);
        vo.setBackground(backgroundUrl);
        vo.setIntro(dto.getIntroduce());
        vo.setIsFollow(followed);
        vo.setIsFollowMe(false);
        vo.setIsAuthentication(true);
        vo.setAuthenticationIntro("");
        return vo;
    }

    public AuthorUgcVO convertUgc(ConsumerUserDTO dto) {
        Long jid = ThreadLocalCache.getJid();
        String avatarUrl = ossManager.getOssFile(dto.getAvatar()).map(OssDTO::getUrl).orElse("");
        //  我是否关注了此人
        Boolean isFollow = Objects.nonNull(jid) && !Objects.equals(jid, dto.getJid()) && interactClient.isAlreadyFollow(dto.getJid(), jid).orElse(false);
        //  此人关注的总数量
        Integer followCount = interactClient.getFollowCount(dto.getJid()).orElse(0);
        //  此人是否关注了我
        Boolean isFollowMe = Objects.nonNull(jid) && !Objects.equals(jid, dto.getJid()) && interactClient.isFollowMe(jid, dto.getJid()).orElse(false);
        //  此人的粉丝数量
        Integer fansCount = interactClient.getFanCount(dto.getJid()).orElse(0);
        //  此人加入的社区数量
        Integer followGroupCount = groupTopicClient.myGroupCount(dto.getJid()).orElse(0);

        AuthorUgcVO vo = new AuthorUgcVO();
        vo.setId(dto.getJid());
        vo.setShareUrl(ShareUrlUtils.generateShareUrl("ugc", dto.getJid()));
        vo.setType(1);
        vo.setProvinceId(dto.getProvinceId());
        vo.setProvince(dto.getProvince());
        vo.setCityId(dto.getCityId());
        vo.setCity(dto.getCity());
        vo.setName(dto.getNickname());
        vo.setAvatar(avatarUrl);
        vo.setIntro(dto.getInfo());
        vo.setGender(dto.getGender());
        vo.setBirthday(dto.getBirthday());
        vo.setIsFollow(isFollow);
        vo.setIsFollowMe(isFollowMe);
        vo.setFollowCount(followCount);
        // 此人关注的圈子数量
        vo.setFollowGroupCount(followGroupCount);
        // 此人的粉丝数量
        vo.setFansCount(fansCount);
        // 此人的认证信息
        ConsumerAuthenticationDTO authenticationDTO = dto.getConsumerAuthentication();
        if (Objects.nonNull(authenticationDTO)) {
            vo.setIsAuthentication(true);
            vo.setAuthenticationIntro(authenticationDTO.getIntroduction());
        } else {
            vo.setIsAuthentication(false);
            vo.setAuthenticationIntro("");
        }

        vo.setNewAvatar(getNewAvatar(dto.getJid()));

        //  设置导师相关信息
        GroupFreeMentorSearchDTO mentorInfoSearchDTO = new GroupFreeMentorSearchDTO();
        mentorInfoSearchDTO.setMentorJid(dto.getJid());
        mentorInfoSearchDTO.setSize(1);
        mentorInfoSearchDTO.setCurrent(1);
        List<GroupFreeMentorDTO> mentorInfoResps = mentorInfoClient.searchFreeMentor(mentorInfoSearchDTO).map(PageDTO::getList).orElse(Collections.emptyList());
        if (!CollectionUtils.isEmpty(mentorInfoResps)) {
            GroupFreeMentorDTO mentorInfoRespDTO = mentorInfoResps.get(0);
            Long componentId = mentorInfoRespDTO.getComponentId();
            GroupGatherDTO groupGatherDTO = groupGatherClient.getGroupGather(componentId).orElse(null);
            if (Objects.nonNull(groupGatherDTO)) {
                Long groupId = groupGatherDTO.getGroupId();
                GroupInfoDTO simpleGroupInfo = groupManager.getSimpleById(groupId);
                String groupName = Objects.isNull(simpleGroupInfo) ? "" : simpleGroupInfo.getName();
                String areasOfExpertise = "";
                List<String> areasOfExpertises = mentorInfoRespDTO.getAreasOfExpertise();
                if (!CollectionUtils.isEmpty(areasOfExpertises)) {
                    areasOfExpertise = String.join("/", areasOfExpertises);
                }

                MentorVO mentorVO = new MentorVO();
                mentorVO.setId(dto.getJid());
                mentorVO.setAvatar(vo.getAvatar());
                mentorVO.setName(vo.getName());
                mentorVO.setGroupId(groupId);
                mentorVO.setGroupName(groupName);
                mentorVO.setAreasOfExpertise(areasOfExpertise);
                mentorVO.setIsAuthentication(vo.getIsAuthentication());
                mentorVO.setAuthenticationIntro(vo.getAuthenticationIntro());

                //  判断是否需要添加导师组件信息
                List<GroupGatherDTO> groupGatherDTOS = groupGatherClient.listByType(groupId, GroupGatherTypeEnum.QUESTION_ANSWER.getCode()).orElse(Collections.emptyList());
                List<Long> enableComponentIds = groupGatherDTOS.stream().map(GroupGatherDTO::getId).collect(Collectors.toList());
                if (enableComponentIds.contains(componentId)) {
                    vo.setGather(groupConverter.toGatherVo(groupGatherDTO));
                    vo.setMentor(mentorVO);
                }

            }

        }
        if(!StringUtils.isEmpty(dto.getMobile())){
            vo.setPhoneNumber(AESUtil.AESCBCEncode(dto.getMobile()));
        }
        PendantWearInfoDTO pendantInfo = this.pendantClient.queryWearInfo(vo.getId(), PendantType.AVATAR.getCode()).orElse(null);
        if (Objects.nonNull(pendantInfo) && StringUtils.hasText(pendantInfo.getOssId())){
            FeedPendantVO pendantVO = new FeedPendantVO();
            pendantVO.setId(pendantInfo.getId());
            pendantVO.setUrl(ossManager.getOriFile(pendantInfo.getOssId()).map(OssDTO::getUrl).orElse(""));
            pendantVO.setDescription(pendantInfo.getDescription());
            vo.setAvatarPendant(pendantVO);
        }
        return vo;
    }

    /**
     * 头像审核信息,自己查看自己的时候才可以显示
     *
     * @param jid
     * @return
     */
    private SimpleAuditResultVO getNewAvatar(Long jid) {
        //  只有自己才可以查看到头像审核信息
        if (Objects.equals(jid, ThreadLocalCache.getJid())) {
            ConsumerAvatarDTO consumerAvatarDTO = consumerUserClient.getAvatarByJid(jid).orElse(null);
            if (Objects.nonNull(consumerAvatarDTO)) {
                String url = ossManager.getOssFile(consumerAvatarDTO.getAvatar()).map(OssDTO::getUrl).orElse("");
                return UserAuditUtils.auditAvatar(consumerAvatarDTO, url);
            }
        }
        return null;
    }

}
