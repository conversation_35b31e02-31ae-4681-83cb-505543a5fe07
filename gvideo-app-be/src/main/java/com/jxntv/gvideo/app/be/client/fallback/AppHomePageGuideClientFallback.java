package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.AppHomePageGuideClient;
import com.jxntv.gvideo.om.dto.AppHomePageGuideDTO;
import com.jxntv.gvideo.om.dto.AppHomePageGuideSearchDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AppHomePageGuideClientFallback implements FallbackFactory<AppHomePageGuideClient> {

    @Override
    public AppHomePageGuideClient create(Throwable throwable) {
        return new AppHomePageGuideClient(){

            @Override
            public Result<Long> create(AppHomePageGuideDTO appHomePageGuideDTO) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> updateById(Long id, AppHomePageGuideDTO appHomePageGuideDTO) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AppHomePageGuideDTO> getById(Long id) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteById(Long id) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteByKingkongAndStrategyId(Long kingkongId,Long StrategyId) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<AppHomePageGuideDTO>> page(AppHomePageGuideSearchDTO appHomePageGuideSearchDTO) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<AppHomePageGuideDTO> getByChannel(Long channelId) {
                log.error("ActivityClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
