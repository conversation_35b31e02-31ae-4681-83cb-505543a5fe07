package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.user.client.PushClient;
import com.jxntv.gvideo.user.client.dto.PushTokenDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> Created on 2021/1/19.
 */
@Slf4j
@RestController
@RequestMapping(PushClient.PREFIX)
@Api(value = "推送接口", tags = {"推送接口"})
public class PushController {
    @Resource
    private PushClient pushClient;

    @PostMapping("/token")
    @ApiOperation(value = "推送token数据", notes = "推送token数据")
    Result<Map<String, Object>> token(@RequestBody PushTokenDTO dto) {
        if (StringUtils.isEmpty(dto.getToken())) {
            return Result.fail("token为空");
        }
        log.info("【推送token】请求参数：{}", JsonUtils.toJson(dto));
        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.nonNull(userInfo)) {
            dto.setJid(userInfo.getJid());
        }
        Object tokenId = pushClient.token(dto).getResult();

        Map<String, Object> rst = new HashMap<>(2);
        rst.put("id", tokenId);
        return Result.ok(rst);
    }

    @PutMapping("/reset/badge/{id}")
    @ApiOperation(value = "重置msg总数", notes = "重置msg总数")
    Result<Boolean> resetBadge(@PathVariable Long id) {
        return pushClient.updateRecMsg(id, 0);
    }
}
