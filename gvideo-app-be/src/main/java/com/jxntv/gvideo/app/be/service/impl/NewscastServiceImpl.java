package com.jxntv.gvideo.app.be.service.impl;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.config.NewscastConfig;
import com.jxntv.gvideo.app.be.converter.NewscastConverter;
import com.jxntv.gvideo.app.be.manager.*;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastEntireVO;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitPage;
import com.jxntv.gvideo.app.be.model.vo.news.NewscastSplitVO;
import com.jxntv.gvideo.app.be.service.NewscastService;
import com.jxntv.gvideo.app.be.utils.ShareUrlUtils;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.media.client.dto.newscast.*;
import com.jxntv.gvideo.media.client.dto.tv.TvChannelDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvColumnDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class NewscastServiceImpl implements NewscastService {

    @Resource
    private NewscastManager newscastManager;
    @Resource
    private NewscastConverter newscastConverter;
    @Resource
    private NewscastConfig newscastConfig;
    @Resource
    private TvChannelManager tvChannelManager;
    @Resource
    private TvColumnManager tvColumnManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private MediaResourceExternalManager mediaResourceGanyunManager;

    @Override
    public NewscastEntireVO getEntire(String publishDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(publishDate, formatter);
        LocalDateTime liveStartTime = getLiveStartTime();
        LocalDateTime liveEndTime = getLiveEndTime();


        if (LocalDate.now().isEqual(date)) {
            //  今日请求
            LocalDateTime now = LocalDateTime.now();
            //  新闻联播开始之前,肯定没有当日整期
            if (now.isBefore(liveStartTime)) {
                return null;
            }

            //  新闻联播直播中，构建一个电视直播的返回对象
            if (now.isBefore(liveEndTime)) {
                return getLiveNewscast();
            }

            //  直播结束之后，如果还没有发布当天整期，就还继续返回电视直播，但是状态改为直播结束
            JxntvContentRequest param = new JxntvContentRequest();
            param.setSiteid(10001);
            param.setCatids("85");
            param.setPage(1);
            param.setPagesize(1);
            param.setPub_from(date.format(formatter));
            param.setPub_end(date.plusDays(1L).format(formatter));
            JxntvContentResponse response = newscastManager.getContentList(param);
            AssertUtil.notNull(response, CodeMessage.ERROR);

            List<JxntvContentDTO> contentList = response.getData();
            if (CollectionUtils.isEmpty(contentList)) {
                return getLiveNewscast();
            } else {
                return newscastConverter.convert(contentList.get(0));
            }

        } else {
            //  往日请求，直接返回查询结果
            JxntvContentRequest param = new JxntvContentRequest();
            param.setSiteid(10001);
            param.setCatids("85");
            param.setPage(1);
            param.setPagesize(1);
            param.setPub_from(date.format(formatter));
            param.setPub_end(date.plusDays(1L).format(formatter));
            JxntvContentResponse response = newscastManager.getContentList(param);
            AssertUtil.notNull(response, CodeMessage.ERROR);

            List<JxntvContentDTO> contentList = response.getData();
            AssertUtil.notEmpty(contentList, CodeMessage.NOT_FOUND);

            return newscastConverter.convert(contentList.get(0));
        }

    }

    @Override
    public Page<NewscastEntireVO> entireList(Integer pageNum, Integer pageSize) {
        JxntvContentRequest param = new JxntvContentRequest();
        param.setSiteid(10001);
        param.setCatids("85");
        param.setPage(pageNum);
        param.setPagesize(pageSize);
        JxntvContentResponse response = newscastManager.getContentList(param);
        AssertUtil.notNull(response, CodeMessage.ERROR);

        log.info("赣云整期数据：{}", JsonUtils.toJson(response));

        List<JxntvContentDTO> contentList = response.getData();

        List<NewscastEntireVO> voList = contentList.stream().map(e -> newscastConverter.convert(e)).filter(Objects::nonNull).collect(Collectors.toList());

        LocalDateTime liveStartTime = getLiveStartTime();

        //  如果是第一页请求且在今天开始直播之后，还没有整期，插入一个直播整期
        if (Objects.equals(pageNum, 1) && LocalDateTime.now().isAfter(liveStartTime)) {
            if (CollectionUtils.isEmpty(voList) || !Objects.equals(voList.get(0).getPublishDate(), LocalDate.now())) {
                NewscastEntireVO liveNewscast = getLiveNewscast();
                voList.add(0, liveNewscast);
            }
        }

        Page<NewscastEntireVO> page = new Page<>();
        page.setHasMore(response.getNextpage() && !CollectionUtils.isEmpty(voList));
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setList(voList);

        return page;
    }

    @Override
    public NewscastSplitPage<NewscastSplitVO> splitList(String publishDate, Integer pageNum, Integer pageSize) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date = LocalDate.parse(publishDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));


        JxntvContentRequest param = new JxntvContentRequest();
        param.setSiteid(10001);
        param.setCatids(Stream.of(25, 52, 53, 54, 327, 386).map(String::valueOf).collect(Collectors.joining(",")));
        param.setTags("江西新闻联播");
        param.setPub_from(date.format(formatter));
        param.setPub_end(date.plusDays(1L).format(formatter));
        param.setPagesize(120);
        param.setAppid(4);
        param.setOrderby("published+asc");
        JxntvContentResponse response = newscastManager.getContentList(param);
        AssertUtil.notNull(response, CodeMessage.ERROR);

        log.info("赣云拆条数据：{}", JsonUtils.toJson(response));

        List<JxntvContentDTO> contentList = response.getData();
        List<NewscastSplitVO> voList = contentList.stream().map(e -> newscastConverter.convertSplit(e)).filter(Objects::nonNull).collect(Collectors.toList());

        //  请求发布日期
        LocalDateTime publishDateTime = LocalDateTime.of(date, LocalTime.now());
        int liveStatus = getLiveStatus(publishDateTime);

        NewscastSplitPage<NewscastSplitVO> page = new NewscastSplitPage<>();
        page.setHasMore(response.getNextpage());
        page.setPageNum(pageNum);
        page.setPageSize(pageSize);
        page.setList(voList);
        page.setLiveStatus(liveStatus);

        return page;
    }


    @Override
    @Deprecated
    public JxntvCategoryResponse h5EntireList(JxntvCategroyRequest param) {
        JxntvCategoryResponse response = newscastManager.getCategoryList(param);
        if (Objects.nonNull(response)) {
            JxntvCategoryResponse.OutDataDTO data = response.getData();
            if (Objects.nonNull(data)) {
                List<JxntvCategoryDTO> data1 = data.getData();
                if (!CollectionUtils.isEmpty(data1)) {
                    List<JxntvCategoryDTO> collect = data1.stream().filter(e -> hasContent(e.getContentid())).collect(Collectors.toList());
                    collect.forEach(e -> e.setUrl(ShareUrlUtils.generateShareUrl("media", Long.valueOf(e.getJspid()))));
                    data.setData(collect);
                }
            }


        }

        return response;
    }


    @Override
    @Deprecated
    public JxntvContentResponse h5ContentList(JxntvContentRequest param) {
        JxntvContentResponse result = newscastManager.getContentList(param);
        List<JxntvContentDTO> data = result.getData();
        if (!CollectionUtils.isEmpty(data)) {
            List<JxntvContentDTO> collect = data.stream().filter(e -> hasContent(e.getContentid())).collect(Collectors.toList());
            collect.forEach(e -> e.setUrl(ShareUrlUtils.generateShareUrl("media", Long.valueOf(e.getJspid()))));
            result.setData(collect);
        }

        return result;

    }

    @Override
    public JxntvMediaResponse getMediaIds(List<Long> contentIds) {
        List<JxntvMediaResponse.DataDTO> collect = contentIds.stream().map(newscastConverter::convert).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, JxntvMediaResponse.DataDTO> map = collect.stream().collect(Collectors.toMap(e -> String.valueOf(e.getContentId()), e -> e, (e1, e2) -> e1));

        JxntvMediaResponse result = new JxntvMediaResponse();
        result.setCoede(200);
        result.setState(true);
        result.setData(map);
        return result;
    }

    @Override
    public JxntvCategoryResponse h5EntireListV2(JxntvCategroyRequest request) {
        if (Objects.isNull(request.getPage())) {
            request.setPage(1);
        }
        JxntvCategoryResponse response = newscastManager.getCategoryListV2(request);
        if (Objects.nonNull(response)) {
            JxntvCategoryResponse.OutDataDTO data = response.getData();
            if (Objects.nonNull(data)) {
                List<JxntvCategoryDTO> data1 = data.getData();
                if (!CollectionUtils.isEmpty(data1)) {
                    List<JxntvCategoryDTO> collect = data1.stream().filter(e -> hasContent(e.getContentid())).collect(Collectors.toList());
                    collect.forEach(e -> e.setUrl(ShareUrlUtils.generateShareUrl("media", Long.valueOf(e.getJspid()))));
                    data.setData(collect);
                }
            }


        }

        return response;
    }


    private boolean hasContent(Long contentId) {
        return Objects.nonNull(mediaResourceGanyunManager.getByContentId(String.valueOf(contentId)));
    }


    @Override
    public JxntvContentResponse h5ContentListV2(JxntvContentRequest request) {
        if (Objects.isNull(request.getPage())) {
            request.setPage(1);
        }
        JxntvContentResponse result = newscastManager.getContentListV2(request);
        List<JxntvContentDTO> data = result.getData();
        if (!CollectionUtils.isEmpty(data)) {
            List<JxntvContentDTO> collect = data.stream().filter(e -> hasContent(e.getContentid())).collect(Collectors.toList());
            collect.forEach(e -> e.setUrl(ShareUrlUtils.generateShareUrl("media", Long.valueOf(e.getJspid()))));
            result.setData(collect);
        }

        return result;

    }

    /**
     * 构建江西新闻联播直播
     *
     * @return 江西新闻联播直播整期
     */
    private NewscastEntireVO getLiveNewscast() {

        TvChannelDTO tvChannelDTO = tvChannelManager.getById(newscastConfig.getChannelId()).orElseThrow();
        TvColumnDTO tvColumnDTO = tvColumnManager.getById(newscastConfig.getColumnId()).orElseThrow();
        String thumb = ossManager.getOssFile(tvColumnDTO.getCoverId()).map(OssDTO::getUrl).orElse(tvColumnDTO.getCoverUrl());


        NewscastEntireVO result = new NewscastEntireVO();
        result.setMediaId(0L);
        result.setTitle("江西新闻联播");
        result.setThumb(thumb);
        result.setLiveStatus(LocalDateTime.now().isBefore(getLiveEndTime()) ? 1 : 2);
        result.setLiveStartTime(getLiveStartTime());
        result.setLiveEndTime(getLiveEndTime());
        result.setLiveUrl(tvChannelDTO.getPlayUrl());
        result.setAuthUrl(tvChannelDTO.getAuthUrl());
        result.setPublishDate(LocalDate.now());
        result.setPublishTime(LocalDateTime.now());

        return result;
    }


    /**
     * 判断直播状态
     *
     * @param publishDateTime 发布时间
     * @return 当前直播状态
     */
    private Integer getLiveStatus(LocalDateTime publishDateTime) {
        int liveStatus;

        LocalDateTime liveStartDateTime = getLiveStartTime();
        LocalDateTime liveEndDateTime = getLiveEndTime();
        if (publishDateTime.isBefore(liveStartDateTime)) {
            liveStatus = 0;
        } else if (publishDateTime.isBefore(liveEndDateTime)) {
            liveStatus = 1;
        } else {
            liveStatus = 2;
        }
        return liveStatus;

    }


    private LocalDateTime getLiveStartTime() {
        LocalTime localTime = LocalTime.parse(newscastConfig.getLiveStartTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        return LocalDateTime.of(LocalDate.now(), localTime);
    }

    private LocalDateTime getLiveEndTime() {
        LocalTime localTime = LocalTime.parse(newscastConfig.getLiveEndTime(), DateTimeFormatter.ofPattern("HH:mm:ss"));
        return LocalDateTime.of(LocalDate.now(), localTime);
    }
}
