package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.SoundDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodAudioPlayDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodVideoPlayDTO;
import com.jxntv.gvideo.app.be.client.*;
import com.jxntv.gvideo.app.be.manager.GroupManager;
import com.jxntv.gvideo.app.be.manager.MediaResourceExternalManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.service.MediaService;
import com.jxntv.gvideo.app.be.utils.*;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.sdk.dto.GroupRelatedDTO;
import com.jxntv.gvideo.interact.client.CommentClient;
import com.jxntv.gvideo.interact.client.CommentStatisticClient;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.interact.client.dto.AuthedUgcIdDTO;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.media.client.*;
import com.jxntv.gvideo.media.client.dto.*;
import com.jxntv.gvideo.media.client.enums.*;
import com.jxntv.gvideo.om.dto.AuditingMediaDTO;
import com.jxntv.gvideo.user.client.CertificationClient;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.CertificationDTO;
import com.jxntv.gvideo.user.client.dto.CertificationTenantDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerAuthenticationDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class FeedConverter extends BaseConverter {
    @Autowired
    private MediaResourceClient mediaResourceClient;
    @Autowired
    private MediaFileClient fileClient;
    @Autowired
    private CertificationClient certificationClient;
    @Autowired
    private MediaResourceClient resourceClient;
    @Autowired
    private InteractClient interactClient;
    @Autowired
    private CommentClient commentClient;
    @javax.annotation.Resource
    private CommentStatisticClient commentStatisticClient;
    @Autowired
    private ConsumerUserClient consumerUserClient;
    @javax.annotation.Resource
    private OssClient ossClient;
    @Autowired
    private SourceClient sourceClient;
    @Autowired
    private SpecialTagClient specialTagClient;
    @Autowired
    private CommentConverter commentConverter;
    @Autowired
    private OssManager ossManager;
    @Autowired
    private GroupManager groupManager;
    @Autowired
    private QuestionAnswerClient questionAnswerClient;
    @Autowired
    private MediaService mediaService;
    @Resource
    private MediaResourceExternalManager mediaResourceExternalManager;

    private Map<Long, List<String>> getFileUrlsMap(List<Long> fileIds) {
        Map<Long, List<String>> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(fileIds)) {
            List<MediaFileDTO> files = fileClient.listByIds(fileIds).getResult();
            if (!CollectionUtils.isEmpty(files)) {
                List<MediaFileDTO> videoFiles = files.stream().filter(f -> MediaFileType.VIDEO.getCode() == f.getFileType()).collect(Collectors.toList());
                List<String> videoIds = videoFiles.stream().map(MediaFileDTO::getOssId).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
                Map<String, String> videoUrlMap = getVideoUrlMap(videoIds);

                videoFiles.forEach(video -> {
                    String url = videoUrlMap.get(video.getOssId());
                    if (!StringUtils.isEmpty(url)) {
                        map.put(video.getId(), Collections.singletonList(url));
                    }

                });


                List<MediaFileDTO> audioFiles = files.stream().filter(f -> MediaFileType.AUDIO.getCode() == f.getFileType() || MediaFileType.SOUND.getCode() == f.getFileType()).collect(Collectors.toList());
                List<String> audioIds = audioFiles.stream().map(MediaFileDTO::getOssId).filter(s -> !StringUtils.isEmpty(s)).collect(Collectors.toList());
                Map<String, String> audioUrlMap = getAudioUrlMap(audioIds);

                audioFiles.forEach(video -> {
                    String url = audioUrlMap.get(video.getOssId());
                    if (!StringUtils.isEmpty(url)) {
                        map.put(video.getId(), Collections.singletonList(url));
                    }

                });

            }

        }

        return map;

    }


    private Map<String, String> getVideoUrlMap(List<String> videoIds) {
        Map<String, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(videoIds)) {
            Result<List<VodVideoPlayDTO>> listResult = ossClient.listVideoFileUrl(videoIds);
            List<VodVideoPlayDTO> list = listResult.getResult();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(e -> map.put(e.getVideoId(), e.getUrl()));
            }

        }

        return map;
    }

    private Map<String, String> getAudioUrlMap(List<String> audioIds) {
        Map<String, String> map = new HashMap<>();
        if (!CollectionUtils.isEmpty(audioIds)) {
            Result<List<VodAudioPlayDTO>> listResult = ossClient.listAudioFileUrl(audioIds);
            List<VodAudioPlayDTO> list = listResult.getResult();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(e -> map.put(e.getAudioId(), e.getUrl()));
            }

        }

        return map;
    }


    private List<String> getUrls(Long fileId) {
        if (fileId == null) {
            return null;
        }
        MediaFileDTO file = fileClient.get(fileId).getResult();
        if (file != null) {
            String ossId = file.getOssId();
            if (!StringUtils.isEmpty(ossId)) {
                Integer fileType = file.getFileType();
                if (fileType.equals(MediaFileType.VIDEO.getCode())) {
                    VodVideoPlayDTO video = ossClient.getVideoFileUrl(ossId).getResult();
                    if (video != null) {
                        return Collections.singletonList(video.getUrl());
                    }
                } else if (fileType.equals(MediaFileType.AUDIO.getCode()) || fileType.equals(MediaFileType.SOUND.getCode())) {
                    VodAudioPlayDTO audio = ossClient.getAudioFileUrl(ossId).getResult();
                    if (audio != null) {
                        return Collections.singletonList(audio.getUrl());
                    }
                }
            }
        }
        return null;
    }

    private SoundDTO getSoundContent(Long fileId) {
        MediaFileDTO file = fileClient.get(fileId).getResult();
        if (file != null) {
            Result<SoundDTO> rst = ossClient.getContentBySoundId(file.getOssId());
            if (rst.callSuccess()) {
                return rst.getResult();
            }
        }
        return null;
    }

    private AuthorVO getAuthor(Long authorId, Integer releaseType, Long jid) {
        if (releaseType.equals(0)) {
            // 0是认证号
            CertificationDTO certification = certificationClient.get(authorId).getResult();
            if (Objects.nonNull(certification)) {
                AuthorVO vo = new AuthorVO();
                vo.setId(authorId);
                vo.setType(0);
                vo.setName(certification.getName());
                AuthorVO.addAvatar(vo, ossClient, certification.getAvatar());
                vo.setIntro(certification.getIntroduce());
                if (jid == null) {
                    vo.setIsFollow(false);
                } else {
                    Boolean isFollow = interactClient.isAlreadyFollow(authorId, jid).getResult();
                    if (isFollow == null) {
                        vo.setIsFollow(false);
                    } else {
                        vo.setIsFollow(isFollow);
                    }
                }
                //设置所属租户
                List<CertificationTenantDTO> tenantDTOList = certificationClient.allTenants(authorId).orElse(Collections.emptyList());
                if (!CollectionUtils.isEmpty(tenantDTOList)) {
                    vo.setTenantIdList(tenantDTOList.stream().map(CertificationTenantDTO::getTenantId).distinct().collect(Collectors.toList()));
                    vo.setTenantNameList(tenantDTOList.stream().map(CertificationTenantDTO::getTenantName).distinct().collect(Collectors.toList()));
                }
                return vo;
            }
        } else if (releaseType.equals(1)) {
            ConsumerUserDTO consumerUser = consumerUserClient.getUserByJid(authorId).getResult();
            if (consumerUser != null) {
                AuthorVO vo = new AuthorVO();
                vo.setId(authorId);
                vo.setType(1);
                vo.setName(consumerUser.getNickname());
                AuthorVO.addAvatar(vo, ossClient, consumerUser.getAvatar());
                vo.setIntro(consumerUser.getInfo());
                if (jid == null) {
                    vo.setIsFollow(false);
                } else {
                    Boolean isFollow = interactClient.isAlreadyFollow(authorId, jid).getResult();
                    if (isFollow == null) {
                        vo.setIsFollow(false);
                    } else {
                        vo.setIsFollow(isFollow);
                    }
                }
                //获取用户认证信息
                getAuthorAuth(vo);
                return vo;
            }
        } else if (releaseType.equals(2)) {
            AuthorVO authorVo = new AuthorVO();
            authorVo.setType(2);
            if (jid == null) {
                authorVo.setIsFollow(false);
            } else {
                Boolean isFollow = interactClient.isAlreadyFollow(authorId, jid).getResult();
                if (isFollow == null) {
                    authorVo.setIsFollow(false);
                } else {
                    authorVo.setIsFollow(isFollow);
                }
            }
            authorVo.setIsFollowMe(false);
            authorVo.setIntro(PptvUtils.INTRO);
            authorVo.setName(PptvUtils.NAME);
            authorVo.setId(-1L);
            AuthorVO.addAvatar(authorVo, ossClient, PptvUtils.AVATAR);
            return authorVo;
        }
        return null;
    }

    //获取作者认证信息
    private void getAuthorAuth(AuthorVO vo) {
        Result<ConsumerAuthenticationDTO> dtoResult = consumerUserClient.queryAuthenticationInfo(vo.getId());
        if (dtoResult.callSuccess()) {
            vo.setIsAuthentication(true);
            vo.setAuthenticationIntro(dtoResult.getResult().getIntroduction());
        } else {
            vo.setIsAuthentication(false);
            vo.setAuthenticationIntro("");
        }
    }


    //获取作品认证用户回复
    private AuthUgcReplyVo getAuthUgcReply(Long authorId, Long mediaId) {
        Long ugcId = commentStatisticClient.getAuthedUgc(mediaId).map(AuthedUgcIdDTO::getUgcId).orElse(null);
        if (Objects.nonNull(ugcId)) {
            ConsumerUserDTO consumerUser = consumerUserClient.getUserByJid(ugcId).getResult();
            if (Objects.nonNull(consumerUser)) {
                AuthUgcReplyVo vo = new AuthUgcReplyVo();
                AuthUgcReplyVo.addAvatar(vo, ossClient, consumerUser.getAvatar());
                vo.setContent(consumerUser.getNickname());
                return vo;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    /**
     * 处理直播相关信息
     *
     * @param dto
     * @param vo
     */
    private void genVO(MediaResourceDTO dto, FeedVO vo) {
        Long id = dto.getId();
        Integer contentType = dto.getContentType();
        vo.setContent(dto.getContent());
        vo.setMediaType(MediaUtils.convertMediaType(dto));

        // 设置PV
        vo.setPv(mediaService.getPv(dto.getId()));
        vo.setContentLabel(ContentType.getContentLabel(dto.getContentType()));
        //查询是否被专题关联，如果关联需要返回专题ID及专题详情页图
        if (dto.getContentType() != ContentType.SPECIAL.getCode()) {
            Result<SpecialTagResourceDTO> dtoResult = specialTagClient.getOneRelate(id);
            if (dtoResult.getCode() == CodeMessage.OK.getCode()) {
                vo.setSpecialId(dtoResult.getResult().getSpecialId());
                Result<List<MediaResourceImageDTO>> imagesResult = resourceClient.getImages(vo.getSpecialId());
                if (imagesResult.getCode() == CodeMessage.OK.getCode()) {
                    vo.setHeadPic(getImage(imagesResult.getResult(), MediaResourceImageType.HEAD_PIC));
                    vo.setDetailBigPic(getImage(imagesResult.getResult(), MediaResourceImageType.DETAIL_BIG_PIC));
                    vo.setDetailSmallPic(getImage(imagesResult.getResult(), MediaResourceImageType.DETAIL_SMALL_PIC));
                }
            }
        } else {
            vo.setSpecialId(dto.getId());
            //如果是专题，则判断是否是H5专题
            MediaResourceExternalDTO mediaResourceExternalDTO = mediaResourceExternalManager.getByMediaId(dto.getId());
            if (Objects.nonNull(mediaResourceExternalDTO) && !StringUtils.isEmpty(mediaResourceExternalDTO.getLinkUrl())) {
                vo.setLinkTitle(StringUtils.isEmpty(mediaResourceExternalDTO.getLinkTitle()) ? dto.getShowName() : mediaResourceExternalDTO.getLinkTitle());
                vo.setLinkUrl(mediaResourceExternalDTO.getLinkUrl());
                vo.setShareUrl(mediaResourceExternalDTO.getLinkUrl());
            }
        }
        //下发直播相关信息
        if (contentType == ContentType.LIVE_BROADCAST.getCode()) {
            //活动直播
            BroadcastClient broadcastService = SpringUtils.getBean(BroadcastClient.class);
            Result<BroadcastDTO> broadDTO = broadcastService.findByMediaId(id);
            if (broadDTO.callSuccess()) {
                BroadcastDTO rst = broadDTO.getResult();
                if (Objects.nonNull(rst)) {
                    vo.setLiveBroadcastStatus(rst.getStatus());
                    vo.setLiveBroadcastStatusStr(rst.getBroadcastStatus());
                    vo.setPlayType(PlayStyle.LONG.getCode());
                    vo.setMediaUrls(Collections.singletonList(rst.getLiveUrl()));
                    //预告将创建时间设置成开始时间
                    if (Integer.valueOf(1).equals(rst.getStatus()) && Objects.nonNull(rst.getStartTime())) {
                        vo.setLiveBroadcastStartTime(Date.from(rst.getStartTime().atZone(ZoneId.systemDefault()).toInstant()));
                    }
                }
            }
        } else if (contentType == ContentType.INTERACTIVE_BROADCAST.getCode()) {
            //互动直播
            InteractiveBroadcastFeignClient broadcastService = SpringUtils.getBean(InteractiveBroadcastFeignClient.class);
            Result<InteractiveBroadcastDTO> broadDTO = broadcastService.findByMediaId(id);
            if (broadDTO.callSuccess()) {
                InteractiveBroadcastDTO rst = broadDTO.getResult();
                if (Objects.nonNull(rst)) {
                    Integer status = rst.getStatus();
                    //统一处理活动直播互动直播状态值
                    if (status == InteractiveBroadcastStatusEnum.LIVING.getCode()) {
                        vo.setLiveBroadcastStatus(BroadcastStatusEnum.LIVING.getCode());
                    }
                    vo.setLiveBroadcastStatusStr(rst.getBroadcastStatus());
                    vo.setPlayType(PlayStyle.SHORT.getCode());
                    vo.setMediaUrls(Collections.singletonList(rst.getLiveUrl()));
                }
            }
        } else if (contentType == ContentType.PIC_FONT.getCode()) {
            //图文
            List<ImageVO> imageList = getResizeImageList(id);
            vo.setImageUrls(ImageVO.getUrls(imageList));
            vo.setImageList(imageList);
            vo.setOriUrls(getImageList(id));
        } else if (contentType == ContentType.GAN_YUN.getCode() || Integer.valueOf(ContentType.GAN_YUN_VIDEO.getCode()).equals(contentType)) {
            //赣云同步的视频放在pptvcover字段中
            GanyunCoverVO map = JsonUtils.fromJson(dto.getPptvCover(), GanyunCoverVO.class);

            //封面图，如果后台修改了，则采用后台设置的数据
            if (Objects.nonNull(map) && StringUtils.hasText(map.getCover())) {
                vo.setCoverUrl(map.getCover());
                vo.setImageUrls(Collections.singletonList(map.getCover()));
            }
            //视频地址，如果后台修改了，则采用后台设置的数据
            if (Objects.isNull(dto.getFileId())) {
                if (Objects.nonNull(map) && StringUtils.hasText(map.getUrl())) {
                    vo.setMediaUrls(Collections.singletonList(map.getUrl()));
                }
            } else {
                vo.setMediaUrls(getUrls(dto.getFileId()));
            }
        } else if (contentType == ContentType.NEWS.getCode()) {
            //文章：11 文章大图/三图 12 文章左文右图 13 外链
            //playstyle: 1-标题模式、2-左文右图模式、3-三图模式、4-大图模式、5-外链模式
            if (dto.getPlayStyle() == PlayStyle.SHORT.getCode()) {
            } else if (dto.getPlayStyle() == PlayStyle.OUTER_LINK_MODE.getCode()) {
                vo.setMediaUrls(Collections.singletonList(dto.getContent()));
            }
            vo.setLength(dto.getLength());
            //设置来源
            MediaResourceExternalDTO mediaResourceGanyunRelateDTO = mediaResourceExternalManager.getByMediaId(dto.getId());
            if (Objects.nonNull(mediaResourceGanyunRelateDTO)) {
                vo.setSource(mediaResourceGanyunRelateDTO.getSourceName());
            }

            //设置封面
            if (org.apache.commons.lang.StringUtils.isNotEmpty(dto.getPptvCover())) {
                GanyunCoverVO tmp = JsonUtils.fromJson(dto.getPptvCover(), GanyunCoverVO.class);
                if (Objects.nonNull(tmp) && Objects.nonNull(tmp.getImages())) {
                    List<String> images = tmp.getImages();
                    List<ImageVO> imgList = new ArrayList<>();
                    for (String image : images) {
                        ImageVO vo1 = new ImageVO();
                        vo1.setUrl(image);
                        imgList.add(vo1);
                    }
                    vo.setImageUrls(images);
                    vo.setImageList(imgList);
                }
            }
            //如果文章没有封面图，则固定11
            if (CollectionUtils.isEmpty(vo.getImageUrls())) {
                vo.setMediaType(11);
            }
        } else {
            Long fileId = dto.getFileId();
            if (contentType == ContentType.SOUND.getCode()) {
                //语音
                vo.setMediaUrls(getUrls(fileId));
                SoundDTO soundContent = getSoundContent(fileId);
                if (Objects.nonNull(soundContent)) {
                    vo.setSoundContent(soundContent.getContent());
                    vo.setLength(soundContent.getLength());
                }
            } else {
                vo.setMediaUrls(getUrls(fileId));
                //            VideoTestUrl video = SpringUtils.getBean(VideoTestUrl.class);
                //            vo.setMediaUrls(Collections.singletonList(video.getOne()));
            }
        }
    }

    /**
     * 获取图片远程地址
     *
     * @param uuid
     * @return
     */
    public String getImageUrl(String uuid) {
        Result<OssDTO> result = ossClient.getOssFile(uuid);
        if (result.getCode() != CodeMessage.OK.getCode()) {
            log.warn("call oss service getImageFileUrl error,{}", result.getMessage());
        }
        if (result.getResult() == null) {
            return "";
        }
        return result.getResult().getUrl();
    }

    /**
     * 获取图片远程地址
     *
     * @param uuid
     * @return
     */
    public ImageVO getImageVO(String uuid) {
        return ImageVO.convertVO(uuid, ossClient);
    }


    /**
     * 信息流增加提问回答数据结构
     *
     * @param dtoList
     * @param jid
     * @return
     */
    public List<FeedVO> convertMediaToFeedListWithQA(List<MediaResourceDTO> dtoList, Long jid) {
        List<FeedVO> voList = convertMediaToFeedList(dtoList, jid);
        List<Long> qaList = dtoList.stream().map(MediaResourceDTO::getId).collect(Collectors.toList());
        //  添加提问后续回答内容
        return addQaInfo(voList, qaList, jid);
    }

    public List<FeedVO> convertMediaToFeedList(List<MediaResourceDTO> dtoList, Long jid) {
        List<FeedVO> voList = new ArrayList<>();

        Map<Long, MediaResourceDTO> mediaResourceMap = dtoList.stream().collect(Collectors.toMap(MediaResourceDTO::getId, e -> e, (o, n) -> o));

        //  获取媒体图片信息
        Map<Long, Map<Integer, String>> mediaImageUrlMap = getMediaImageUrlMap(dtoList);

        for (MediaResourceDTO dto : dtoList) {
            log.info("convertMediaToFeedListWithQA mediaId is {}", dto.getId());
            FeedVO vo = new FeedVO();
            Long id = dto.getId();
            Long answerId = dto.getAnswerId();
            vo.setId(id);
            vo.setLabels(dto.getContentTypeLabelStr());
            //当前循环默认都是提问
            vo.setAnswerSquareType(1);
            vo.setAnswerSquareId(answerId);
            questionAnswerClient.getByMediaId(id).ifPresent(questionAnswerDTO -> {
                if (Objects.nonNull(questionAnswerDTO.getMentorJid())) {
                    vo.setMentorJid(questionAnswerDTO.getMentorJid());
                }
            });
            vo.setTitle(dto.getShowName());
            vo.setCreateDate(dto.getCreateDate());
            vo.setDescription(dto.getIntroduction());
            vo.setColumnId(dto.getColumnId());
            vo.setCanComment(dto.getIsComment());
            vo.setIsPublic(isPublic(dto));
            vo.setIsStick(1);
            vo.setOutShareTitle(dto.getOutShareTitle());
            vo.setOutShareUrl(dto.getOutShareUrl());

            vo.setVodId(String.valueOf(dto.getPptvProgramCode()));
            if (!dto.getReleaseType().equals(2)) {
                vo.setShareUrl(ShareUrlUtils.generateShareUrl("media", id));
            }
            vo.setShowRecTime(5);
            if (!dto.getReleaseType().equals(2)) {
                Map<Integer, String> imageUrlMap = mediaImageUrlMap.get(id);
                if (!CollectionUtils.isEmpty(imageUrlMap)) {
                    String coverUrl = getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.RECOMMENDED_FLOW);
                    List<MediaResourceImageDTO> images = dto.getImages();
                    if (!dto.getContentType().equals(ContentType.GAN_YUN.getCode())
                            && !Integer.valueOf(ContentType.GAN_YUN_VIDEO.getCode()).equals(dto.getContentType())) {
                        vo.setCoverUrl(coverUrl);
                        vo.setCoverImage(ImageVO.convertVO(getUUID(images,
                                MediaResourceImageType.RECOMMENDED_FLOW), ossClient));
                    } else {
                        //如果有设置，则采用设置的，否则，前面逻辑有采用pptvcover
                        if (!StringUtils.isEmpty(coverUrl)) {
                            vo.setCoverUrl(coverUrl);
                            vo.setCoverImage(ImageVO.buildUrl(coverUrl));
                        }
                    }
                    //专题需要额外图片
                    if (dto.getContentType() == ContentType.SPECIAL.getCode()) {
                        //专题和文章一致, 将封面图片放入图片列表
                        List<ImageVO> imgs = new ArrayList<>(3);
                        String followImageUrl = getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.RECOMMENDED_FLOW);
                        if (!StringUtils.isEmpty(followImageUrl)) {
                            imgs.add(ImageVO.buildUrl(followImageUrl));
                        }

                        vo.setImageUrls(ImageVO.getUrls(imgs));
                        vo.setImageList(imgs);
                        vo.setHeadPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.HEAD_PIC));
                        vo.setDetailBigPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.DETAIL_BIG_PIC));
                        vo.setDetailSmallPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.DETAIL_SMALL_PIC));
                    }
                }
            } else {
                vo.setCoverUrl(dto.getPptvCover());
                vo.setCoverImage(ImageVO.buildUrl(dto.getPptvCover()));
            }
            voList.add(vo);
        }
        //  添加作者信息
        addAuthorInfo(voList, mediaResourceMap, jid);
        //  添加圈子信息
        addGroupInfo(voList);
        //  添加直播相关信息
        addLivingInfo(voList, mediaResourceMap);
        //  添加互动信息
        addInteractInfo(voList, jid);
        //  添加评论信息
        addCommentTotalInfo(voList, jid);
        //  添加认证UGC回复信息
        addUgcReplyInfo(voList, mediaResourceMap);
        Result<List<CommentDTO>> qualityRst = commentClient.queryQuality(dtoList.stream()
                .map(MediaResourceDTO::getId).collect(Collectors.toList()));
        if (qualityRst.callSuccess() && !CollectionUtils.isEmpty(qualityRst.getResult())) {
            List<CommentDTO> commentDTOList = qualityRst.getResult().stream().map(commentDTO -> {
                commentDTO.setCurrentJid(jid);
                return commentDTO;
            }).collect(Collectors.toList());
            addQualityComment(voList, commentDTOList);
        }
        return voList;
    }

    /**
     * 设置评论/回复点赞信息
     *
     * @param type      类型：0-评论、1-回复
     * @param commentId 评论/回复ID
     */
    public int getPraiseSum(Integer type, Long commentId) {
        //获取总点赞
        Result<Integer> sumResult = commentClient.praiseSum(type, commentId);
        if (sumResult.getCode() == CodeMessage.OK.getCode()) {
            return sumResult.getResult();
        } else {
            return 0;
        }
    }

    /**
     * 评论/回复是否点赞
     *
     * @param jid       用户ID
     * @param type      类型：0-评论、1-回复
     * @param commentId 评论/回复ID
     */
    public boolean isPraise(Integer type, Long jid, Long commentId) {
        //判断当前用户是否点赞
        if (Objects.isNull(jid)) {
            return false;
        } else {
            Result result = commentClient.isPraise(jid, type, commentId);
            return result.getCode() == CodeMessage.OK.getCode();
        }
    }

    /**
     * 构造提问后续回答逻辑
     * 1.查询提问中当前用户关注人的回答以及认证用户的回答
     * 2.将回答内容转为feedVO对象
     * 3.插入列表中对应提问后方
     *
     * @param voList
     * @param qaList
     * @param jid
     */
    public List<FeedVO> addQaInfo(List<FeedVO> voList, List<Long> qaList, Long jid) {
        AnswerFeignClient answerService = SpringUtils.getBean(AnswerFeignClient.class);
        List<Long> followList = new ArrayList<>();
        //查询我关注的所有用户
        if (Objects.nonNull(jid)) {
            Result<List<Long>> followRst = interactClient.allMyFollow(jid);
            if (!followRst.callSuccess()) {
                log.error("关注接口调用异常:" + followRst.getMessage());
                return voList;
            }
            followList = followRst.getResult();
        }

        //所有回答聚合
        Result<Map<Long, List<AnswerSquareDTO>>> qaRst = answerService.fetchAnswerByQuestion(qaList);
        if (!qaRst.callSuccess()) {
            log.error("回答聚合异常:" + qaRst.getMessage());
            return voList;
        }
        Map<Long, List<AnswerSquareDTO>> questionMap = qaRst.getResult();

        //获取回答所有者列表，用于筛选是否为认证用户回答或关注人回答
        List<Long> replyList = questionMap.values().stream().flatMap(Collection::stream).map(AnswerSquareDTO::getReplyJid).collect(Collectors.toList());
        //查询所有认证号用户
        List<Long> authList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(replyList)) {
            Result<List<ConsumerAuthenticationDTO>> authListRst = consumerUserClient.listConsumerAuthByJidList(replyList);
            if (!authListRst.callSuccess()) {
                log.error("认证号用户异常:" + authListRst.getMessage());
                return voList;
            }
            authList = authListRst.getResult().stream().map(ConsumerAuthenticationDTO::getJid).collect(Collectors.toList());
        }
        List<Long> mergeList = new ArrayList<>((followList.size() + authList.size()) * 4 / 3);
        mergeList.addAll(followList);
        mergeList.addAll(authList);
        //找出所有回答中，认证号回答的或关注人回答的
        List<AnswerSquareDTO> allAnswerDTO = questionMap.values().stream().flatMap(Collection::stream)
                .filter(item -> Objects.nonNull(item.getReplyJid()) && mergeList.contains(item.getReplyJid())).collect(Collectors.toList());

        Map<Long, List<FeedVO>> answerFeedListMap = convertFromAnswerSquare(allAnswerDTO, jid, voList);
        List<FeedVO> rstList = new ArrayList<>();

        for (FeedVO vo : voList) {
            Long id = vo.getId();
            Long answerId = vo.getAnswerSquareId();
            Integer answerType = vo.getAnswerSquareType();
            //资源为提问时 增加回答内容
            if (Objects.nonNull(answerId) && answerId > 0 && answerType == 1) {
                List<FeedVO> answerList = answerFeedListMap.get(id);
                // 若有关注的人或者认证ugc回复 则展示回答  否则展示提问
                if (!CollectionUtils.isEmpty(answerList)) {
                    answerList = answerList.stream().sorted((a, b) -> b.getCreateDate().compareTo(a.getCreateDate()))
                            .collect(Collectors.toList());
                    rstList.addAll(answerList);
                } else {
                    rstList.add(vo);
                }
            } else {
                rstList.add(vo);
            }
        }
        return rstList;
    }

    /**
     * 回复对象转为信息流
     */
    public Map<Long, List<FeedVO>> convertFromAnswerSquare(List<AnswerSquareDTO> dtoList, Long jid, List<FeedVO> voList) {
        Map<Long, FeedVO> feedMap = voList.stream().collect(Collectors.toMap(FeedVO::getId, Function.identity(), (a, b) -> a));
        Map<Long, List<FeedVO>> rstMap = new HashMap<>(dtoList.size() * 4 / 3);
        //获取回答者的作者数据
        List<MediaResourceDTO> answerAuthorList = new ArrayList<>();
        //图片集合
        List<String> imgList = new ArrayList<>();
        //语音集合
        List<String> ossList = new ArrayList<>();
        for (AnswerSquareDTO dto : dtoList) {
            String image = dto.getImage();
            String ossId = dto.getSoundOssId();
            if (!StringUtils.isEmpty(image)) {
                Collections.addAll(imgList, image.split(":"));
            }
            ossList.add(ossId);

            MediaResourceDTO resourceDTO = new MediaResourceDTO();
            resourceDTO.setReleaseId(dto.getReplyJid());
            resourceDTO.setReleaseType(1);
            answerAuthorList.add(resourceDTO);
        }
        Map<String, SoundDTO> ossRst = Optional.ofNullable(ossClient.listContentBySoundIds(ossList)).map(Result::getResult)
                .map(list -> list.stream().collect(Collectors.toMap(SoundDTO::getUuid, e -> e, (o, n) -> o)))
                .orElse(Collections.emptyMap());
        //获取作者集合
        Map<String, AuthorVO> authorMap = getAuthorMap(answerAuthorList, jid);

        Result<List<OssDTO>> listResult = ossClient.listByIds(imgList);
        //获取原图集合
        Map<String, OssDTO> fileMap = Optional.ofNullable(listResult).map(Result::getResult)
                .map(list -> list.stream().collect(Collectors.toMap(OssDTO::getUuid, e -> e, (o, n) -> o)))
                .orElse(Collections.emptyMap());

        Result<List<OssDTO>> resizeRst = ossClient.listResizeByIds(imgList);
        //获取处理图片集合
        Map<String, OssDTO> resizeMap = Optional.ofNullable(resizeRst).map(Result::getResult)
                .map(list -> list.stream().collect(Collectors.toMap(OssDTO::getUuid, e -> e, (o, n) -> o)))
                .orElse(Collections.emptyMap());

        for (AnswerSquareDTO dto : dtoList) {
            FeedVO vo = new FeedVO();
            String ossId = dto.getSoundOssId();
            Long answerId = dto.getId();
            Long mediaId = dto.getMediaId();
            Integer answerType = dto.getAnswerType();
            FeedVO feedVO = feedMap.get(mediaId);
            //如果没查询到提问的数据  直接下一个
            if (Objects.isNull(feedVO)) {
                continue;
            }
            String title = dto.getTitle();
            Long replyJid = dto.getReplyJid();
            String img = dto.getImage();
            String content = dto.getContent();
            GroupRelatedVO groupInfo = feedVO.getGroupInfo();
            Long commentId = dto.getCommentId();
            vo.setCommentId(commentId);
            vo.setTitle(title);
            vo.setContent(content);
            LocalDateTime createTime = dto.getCreateTime();
            if (Objects.nonNull(createTime)) {
                vo.setCreateDate(Date.from(createTime.atZone(ZoneId.systemDefault()).toInstant()));
            }
            vo.setAuthor(authorMap.get(getAuthorKey(replyJid, 1)));
            vo.setAnswerSquareId(answerId);
            vo.setAnswerSquareType(2);
            vo.setReviews(feedVO.getReviews());
            vo.setId(mediaId);
            vo.setShareUrl(ShareUrlUtils.generateShareUrl("media", mediaId));
            vo.setGroupInfo(groupInfo);
            if (Objects.nonNull(answerType)) {
                vo.setMediaType(MediaUtils.convertMediaTypeFormAnswer(answerType));
            }
            //处理语音
            if (!StringUtils.isEmpty(ossId)) {
                SoundDTO soundContent = ossRst.get(ossId);
                if (Objects.nonNull(soundContent)) {
                    vo.setSoundContent(soundContent.getContent());
                    vo.setLength(soundContent.getLength());
                    vo.setMediaUrls(Collections.singletonList(soundContent.getUrl()));
                }
            }
            //处理图片
            if (!StringUtils.isEmpty(img)) {
                List<String> mediaUrls = Arrays.stream(img.split(":")).map(fileMap::get).filter(Objects::nonNull).map(OssDTO::getUrl).collect(Collectors.toList());
                List<ImageVO> resizeUrls = Arrays.stream(img.split(":")).map(resizeMap::get).filter(Objects::nonNull).map(ImageVO::buildDTO).collect(Collectors.toList());
                vo.setImageUrls(ImageVO.getUrls(resizeUrls));
                vo.setImageList(resizeUrls);
                vo.setOriUrls(mediaUrls);
            }
            //处理提问对象
            FeedQuestionVO questionVO = new FeedQuestionVO();
            questionVO.setAuthor(feedVO.getAuthor());
            questionVO.setMediaId(mediaId);
            questionVO.setContent(feedVO.getContent());
            vo.setQuestionVO(questionVO);

            //查询登录用户是否点赞回答和点赞数量
            vo.setIsPraise(isPraise(0, jid, commentId));
            vo.setPraiseTotal(getPraiseSum(0, commentId));
            List<FeedVO> rst = rstMap.getOrDefault(mediaId, new ArrayList<>());
            rst.add(vo);
            rstMap.put(mediaId, rst);
        }
        return rstMap;
    }

    public List<FeedVO> convertMediaToFeedListWithoutQualityComment(List<MediaResourceDTO> dtoList, Long jid) {
        List<FeedVO> voList = new ArrayList<>();

        Map<Long, MediaResourceDTO> mediaResourceMap = dtoList.stream().collect(Collectors.toMap(MediaResourceDTO::getId, e -> e, (o, n) -> o));

        //  获取媒体图片信息
        Map<Long, Map<Integer, String>> mediaImageUrlMap = getMediaImageUrlMap(dtoList);

        for (MediaResourceDTO dto : dtoList) {
            if (Objects.nonNull(dto)) {

                FeedVO vo = new FeedVO();
                vo.setId(dto.getId());
                vo.setTitle(dto.getShowName());
                vo.setCreateDate(dto.getCreateDate());
                vo.setLabels(dto.getContentTypeLabelStr());
                vo.setDescription(dto.getIntroduction());
                Long id = dto.getId();
                vo.setColumnId(dto.getColumnId());
                vo.setCanComment(dto.getIsComment());
                vo.setIsPublic(isPublic(dto));
                vo.setIsStick(1);

                vo.setVodId(String.valueOf(dto.getPptvProgramCode()));
                if (!dto.getReleaseType().equals(2)) {
                    vo.setShareUrl(ShareUrlUtils.generateShareUrl("media", id));
                }
                vo.setShowRecTime(5);
                if (!dto.getReleaseType().equals(2)) {
                    Map<Integer, String> imageUrlMap = mediaImageUrlMap.get(dto.getId());
                    if (!CollectionUtils.isEmpty(imageUrlMap)) {
                        List<MediaResourceImageDTO> images = dto.getImages();
                        String coverUrl = getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.RECOMMENDED_FLOW);
                        if (!dto.getContentType().equals(ContentType.GAN_YUN.getCode()) && !Integer.valueOf(ContentType.GAN_YUN_VIDEO.getCode()).equals(dto.getContentType())) {
                            vo.setCoverUrl(coverUrl);
                            vo.setCoverImage(ImageVO.convertVO(getUUID(images, MediaResourceImageType.RECOMMENDED_FLOW), ossClient));
                        } else {
                            //如果有设置，则采用设置的，否则，前面逻辑有采用pptvcover
                            if (!StringUtils.isEmpty(coverUrl)) {
                                vo.setCoverUrl(coverUrl);
                                vo.setCoverImage(ImageVO.buildUrl(coverUrl));
                            }
                        }
                        //专题需要额外图片
                        if (dto.getContentType() == ContentType.SPECIAL.getCode()) {
                            //专题和文章一致, 将封面图片放入图片列表
                            List<ImageVO> imgs = new ArrayList<>(3);
                            String followImageUrl = getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.RECOMMENDED_FLOW);
                            if (!StringUtils.isEmpty(followImageUrl)) {
                                imgs.add(ImageVO.buildUrl(followImageUrl));
                            }

                            vo.setImageUrls(ImageVO.getUrls(imgs));
                            vo.setImageList(imgs);
                            vo.setHeadPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.HEAD_PIC));
                            vo.setDetailBigPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.DETAIL_BIG_PIC));
                            vo.setDetailSmallPic(getImageUrlOrCommon(imageUrlMap, MediaResourceImageType.DETAIL_SMALL_PIC));
                        }
                    }
                } else {
                    vo.setCoverUrl(dto.getPptvCover());
                    vo.setCoverImage(ImageVO.buildUrl(dto.getPptvCover()));
                }
                voList.add(vo);
            }
        }
        //  添加作者信息
        addAuthorInfo(voList, mediaResourceMap, jid);
        //  添加圈子信息
        addGroupInfo(voList);
        //  添加直播相关信息
        addLivingInfo(voList, mediaResourceMap);
        //  添加互动信息
        addInteractInfo(voList, jid);
        //  添加评论信息
        addCommentTotalInfo(voList, jid);
        //  添加认证UGC回复信息
        addUgcReplyInfo(voList, mediaResourceMap);
        return voList;
    }

    private void addQualityComment(List<FeedVO> voList, List<CommentDTO> commentDTOList) {
        List<CommentVO> commentVOList = commentDTOList.stream().map(commentConverter::toVo).collect(Collectors.toList());
        Map<Long, CommentVO> commentVOMap = commentVOList.stream().filter(e -> Objects.nonNull(e.getMedia()))
                .collect(Collectors.toMap(e -> e.getMedia().getMediaId(), e -> e, (e1, e2) -> e1));

        voList.forEach(item -> {
            Long id = item.getId();
            CommentVO commentVO = commentVOMap.get(id);
            if (Objects.nonNull(commentVO)) {
                item.setQualityComment(commentVO);
                Long answerSquareId = item.getAnswerSquareId();
                Integer answerType = item.getAnswerSquareType();
                //提问类型
                if (Objects.nonNull(answerSquareId) && Objects.nonNull(answerType)
                        && answerSquareId > 0 && answerType == 1) {
                    //非认证号评论
                    if (!commentVO.authUserReplay()) {
                        item.setQualityComment(null);
                    }
                }
            }
        });
    }


    private void addUgcReplyInfo(List<FeedVO> voList, Map<Long, MediaResourceDTO> mediaResourceMap) {
        if (!CollectionUtils.isEmpty(voList)) {
            voList.forEach(vo -> {
                MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
                vo.setAuthUgcReply(getAuthUgcReply(dto.getReleaseId(), dto.getId()));
            });
        }

    }

    private void addAuthorInfo(List<FeedVO> voList, Map<Long, MediaResourceDTO> mediaResourceMap, Long jid) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }

        List<MediaResourceDTO> dtoList = voList.stream().map(e -> mediaResourceMap.get(e.getId())).filter(Objects::nonNull).collect(Collectors.toList());

        Map<String, AuthorVO> authorMap = getAuthorMap(dtoList, jid);

        voList.forEach(vo -> {
            MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
            String key = getAuthorKey(dto.getReleaseId(), dto.getReleaseType());
            AuthorVO authorVO = authorMap.get(key);
            vo.setAuthor(authorVO);
        });


    }

    private String getAuthorKey(Long authorId, Integer releaseType) {
        return authorId + "_" + releaseType;
    }

    private Map<String, AuthorVO> getAuthorMap(List<MediaResourceDTO> dtoList, Long jid) {
        Map<String, AuthorVO> authorMap = new HashMap<>();
        //作者ID集合
        Set<Long> followSet = new HashSet<>();
        if (Objects.nonNull(jid)) {
            List<Long> releaseIds = dtoList.stream().map(MediaResourceDTO::getReleaseId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(releaseIds)) {
                interactClient.batchAlreadyFollow(releaseIds, jid).ifPresent(map -> {
                    Set<Long> trueSet = map.entrySet().stream().filter(e -> Boolean.TRUE.equals(e.getValue())).map(Map.Entry::getKey).collect(Collectors.toSet());
                    followSet.addAll(trueSet);
                });

            }
        }
        //  releaseType=0，认证号
        List<MediaResourceDTO> release0List = dtoList.stream().filter(e -> Integer.valueOf(0).equals(e.getReleaseType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(release0List)) {
            List<Long> authorIds = release0List.stream().map(MediaResourceDTO::getReleaseId).filter(Objects::nonNull).collect(Collectors.toList());
            List<CertificationDTO> certifications = certificationClient.bulk(authorIds).getResult();
            if (!CollectionUtils.isEmpty(certifications)) {
                Map<Long, CertificationDTO> certificationMap = certifications.stream().collect(Collectors.toMap(CertificationDTO::getId, e -> e));
                List<String> avatarIds = certifications.stream().map(CertificationDTO::getAvatar).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                Map<String, String> avatarMap = getAvatarMap(avatarIds);
                List<CertificationTenantDTO> tenantDTOList = certificationClient.allTenants(authorIds).orElse(Collections.EMPTY_LIST);
                Map<Long, List<CertificationTenantDTO>> tenantDTOMap = tenantDTOList.stream().collect(Collectors.groupingBy(CertificationTenantDTO::getCertificationId));
                for (MediaResourceDTO dto : release0List) {
                    Long authorId = dto.getReleaseId();
                    // 0是认证号
                    CertificationDTO certification = certificationMap.get(authorId);
                    if (Objects.nonNull(certification)) {
                        AuthorVO vo = new AuthorVO();
                        vo.setId(authorId);
                        vo.setType(0);
                        vo.setName(certification.getName());
                        vo.setAvatar(avatarMap.get(certification.getAvatar()));
                        vo.setIntro(certification.getIntroduce());
                        vo.setIsFollow(followSet.contains(authorId));
                        //设置所属租户
                        List<CertificationTenantDTO> tenantList = tenantDTOMap.get(certification.getId());
                        if (!CollectionUtils.isEmpty(tenantList)) {
                            vo.setTenantIdList(tenantList.stream().map(CertificationTenantDTO::getTenantId).distinct().collect(Collectors.toList()));
                            vo.setTenantNameList(tenantList.stream().map(CertificationTenantDTO::getTenantName).distinct().collect(Collectors.toList()));
                        }
                        authorMap.put(getAuthorKey(authorId, dto.getReleaseType()), vo);
                    }
                }
            }
        }

        //  releaseType=1
        List<MediaResourceDTO> release1List = dtoList.stream().filter(e -> Integer.valueOf(1).equals(e.getReleaseType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(release1List)) {

            List<Long> authorIds = release1List.stream().map(MediaResourceDTO::getReleaseId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<ConsumerUserDTO> consumerUsers = consumerUserClient.listUserByJids(authorIds).orElse(Collections.emptyList());

            if (!CollectionUtils.isEmpty(consumerUsers)) {
                Map<Long, ConsumerUserDTO> consumerUserMap = consumerUsers.stream().collect(Collectors.toMap(ConsumerUserDTO::getJid, e -> e, (e1, e2) -> e1));
                List<String> fileIds = consumerUsers.stream().map(ConsumerUserDTO::getAvatar).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                Map<String, String> avatarMap = getAvatarMap(fileIds);

                for (MediaResourceDTO dto : release1List) {
                    Long authorId = dto.getReleaseId();
                    ConsumerUserDTO consumerUser = consumerUserMap.get(authorId);
                    if (Objects.nonNull(consumerUser)) {
                        AuthorVO vo = new AuthorVO();
                        vo.setId(authorId);
                        vo.setType(1);
                        vo.setName(consumerUser.getNickname());
                        vo.setAvatar(avatarMap.get(consumerUser.getAvatar()));
                        vo.setIntro(consumerUser.getInfo());
                        vo.setIsFollow(followSet.contains(authorId));

                        //  补充认证信息
                        ConsumerAuthenticationDTO consumerAuthenticationDTO = consumerUser.getConsumerAuthentication();
                        if (Objects.nonNull(consumerAuthenticationDTO)) {
                            vo.setIsAuthentication(true);
                            vo.setAuthenticationIntro(consumerAuthenticationDTO.getIntroduction());
                        } else {
                            vo.setIsAuthentication(false);
                            vo.setAuthenticationIntro("");
                        }

                        authorMap.put(getAuthorKey(authorId, dto.getReleaseType()), vo);
                    }


                }


            }

        }

        return authorMap;
    }

    /**
     * 设置评论数量最优评论
     *
     * @param voList
     * @param jid
     */
    private void addCommentTotalInfo(List<FeedVO> voList, Long jid) {
        if (!CollectionUtils.isEmpty(voList)) {
            for (FeedVO vo : voList) {
                vo.setReviews(commentStatisticClient.getTotalByMediaId(vo.getId(), jid).orElse(0));
            }
        }


    }

    /**
     * @description: 为信息流添加互动信息：收藏数， jid是否喜欢，
     * <AUTHOR>
     * @date 2021/12/3 20:10
     * @version 1.0
     */
    private void addInteractInfo(List<FeedVO> voList, Long jid) {
        if (!CollectionUtils.isEmpty(voList)) {
            for (FeedVO vo : voList) {
                Integer isFavor = interactClient.isAlreadyKeep(vo.getId(), jid).map(e -> e ? 1 : 0).orElse(0);
                Integer favors = interactClient.favoriteSum(vo.getId()).orElse(0);

                vo.setIsFavor(isFavor);
                vo.setFavors(favors);
            }
        }

    }

    private String getImageUrlOrCommon(Map<Integer, String> imageUrlMap, MediaResourceImageType mediaResourceImageType) {
        String url = imageUrlMap.get(mediaResourceImageType.getCode());
        if (StringUtils.isEmpty(url)) {
            url = imageUrlMap.get(MediaResourceImageType.COMMON.getCode());
        }
        return Objects.isNull(url) ? "" : url;
    }


    private Map<Long, Map<Integer, String>> getMediaImageUrlMap(List<MediaResourceDTO> mediaResources) {
        if (CollectionUtils.isEmpty(mediaResources)) {
            return Collections.emptyMap();
        }

        Map<Long, Map<Integer, String>> mediaImageUrlMap = new HashMap<>();
        List<String> fileIds = mediaResources.stream()
                .map(MediaResourceDTO::getImages)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .map(MediaResourceImageDTO::getOssId)
                .filter(s -> !StringUtils.isEmpty(s))
                .collect(Collectors.toList());
        //如果是空列表，停止服务调用
        if (CollectionUtils.isEmpty(fileIds)) {
            return mediaImageUrlMap;
        }
        List<OssDTO> result = ossManager.listOssFileByIds(fileIds);
        if (Objects.nonNull(result)) {
            Map<String, String> urlMap = result.stream().collect(Collectors.toMap(OssDTO::getUuid, OssDTO::getUrl, (o, n) -> o));
            for (MediaResourceDTO mediaResource : mediaResources) {
                List<MediaResourceImageDTO> images = mediaResource.getImages();
                if (!CollectionUtils.isEmpty(images)) {
                    Map<Integer, String> map = new HashMap<>();
                    for (MediaResourceImageDTO image : images) {
                        map.put(image.getType(), urlMap.getOrDefault(image.getOssId(), ""));
                    }
                    mediaImageUrlMap.put(mediaResource.getId(), map);
                }
            }
        }
        return mediaImageUrlMap;
    }

    private Map<String, String> getAvatarMap(List<String> ossIds) {
        Map<String, String> avatarMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(ossIds)) {
            ossClient.listAvatarByIds(ossIds)
                    .ifPresent(list -> list.forEach(e -> avatarMap.put(e.getUuid(), e.getUrl())));

        }
        return avatarMap;
    }


    public AdvertVO convert(MediaResourceRecommendDTO dto) {
        AdvertVO vo = new AdvertVO();
        vo.setResourceId(dto.getResourceId());
        vo.setExtendId(dto.getId());
        vo.setIsManual(dto.getId() == null ? 0 : 1);
        if (dto.getType().equals(MediaResourceRecommendType.RESOURCE.getCode())) {
            MediaResourceDTO mediaResource = resourceClient.get(dto.getContentId()).getResult();
            if (mediaResource != null) {
                vo.setTitle(mediaResource.getShowName());
                vo.setIsAd(mediaResource.getCornerMark() == null ? 0 : 1);
                vo.setContentLabel(ContentType.getContentLabel(mediaResource.getContentType()));
                vo.setMediaType(MediaUtils.convertMediaType(mediaResource));
                if (mediaResource.getReleaseType().equals(0)) {
                    CertificationDTO certification = certificationClient.get(mediaResource.getReleaseId()).getResult();
                    if (certification != null) {
                        vo.setAuthor(certification.getName());
                        vo.setSubTitle(certification.getName());
                    }
                    Result<List<MediaResourceImageDTO>> result = resourceClient.getImages(dto.getContentId());
                    if (result.getCode() == CodeMessage.OK.getCode()) {
                        vo.setImageUrl(getImage(result.getResult(), MediaResourceImageType.RECOMMENDED_POSITION));
                        //专题需要额外图片
                        if (mediaResource.getContentType() == ContentType.SPECIAL.getCode()) {
                            vo.setHeadPic(getImage(result.getResult(), MediaResourceImageType.HEAD_PIC));
                            vo.setDetailBigPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_BIG_PIC));
                            vo.setDetailSmallPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_SMALL_PIC));
                        }
                    }
                } else if (mediaResource.getReleaseType().equals(1)) {
                    ConsumerUserDTO consumerUser = consumerUserClient.getUserByJid(mediaResource.getReleaseId()).getResult();
                    if (consumerUser != null) {
                        vo.setAuthor(consumerUser.getNickname());
                        vo.setSubTitle(consumerUser.getNickname());
                    }
                    Result<List<MediaResourceImageDTO>> result = resourceClient.getImages(dto.getContentId());
                    if (result.getCode() == CodeMessage.OK.getCode()) {
                        vo.setImageUrl(getImage(result.getResult(), MediaResourceImageType.RECOMMENDED_POSITION));
                        //专题需要额外图片
                        if (mediaResource.getContentType() == ContentType.SPECIAL.getCode()) {
                            vo.setHeadPic(getImage(result.getResult(), MediaResourceImageType.HEAD_PIC));
                            vo.setDetailBigPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_BIG_PIC));
                            vo.setDetailSmallPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_SMALL_PIC));
                        }
                    }
                } else if (mediaResource.getReleaseType().equals(2)) {
                    vo.setAuthor(PptvUtils.NAME);
                    vo.setSubTitle(PptvUtils.INTRO);
                    vo.setImageUrl(mediaResource.getPptvCover());
                }
                vo.setMediaId(mediaResource.getId());
                // 设置PV
                vo.setPv(mediaService.getPv(mediaResource.getId()));
                vo.setCreateDate(mediaResource.getCreateDate());
                //查询是否被专题关联，如果关联需要返回专题ID及专题详情页图
                if (mediaResource.getContentType() != ContentType.SPECIAL.getCode()) {
                    Result<SpecialTagResourceDTO> dtoResult = specialTagClient.getOneRelate(mediaResource.getId());
                    if (dtoResult.getCode() == CodeMessage.OK.getCode()) {
                        vo.setSpecialId(dtoResult.getResult().getSpecialId());
                        Result<List<MediaResourceImageDTO>> imagesResult = resourceClient.getImages(vo.getSpecialId());
                        if (imagesResult.getCode() == CodeMessage.OK.getCode()) {
                            vo.setHeadPic(getImage(imagesResult.getResult(), MediaResourceImageType.HEAD_PIC));
                            vo.setDetailBigPic(getImage(imagesResult.getResult(), MediaResourceImageType.DETAIL_BIG_PIC));
                            vo.setDetailSmallPic(getImage(imagesResult.getResult(), MediaResourceImageType.DETAIL_SMALL_PIC));
                        }
                    }
                } else {
                    vo.setSpecialId(mediaResource.getId());
                }
                //如果是文章
                if (mediaResource.getContentType() == ContentType.NEWS.getCode()) {
                    //设置来源
                    if (!Objects.isNull(mediaResource.getSource()) && !"0".equals(mediaResource.getSource())) {
                        SourceDTO sourceDTO = sourceClient.get(Long.valueOf(mediaResource.getSource())).getResult();
                        vo.setSource(sourceDTO.getName());
                    }
                    //设置封面
                    if (org.apache.commons.lang.StringUtils.isNotEmpty(mediaResource.getPptvCover())) {
                        GanyunCoverVO tmp = JsonUtils.fromJson(mediaResource.getPptvCover(), GanyunCoverVO.class);
                        if (Objects.nonNull(tmp) && Objects.nonNull(tmp.getImages())) {
                            vo.setImageUrls(tmp.getImages());
                            if (!CollectionUtils.isEmpty(vo.getImageUrls())) {
                                vo.setImageUrl(vo.getImageUrls().get(0));
                            }
                        }
                    }
                    //如果封面图为空，则固定11
                    if (CollectionUtils.isEmpty(vo.getImageUrls())) {
                        vo.setMediaType(11);
                    }
                }
            }
        } else if (dto.getType().equals(MediaResourceRecommendType.MATERIAL.getCode())) {
            MaterialDTO material = SpringUtils.getBean(MaterialService.class).get(dto.getContentId()).getResult();
            if (material != null) {
                vo.setMediaId(material.getId());
                vo.setMediaType(-100);
                vo.setTitle(material.getName());
                vo.setIsAd(material.getCornerMark() == null ? 0 : 1);
                String image = null;
                if (material.getType().equals(MaterialType.SEO_LINK.getCode())) {
                    SeoLinkDTO seoLinkDTO = material.getSeoLinkDTO();
                    if (seoLinkDTO != null) {
                        image = seoLinkDTO.getImage();
                        vo.setActUrl(seoLinkDTO.getUrl());
                        vo.setSubTitle(seoLinkDTO.getSubTitle());
                    }
                }
                if (material.getType().equals(MaterialType.OPERATION_LINK.getCode())) {
                    OperationLinkDTO operationLinkDTO = material.getOperationLinkDTO();
                    if (operationLinkDTO != null) {
                        image = operationLinkDTO.getImage();
                        vo.setActUrl(operationLinkDTO.getUrl());
                        vo.setSubTitle(operationLinkDTO.getPlatform() == null ? "" : "三方平台");
                    }
                }
                if (material.getType().equals(MaterialType.MAP_LOCATION.getCode())) {
                    MapLocationDTO mapLocationDTO = material.getMapLocationDTO();
                    if (mapLocationDTO != null) {
                        image = mapLocationDTO.getImage();
                        vo.setActUrl(mapLocationDTO.getUrl());
                        vo.setSubTitle(mapLocationDTO.getUrl());
                    }
                }
                if (material.getType().equals(MaterialType.ACTIVITY_REGISTRATION.getCode())) {
                    ActivityRegistrationDTO activityRegistrationDTO = material.getActivityRegistrationDTO();
                    if (activityRegistrationDTO != null) {
                        image = activityRegistrationDTO.getImage();
                        vo.setActUrl(activityRegistrationDTO.getUrl());
                        vo.setSubTitle(DateUtils.format2(activityRegistrationDTO.getDeadLine()));
                    }
                }

                OssDTO oss = ossClient.getOssFile(image).getResult();
                if (oss != null) {
                    vo.setImageUrl(oss.getUrl());
                }
                vo.setCreateDate(material.getCreateDate());
            }
        }
        return vo;
    }




    /**
     * 判断是否公开
     *
     * @param dto
     * @return
     */
    private Integer isPublic(MediaResourceDTO dto) {
        if (dto.getReleaseType().equals(1)) {
            // 是否公开
            if (!dto.getIsSearch()) {
                // 私密的
                return 0;
            }
            if (dto.getStatus().equals(MediaResourceStatus.AUDIT.getCode()) || dto.getStatus().equals(MediaResourceStatus.DISABLE.getCode())) {
                // 审核中
                return 2;
            }
        }
        return 1;
    }

    private void addLivingInfo(List<FeedVO> voList, Map<Long, MediaResourceDTO> mediaResourceMap) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (FeedVO vo : voList) {
            MediaResourceDTO dto = mediaResourceMap.get(vo.getId());

            // 设置PV
            vo.setPv(mediaService.getPv(dto.getId()));
            vo.setContent(dto.getContent());
            vo.setContentLabel(ContentType.getContentLabel(dto.getContentType()));
        }

        //  处理专题类型(被专题关联，如果关联需要返回专题ID及专题详情页图)
        addSpecialTagInfo(voList, mediaResourceMap);

        //  按照contentType对vo进行分组处理
        Map<Integer, List<FeedVO>> feedGroupByContentType = voList.stream()
                .collect(Collectors.groupingBy(e -> mediaResourceMap.get(e.getId()).getContentType()));

        //  活动直播
        List<FeedVO> liveBroadcastList = feedGroupByContentType.get(ContentType.LIVE_BROADCAST.getCode());
        if (!CollectionUtils.isEmpty(liveBroadcastList)) {
            List<Long> mediaIds = liveBroadcastList.stream().map(FeedVO::getId).collect(Collectors.toList());
            BroadcastClient broadcastService = SpringUtils.getBean(BroadcastClient.class);
            Result<List<BroadcastDTO>> rst = broadcastService.listByMediaIds(mediaIds);
            List<BroadcastDTO> result = rst.getResult();
            Map<Long, BroadcastDTO> broadcastDTOMap = Objects.isNull(result) ? Collections.emptyMap()
                    : result.stream().collect(Collectors.toMap(BroadcastDTO::getMediaId, e -> e));
            for (FeedVO vo : liveBroadcastList) {
                BroadcastDTO broadcastDTO = broadcastDTOMap.get(vo.getId());
                if (Objects.nonNull(broadcastDTO)) {
                    vo.setPlayType(1);
                    vo.setLiveBroadcastStatus(broadcastDTO.getStatus());
                    vo.setLiveBroadcastStatusStr(broadcastDTO.getBroadcastStatus());
                    vo.setMediaType(broadcastDTO.genMediaType());
                    vo.setMediaUrls(Collections.singletonList(broadcastDTO.getLiveUrl()));
                }
            }

        }

        //  互动直播
        List<FeedVO> interactiveBroadcastList = feedGroupByContentType.get(ContentType.INTERACTIVE_BROADCAST.getCode());
        if (!CollectionUtils.isEmpty(interactiveBroadcastList)) {
            List<Long> mediaIds = interactiveBroadcastList.stream().map(FeedVO::getId).collect(Collectors.toList());
            InteractiveBroadcastFeignClient broadcastService = SpringUtils.getBean(InteractiveBroadcastFeignClient.class);
            Result<List<InteractiveBroadcastDTO>> listResult = broadcastService.listByMediaIds(mediaIds);
            List<InteractiveBroadcastDTO> result = listResult.getResult();
            Map<Long, InteractiveBroadcastDTO> dtoMap = Objects.isNull(result) ? Collections.emptyMap()
                    : result.stream().collect(Collectors.toMap(InteractiveBroadcastDTO::getMediaId, d -> d));

            for (FeedVO vo : interactiveBroadcastList) {
                vo.setPlayType(2);
                vo.setMediaType(8);

                InteractiveBroadcastDTO dto = dtoMap.get(vo.getId());
                if (Objects.nonNull(dto)) {
                    Integer status = dto.getStatus();
                    //统一处理活动直播互动直播状态值
                    if (status == InteractiveBroadcastStatusEnum.LIVING.getCode()) {
                        vo.setLiveBroadcastStatus(BroadcastStatusEnum.LIVING.getCode());
                    }
                    vo.setLiveBroadcastStatusStr(dto.getBroadcastStatus());
                    vo.setMediaUrls(Collections.singletonList(dto.getLiveUrl()));
                }
            }

        }

        //  图文
        List<FeedVO> picFontList = feedGroupByContentType.get(ContentType.PIC_FONT.getCode());
        if (!CollectionUtils.isEmpty(picFontList) && !CollectionUtils.isEmpty(mediaResourceMap)) {
            Map<Long, List<MediaResourceImageDTO>> mediaImageMap = picFontList.stream().map(e -> mediaResourceMap.get(e.getId()))
                    .filter(item -> !CollectionUtils.isEmpty(item.getImages()))
                    .collect(Collectors.toMap(MediaResourceDTO::getId, MediaResourceDTO::getImages, (e1, e2) -> e1));
            List<String> imageIds = mediaImageMap.values().stream()
                    .filter(Objects::nonNull)
                    .flatMap(Collection::stream)
                    .map(MediaResourceImageDTO::getOssId)
                    .collect(Collectors.toList());

            // Result<List<OssDTO>> listResult = ossClient.listByIds(imageIds);
            List<OssDTO> listResult = ossManager.listOssFileByIds(imageIds);
            Map<String, OssDTO> fileMap = listResult.stream()
                    .collect(Collectors.toMap(OssDTO::getUuid, e -> e, (o, n) -> o));

            //Result<List<OssDTO>> resizeRst = ossClient.listResizeByIds(imageIds);
            List<OssDTO> resizeRst = ossManager.listResizeByIds(imageIds);
            Map<String, OssDTO> resizeMap = resizeRst.stream()
                    .collect(Collectors.toMap(OssDTO::getUuid, e -> e, (o, n) -> o));


            for (FeedVO vo : picFontList) {
                vo.setMediaType(9);

                List<MediaResourceImageDTO> imageDTOList = mediaImageMap.get(vo.getId());
                if (!CollectionUtils.isEmpty(imageDTOList)) {
                    List<String> mediaUrls = imageDTOList.stream().map(e -> fileMap.get(e.getOssId()))
                            .filter(Objects::nonNull).map(OssDTO::getUrl).collect(Collectors.toList());
                    List<ImageVO> resizeUrls = imageDTOList.stream().map(e -> resizeMap.get(e.getOssId()))
                            .filter(Objects::nonNull).map(ImageVO::buildDTO).collect(Collectors.toList());
                    vo.setImageUrls(ImageVO.getUrls(resizeUrls));
                    vo.setImageList(resizeUrls);
                    vo.setOriUrls(mediaUrls);
                }
            }
        }

        //  赣云资源
        List<FeedVO> ganyunList = new ArrayList<>(16);
        if (!CollectionUtils.isEmpty(feedGroupByContentType.get(ContentType.GAN_YUN.getCode()))) {
            ganyunList.addAll(feedGroupByContentType.get(ContentType.GAN_YUN.getCode()));
        }
        if (!CollectionUtils.isEmpty(feedGroupByContentType.get(ContentType.GAN_YUN_VIDEO.getCode()))) {
            ganyunList.addAll(feedGroupByContentType.get(ContentType.GAN_YUN_VIDEO.getCode()));
        }
        if (!CollectionUtils.isEmpty(ganyunList)) {
            List<MediaResourceDTO> collect = ganyunList.stream().map(v -> mediaResourceMap.get(v.getId()))
                    .filter(Objects::nonNull).collect(Collectors.toList());
            List<Long> fileIds = collect.stream().map(MediaResourceDTO::getFileId)
                    .filter(Objects::nonNull).collect(Collectors.toList());
            Map<Long, List<String>> fileUrlMap = getFileUrlsMap(fileIds);

            for (FeedVO vo : ganyunList) {
                MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
                vo.setMediaType(MediaUtils.convertMediaType(dto));
                //赣云同步的视频放在pptvcover字段中
                GanyunCoverVO map = JsonUtils.fromJson(dto.getPptvCover(), GanyunCoverVO.class);

                //封面图，如果后台修改了，则采用后台设置的数据
                if (Objects.nonNull(map) && StringUtils.hasText(map.getCover())) {
                    vo.setCoverUrl(map.getCover());
                    vo.setCoverImage(ImageVO.buildUrl(map.getCover()));
                }
                //视频地址，如果后台修改了，则采用后台设置的数据
                if (Objects.isNull(dto.getFileId())) {
                    if (Objects.nonNull(map) && Objects.nonNull(map.getUrl())) {
                        vo.setMediaUrls(Collections.singletonList(map.getUrl()));
                    }
                } else {
                    vo.setMediaUrls(fileUrlMap.get(dto.getFileId()));
                }
            }
        }

        //  文章
        List<FeedVO> newsList = feedGroupByContentType.get(ContentType.NEWS.getCode());
        if (!CollectionUtils.isEmpty(newsList)) {
            //获取来源
            List<Long> sourceIds = newsList.stream().map(e -> mediaResourceMap.get(e.getId()))
                    .filter(mediaResourceDTO -> !StringUtils.isEmpty(mediaResourceDTO.getSource()) && !"0".equals(mediaResourceDTO.getSource()))
                    .map(mediaResourceDTO -> Long.valueOf(mediaResourceDTO.getSource()))
                    .distinct()
                    .collect(Collectors.toList());
            for (FeedVO vo : newsList) {
                MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
                //文章：11 文章大图/三图 12 文章左文右图 13 外链
                //playstyle: 1-标题模式、2-左文右图模式、3-三图模式、4-大图模式、5-外链模式
                if (dto.getPlayStyle() == PlayStyle.SHORT.getCode()) {
                    vo.setMediaType(12);
                } else if (dto.getPlayStyle() == PlayStyle.OUTER_LINK_MODE.getCode()) {
                    vo.setMediaType(13);
                    vo.setMediaUrls(Collections.singletonList(dto.getContent()));
                } else {
                    vo.setMediaType(11);
                }
                vo.setLength(dto.getLength());
                //设置来源
                MediaResourceExternalDTO mediaResourceGanyunRelateDTO = mediaResourceExternalManager.getByMediaId(dto.getId());
                if (Objects.nonNull(mediaResourceGanyunRelateDTO)) {
                    vo.setSource(mediaResourceGanyunRelateDTO.getSourceName());
                }
                //设置封面
                if (org.apache.commons.lang.StringUtils.isNotEmpty(dto.getPptvCover())) {
                    Map<String, Object> tmp = JsonUtils.fromJsonToMap(dto.getPptvCover(), Object.class);
                    if (!CollectionUtils.isEmpty(tmp) && tmp.containsKey("images")) {
                        List<String> images = (List<String>) tmp.get("images");
                        List<ImageVO> imageList = new ArrayList<>();
                        for (String img : images) {
                            imageList.add(ImageVO.buildUrl(img));
                        }
                        vo.setImageUrls(images);
                        vo.setImageList(imageList);
                    }
                }
                //如果文章没有封面图，则固定11
                if (CollectionUtils.isEmpty(vo.getImageUrls())) {
                    vo.setMediaType(11);
                }
            }
        }

        //  专题，都使用文章左文右图样式
        List<FeedVO> specialList = feedGroupByContentType.get(ContentType.SPECIAL.getCode());
        if (!CollectionUtils.isEmpty(specialList)) {
            specialList.forEach(vo -> vo.setMediaType(12));
        }

        //  语言
        List<FeedVO> soundList = feedGroupByContentType.get(ContentType.SOUND.getCode());
        if (!CollectionUtils.isEmpty(soundList)) {
            for (FeedVO vo : soundList) {
                MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
                Long fileId = dto.getFileId();

                vo.setMediaType(10);
                vo.setMediaUrls(getUrls(fileId));
                SoundDTO soundContent = getSoundContent(fileId);
                if (Objects.nonNull(soundContent)) {
                    vo.setSoundContent(soundContent.getContent());
                    vo.setLength(soundContent.getLength());
                }
            }
        }

        //  兜底逻辑
        List<FeedVO> finalList = voList.stream().filter(e -> Objects.isNull(e.getMediaType())).collect(Collectors.toList());
        List<Long> finalFileIds = finalList.stream().map(v -> mediaResourceMap.get(v.getId())).filter(Objects::nonNull)
                .map(MediaResourceDTO::getFileId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, List<String>> fileUrlMap = getFileUrlsMap(finalFileIds);
        finalList.forEach(vo -> {
            MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
            Long fileId = dto.getFileId();
            vo.setMediaType(MediaUtils.convertMediaType(dto));
            vo.setMediaUrls(fileUrlMap.get(fileId));
        });

        // 处理动态选择的赣云视频
        addPostGanyunVideo(finalList, mediaResourceMap);
    }

    private void addPostGanyunVideo(List<FeedVO> finalList, Map<Long, MediaResourceDTO> mediaResourceMap) {
        // 如果是动态，但是采用了赣云的封面和视频, 并且前面没有设置封面 和 文件
        finalList.forEach(vo -> {
            MediaResourceDTO dto = mediaResourceMap.get(vo.getId());
            if (Integer.valueOf(ContentType.VIDEO_HOMEMADE.getCode()).equals(dto.getContentType()) && !StringUtils.isEmpty(dto.getPptvCover())) {
                //视频放在pptvcover字段中
                GanyunCoverVO map = JsonUtils.fromJson(dto.getPptvCover(), GanyunCoverVO.class);
                //如果动态采用了赣云视频，且没有在后台设置自己的封面图，则封面图取赣云封面图
                if (StringUtils.isEmpty(vo.getCoverUrl()) && Objects.nonNull(map) && Objects.nonNull(map.getCover())) {
                    vo.setCoverUrl(map.getCover());
                    vo.setCoverImage(ImageVO.buildUrl(map.getCover()));
                }
                //如果动态采用了赣云视频，，且没有在后台设置自己的视频文件，则封面图取赣云视频
                if (Objects.isNull(dto.getFileId())) {
                    if (Objects.nonNull(map) && Objects.nonNull(map.getUrl())) {
                        vo.setMediaUrls(Collections.singletonList(map.getUrl()));
                    }
                }
            }
        });
    }

    private void addSpecialTagInfo(List<FeedVO> voList, Map<Long, MediaResourceDTO> mediaResourceMap) {
        Map<Boolean, List<FeedVO>> booleanListMap = voList.stream().collect(Collectors.partitioningBy(e -> ContentType.SPECIAL.getCode() == mediaResourceMap.get(e.getId()).getContentType()));
        //  专题
        List<FeedVO> trueList = booleanListMap.get(true);
        trueList.forEach(e -> e.setSpecialId(e.getId()));

        //  非专题
        List<FeedVO> falseList = booleanListMap.get(false);
        List<Long> resourceIds = falseList.stream().map(FeedVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceIds)) {
            return;
        }
        Result<List<SpecialTagResourceDTO>> listResult = specialTagClient.listByResourceIds(resourceIds);
        if (listResult.callSuccess()) {
            List<SpecialTagResourceDTO> result = listResult.getResult();
            Map<Long, SpecialTagResourceDTO> specialTagResourceDTOMap = result.stream().collect(Collectors.toMap(SpecialTagResourceDTO::getResourceId, e -> e, (o, n) -> o));

            List<Long> specialIds = result.stream().map(SpecialTagResourceDTO::getSpecialId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            List<MediaResourceDTO> specialMediaResources = CollectionUtils.isEmpty(specialIds) ? Collections.emptyList() : mediaResourceClient.bulk(specialIds).getResult();
            Map<Long, Map<Integer, String>> specialImageUrlMap = getMediaImageUrlMap(specialMediaResources);

            for (FeedVO vo : voList) {
                SpecialTagResourceDTO specialTagResourceDTO = specialTagResourceDTOMap.get(vo.getId());
                if (Objects.nonNull(specialTagResourceDTO)) {
                    vo.setSpecialId(specialTagResourceDTO.getSpecialId());

                    Map<Integer, String> integerStringMap = specialImageUrlMap.get(specialTagResourceDTO.getSpecialId());
                    if (Objects.nonNull(integerStringMap)) {
                        vo.setHeadPic(getImageUrlOrCommon(integerStringMap, MediaResourceImageType.HEAD_PIC));
                        vo.setDetailBigPic(getImageUrlOrCommon(integerStringMap, MediaResourceImageType.DETAIL_BIG_PIC));
                        vo.setDetailSmallPic(getImageUrlOrCommon(integerStringMap, MediaResourceImageType.DETAIL_SMALL_PIC));
                    }
                }


            }

        }
    }

    private void addGroupInfo(List<FeedVO> voList) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        //排除PGC发布的资源，添加作者信息必须放在此逻辑前面
        List<Long> mediaIds = voList.stream()
                .filter(mediaVo -> Objects.isNull(mediaVo.getAuthor())
                        || Integer.valueOf(1).equals(mediaVo.getAuthor().getType()))
                .map(FeedVO::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(mediaIds)) {
            return;
        }
        List<GroupRelatedDTO> groupRelatedDTOList = groupManager.getByMediaIds(mediaIds);
        if (!CollectionUtils.isEmpty(groupRelatedDTOList)) {
            //PGC存在一个动态多个圈子情况（暂时不展示），代码兼容处理，此处取第一个展示
            Map<Long, GroupRelatedDTO> relatedMap = groupRelatedDTOList.stream()
                    .collect(Collectors.toMap(GroupRelatedDTO::getMediaId, e -> e, (o, n) -> o));
            for (FeedVO feedVO : voList) {
                GroupRelatedVO groupRelatedVO = new GroupRelatedVO();
                GroupRelatedDTO dto = relatedMap.get(feedVO.getId());
                if (Objects.nonNull(dto)) {
                    buildGroupRelatedVO(groupRelatedVO, dto);
                    feedVO.setGroupInfo(groupRelatedVO);
                }
            }
        }
    }

    private void buildGroupRelatedVO(GroupRelatedVO groupRelatedVO, GroupRelatedDTO dto) {
        groupRelatedVO.setLabels(dto.getLabelStr());
        groupRelatedVO.setEssence(dto.getEssence());
        groupRelatedVO.setGroupId(dto.getGroupId());
        groupRelatedVO.setGroupName(dto.getGroupName());
        groupRelatedVO.setTopicId(dto.getTopicId());
        groupRelatedVO.setTopicName(dto.getTopicName());
        groupRelatedVO.setStatus(dto.getStatus());
        groupRelatedVO.setIntroduction(dto.getIntroduction());
        groupRelatedVO.setTenantId(dto.getTenantId());
        groupRelatedVO.setTenantName(dto.getTenantName());
    }





    public FeedVO convert(MediaResourceDTO dto, Long jid, Boolean noAdvert) {
        FeedVO vo = new FeedVO();
        Long id = dto.getId();
        Boolean needComment = dto.getNeedComment();
        if (Objects.isNull(needComment) || needComment) {
            Result<CommentDTO> qualityRst = commentStatisticClient.getQualityComment(id);
            if (qualityRst.callSuccess()) {
                CommentDTO result = qualityRst.getResult();
                if (Objects.nonNull(result)) {
                    vo.setQualityComment(commentConverter.toVo(result));
                }
            }
        }
        //排除PGC发布的资源
        if (Integer.valueOf(1).equals(dto.getReleaseType())) {
            SpringUtils.getBean(GroupConverter.class).buildGroupInfo(vo, id);
        }
        vo.setMediaStatus(dto.getStatus());
        vo.setAnswerSquareId(dto.getAnswerId());
        vo.setTitle(dto.getShowName());
        vo.setLabels(dto.getContentTypeLabelStr());
        vo.setContent(dto.getContent());
        vo.setCreateDate(dto.getCreateDate());
        vo.setDescription(dto.getIntroduction());
        vo.setId(id);
        vo.setOutShareTitle(dto.getOutShareTitle());
        vo.setOutShareUrl(dto.getOutShareUrl());
        vo.setColumnId(dto.getColumnId());
        if (Objects.nonNull(dto.getLastAuditPassDate())) {
            vo.setLastPublish(dto.getLastAuditPassDate().getTime());
        }
        vo.setCanComment(dto.getIsComment());
        if (!dto.getReleaseType().equals(2)) {
            vo.setShareUrl(ShareUrlUtils.generateShareUrl("media", id));
        }
        //处理直播相关逻辑
        genVO(dto, vo);
        if (dto.getReleaseType().equals(1)) {
            // 是否公开
            if (!dto.getIsSearch()) {
                // 私密的
                vo.setIsPublic(0);
            } else if (dto.getStatus().equals(MediaResourceStatus.AUDIT.getCode()) ||
                    dto.getStatus().equals(MediaResourceStatus.DISABLE.getCode())) {
                // 审核中
                vo.setIsPublic(2);
            } else {
                vo.setIsPublic(1);
            }
        } else {
            vo.setIsPublic(1);
        }
        if (jid == null) {
            vo.setIsFavor(0);
        } else {
            Result<Boolean> keepRes = interactClient.isAlreadyKeep(id, jid);
            if (keepRes.getCode() != CodeMessage.OK.getCode()) {
                vo.setIsFavor(0);
            } else {
                vo.setIsFavor(keepRes.getResult() ? 1 : 0);
            }
        }
        Result<Integer> favorRst = interactClient.favoriteSum(id);
        if (favorRst.callSuccess()) {
            vo.setFavors(favorRst.getResult());
        }
        vo.setIsStick(1);
        Integer commentTotal = commentStatisticClient.getTotalByMediaId(id, jid).getResult();
        if (commentTotal == null) {
            vo.setReviews(0);
        } else {
            vo.setReviews(commentTotal);
        }
        vo.setVodId(String.valueOf(dto.getPptvProgramCode()));
        vo.setAuthor(getAuthor(dto.getReleaseId(), dto.getReleaseType(), jid));
        //设置认证回复
        vo.setAuthUgcReply(getAuthUgcReply(dto.getReleaseId(), id));
        vo.setShowRecTime(5);
        if (!dto.getReleaseType().equals(2)) {
            //赣云同步视频图片放在pptvcover，如果自己设置了，则覆盖
            Result<List<MediaResourceImageDTO>> result = resourceClient.getImages(id);
            if (result.getCode() == CodeMessage.OK.getCode()) {
                String image = getImage(result.getResult(), MediaResourceImageType.RECOMMENDED_FLOW);
                List<MediaResourceImageDTO> images = dto.getImages();
                if (!dto.getContentType().equals(ContentType.GAN_YUN.getCode())
                        && !Integer.valueOf(ContentType.GAN_YUN_VIDEO.getCode()).equals(dto.getContentType())) {
                    vo.setCoverUrl(image);
                    vo.setCoverImage(ImageVO.convertVO(getUUID(images, MediaResourceImageType.RECOMMENDED_FLOW), ossClient));
                } else {
                    //如果有设置，则采用设置的，否则，前面逻辑有采用pptvcover
                    if (!StringUtils.isEmpty(image)) {
                        vo.setCoverUrl(image);
                        vo.setCoverImage(ImageVO.buildUrl(image));
                    }
                }
                //专题需要额外图片
                if (dto.getContentType() == ContentType.SPECIAL.getCode()) {
                    //专题和文章一致, 将封面图片放入图片列表
                    List<ImageVO> imgs = new ArrayList<>(3);
                    if (!StringUtils.isEmpty(image)) {
                        imgs.add(ImageVO.buildUrl(image));
                    }
                    vo.setImageUrls(ImageVO.getUrls(imgs));
                    vo.setImageList(imgs);
                    vo.setHeadPic(getImage(result.getResult(), MediaResourceImageType.HEAD_PIC));
                    vo.setDetailBigPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_BIG_PIC));
                    vo.setDetailSmallPic(getImage(result.getResult(), MediaResourceImageType.DETAIL_SMALL_PIC));
                }
            }
        } else {
            vo.setCoverUrl(dto.getPptvCover());
            vo.setCoverImage(ImageVO.buildUrl(dto.getPptvCover()));
        }
        // 处理动态选择的赣云视频
        addPostGanyunVideo(vo, dto);
        return vo;
    }

    private void addPostGanyunVideo(FeedVO vo, MediaResourceDTO dto) {
        // 如果是动态，但是采用了赣云的封面和视频, 并且前面没有设置封面 和 文件
        if (Integer.valueOf(ContentType.VIDEO_HOMEMADE.getCode()).equals(dto.getContentType()) && !StringUtils.isEmpty(dto.getPptvCover())) {
            //视频放在pptvcover字段中
            GanyunCoverVO map = JsonUtils.fromJson(dto.getPptvCover(), GanyunCoverVO.class);
            //如果动态采用了赣云视频，且没有在后台设置自己的封面图，则封面图取赣云封面图
            if (StringUtils.isEmpty(vo.getCoverUrl()) && Objects.nonNull(map) && Objects.nonNull(map.getCover())) {
                vo.setCoverUrl(map.getCover());
                vo.setCoverImage(ImageVO.buildUrl(map.getCover()));
            }
            //如果动态采用了赣云视频，，且没有在后台设置自己的视频文件，则封面图取赣云视频
            if (Objects.isNull(dto.getFileId())) {
                if (Objects.nonNull(map) && Objects.nonNull(map.getUrl())) {
                    vo.setMediaUrls(Collections.singletonList(map.getUrl()));
                }
            }
        }
    }

    public FeedVO listRecourceConvert(ContentListResourceDTO listResource) {
        FeedVO vo = new FeedVO();
        vo.setIsPublic(1);
        vo.setIsStick(1);
        vo.setReviews(0);
        vo.setIsFavor(0);
        vo.setCanComment(false);
        vo.setMediaType(12);
        vo.setTitle(listResource.getName());
        vo.setContent(listResource.getName());
        vo.setCreateDate(listResource.getCreateDate());
        vo.setId(listResource.getResourceId());
        vo.setShareUrl(listResource.getLinkUrl());
        vo.setLinkTitle(listResource.getName());
        vo.setLinkUrl(listResource.getLinkUrl());
        if (!CollectionUtils.isEmpty(listResource.getCoverList())) {
            String imgUrl = getImageUrl(listResource.getCoverList().get(0));
            vo.setImageUrls(Arrays.asList(imgUrl));
            vo.setCoverUrl(imgUrl);
        }
        return vo;
    }

    public FeedVO specialTagResourceConvert(SpecialTagResourceDTO specialTagResourceDTO) {
        FeedVO vo = new FeedVO();
        vo.setIsPublic(1);
        vo.setIsStick(1);
        vo.setReviews(0);
        vo.setIsFavor(0);
        vo.setCanComment(false);
        vo.setMediaType(11);
        vo.setTitle(specialTagResourceDTO.getName());
        vo.setContent(specialTagResourceDTO.getName());
        vo.setCreateDate(specialTagResourceDTO.getCreateDate());
        vo.setId(specialTagResourceDTO.getResourceId());
        vo.setShareUrl(specialTagResourceDTO.getLinkUrl());
        vo.setLinkTitle(specialTagResourceDTO.getName());
        vo.setLinkUrl(specialTagResourceDTO.getLinkUrl());
        if (org.apache.commons.lang.StringUtils.isNotBlank(specialTagResourceDTO.getCover())) {
            String imgUrl = getImageUrl(specialTagResourceDTO.getCover());
            vo.setImageUrls(Arrays.asList(imgUrl));
            vo.setCoverUrl(imgUrl);
        }
        return vo;
    }


    public AuditingMediaDTO toAuditingDTO(MediaUgcFileResourceDTO dto) {
        AuditingMediaDTO auditingDto = new AuditingMediaDTO();
        auditingDto.setMediaId(dto.getId());
        // 如果是提问
        auditingDto.setDataType(dto.getDataType());
        auditingDto.setMediaType(dto.getResourceType());
        auditingDto.setAuthorId(dto.getReleaseId());
        Result<ConsumerUserDTO> result = consumerUserClient.getUserByJid(dto.getReleaseId());
        if (result.getResult() != null) {
            auditingDto.setAuthorName(result.getResult().getNickname());
        }
        auditingDto.setMediaTitle(dto.getShowName());
        // ugc类型为1
        auditingDto.setAuthorType(1);
        auditingDto.setContent(dto.getContent());
        auditingDto.setContentType(dto.getContentType());
        auditingDto.setOutShareUrl(dto.getOutShareUrl());
        auditingDto.setOutShareTitle(dto.getOutShareTitle());
        return auditingDto;
    }


    @Data
    public static class QueryRule {
        private Integer id;
        private String rule;
        private List<String> value;
    }

    @Data
    public static class QuerySimpleRule {
        private Integer id;
        private String name;
    }

}
