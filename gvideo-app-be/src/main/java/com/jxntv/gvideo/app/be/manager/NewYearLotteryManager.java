package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.media.client.NewYearLotteryClient;
import com.jxntv.gvideo.media.client.dto.lottery.NewYearLotteryActivityDTO;
import com.jxntv.gvideo.om.client.AppBlacklistClient;
import com.jxntv.gvideo.om.dto.AppBlacklistDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NewYearLotteryManager {

    @Resource
    private NewYearLotteryClient newYearLotteryClient;

    @Resource
    private AppBlacklistClient appBlacklistClient;

    @Cached(name = "app::new::year::lottery::activity", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES, cacheNullValue = true)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public NewYearLotteryActivityDTO activityById(Long activityId) {
        return newYearLotteryClient.queryActivityById(activityId).orElse(null);
    }

    @Cached(name = "app::new::year::lottery::black", cacheType = CacheType.BOTH, expire = 1, timeUnit = TimeUnit.MINUTES)
    public boolean isLotteryBlack(Long jid) {
        AppBlacklistDTO dto = appBlacklistClient.queryByJidAndType(jid, 2).orElse(null);
        return Objects.nonNull(dto);
    }
}
