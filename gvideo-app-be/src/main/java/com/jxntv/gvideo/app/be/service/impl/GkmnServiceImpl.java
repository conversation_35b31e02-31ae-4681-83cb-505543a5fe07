package com.jxntv.gvideo.app.be.service.impl;

import com.jxntv.gvideo.app.be.converter.GkmnConverter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.app.be.manager.GkmnManager;
import com.jxntv.gvideo.app.be.model.vo.gkmn.*;
import com.jxntv.gvideo.app.be.service.GkmnService;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.common.utils.Md5Utils;
import com.jxntv.gvideo.group.sdk.client.GkmnClient;
import com.jxntv.gvideo.group.sdk.dto.GkmnMemberRankDTO;
import com.jxntv.gvideo.group.sdk.dto.GkmnMemberRankResultDTO;
import com.jxntv.gvideo.group.sdk.dto.GkmnMemberSearchRankDTO;
import com.jxntv.gvideo.group.sdk.dto.gkmn.*;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GkmnServiceImpl implements GkmnService {

    private final static String QUESTION_FORMAT = "gkmn::questions::%s";

    @Resource
    private GkmnClient gkmnClient;
    @Resource
    private GkmnManager gkmnManager;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private GkmnConverter gkmnConverter;
    @Resource
    private ConsumerUserManager consumerUserManager;


    @Override
    public List<GkmnSchoolVO> getSchools(Long groupId) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(groupId);
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        List<GkmnSchoolsDTO> dtoList = gkmnManager.allSchools(groupId);
        return dtoList.stream().map(gkmnConverter::convert).collect(Collectors.toList());
    }


    @Override
    public GkmnMemberVO getMember(Long jid,Long groupId) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(groupId);
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        GkmnMemberDTO member = gkmnClient.getMember(jid, groupId).orElse(null);
        if (Objects.isNull(member)) {
            ConsumerUserDTO consumerUserDTO = consumerUserManager.getByJid(jid);
            return gkmnConverter.convert(consumerUserDTO,groupId);
        } else {
            return gkmnConverter.convert(member);
        }
    }


    @Override
    public boolean saveOrUpdateMember(Long jid, GkmnMemberVO memberVO) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(memberVO.getGroupId());
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        GkmnMemberDTO memberDTO = gkmnClient.getMember(jid, memberVO.getGroupId()).orElse(null);
        if (Objects.isNull(memberDTO)) {
            GkmnMemberDTO dto = gkmnConverter.convert(memberVO);
            dto.setJid(jid);
            dto.setGroupId(memberVO.getGroupId());
            gkmnClient.createMember(dto).orElseThrow();
        } else {
            GkmnMemberDTO dto = gkmnConverter.convert(memberVO);
            dto.setJid(jid);
            dto.setGroupId(memberVO.getGroupId());
            gkmnClient.updateMember(memberDTO.getId(), dto).orElseThrow();
        }
        return true;
    }

    @Override
    public GkmnQuestionsVO getQuestions(Long groupId) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(groupId);
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        Long jid = ThreadLocalCache.getJid();
        GkmnMemberDTO member = gkmnManager.getMember(jid, groupId);
        AssertUtil.notNull(member, CodeMessage.BAD_REQUEST.getCode(), "用户还未报名");

        //  判断今天是否答题
        String date = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        int countToday = gkmnClient.countQuestionLog(member.getId(), date).orElse(0);
        if (countToday > 0) {
            throw new CodeMessageException(CodeMessage.BAD_REQUEST.getCode(), "今天已经答题");
        }

        //  获取题目元素
        List<GkmnQuestionsDTO> randomQuestions = getDailyQuestions(gkmnGroupDTO, member.getId());

        //  记录列表ID和元素
        String questionId = UUID.randomUUID().toString();
        String questionListStr = randomQuestions.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.joining(","));
        stringRedisTemplate.opsForValue().set(String.format(QUESTION_FORMAT, questionId), questionListStr, 1, TimeUnit.DAYS);

        //  构建返回体
        GkmnQuestionsVO result = new GkmnQuestionsVO();
        result.setId(questionId);
        result.setQuestionId(questionId);
        result.setList(randomQuestions.stream().map(gkmnConverter::convert).collect(Collectors.toList()));

        return result;
    }


    @Override
    public GkmnMemberVO saveQuestionResult(Long jid, GkmnQuestionLogVO questionLogVO) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(questionLogVO.getGroupId());
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        GkmnMemberDTO member = gkmnManager.getMember(jid, questionLogVO.getGroupId());
        AssertUtil.notNull(member, CodeMessage.BAD_REQUEST.getCode(), "没有报名信息");

        String questionId = questionLogVO.getQuestionId();
        String questionKey = questionLogVO.getQuestionKey();
        List<GkmnQuestionLogVO.DetailVO> detail = questionLogVO.getDetail();

        //  检查参数

        //  1、题目是否篡改
        String questionListStr = stringRedisTemplate.opsForValue().get(String.format(QUESTION_FORMAT, questionId));
        AssertUtil.notEmpty(questionListStr, CodeMessage.BAD_REQUEST.getCode(), "提交失败，重复提交");
        String iValueStr = detail.stream().map(GkmnQuestionLogVO.DetailVO::getI).collect(Collectors.joining(","));

        AssertUtil.assertOrThrow(Objects.equals(questionListStr, iValueStr), CodeMessage.BAD_REQUEST.getCode(), "提交失败，题目列表错误");

        //  2、答案是否篡改
        String aValueStr = detail.stream().map(GkmnQuestionLogVO.DetailVO::getA).collect(Collectors.joining(","));
        String aValueSignStr = Md5Utils.encrypt(aValueStr);
        AssertUtil.assertOrThrow(Objects.equals(aValueSignStr, questionKey), CodeMessage.BAD_REQUEST.getCode(), "提交失败，答案错误");

        //  保存答题结果
        GkmnQuestionLogDTO dto = gkmnConverter.convert(questionLogVO);
        dto.setMemberid(member.getId());
        dto.setList(questionListStr);
        gkmnClient.saveQuestionLog(dto).orElseThrow();

        //  移除题目记录
        stringRedisTemplate.delete(String.format(QUESTION_FORMAT, questionId));

        return this.getMember(jid,questionLogVO.getGroupId());

    }

    @Override
    public List<Integer> queryQuestionAnswer(Long jid, GkmnQuestionAnswerVO vo) {
        GkmnGroupDTO gkmnGroupDTO = gkmnManager.groupInfo(vo.getGroupId());
        AssertUtil.notNull(gkmnGroupDTO, CodeMessage.BAD_REQUEST.getCode(), "活动不存在或已下线");

        GkmnMemberDTO member = gkmnManager.getMember(jid, vo.getGroupId());
        AssertUtil.notNull(member, CodeMessage.BAD_REQUEST.getCode(), "没有报名信息");

        String questionListStr = stringRedisTemplate.opsForValue().get(String.format(QUESTION_FORMAT, vo.getQuestionId()));
        AssertUtil.notEmpty(questionListStr, CodeMessage.BAD_REQUEST.getCode(), "请求参数错误");
        List<Long> questionIdList = Arrays.stream(questionListStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
        AssertUtil.assertOrThrow(questionIdList.contains(vo.getId()), CodeMessage.BAD_REQUEST.getCode(), "请求参数错误");
        GkmnQuestionsDTO question = gkmnManager.getQuestion(vo.getId());
        AssertUtil.notNull(member, CodeMessage.BAD_REQUEST.getCode(), "题目查询失败");
        return question.getAnswer();
    }

    @Override
    public Result<Map<String, Object>> memberRank(Long groupId, Integer pageNum, Integer pageSize) {
        GkmnMemberSearchRankDTO dto = new GkmnMemberSearchRankDTO();
        dto.setGroupId(groupId);
        dto.setJid(ThreadLocalCache.getJid());
        dto.setCurrent(pageNum);
        dto.setSize(pageSize);
        Result<GkmnMemberRankResultDTO> result = gkmnClient.rankSearch(dto);
        if (!result.callSuccess() || Objects.isNull(result.getResult())){
            return Result.fail(result.getCode(),result.getMessage());
        }
        GkmnMemberRankResultDTO rankDto = result.getResult();

        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(rankDto.getCurrentUser())) {
            map.put("self", gkmnConverter.convertRank(rankDto.getCurrentUser()));
        }

        PageDTO<GkmnMemberRankDTO> page = rankDto.getPage();

        if (Objects.nonNull(page)) {
            map.put("pageNum", page.getPageNum());
            map.put("pageSize", page.getPageSize());
            map.put("list", page.getList().stream().map(gkmnConverter::convertRank).collect(Collectors.toList()));
        }
        return Result.ok(map);
    }


    private List<GkmnQuestionsDTO> getDailyQuestions(GkmnGroupDTO dto, Long memberId) {
        List<GkmnQuestionsDTO> dtoList = gkmnManager.allQuestions(dto.getId());

        List<GkmnQuestionsDTO> list = new ArrayList<>(dtoList);
        if (Objects.equals(1,dto.getQuestionOrder())) {
            Collections.shuffle(list);
            return dto.getDailyQuestions() > list.size() ? list : list.subList(0, dto.getDailyQuestions());
        }else{
            Integer orderNo = gkmnClient.getQuestionLogCount(memberId).orElse(0);
            return dto.getDailyQuestions() > list.size() ? list : list.subList(orderNo, orderNo+dto.getDailyQuestions());
        }
    }
}
