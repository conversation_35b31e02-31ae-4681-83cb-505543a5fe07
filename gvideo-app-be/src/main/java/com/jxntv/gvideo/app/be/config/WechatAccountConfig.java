package com.jxntv.gvideo.app.be.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
 
/**
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "wechat")
@RefreshScope
public class WechatAccountConfig {
 
    private String appId;
 
    private String appSecret;
 
}