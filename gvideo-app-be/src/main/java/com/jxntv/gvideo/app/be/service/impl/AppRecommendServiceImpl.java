package com.jxntv.gvideo.app.be.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.jxntv.gvideo.aliyun.sdk.JspVoteClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.raffle.RaffleInfoDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.vote.VoteInfoDTO;
import com.jxntv.gvideo.aliyun.sdk.param.vote.VoteSearchParam;
import com.jxntv.gvideo.app.be.common.config.AppConfig;
import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.common.vo.PageC;
import com.jxntv.gvideo.app.be.converter.FeedConverter;
import com.jxntv.gvideo.app.be.converter.SignAdvertConverter;
import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.manager.AppAuditingManager;
import com.jxntv.gvideo.app.be.manager.AppRecommendManager;
import com.jxntv.gvideo.app.be.manager.AppUserSignAdvertManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.AppRecommendVo;
import com.jxntv.gvideo.app.be.model.vo.BannerImageVO;
import com.jxntv.gvideo.app.be.model.vo.ImageVO;
import com.jxntv.gvideo.app.be.model.vo.drama.DramaListVO;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.service.AppRecommendService;
import com.jxntv.gvideo.app.be.service.BannerService;
import com.jxntv.gvideo.app.be.service.KingKongPositionService;
import com.jxntv.gvideo.app.be.service.MediaService;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.media.client.AppRecommendClient;
import com.jxntv.gvideo.media.client.ContentListClient;
import com.jxntv.gvideo.media.client.SpecialTagClient;
import com.jxntv.gvideo.media.client.dto.AppUserSignAdvertDTO;
import com.jxntv.gvideo.media.client.dto.ContentListResourceDTO;
import com.jxntv.gvideo.media.client.dto.SpecialTagResourceDTO;
import com.jxntv.gvideo.media.client.dto.recommend.*;
import com.jxntv.gvideo.media.client.dto.special.SpecialResourceSearchParam;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.om.dto.AppAuditingDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppRecommendServiceImpl implements AppRecommendService {

    @Resource
    private AppUserSignAdvertManager appUserSignAdvertManager;
    @Resource
    private SignAdvertConverter signAdvertConverter;
    @Resource
    private AppRecommendManager appRecommendManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private AppConfig appConfig;
    @Resource
    private KingKongPositionService kingKongPositionService;
    @Resource
    private BannerService bannerService;
    @Resource
    private FeedV2Converter feedV2Converter;
    @Resource
    private AppRecommendClient appRecommendClient;
    @Resource
    private SpecialTagClient specialTagClient;
    @Resource
    private JspVoteClient jspVoteClient;

    @Resource
    private MediaService mediaService;
    @Resource
    private AppAuditingManager appAuditingManager;
    @Resource
    private FeedConverter feedConverter;
    @Resource
    private ContentListClient contentListClient;



    @Override
    public Result<AppRecommendVo> info(UserInfo userInfo) {
        AppRecommendVo vo = new AppRecommendVo();
        // 金刚位
        // vo.setKingKongPosition(kingKongPositionService.getPositionsV2(userInfo));
        // banner
        // vo.setBanner(bannerService.getBanner(-1L, 0));

        AppRecommendConfigDTO appRecommendConfig = appRecommendManager.getAppRecommendConfig();
        if (Objects.nonNull(appRecommendConfig) && !CollectionUtils.isEmpty(appRecommendConfig.getConfigList())) {
            List<AppRecommendVo.RecommendVo> recommendVoList = new ArrayList<>(appRecommendConfig.getConfigList().size());
            for (AppRecommendDTO recommendDTO : appRecommendConfig.getConfigList()) {
                AppRecommendVo.RecommendVo recommendVo = new AppRecommendVo.RecommendVo();
                recommendVo.setListId(recommendDTO.getId());
                recommendVo.setName(recommendDTO.getName());
                recommendVo.setType(recommendDTO.getType());
                recommendVo.setBgColor(recommendDTO.getBgColor());
                recommendVo.setIconUrl(ossManager.getOriFile(recommendDTO.getIcon()).map(OssDTO::getUrl).orElse(""));
                recommendVo.setLinkUrl(recommendDTO.getLinkUrl());
                recommendVo.setSort(recommendDTO.getSort());
                if (Objects.equals(1, recommendDTO.getType())) {
                    recommendVo.setData(this.convertWelfareBroadcast(recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(2, recommendDTO.getType())) {
                    recommendVo.setData(this.convertRaffle(recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(3, recommendDTO.getType())) {
                    recommendVo.setData(this.convertVote(recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(4, recommendDTO.getType())) {
                    recommendVo.setData(this.convertRecommendResource(recommendDTO.getResourceList()));
                } else if (Objects.equals(5, recommendDTO.getType())) {
                    // 如果版本正在审核，跳过BANNER推荐
                    AppAuditingDTO auditing = appAuditingManager.getAuditing(userInfo.getOs(), userInfo.getAppVersion());
                    if (Objects.nonNull(auditing) && Objects.equals(auditing.getStatus(), 1)) {
                        continue;
                    }
                    recommendVo.setData(this.convertBannerResource());
                } else if (Objects.equals(6, recommendDTO.getType())) {
                    recommendVo.setData(this.convertContentListResource(recommendDTO.getRemarks(), recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(7, recommendDTO.getType())) {
                    recommendVo.setData(this.convertLiveCollectionV2(userInfo.getJid(), recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(8, recommendDTO.getType())) {
                    recommendVo.setData(this.convertContentListResource(recommendDTO.getRemarks(), recommendDTO.getHomeDefaultNum()));
                } else if (Objects.equals(9, recommendDTO.getType())) {
                    recommendVo.setData(this.convertDramaResource(recommendDTO.getHomeDefaultNum()));
                }


                recommendVoList.add(recommendVo);
            }
            vo.setRecommendList(recommendVoList);
        }

        return Result.ok(vo);
    }

    @Override
    public Result<Object> resourceList(Long listId, Integer pageNum, Integer pageSize) {
        Long jid = ThreadLocalCache.getJid();
        AppRecommendListDTO listInfo = appRecommendClient.getListInfo(listId).orElse(null);
        if (Objects.isNull(listInfo)) {
            return Result.fail("查询失败，请稍后再试！");
        }
        if (Objects.equals(3, listInfo.getType())) {

            VoteSearchParam param = new VoteSearchParam();
            param.setCurrent(pageNum);
            param.setSize(pageSize);
            Result<PageDTO<VoteInfoDTO>> pageDTOResult = jspVoteClient.pageActivity(param);
            if (!pageDTOResult.callSuccess()) {
                throw new CodeMessageException(CodeMessage.BAD_REQUEST, "查询失败");
            }
            return Result.ok(PageC.of(pageDTOResult.getResult(), i -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", i.getId());
                map.put("activityName", i.getTitle());
                map.put("linkUrl", i.getActivityUrl());
                map.put("bannerUrl", i.getBannerUrl());
                map.put("status", i.getStatus());
                return map;
            }));

        } else if (Objects.equals(6, listInfo.getType()) || Objects.equals(8, listInfo.getType())) {
            Long contentListId = this.getContentListId(listInfo.getRemarks());
            if (Objects.isNull(contentListId) || contentListId <= 0) {
                throw new CodeMessageException(CodeMessage.BAD_REQUEST, "查询失败");
            }
            Result<PageDTO<ContentListResourceDTO>> pageDTOResult = contentListClient.resourcePage(contentListId, "", MediaResourceStatus.ENABLE.getCode(), pageNum, pageSize);
            if (!pageDTOResult.callSuccess()) {
                throw new CodeMessageException(CodeMessage.BAD_REQUEST, "查询失败");
            }

            pageDTOResult.getResult().setList(appRecommendManager.convertContentList(pageDTOResult.getResult().getList()));

            PageC<FeedVO> pageC = PageC.of(pageDTOResult.getResult(), i -> feedV2Converter.convert(FeedContext.builder().id(i.getResourceId()).jid(jid).showRecommend(true).build()));

            // this.handelFeedTitle(pageC.getList(), listInfo.getRemarks());
            this.handleFeedVOV2(pageC.getList(), pageDTOResult.getResult().getList());
            return Result.ok(pageC);

        } else {
            AppRecommendListResourceSearchDTO searchDTO = new AppRecommendListResourceSearchDTO();
            searchDTO.setListId(listId);
            searchDTO.setCurrent(pageNum);
            searchDTO.setSize(pageSize);
            Result<PageDTO<AppRecommendListResourceDTO>> pageDTOResult = appRecommendClient.pageByListId(searchDTO);
            if (!pageDTOResult.callSuccess()) {
                throw new CodeMessageException(CodeMessage.BAD_REQUEST, "查询失败");
            }
            PageC<FeedVO> pageC = PageC.of(pageDTOResult.getResult(), i -> feedV2Converter.convert(FeedContext.builder().id(i.getResourceId()).jid(jid).showRecommend(true).build()));

            // this.handelFeedTitle(pageC.getList(), listInfo.getRemarks());
            this.handleFeedVO(pageC.getList(), pageDTOResult.getResult().getList());
            return Result.ok(pageC);
        }
    }


    /**
     * 获取福利播报
     *
     * @return
     */
    public AppRecommendVo.WelfareBroadcastVO convertWelfareBroadcast(Integer homeDefaultNum) {
        List<AppUserSignAdvertDTO> advertList = appUserSignAdvertManager.getAdvertList();
        if (!CollectionUtils.isEmpty(advertList)) {
            List<AppRecommendVo.WelfareItem> itemList = advertList.stream().map(signAdvertConverter::convertWelfare).collect(Collectors.toList());
            AppRecommendVo.WelfareBroadcastVO vo = new AppRecommendVo.WelfareBroadcastVO();
            vo.setLinkUrl(appConfig.getSignLinkUrl());
            vo.setItems(itemList.stream().limit(homeDefaultNum).collect(Collectors.toList()));
            vo.setJoinUserCount(appRecommendManager.getJoinUserCount());
            vo.setJoinUserAvatars(appRecommendManager.getJoinUserAvatars());
            return vo;
        }
        return null;
    }

    /**
     * 获取免费看演出
     *
     * @return
     */
    public List<AppRecommendVo.RaffleVO> convertRaffle(Integer homeDefaultNum) {
        List<RaffleInfoDTO> raffleList = appRecommendManager.getRaffleList(homeDefaultNum);
        if (CollectionUtils.isEmpty(raffleList)) {
            return null;
        }
        return raffleList.stream().map(e -> {
            AppRecommendVo.RaffleVO vo = new AppRecommendVo.RaffleVO();
            vo.setId(e.getId());
            vo.setActivityName(e.getActivityName());
            vo.setActivityPrizePicUrl(e.getActivityPrizePicUrl());
            vo.setBgColor(e.getBgColor());
            vo.setIsDarkFont(e.getIsDarkFont());
            vo.setActivityPrizeValue(e.getActivityPrizeValue());
            vo.setLinkUrl(e.getActivityUrl());
            vo.setApplyCount(e.getApplyCount());
            vo.setApplyUserAvatars(e.getApplyUserAvatars());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 活动1(投票)
     *
     * @return
     */
    public List<AppRecommendVo.ActivityVo> convertVote(Integer homeDefaultNum) {
        List<VoteInfoDTO> voteList = appRecommendManager.getVoteList(homeDefaultNum);
        if (CollectionUtils.isEmpty(voteList)) {
            return null;
        }
        return voteList.stream().map(e -> {
            AppRecommendVo.ActivityVo vo = new AppRecommendVo.ActivityVo();
            vo.setId(e.getId());
            vo.setActivityName(e.getTitle());
            vo.setLinkUrl(e.getActivityUrl());
            vo.setStatus(e.getStatus());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取推荐资源对象
     *
     * @return
     */
    public List<AppRecommendVo.ActivityVo> convertActivity(List<AppRecommendListResourceDTO> resourceList) {

        if (CollectionUtils.isEmpty(resourceList)) {
            return null;
        }
        return resourceList.stream().map(e -> {
            AppRecommendVo.ActivityVo vo = new AppRecommendVo.ActivityVo();
            vo.setId(e.getId());
            vo.setActivityName(e.getName());
            vo.setLinkUrl(e.getLinkUrl());
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取推荐资源对象
     *
     * @return
     */
    public List<AppRecommendVo.RecommendResourceVO> convertRecommendResource(List<AppRecommendListResourceDTO> resourceList) {

        if (CollectionUtils.isEmpty(resourceList)) {
            return null;
        }
        return resourceList.stream().map(e -> {
            AppRecommendVo.RecommendResourceVO vo = new AppRecommendVo.RecommendResourceVO();
            vo.setId(e.getId());
            vo.setListId(e.getListId());
            vo.setName(e.getName());
            vo.setContent(e.getContent());
            vo.setIconUrl(ossManager.getOriFile(e.getIcon()).map(OssDTO::getUrl).orElse(""));
            vo.setLinkUrl(e.getLinkUrl());
            vo.setBgColor(e.getBgColor());
            return vo;
        }).collect(Collectors.toList());
    }


    /**
     * 获取直播合集对象（长天新闻版）
     *
     * @return
     */
    public List<FeedVO> convertLiveCollection(String remarks, Long jid, Integer homeDefaultNum) {
        if (StringUtils.isEmpty(remarks)) {
            return null;
        }
        JSONObject jsonObject = JSONObject.parseObject(remarks);
        if (Objects.nonNull(jsonObject) && jsonObject.containsKey("specialId")) {
            SpecialResourceSearchParam param = new SpecialResourceSearchParam();
            param.setSpecialId(jsonObject.getLong("specialId"));
            param.setCurrent(1);
            param.setSize(10);
            param.addSort(SearchDTO.Sort.of("tagId", true));
            param.addSort(SearchDTO.Sort.of("sort", false));

            PageDTO<SpecialTagResourceDTO> page = specialTagClient.resourcePage(param).orElseThrow();

            Page<FeedVO> contents = Page.asyncOf(page, e -> feedV2Converter.convert(FeedContext.builder().id(e.getResourceId()).jid(jid).showRecommend(false).build()));
            List<FeedVO> list = contents.getList();

            if (!CollectionUtils.isEmpty(list)) {
                // 移除已经下架的内容
                list.removeIf(e -> Objects.equals(e.getLiveBroadcastStatus(), 4));
                return list.stream().limit(homeDefaultNum).collect(Collectors.toList());
            }

            // return appRecommendManager.getLiveCollection(jsonObject.getLong("specialId"));
        }
        return null;
    }


    /**
     * 获取直播合集对象V2
     *
     * @return
     */
    public List<FeedVO> convertLiveCollectionV2(Long jid, Integer homeDefaultNum) {

        List<FeedVO> list = mediaService.getPlayingList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list.sort(Comparator.comparingInt(FeedVO::getLiveBroadcastStatus).reversed());
        return list.stream().limit(homeDefaultNum).collect(Collectors.toList());
    }

    /**
     * 获取推荐视频资源对象
     *
     * @return
     */
    public List<FeedVO> convertVideoResource(List<AppRecommendListResourceDTO> resourceList, String remarks) {

        if (CollectionUtils.isEmpty(resourceList)) {
            return null;
        }

        List<FeedVO> list = resourceList.stream().map(e -> feedV2Converter.convert(FeedContext.builder().id(e.getResourceId()).showRecommend(true).build())).collect(Collectors.toList());

        // this.handelFeedTitle(list, remarks);
        this.handleFeedVO(list, resourceList);
        return list;
    }


    /**
     * 获取推荐视频资源对象
     *
     * @return
     */
    public List<FeedVO> convertContentListResource(String remarks, Integer homeDefaultNum) {

        Long contentListId = this.getContentListId(remarks);
        if (Objects.isNull(contentListId) || contentListId <= 0) {
            return null;
        }
        List<ContentListResourceDTO> resourceList = this.appRecommendManager.getContentList(contentListId);
        if (CollectionUtils.isEmpty(resourceList)){
            return null;
        }

        List<FeedVO> list = resourceList.stream().map(e -> feedV2Converter.convert(FeedContext.builder().id(e.getResourceId()).showRecommend(true).build())).collect(Collectors.toList());

        // this.handelFeedTitle(list, remarks);
        this.handleFeedVOV2(list, resourceList);
        return list.stream().filter(Objects::nonNull).limit(homeDefaultNum).collect(Collectors.toList());
    }

    /**
     * 获取推荐页中部banner对象
     *
     * @return
     */
    public BannerImageVO convertBannerResource() {
        BannerImageVO recommendBanner = appRecommendManager.getRecommendBanner();
        if (Objects.isNull(recommendBanner)) {
            return null;
        }

        return recommendBanner;
    }

    /**
     * 获取短剧推荐对象
     *
     * @return
     */
    public List<DramaListVO> convertDramaResource(Integer homeDefaultNum) {
        return appRecommendManager.getDramaList(homeDefaultNum);
    }

    boolean showTitleFlag(String remarks) {
        Boolean isShowTitle = Boolean.TRUE;
        if (StringUtils.hasText(remarks)) {
            JSONObject jsonObject = JSONObject.parseObject(remarks);
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("titleType")) {
                Integer titleType = jsonObject.getInteger("titleType");
                isShowTitle = Objects.equals(titleType, 1);
            }
        }
        return isShowTitle;
    }

    Long getContentListId(String remarks) {
        if (StringUtils.hasText(remarks)) {
            JSONObject jsonObject = JSONObject.parseObject(remarks);
            if (Objects.nonNull(jsonObject) && jsonObject.containsKey("contentListId")) {
                return jsonObject.getLong("contentListId");
            }
        }
        return null;
    }

    void handelFeedTitle(List<FeedVO> list, String remarks) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Boolean showTitle = this.showTitleFlag(remarks);
        if (Boolean.FALSE.equals(showTitle)) {
            list.forEach(e -> {
                if (StringUtils.hasText(e.getDescription())) {
                    e.setTitle(e.getDescription());
                }
            });
        }
    }

    void handleFeedVO(List<FeedVO> list, List<AppRecommendListResourceDTO> resourceList) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        Map<Long, AppRecommendListResourceDTO> resourceMap = resourceList.stream().filter(res -> res.getResourceId() != null).collect(Collectors.toMap(AppRecommendListResourceDTO::getResourceId, Function.identity(), (existing, replacement) -> existing));

        for (FeedVO feed : list) {
            if (Objects.isNull(feed.getId())) {
                continue;
            }

            AppRecommendListResourceDTO resource = resourceMap.get(feed.getId());
            if (Objects.isNull(resource)) {
                continue;
            }

            // 处理名称设置
            if (StringUtils.hasText(resource.getName())) {
                feed.setTitle(resource.getName());
            }

            // 处理图标设置
            String icon = resource.getIcon();
            if (StringUtils.hasText(icon)) {
                ossManager.getOssFile(icon).ifPresent(ossDTO -> {
                    feed.setCoverUrl(ossDTO.getUrl());
                    feed.setCoverImage(ImageVO.buildDTO(ossDTO));
                });
            }
        }
    }

    void handleFeedVOV2(List<FeedVO> list, List<ContentListResourceDTO> resourceList) {
        if (CollectionUtils.isEmpty(list) || CollectionUtils.isEmpty(resourceList)) {
            return;
        }
        Map<Long, ContentListResourceDTO> resourceMap = resourceList.stream().filter(res -> Objects.nonNull(res.getResourceId())).collect(Collectors.toMap(ContentListResourceDTO::getResourceId, Function.identity(), (existing, replacement) -> existing));

        for (FeedVO feedVO : list) {
            if (Objects.isNull(feedVO.getId())) {
                continue;
            }

            ContentListResourceDTO listResource = resourceMap.get(feedVO.getId());
            if (Objects.isNull(listResource)) {
                continue;
            }

            // 处理名称设置
            if (StringUtils.hasText(listResource.getName())) {
                feedVO.setTitle(listResource.getName());
            }

            // 处理图标设置
            if (!CollectionUtils.isEmpty(listResource.getCoverList())) {
                // 判断资源图片
                List<ImageVO> imageUrls;
                if (CollectionUtils.isEmpty(feedVO.getImageUrls())) {
                    imageUrls = new ArrayList<>(listResource.getCoverList().size());
                    for (int i = 0; i < listResource.getCoverList().size(); i++) {
                        imageUrls.add(new ImageVO());
                    }
                } else {
                    imageUrls = feedVO.getImageList();
                }
                for (int i = 0; i < listResource.getCoverList().size(); i++) {
                    String ossId = listResource.getCoverList().get(i);
                    if (StringUtils.isEmpty(ossId)) {
                        continue;
                    }
                    // 如果存在修改则对应的位置信息修改
                    if (i < imageUrls.size()) {
                        imageUrls.set(i, feedConverter.getImageVO(ossId));
                    }
                }
                if (!CollectionUtils.isEmpty(imageUrls)) {
                    feedVO.setImageUrls(ImageVO.getUrls(imageUrls));
                    feedVO.setImageList(imageUrls);
                    feedVO.setCoverUrl(imageUrls.get(0).getUrl());
                    feedVO.setCoverImage(ImageVO.buildUrl(imageUrls.get(0).getUrl()));
                }
            }
        }
    }
}
