package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.app.be.client.ConsumerPurseService;
import com.jxntv.gvideo.user.client.dto.ConsumerPurseBrokeDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Slf4j
@Component
public class ConsumerUserPurseManager {
    @Resource
    private ConsumerPurseService consumerPurseService;

    @Cached(name = "app::purse:reward::brokeName", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES)
    public ConsumerPurseBrokeDTO getBrokeNameByPurseRecordId(Long purseRecordId) {
        return Objects.isNull(purseRecordId) ? null : consumerPurseService.getBrokeNameByPurseRecordId(purseRecordId).orElse(null);
    }
}
