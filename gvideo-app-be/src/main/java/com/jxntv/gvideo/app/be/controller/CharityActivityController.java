package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.app.be.converter.charity.CharityActivityConverter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.model.vo.charity.CharityActivityEnrollInfoVO;
import com.jxntv.gvideo.app.be.model.vo.charity.CharityActivityEnrollVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.CharityActivityClient;
import com.jxntv.gvideo.media.client.dto.charity.CharityActivityEnrollDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 公益活动App端控制器 处理活动报名相关的请求
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/charity/activity")
@Api(value = "公益活动App端接口", tags = {"公益活动App端接口"})
public class CharityActivityController {

    @Resource
    private CharityActivityClient charityActivityClient;

    @Resource
    private CharityActivityConverter charityActivityConverter;

    @Resource
    private OssClient ossClient;

    @PostMapping("/enroll")
    @ApiOperation(value = "新增活动报名", notes = "新增活动报名")
    public Result<Boolean> activityEnroll(@RequestBody CharityActivityEnrollVO vo) {
        Long jid = ThreadLocalCache.getJid();
        CharityActivityEnrollDTO dto = charityActivityConverter.convert(vo);
        dto.setJid(jid);
        return charityActivityClient.activityEnroll(dto);
    }

    @GetMapping("/enroll/info")
    @ApiOperation(value = "查询用户报名信息", notes = "通过活动ID和用户JID查询报名信息")
    public Result<CharityActivityEnrollInfoVO> queryEnrollInfo(@ApiParam(name = "activityId", value = "活动ID", required = true) @RequestParam Long activityId) {
        Long jid = ThreadLocalCache.getJid();
        Result<List<CharityActivityEnrollDTO>> result = charityActivityClient.queryEnrollListByJid(activityId, jid);
        if (!result.callSuccess()) {
            return Result.fail(result.getMessage());
        }

        List<CharityActivityEnrollDTO> enrollList = result.getResult();
        if (enrollList == null || enrollList.isEmpty()) {
            return Result.ok(null);
        }

        // 查找审核状态为0(未审核)或1(通过)的记录
        CharityActivityEnrollDTO targetEnroll = enrollList.stream()
                .filter(enroll -> Objects.equals(enroll.getAuditStatus(), 0) || Objects.equals(enroll.getAuditStatus(), 1))
                .findFirst()
                .orElse(null);

        // 如果没有状态为0或1的记录，则返回最新的一条记录（列表已按ID降序排序）
        if (targetEnroll == null) {
            targetEnroll = enrollList.get(0);
        }

        // 转换为VO（在转换器中已包含获取URL的逻辑）
        CharityActivityEnrollInfoVO vo = charityActivityConverter.convert(targetEnroll);

        return Result.ok(vo);
    }
}
