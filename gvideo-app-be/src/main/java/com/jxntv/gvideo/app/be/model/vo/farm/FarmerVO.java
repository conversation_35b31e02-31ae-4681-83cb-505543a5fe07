package com.jxntv.gvideo.app.be.model.vo.farm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("农场主个人信息对象")
public class FarmerVO {

    @ApiModelProperty("农场主ID")
    private Long id;

    @ApiModelProperty("农场主JID")
    private Long jid;

    @ApiModelProperty("鸡蛋余额")
    private Integer eggsBalance;

    @ApiModelProperty("饲料余额")
    private Integer feedBalance;

    @ApiModelProperty("是否签到")
    private Boolean signed=false;

    @ApiModelProperty("待领取饲料")
    private Integer toBeClaimed=0;
}
