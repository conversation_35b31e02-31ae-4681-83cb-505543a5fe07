package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.BroadcastLocationClientFallback;
import com.jxntv.gvideo.media.client.BroadcastLocationClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> Created on 2021/6/30.
 */
@FeignClient(name = "media-service", contextId = "broadcast-location", fallbackFactory = BroadcastLocationClientFallback.class)
public interface BroadcastLocationService extends BroadcastLocationClient {
}
