package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.AppRecommendVo;
import com.jxntv.gvideo.app.be.model.vo.sign.SignAdvertVO;
import com.jxntv.gvideo.media.client.dto.AppUserSignAdvertDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class SignAdvertConverter {

    @Resource
    private OssManager ossManager;


    public SignAdvertVO.Item convert(AppUserSignAdvertDTO dto){
        String coverUrl = ossManager.getOssFile(dto.getCover()).map(OssDTO::getUrl).orElse("");
        SignAdvertVO.Item item = new SignAdvertVO.Item();
        item.setTitle(dto.getTitle());
        item.setPrice(dto.getPrice());
        item.setIsFree(dto.getIsFree());
        item.setRequirement(dto.getRequirement());
        item.setCover(coverUrl);
        return item;
    }

    public AppRecommendVo.WelfareItem convertWelfare(AppUserSignAdvertDTO dto){
        String coverUrl = ossManager.getOssFile(dto.getCover()).map(OssDTO::getUrl).orElse("");
        AppRecommendVo.WelfareItem item = new AppRecommendVo.WelfareItem();
        item.setTitle(dto.getTitle());
        item.setPrice(dto.getPrice());
        item.setIsFree(dto.getIsFree());
        item.setRequirement(dto.getRequirement());
        item.setCover(coverUrl);
        return item;
    }
}
