package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("圈子广播对象")
public class BroadcastDetailVO {
    @ApiModelProperty("主持人信息")
    private AuthorVO author;
    @ApiModelProperty("节目名称")
    private String name;
    @ApiModelProperty("节目地址")
    private String url;
    @ApiModelProperty("节目开始时间")
    private Date startTime;
    @ApiModelProperty("节目结束时间")
    private Date endTime;
}
