package com.jxntv.gvideo.app.be.model.vo.blind.date.accost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="GroupBlindDateAccostReadVO", description="都市放心爱-相亲-招呼阅知")
public class GroupBlindDateAccostReadVO {

    @ApiModelProperty(value="招呼ID")
    @NotNull(message = "招呼ID不能为空")
    private Long id;

    @ApiModelProperty(value="阅知类型  1-接收到的招呼阅知 2-被回应的招呼阅知")
    @NotNull(message = "阅知类型不能为空")
    @Min(value=1,message = "审核状态在1-2之间")
    @Max(value=2,message = "审核状态在1-2之间")
    private Integer type;

}
