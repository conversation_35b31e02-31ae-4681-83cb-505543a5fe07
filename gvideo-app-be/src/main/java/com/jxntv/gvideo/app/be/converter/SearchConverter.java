package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.converter.feed.FeedV2Converter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.manager.*;
import com.jxntv.gvideo.app.be.model.dto.ColumnProgramDTO;
import com.jxntv.gvideo.app.be.model.dto.LinkJumpDto;
import com.jxntv.gvideo.app.be.model.vo.AuthorVO;
import com.jxntv.gvideo.app.be.model.vo.GanyunCoverVO;
import com.jxntv.gvideo.app.be.model.vo.GanyunVideoVO;
import com.jxntv.gvideo.app.be.model.vo.ImageVO;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.search.RecentProgramVO;
import com.jxntv.gvideo.app.be.model.vo.search.SearchResponseVO;
import com.jxntv.gvideo.app.be.model.vo.tv.TvProgramFeedVO;
import com.jxntv.gvideo.app.be.utils.AppVersionUtils;
import com.jxntv.gvideo.app.be.utils.ShareUrlUtils;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.sdk.client.GroupManageClient;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.media.client.dto.AdvertSearchMarketingDTO;
import com.jxntv.gvideo.media.client.dto.DramaColumnDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceImageDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvColumnDTO;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.EnableEnum;
import com.jxntv.gvideo.media.client.enums.PlayStyle;
import com.jxntv.gvideo.search.client.dto.MultiContentDTO;
import com.jxntv.gvideo.search.client.dto.group.GroupIndexDTO;
import com.jxntv.gvideo.search.client.dto.live.LiveBroadcastIndexDTO;
import com.jxntv.gvideo.search.client.dto.news.NewsIndexDTO;
import com.jxntv.gvideo.search.client.dto.post.PostIndexDTO;
import com.jxntv.gvideo.search.client.dto.program.ProgramIndexDTO;
import com.jxntv.gvideo.search.client.dto.user.UserIndexDTO;
import com.jxntv.gvideo.user.client.dto.CertificationDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerAuthenticationDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SearchConverter {

    @Resource
    private MediaResourceManager mediaResourceManager;
    @Resource
    private CertificationManager certificationManager;
    @Resource
    private InteractClient interactClient;
    @Resource
    private ConsumerUserManager consumerUserManager;
    @Resource
    private GroupManageClient groupManageClient;
    @Resource
    private TvColumnManager tvColumnManager;
    @Resource
    private TvProgramManager tvProgramManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private GroupManager groupManager;
    @Resource
    private SourceManager sourceManager;
    @Resource
    private FeedV2Converter feedV2Converter;
    @Resource
    private LinkUrlConverter linkUrlConverter;
    @Resource
    private TvConverter tvConverter;
    @Resource
    private DramaProgramManager dramaProgramManager;

    public SearchResponseVO convert(Object dto, Long jid) {

        //  用户
        if (dto instanceof UserIndexDTO) {
            return convertUser((UserIndexDTO) dto, jid);
        }

        //  社区
        if (dto instanceof GroupIndexDTO) {
            return convertGroup((GroupIndexDTO) dto, jid);
        }

        //  新闻
        if (dto instanceof NewsIndexDTO) {
            return convertNews((NewsIndexDTO) dto, jid);
        }

        //  动态
        if (dto instanceof PostIndexDTO) {
            return convertPost((PostIndexDTO) dto, jid);
        }

        //  节目
        if (dto instanceof ColumnProgramDTO) {
            return convertColumn((ColumnProgramDTO) dto, jid);
        }

        //  搜索触达服务
        if (dto instanceof AdvertSearchMarketingDTO) {
            return convertTriggerService((AdvertSearchMarketingDTO) dto, jid);
        }

        //  直播
        if (dto instanceof LiveBroadcastIndexDTO) {
            return convertLiveBroadcast((LiveBroadcastIndexDTO) dto, jid);
        }


        return null;
    }


    public SearchResponseVO convertMulti(MultiContentDTO dto, Long jid) {
        switch (dto.getType()) {
            case CONSUMER_USER:
                return convertUgc(dto, jid);
            case CERTIFICATION_USER:
                return convertPgc(dto, jid);
            case COMMUNITY:
                return convertGroup(dto, jid);
            case NEWS:
            case SPECIAL:
                return convertNews(dto, jid);
            case POST:
                return convertPost(dto, jid);
            case LIVE:
                return convertLiveBroadcast(dto, jid);
            case PROGRAM:
                return convertColumn(dto, jid);
            case DRAMA:
                return convertDrama(dto, jid);
            default:
                return null;
        }
    }


    private SearchResponseVO.SearchLiveBroadcastRespVO convertLiveBroadcast(LiveBroadcastIndexDTO dto, Long jid) {
        FeedVO feed = feedV2Converter.convert(FeedContext.of(dto.getId(), jid));
        if (Objects.nonNull(feed)) {
            SearchResponseVO.SearchLiveBroadcastRespVO vo = new SearchResponseVO.SearchLiveBroadcastRespVO();
            vo.setId(dto.getId());
            vo.setCategory(7);
            vo.setWords(dto.getTokens());
            vo.setScore(dto.getScore());
            copy(feed, vo);
            return vo;
        }
        return null;
    }

    private SearchResponseVO.SearchLiveBroadcastRespVO convertLiveBroadcast(MultiContentDTO dto, Long jid) {
        Long id = dto.getContentId();
        FeedVO feed = feedV2Converter.convert(FeedContext.of(id, jid));
        if (Objects.nonNull(feed)) {
            SearchResponseVO.SearchLiveBroadcastRespVO vo = new SearchResponseVO.SearchLiveBroadcastRespVO();
            vo.setId(id);
            vo.setCategory(7);
            vo.setWords(dto.getTokens());
            copy(feed, vo);
            return vo;
        }
        return null;
    }

    private SearchResponseVO.SearchPostsRespVO convertPost(PostIndexDTO dto, Long jid) {
        FeedVO feed = feedV2Converter.convert(FeedContext.of(dto.getMediaId(), jid));
        if (Objects.nonNull(feed)) {
            SearchResponseVO.SearchPostsRespVO vo = new SearchResponseVO.SearchPostsRespVO();
            vo.setId(dto.getMediaId());
            vo.setCategory(4);
            vo.setWords(dto.getTokens());
            vo.setScore(dto.getScore());
            copy(feed, vo);
            return vo;
        }
        return null;
    }

    private SearchResponseVO.SearchPostsRespVO convertPost(MultiContentDTO dto, Long jid) {
        Long mediaId = dto.getContentId();
        FeedVO feed = feedV2Converter.convert(FeedContext.of(mediaId, jid));
        if (Objects.nonNull(feed)) {
            SearchResponseVO.SearchPostsRespVO vo = new SearchResponseVO.SearchPostsRespVO();
            vo.setId(mediaId);
            vo.setCategory(4);
            vo.setWords(dto.getTokens());
            copy(feed, vo);
            return vo;
        }
        return null;
    }

    private SearchResponseVO.SearchDramaColumnRespVO convertDrama(MultiContentDTO dto, Long jid) {

        DramaColumnDTO columnDTO = dramaProgramManager.getColumnById(dto.getContentId());
        if (Objects.nonNull(columnDTO)) {
            SearchResponseVO.SearchDramaColumnRespVO vo = new SearchResponseVO.SearchDramaColumnRespVO();
            vo.setId(dto.getContentId());
            vo.setCategory(8);
            vo.setWords(dto.getTokens());
            vo.setTitle(columnDTO.getColumnName());
            vo.setCoverUrl(ossManager.getOssFile(columnDTO.getCoverId()).map(OssDTO::getUrl).orElse(""));
            vo.setTotalNum(columnDTO.getDramaTotalNum());
            vo.setCurrentNum(dramaProgramManager.getProgramNumByColumnId(dto.getContentId()));
            vo.setIsMore(Boolean.TRUE);
            return vo;
        }

        return null;
    }


    private SearchResponseVO.SearchColumnRespVO convertColumn(MultiContentDTO dto, Long jid) {

        TvColumnDTO columnDTO = tvColumnManager.getById(dto.getContentId()).orElse(null);
        if (Objects.nonNull(columnDTO)) {
            SearchResponseVO.SearchColumnRespVO vo = new SearchResponseVO.SearchColumnRespVO();
            vo.setId(dto.getContentId());
            vo.setCategory(5);
            vo.setWords(dto.getTokens());
            vo.setTitle(columnDTO.getColumnName());
            vo.setIntro(columnDTO.getIntroduction());
            vo.setCoverUrl(columnDTO.getCoverUrl());
            List<RecentProgramVO> programs = getPrograms(Objects.isNull(dto.getEpisodes()) ? Collections.emptyList():dto.getEpisodes().stream().map(MultiContentDTO.Episode::getEpisodeId).collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(programs) && programs.size() > 5) {
                vo.setIsMore(Boolean.TRUE);
                vo.setRecentPrograms(programs.subList(0, 5));
            } else {
                vo.setIsMore(Boolean.FALSE);
                vo.setRecentPrograms(programs);
            }
            return vo;
        }

        return null;
    }


    private SearchResponseVO.SearchColumnRespVO convertColumn(ColumnProgramDTO dto, Long jid) {

        TvColumnDTO columnDTO = tvColumnManager.getById(dto.getColumnId()).orElse(null);
        if (Objects.nonNull(columnDTO)) {
            SearchResponseVO.SearchColumnRespVO vo = new SearchResponseVO.SearchColumnRespVO();
            vo.setId(dto.getColumnId());
            vo.setCategory(5);
            vo.setWords(dto.getTokens());
            vo.setTitle(columnDTO.getColumnName());
            vo.setCoverUrl(columnDTO.getCoverUrl());
            List<RecentProgramVO> programs = getPrograms(dto.getProgramIds());
            if (!CollectionUtils.isEmpty(programs) && programs.size() > 5) {
                vo.setIsMore(Boolean.TRUE);
                vo.setRecentPrograms(programs.subList(0, 5));
            } else {
                vo.setIsMore(Boolean.FALSE);
                vo.setRecentPrograms(programs);
            }
            vo.setScore(dto.getScore());
            return vo;
        }

        return null;
    }

//    private SearchResponseVO.SearchColumnRespVO convertColumn(MultiContentAggregationDTO dto, Long jid) {
//        Long columnId = StringUtils.hasText(dto.getKey()) ? parseLong(dto.getKey()) : null;
//
//        TvColumnDTO columnDTO = tvColumnManager.getById(columnId).orElse(null);
//        if (Objects.nonNull(columnDTO)) {
//            SearchResponseVO.SearchColumnRespVO vo = new SearchResponseVO.SearchColumnRespVO();
//            vo.setId(columnId);
//            vo.setCategory(5);
//            vo.setWords(dto.getTopHits().get(0).getTokens());
//            vo.setTitle(columnDTO.getColumnName());
//            vo.setCoverUrl(columnDTO.getCoverUrl());
//            vo.setScore(dto.getMaxScore());
//            List<MultiContentDTO> topHits = dto.getTopHits();
//            boolean isMore = topHits.size() > 4;
//
//            List<RecentProgramVO> recentPrograms = topHits.parallelStream()
//                    .map(e -> tvProgramManager.getByMediaId(parseLong(e.getCid())).map(this::convert).orElse(null))
//                    .filter(Objects::nonNull)
//                    .limit(3)
//                    .collect(Collectors.toList());
//            vo.setIsMore(isMore);
//            vo.setRecentPrograms(recentPrograms);
//            return vo;
//        }
//
//        return null;
//    }

    private List<RecentProgramVO> getPrograms(List<Long> programIds) {

        return programIds.stream()
                .map(e -> tvProgramManager.getById(e).map(this::convert).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private RecentProgramVO convert(TvProgramDTO e) {
        RecentProgramVO vo = new RecentProgramVO();
        vo.setId(e.getMediaId());
        vo.setProgramName(e.getProgramName());
        vo.setColumnId(e.getColumnId());
        vo.setPlayDate(e.getPlayDate());
        return vo;
    }

    public TvProgramFeedVO convertColumnSearch(ProgramIndexDTO dto) {
        return tvProgramManager.getById(dto.getProgramId()).map(e -> {
            TvColumnDTO tvColumnDTO = tvColumnManager.getById(e.getColumnId()).orElse(null);
            return tvConverter.convert(tvColumnDTO, e);
        }).orElse(null);
    }

    private SearchResponseVO convertNews(NewsIndexDTO dto, Long jid) {
        MediaResourceDTO mediaResourceDTO1 = mediaResourceManager.get(dto.getMediaId()).orElse(null);
        if (Objects.nonNull(mediaResourceDTO1)) {

            if (Objects.equals(ContentType.GAN_YUN_VIDEO.getCode(), mediaResourceDTO1.getContentType())) {
                SearchResponseVO.SearchSingleVideoRespVO singleVideoRespVO = new SearchResponseVO.SearchSingleVideoRespVO();
                singleVideoRespVO.setId(dto.getMediaId());
                singleVideoRespVO.setCategory(3);
                singleVideoRespVO.setWords(dto.getTokens());

                singleVideoRespVO.setTitle(mediaResourceDTO1.getShowName());
                singleVideoRespVO.setAuthor(getAuthor(mediaResourceDTO1.getReleaseId(), mediaResourceDTO1.getReleaseType(), ThreadLocalCache.getJid()));
                singleVideoRespVO.setContent(mediaResourceDTO1.getContent());
                singleVideoRespVO.setCreateDate(mediaResourceDTO1.getCreateDate());
                singleVideoRespVO.setDescription(mediaResourceDTO1.getIntroduction());
                singleVideoRespVO.setShareUrl(ShareUrlUtils.generateShareUrl("media", dto.getMediaId()));

                singleVideoRespVO.setPv(mediaResourceDTO1.getPv() + mediaResourceDTO1.getVirtualPv());
                singleVideoRespVO.setContentLabel(ContentType.getContentLabel(mediaResourceDTO1.getContentType()));
                singleVideoRespVO.setLabels(mediaResourceDTO1.getContentTypeLabelStr());
                singleVideoRespVO.setMediaType(12);
                GanyunVideoVO ganyunVideoVO = JsonUtils.fromJson(mediaResourceDTO1.getPptvCover(), GanyunVideoVO.class);
                if (Objects.nonNull(ganyunVideoVO)) {
                    singleVideoRespVO.setMediaUrls(Collections.singletonList(ganyunVideoVO.getUrl()));
                    singleVideoRespVO.setImageUrls(Collections.singletonList(ganyunVideoVO.getCover()));
                }
                singleVideoRespVO.setScore(dto.getScore());
                return singleVideoRespVO;
            }

        }


        SearchResponseVO.SearchNewsRespVO newsRespVO = new SearchResponseVO.SearchNewsRespVO();
        newsRespVO.setId(dto.getMediaId());
        newsRespVO.setCategory(3);
        newsRespVO.setWords(dto.getTokens());
        mediaResourceManager.get(dto.getMediaId()).ifPresent(mediaResourceDTO -> {

            newsRespVO.setTitle(mediaResourceDTO.getShowName());
            newsRespVO.setAuthor(getAuthor(mediaResourceDTO.getReleaseId(), mediaResourceDTO.getReleaseType(), ThreadLocalCache.getJid()));
            newsRespVO.setContent(mediaResourceDTO.getContent());
            newsRespVO.setCreateDate(mediaResourceDTO.getCreateDate());
            newsRespVO.setDescription(mediaResourceDTO.getIntroduction());
            newsRespVO.setShareUrl(ShareUrlUtils.generateShareUrl("media", dto.getMediaId()));

            newsRespVO.setPv(mediaResourceDTO.getPv() + mediaResourceDTO.getVirtualPv());
            newsRespVO.setContentLabel(ContentType.getContentLabel(mediaResourceDTO.getContentType()));
            newsRespVO.setLabels(mediaResourceDTO.getContentTypeLabelStr());
            newsRespVO.setScore(dto.getScore());

            //  按照不同数据类型，设置参数
            ContentType contentType = ContentType.parse(mediaResourceDTO.getContentType());
            if (Objects.nonNull(contentType)) {
                switch (contentType) {
                    case PIC_FONT:
                        List<ImageVO> imageList = getResizeImageListFrom(mediaResourceDTO.getImages());
                        newsRespVO.setImageUrls(ImageVO.getUrls(imageList));
                        newsRespVO.setImageList(imageList);
                        newsRespVO.setMediaType(9);
                        break;
                    case NEWS:
                        // 文章：11 文章大图/三图 12 文章左文右图 13 外链
                        // playstyle: 1-标题模式、2-左文右图模式、3-三图模式、4-大图模式、5-外链模式
                        if (mediaResourceDTO.getPlayStyle() == PlayStyle.SHORT.getCode()) {
                            newsRespVO.setMediaType(12);
                        } else if (mediaResourceDTO.getPlayStyle() == PlayStyle.OUTER_LINK_MODE.getCode()) {
                            newsRespVO.setMediaType(11);
                            newsRespVO.setLinkTitle(mediaResourceDTO.getShowName());
                            newsRespVO.setLinkUrl(mediaResourceDTO.getContent());
                            newsRespVO.setMediaUrls(Collections.singletonList(mediaResourceDTO.getContent()));
                        } else {
                            newsRespVO.setMediaType(11);
                        }

                        // 设置封面
                        if (!StringUtils.isEmpty(mediaResourceDTO.getPptvCover())) {
                            GanyunCoverVO ganyunInfo = JsonUtils.fromJson(mediaResourceDTO.getPptvCover(), GanyunCoverVO.class);
                            newsRespVO.setImageUrls(ganyunInfo.getImages());
                        }
                        // 如果文章没有封面图，则固定11
                        if (CollectionUtils.isEmpty(newsRespVO.getImageUrls())) {
                            newsRespVO.setMediaType(11);
                        }

                        break;
                    case SPECIAL:
                        if (CollectionUtils.isEmpty(newsRespVO.getImageUrls())) {
                            newsRespVO.setMediaType(11);
                        } else {
                            newsRespVO.setMediaType(12);
                        }
                        newsRespVO.setSpecialId(mediaResourceDTO.getId());
                        break;
                    default:
                        break;
                }
            }

            // 设置来源
            if (!Objects.isNull(mediaResourceDTO.getSource()) && !"0".equals(mediaResourceDTO.getSource())) {
                sourceManager.getSource(Long.valueOf(mediaResourceDTO.getSource())).ifPresent(sourceDTO -> newsRespVO.setSource(sourceDTO.getName()));
            }

        });

        return newsRespVO;
    }


    private SearchResponseVO convertNews(MultiContentDTO dto, Long jid) {
        Long mediaId = dto.getContentId();

        MediaResourceDTO mediaResourceDTO1 = mediaResourceManager.get(mediaId).orElse(null);
        if (Objects.nonNull(mediaResourceDTO1)) {

            if (Objects.equals(ContentType.GAN_YUN_VIDEO.getCode(), mediaResourceDTO1.getContentType())) {
                SearchResponseVO.SearchSingleVideoRespVO singleVideoRespVO = new SearchResponseVO.SearchSingleVideoRespVO();
                singleVideoRespVO.setId(mediaId);
                singleVideoRespVO.setCategory(3);
                singleVideoRespVO.setWords(dto.getTokens());

                singleVideoRespVO.setTitle(mediaResourceDTO1.getShowName());
                singleVideoRespVO.setAuthor(getAuthor(mediaResourceDTO1.getReleaseId(), mediaResourceDTO1.getReleaseType(), ThreadLocalCache.getJid()));
                singleVideoRespVO.setContent(mediaResourceDTO1.getContent());
                singleVideoRespVO.setCreateDate(mediaResourceDTO1.getCreateDate());
                singleVideoRespVO.setDescription(mediaResourceDTO1.getIntroduction());
                singleVideoRespVO.setShareUrl(ShareUrlUtils.generateShareUrl("media", mediaId));

                singleVideoRespVO.setPv(mediaResourceDTO1.getPv() + mediaResourceDTO1.getVirtualPv());
                singleVideoRespVO.setContentLabel(ContentType.getContentLabel(mediaResourceDTO1.getContentType()));
                singleVideoRespVO.setLabels(mediaResourceDTO1.getContentTypeLabelStr());
                singleVideoRespVO.setMediaType(12);
                GanyunVideoVO ganyunVideoVO = JsonUtils.fromJson(mediaResourceDTO1.getPptvCover(), GanyunVideoVO.class);
                if (Objects.nonNull(ganyunVideoVO)) {
                    singleVideoRespVO.setMediaUrls(Collections.singletonList(ganyunVideoVO.getUrl()));
                    singleVideoRespVO.setImageUrls(Collections.singletonList(ganyunVideoVO.getCover()));
                }
                return singleVideoRespVO;
            }

        }


        SearchResponseVO.SearchNewsRespVO newsRespVO = new SearchResponseVO.SearchNewsRespVO();
        newsRespVO.setId(mediaId);
        newsRespVO.setCategory(3);
        newsRespVO.setWords(dto.getTokens());


        mediaResourceManager.get(mediaId).ifPresent(mediaResourceDTO -> {

            newsRespVO.setTitle(mediaResourceDTO.getShowName());
            newsRespVO.setAuthor(getAuthor(mediaResourceDTO.getReleaseId(), mediaResourceDTO.getReleaseType(), ThreadLocalCache.getJid()));
            newsRespVO.setContent(mediaResourceDTO.getContent());
            newsRespVO.setCreateDate(mediaResourceDTO.getCreateDate());
            newsRespVO.setDescription(mediaResourceDTO.getIntroduction());
            newsRespVO.setShareUrl(ShareUrlUtils.generateShareUrl("media", mediaId));

            newsRespVO.setPv(mediaResourceDTO.getPv() + mediaResourceDTO.getVirtualPv());
            newsRespVO.setContentLabel(ContentType.getContentLabel(mediaResourceDTO.getContentType()));
            newsRespVO.setLabels(mediaResourceDTO.getContentTypeLabelStr());

            //  按照不同数据类型，设置参数
            ContentType contentType = ContentType.parse(mediaResourceDTO.getContentType());
            if (Objects.nonNull(contentType)) {
                switch (contentType) {
                    case PIC_FONT:
                        List<ImageVO> imageList = getResizeImageListFrom(mediaResourceDTO.getImages());
                        newsRespVO.setImageUrls(ImageVO.getUrls(imageList));
                        newsRespVO.setImageList(imageList);
                        newsRespVO.setMediaType(9);
                        break;
                    case NEWS:
                        // 文章：11 文章大图/三图 12 文章左文右图 13 外链
                        // playstyle: 1-标题模式、2-左文右图模式、3-三图模式、4-大图模式、5-外链模式
                        if (mediaResourceDTO.getPlayStyle() == PlayStyle.SHORT.getCode()) {
                            newsRespVO.setMediaType(12);
                        } else if (mediaResourceDTO.getPlayStyle() == PlayStyle.OUTER_LINK_MODE.getCode()) {
                            newsRespVO.setMediaType(11);
                            newsRespVO.setLinkTitle(mediaResourceDTO.getShowName());
                            newsRespVO.setLinkUrl(mediaResourceDTO.getContent());
                            newsRespVO.setMediaUrls(Collections.singletonList(mediaResourceDTO.getContent()));
                        } else {
                            newsRespVO.setMediaType(11);
                        }

                        // 设置封面
                        if (!StringUtils.isEmpty(mediaResourceDTO.getPptvCover())) {
                            GanyunCoverVO ganyunInfo = JsonUtils.fromJson(mediaResourceDTO.getPptvCover(), GanyunCoverVO.class);
                            newsRespVO.setImageUrls(ganyunInfo.getImages());
                        }
                        // 如果文章没有封面图，则固定11
                        if (CollectionUtils.isEmpty(newsRespVO.getImageUrls())) {
                            newsRespVO.setMediaType(11);
                        }

                        break;
                    case SPECIAL:
                        if (CollectionUtils.isEmpty(newsRespVO.getImageUrls())) {
                            newsRespVO.setMediaType(11);
                        } else {
                            newsRespVO.setMediaType(12);
                        }
                        newsRespVO.setSpecialId(mediaResourceDTO.getId());
                        break;
                    default:
                        break;
                }
            }

            // 设置来源
            if (!Objects.isNull(mediaResourceDTO.getSource()) && !"0".equals(mediaResourceDTO.getSource())) {
                sourceManager.getSource(Long.valueOf(mediaResourceDTO.getSource())).ifPresent(sourceDTO -> newsRespVO.setSource(sourceDTO.getName()));
            }

        });

        return newsRespVO;
    }

    private SearchResponseVO.SearchGroupRespVO convertGroup(GroupIndexDTO dto, Long jid) {

        if (Objects.nonNull(dto.getGroupId())) {
            SearchResponseVO.SearchGroupRespVO vo = new SearchResponseVO.SearchGroupRespVO();
            vo.setId(dto.getGroupId());
            vo.setCategory(2);
            vo.setWords(dto.getTokens());
            GroupInfoDTO groupInfoDTO = groupManager.getSimpleById(dto.getGroupId());
            if (Objects.nonNull(groupInfoDTO)) {
                String coverUrl = ossManager.getAvatar(groupInfoDTO.getCover()).map(OssDTO::getUrl).orElse("");
                Boolean joined = Objects.nonNull(jid) && groupManageClient.queryByCondition(jid, dto.getGroupId()).map(Objects::nonNull).orElse(false);

                vo.setId(groupInfoDTO.getId());
                vo.setTitle(groupInfoDTO.getName());
                vo.setCoverUrl(coverUrl);
                vo.setIntro(groupInfoDTO.getIntroduction());
                vo.setIsFollow(joined);
                vo.setIsAudio(false);
                vo.setTenantId(groupInfoDTO.getTenantId());
                vo.setTenantName(groupInfoDTO.getTenantName());
                vo.setScore(dto.getScore());
            }
            return vo;
        }

        return null;
    }

    private SearchResponseVO.SearchGroupRespVO convertGroup(MultiContentDTO dto, Long jid) {
        Long groupId = dto.getContentId();
        if (Objects.nonNull(groupId)) {
            SearchResponseVO.SearchGroupRespVO vo = new SearchResponseVO.SearchGroupRespVO();
            vo.setId(groupId);
            vo.setCategory(2);
            vo.setWords(dto.getTokens());
            GroupInfoDTO groupInfoDTO = groupManager.getSimpleById(groupId);
            if (Objects.nonNull(groupInfoDTO)) {

                String coverUrl = ossManager.getAvatar(groupInfoDTO.getCover()).map(OssDTO::getUrl).orElse("");
                Boolean joined = groupManager.isJoined(jid, groupId);

                vo.setId(groupInfoDTO.getId());
                vo.setTitle(groupInfoDTO.getName());
                vo.setCoverUrl(coverUrl);
                vo.setIntro(groupInfoDTO.getIntroduction());
                vo.setIsFollow(joined);
                vo.setIsAudio(false);
                vo.setTenantId(groupInfoDTO.getTenantId());
                vo.setTenantName(groupInfoDTO.getTenantName());
            }
            return vo;
        }

        return null;
    }

    private SearchResponseVO.SearchUserRespVO convertUgc(MultiContentDTO dto, Long jid) {
        Long userId = dto.getContentId();
        List<String> tokens = dto.getTokens();

        //  判断是PGC还是UGC
        ConsumerUserDTO consumerUserDTO = consumerUserManager.getByJid(userId);
        if (Objects.nonNull(consumerUserDTO)) {
            ConsumerAuthenticationDTO consumerAuthentication = consumerUserDTO.getConsumerAuthentication();
            boolean isAuthentication = Objects.nonNull(consumerAuthentication);
            String authenticationIntro = Objects.isNull(consumerAuthentication) ? "" : consumerAuthentication.getIntroduction();

            String avatarUrl = ossManager.getAvatar(consumerUserDTO.getAvatar()).map(OssDTO::getUrl).orElse("");
            Boolean followed = interactClient.isAlreadyFollow(userId, ThreadLocalCache.getJid()).orElse(false);


            SearchResponseVO.SearchUserRespVO vo = new SearchResponseVO.SearchUserRespVO();
            vo.setId(userId);
            vo.setCategory(1);
            vo.setWords(tokens);
            vo.setTitle(consumerUserDTO.getNickname());
            vo.setIntro(consumerUserDTO.getInfo());
            vo.setCoverUrl(avatarUrl);
            vo.setAuthorAvatar(avatarUrl);
            vo.setAuthorName(consumerUserDTO.getNickname());
            vo.setAuthorType(1);
            vo.setIsAuthentication(isAuthentication);
            vo.setAuthenticationIntro(authenticationIntro);
            vo.setIsFollow(followed);
            vo.setIsAudio(false);
            vo.setStatus(Objects.equals(EnableEnum.ENABLE.getCode(), consumerUserDTO.getStatus()));

            return vo;
        }
        return null;
    }

    private SearchResponseVO.SearchUserRespVO convertPgc(MultiContentDTO dto, Long jid) {
        Long userId = dto.getContentId();
        List<String> tokens = dto.getTokens();
        //  判断是PGC还是UGC
        CertificationDTO certificationDTO = certificationManager.getById(userId);
        if (Objects.nonNull(certificationDTO)) {
            String avatarUrl = ossManager.getAvatar(certificationDTO.getAvatar()).map(OssDTO::getUrl).orElse("");
            Boolean followed = Objects.nonNull(jid) && interactClient.isAlreadyFollow(userId, jid).orElse(false);

            SearchResponseVO.SearchUserRespVO vo = new SearchResponseVO.SearchUserRespVO();
            vo.setId(userId);
            vo.setCategory(1);
            vo.setWords(tokens);

            vo.setTitle(certificationDTO.getName());
            vo.setIntro(certificationDTO.getIntroduce());
            vo.setCoverUrl(avatarUrl);
            vo.setAuthorAvatar(avatarUrl);
            vo.setAuthorName(certificationDTO.getName());
            vo.setAuthorType(0);
            vo.setIsFollow(followed);
            vo.setIsAudio(false);
            vo.setStatus(certificationDTO.getStatus());
            vo.setIsAuthentication(true);
            vo.setAuthenticationIntro("");

            return vo;
        }

        return null;
    }

    private SearchResponseVO.SearchUserRespVO convertUser(UserIndexDTO dto, Long jid) {
        Long userId = dto.getUserId();
        Integer userType = dto.getUserType();
        List<String> tokens = dto.getTokens();

        //  判断是PGC还是UGC
        if (Objects.equals(userType, 0)) {
            CertificationDTO certificationDTO = certificationManager.getById(userId);
            if (Objects.nonNull(certificationDTO)) {
                String avatarUrl = ossManager.getAvatar(certificationDTO.getAvatar()).map(OssDTO::getUrl).orElse("");
                Boolean followed = Objects.nonNull(jid) && interactClient.isAlreadyFollow(userId, jid).orElse(false);

                SearchResponseVO.SearchUserRespVO vo = new SearchResponseVO.SearchUserRespVO();
                vo.setId(userId);
                vo.setCategory(1);
                vo.setWords(tokens);

                vo.setTitle(certificationDTO.getName());
                vo.setIntro(certificationDTO.getIntroduce());
                vo.setCoverUrl(avatarUrl);
                vo.setAuthorAvatar(avatarUrl);
                vo.setAuthorName(certificationDTO.getName());
                vo.setAuthorType(0);
                vo.setIsFollow(followed);
                vo.setIsAudio(false);
                vo.setStatus(certificationDTO.getStatus());
                vo.setScore(dto.getScore());
                //  PGC用户统一加V处理
                vo.setIsAuthentication(true);
                vo.setAuthenticationIntro("");

                return vo;
            }
        } else {
            ConsumerUserDTO consumerUserDTO = consumerUserManager.getByJid(userId);
            if (Objects.nonNull(consumerUserDTO)) {
                ConsumerAuthenticationDTO consumerAuthentication = consumerUserDTO.getConsumerAuthentication();
                boolean isAuthentication = Objects.nonNull(consumerAuthentication);
                String authenticationIntro = Objects.isNull(consumerAuthentication) ? "" : consumerAuthentication.getIntroduction();

                String avatarUrl = ossManager.getAvatar(consumerUserDTO.getAvatar()).map(OssDTO::getUrl).orElse("");
                Boolean followed = interactClient.isAlreadyFollow(userId, ThreadLocalCache.getJid()).orElse(false);


                SearchResponseVO.SearchUserRespVO vo = new SearchResponseVO.SearchUserRespVO();
                vo.setId(userId);
                vo.setCategory(1);
                vo.setWords(tokens);
                vo.setTitle(consumerUserDTO.getNickname());
                vo.setIntro(consumerUserDTO.getInfo());
                vo.setCoverUrl(avatarUrl);
                vo.setAuthorAvatar(avatarUrl);
                vo.setAuthorName(consumerUserDTO.getNickname());
                vo.setAuthorType(1);
                vo.setIsAuthentication(isAuthentication);
                vo.setAuthenticationIntro(authenticationIntro);
                vo.setIsFollow(followed);
                vo.setIsAudio(false);
                vo.setStatus(Objects.equals(EnableEnum.ENABLE.getCode(), consumerUserDTO.getStatus()));
                vo.setScore(dto.getScore());

                return vo;
            }
        }
        return null;
    }

    private SearchResponseVO.SearchTriggerServiceRespVO convertTriggerService(AdvertSearchMarketingDTO dto, Long jid) {

        if (Objects.nonNull(dto)) {

            SearchResponseVO.SearchTriggerServiceRespVO vo = new SearchResponseVO.SearchTriggerServiceRespVO();
            vo.setId(dto.getId());
            vo.setCategory(6);
            String[] keywords = dto.getTriggerKeyword().split(" ");
            ArrayList<String> keywordList = new ArrayList<>(Arrays.asList(keywords));
            keywordList.removeIf(StringUtils::isEmpty);
            vo.setWords(keywordList);
            vo.setName(dto.getName());
            ossManager.getOriFile(dto.getBannerOssId()).ifPresent(ossDTO -> vo.setBanner(ossDTO.getUrl()));

            LinkJumpDto linkJumpDto = new LinkJumpDto();
            linkJumpDto.setJumpType(dto.getJumpType());
            linkJumpDto.setJumpUrl(dto.getJumpUrl());
            linkJumpDto.setInnerJumpType(dto.getInnerJumpType());
            linkJumpDto.setJumpMenu(dto.getJumpMenu());
            linkJumpDto.setJumpMediaId(dto.getJumpMediaId());
            linkJumpDto.setJumpMediaType(dto.getJumpMediaType());
            vo.setLinkUrl(linkUrlConverter.convert(linkJumpDto));

            //  低版本兼容
            if (Objects.equals(dto.getJumpType(), 2) && !AppVersionUtils.isSupportAppletUrl(ThreadLocalCache.get().getAppVersion())) {
                vo.setLinkUrl(dto.getExtraUrl());
            }
            return vo;
        }

        return null;
    }

    private void copy(FeedVO source, SearchResponseVO.SearchLiveBroadcastRespVO target) {

        target.setAuthor(source.getAuthor());
        target.setContent(source.getContent());
        target.setCoverUrl(source.getCoverUrl());
        target.setCoverImage(source.getCoverImage());
        target.setCreateDate(source.getCreateDate());
        target.setDescription(source.getDescription());
        target.setId(source.getId());
        target.setIsFavor(source.getIsFavor());
        target.setFavors(source.getFavors());
        target.setIsStick(source.getIsStick());
        target.setMediaType(source.getMediaType());
        target.setMediaStatus(source.getMediaStatus());
        target.setMediaUrls(source.getMediaUrls());
        target.setPendant(source.getPendant());
        target.setCanComment(source.getCanComment());
        target.setIsPublic(source.getIsPublic());
        target.setReviews(source.getReviews());
        target.setShareUrl(source.getShareUrl());
        target.setSourceType(source.getSourceType());
        target.setTitle(source.getTitle());
        target.setShowRecTime(source.getShowRecTime());
        target.setManual(source.isManual());
        target.setTop(source.isTop());
        target.setVodId(source.getVodId());
        target.setLiveBroadcastStatus(source.getLiveBroadcastStatus());
        target.setLiveBroadcastStatusStr(source.getLiveBroadcastStatusStr());
        target.setPlayType(source.getPlayType());
        target.setLiveBroadcastStartTime(source.getLiveBroadcastStartTime());
        target.setImageUrls(source.getImageUrls());
        target.setImageList(source.getImageList());
        target.setOriUrls(source.getOriUrls());
        target.setSoundContent(source.getSoundContent());
        target.setLength(source.getLength());
        target.setSource(source.getSource());
        target.setPv(source.getPv());
        target.setContentLabel(source.getContentLabel());
        target.setGroupInfo(source.getGroupInfo());
        target.setLastPublish(source.getLastPublish());
        target.setCommentId(source.getCommentId());
        target.setAnswerSquareType(source.getAnswerSquareType());
        target.setQuestionVO(source.getQuestionVO());
        target.setLabels(source.getLabels());
        target.setIsPraise(source.getIsPraise());
        target.setPraiseTotal(source.getPraiseTotal());
        target.setOutShareTitle(source.getOutShareTitle());
        target.setOutShareUrl(source.getOutShareUrl());
        target.setPlayTime(source.getPlayTime());
        target.setLinkTitle(source.getLinkTitle());
        target.setLinkUrl(source.getLinkUrl());
        target.setId(source.getId());
        target.setLocation(source.getLocation());
        target.setPlatform(source.getPlatform());
    }

    private void copy(FeedVO source, SearchResponseVO.SearchPostsRespVO target) {

        target.setAuthor(source.getAuthor());
        target.setContent(source.getContent());
        target.setCoverUrl(source.getCoverUrl());
        target.setCoverImage(source.getCoverImage());
        target.setCreateDate(source.getCreateDate());
        target.setDescription(source.getDescription());
        target.setId(source.getId());
        target.setIsFavor(source.getIsFavor());
        target.setFavors(source.getFavors());
        target.setIsStick(source.getIsStick());
        target.setMediaType(source.getMediaType());
        target.setMediaStatus(source.getMediaStatus());
        target.setMediaUrls(source.getMediaUrls());
        target.setPendant(source.getPendant());
        target.setCanComment(source.getCanComment());
        target.setIsPublic(source.getIsPublic());
        target.setReviews(source.getReviews());
        target.setShareUrl(source.getShareUrl());
        target.setSourceType(source.getSourceType());
        target.setTitle(source.getTitle());
        target.setShowRecTime(source.getShowRecTime());
        target.setManual(source.isManual());
        target.setTop(source.isTop());
        target.setVodId(source.getVodId());
        target.setLiveBroadcastStatus(source.getLiveBroadcastStatus());
        target.setLiveBroadcastStatusStr(source.getLiveBroadcastStatusStr());
        target.setPlayType(source.getPlayType());
        target.setLiveBroadcastStartTime(source.getLiveBroadcastStartTime());
        target.setImageUrls(source.getImageUrls());
        target.setImageList(source.getImageList());
        target.setOriUrls(source.getOriUrls());
        target.setSoundContent(source.getSoundContent());
        target.setLength(source.getLength());
        target.setSource(source.getSource());
        target.setPv(source.getPv());
        target.setSpecialId(source.getSpecialId());
        target.setHeadPic(source.getHeadPic());
        target.setDetailBigPic(source.getDetailBigPic());
        target.setDetailSmallPic(source.getDetailSmallPic());
        target.setSpecialTagList(source.getSpecialTagList());
        target.setContentLabel(source.getContentLabel());
        target.setGroupInfo(source.getGroupInfo());
        target.setLastPublish(source.getLastPublish());
        target.setAuthUgcReply(source.getAuthUgcReply());
        target.setQualityComment(source.getQualityComment());
        target.setAnswerSquareId(source.getAnswerSquareId());
        target.setMentorJid(source.getMentorJid());
        target.setCommentId(source.getCommentId());
        target.setAnswerSquareType(source.getAnswerSquareType());
        target.setQuestionVO(source.getQuestionVO());
        target.setLabels(source.getLabels());
        target.setIsPraise(source.getIsPraise());
        target.setPraiseTotal(source.getPraiseTotal());
        target.setOutShareTitle(source.getOutShareTitle());
        target.setOutShareUrl(source.getOutShareUrl());
        target.setPlayTime(source.getPlayTime());
        target.setLinkTitle(source.getLinkTitle());
        target.setLinkUrl(source.getLinkUrl());
        target.setId(source.getId());
        target.setLocation(source.getLocation());
        target.setPlatform(source.getPlatform());
    }

    private AuthorVO getAuthor(Long authorId, Integer releaseType, Long jid) {
        if (authorId == null || authorId < 1) {
            return null;
        }

        if (releaseType.equals(0)) {
            // 0是认证号
            CertificationDTO certification = certificationManager.getById(authorId);
            if (Objects.nonNull(certification)) {
                String avatarUrl = ossManager.getAvatar(certification.getAvatar()).map(OssDTO::getUrl).orElse("");
                Boolean followed = Objects.nonNull(jid) && interactClient.isAlreadyFollow(authorId, jid).orElse(false);

                AuthorVO vo = new AuthorVO();
                vo.setId(authorId);
                vo.setType(0);
                vo.setName(certification.getName());
                vo.setAvatar(avatarUrl);
                vo.setIntro(certification.getIntroduce());
                vo.setIsFollow(followed);
                return vo;
            }
        } else {
            ConsumerUserDTO consumerUser = consumerUserManager.getByJid(authorId);
            if (Objects.nonNull(consumerUser)) {
                // 认证信息
                ConsumerAuthenticationDTO consumerAuthentication = consumerUser.getConsumerAuthentication();
                boolean isAuthentication = Objects.nonNull(consumerAuthentication);
                String authenticationIntro = Objects.isNull(consumerAuthentication) ? "" : consumerAuthentication.getIntroduction();
                String avatarUrl = ossManager.getAvatar(consumerUser.getAvatar()).map(OssDTO::getUrl).orElse("");
                Boolean followed = Objects.nonNull(jid) && interactClient.isAlreadyFollow(authorId, jid).orElse(false);

                AuthorVO vo = new AuthorVO();
                vo.setId(authorId);
                vo.setType(1);
                vo.setName(consumerUser.getNickname());
                vo.setAvatar(avatarUrl);
                vo.setIsFollow(followed);
                vo.setIntro(consumerUser.getInfo());
                vo.setIsAuthentication(isAuthentication);
                vo.setAuthenticationIntro(authenticationIntro);

                return vo;
            }
        }
        return null;
    }

    /**
     * 获取剪切图片列表
     *
     * @param images 图片数据列表
     * @return 剪切后图片列表
     */
    public List<ImageVO> getResizeImageListFrom(List<MediaResourceImageDTO> images) {
        if (!CollectionUtils.isEmpty(images)) {
            List<ImageVO> resizeList = new ArrayList<>(images.size());
            List<String> imageIds = images.stream().map(MediaResourceImageDTO::getOssId).collect(Collectors.toList());
            List<OssDTO> dtoList = ossManager.listResizeByIds(imageIds);
            if (!CollectionUtils.isEmpty(dtoList)) {
                for (OssDTO dto : dtoList) {
                    resizeList.add(ImageVO.buildDTO(dto));
                }
            }

            return resizeList;
        }
        return Collections.emptyList();
    }


}
