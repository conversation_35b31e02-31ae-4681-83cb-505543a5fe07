package com.jxntv.gvideo.app.be.sensors;

import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.GroupExitVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.bean.EventRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 取消关注上报
 *
 * <AUTHOR>
 * @date 2022/1/21
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class SensorsReportGroupUnfollowHandler implements SensorsReportHandler {

    @Resource
    private SensorsAnalytics sensorsAnalytics;
    @Resource
    private GroupTopicClient groupTopicClient;

    @Override
    public boolean support(SensorsReportBO sensorsReportBO) {
        return SensorsType.GROUP_UNFOLLOW.equals(sensorsReportBO.getType())
                && Objects.nonNull(sensorsReportBO.getUserInfo())
                && Objects.nonNull(sensorsReportBO.getUserInfo().getJid());
    }

    @Override
    public void handler(SensorsReportBO sensorsReportBO) {
        UserInfo userInfo = sensorsReportBO.getUserInfo();
        Map<String, Object> params = sensorsReportBO.getParams();
        GroupExitVO vo = (GroupExitVO) params.get("vo");
        Result<?> retVal = (Result<?>) sensorsReportBO.getRetVal();

        GroupInfoDTO groupInfo = groupTopicClient.detail(vo.getGroupId()).orElse(null);
        if (Objects.isNull(groupInfo)) {
            log.info("follow group sensors failed，group info is null");
            return;
        }

        Long jid = userInfo.getJid();
        String distinctId = String.valueOf(jid);
        String account = String.valueOf(jid);
        Long unfollowcommunity_id = groupInfo.getId();
        String unfollowcommunity_name = groupInfo.getName();
        List<String> community_tag = Optional.ofNullable(groupInfo.getLabelsStr()).orElse(Collections.emptyList());
        Long user_institution_id = groupInfo.getTenantId();
        String user_institution = groupInfo.getTenantName();
        boolean is_success = retVal.callSuccess();
        String fail_reason = is_success ? "" : retVal.getMessage();
        long unfollow_time = System.currentTimeMillis();

        try {
            EventRecord eventRecord = EventRecord.builder()
                    .setDistinctId(distinctId)
                    .isLoginId(true)
                    .setEventName("back_UnFollowCommunity")
                    .addProperty("account", account)
                    .addProperty("unfollowcommunity_id", unfollowcommunity_id)
                    .addProperty("unfollowcommunity_name", unfollowcommunity_name)
                    .addProperty("community_tag", community_tag)
                    .addProperty("user_institution_id", user_institution_id)
                    .addProperty("user_institution", user_institution)
                    .addProperty("is_success", is_success)
                    .addProperty("fail_reason", fail_reason)
                    .addProperty("unfollow_time", unfollow_time)
                    .build();


            sensorsAnalytics.track(eventRecord);

            log.info("【埋点上报】上报社区取消关注信息：{}", JsonUtils.toJson(eventRecord));
        } catch (Exception e) {
            log.error("【埋点上报】上报社区取消关注信息异常：{}", e.getLocalizedMessage());
        }

    }

}
