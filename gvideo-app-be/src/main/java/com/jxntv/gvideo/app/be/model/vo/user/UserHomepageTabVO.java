package com.jxntv.gvideo.app.be.model.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("个人主页tab展示对象")
public class UserHomepageTabVO {

    @ApiModelProperty("评论tab是否展示")
    private boolean commentTab;

    @ApiModelProperty("喜欢tab是否展示")
    private boolean favoriteTab;

    @ApiModelProperty("提问tab是否展示")
    private boolean questionTab;

    @ApiModelProperty("回答tab是否展示")
    private boolean answerTab;

    @ApiModelProperty("投稿tab是否展示")
    private boolean contributeTab;

}
