package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.app.be.model.bo.SensorsRankReqBO;
import com.jxntv.gvideo.app.be.model.bo.SensorsRankRspBO;
import com.jxntv.gvideo.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 神策排行榜接口
 * @date 2022/01/18 15:34
 */
@Slf4j
@Component
public class SensorsRankManager {

    private final String TOKEN = "16117dcf8e6c6f40952ef979ad97d1ef3cdc15564fab590ec3995216bc669059";

    private final String GROUP_RANK_URL = "https://sensors.jxgdw.com/api/events/report?token=%s&project=production";

    @Resource
    private RestTemplate restTemplate;


    @Cached(name = "app::sensors::group::rank", cacheType = CacheType.BOTH, expire = 5, timeUnit = TimeUnit.MINUTES)
    public List<String> groupRank(int tab, String fromDate, String toDate, int limit) {
        Set<String> groupSet = new LinkedHashSet<>();

        HttpHeaders headers = new HttpHeaders();
        headers.set("Content-Type", "application/json;charset=utf-8");

        SensorsRankReqBO rankReqBO = getRankReqBO(tab, fromDate, toDate, limit);
        String content = new String(JsonUtils.toJson(rankReqBO).getBytes(StandardCharsets.UTF_8));
        HttpEntity<String> request = new HttpEntity<>(content, headers);
        try {
            ResponseEntity<SensorsRankRspBO> responseEntity = restTemplate.postForEntity(String.format(GROUP_RANK_URL, TOKEN), request, SensorsRankRspBO.class);
            SensorsRankRspBO rankRspBO = responseEntity.getBody();
            List<SensorsRankRspBO.RowBO> rows = Optional.ofNullable(rankRspBO)
                    .map(SensorsRankRspBO::getDetailResult)
                    .map(SensorsRankRspBO.detailBO::getRows)
                    .orElse(Collections.emptyList());

            for (SensorsRankRspBO.RowBO row : rows) {
                if (!CollectionUtils.isEmpty(row.getByFields())) {
                    String groupName = row.getByFields().get(0);
                    if (StringUtils.hasText(groupName)) {
                        groupSet.add(groupName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("SensorsRankManager groupRank error: {}", e.getLocalizedMessage());
        }
        return new ArrayList<>(groupSet);
    }

    private SensorsRankReqBO getRankReqBO(int tab, String fromDate, String toDate, int limit) {
        if (tab == 2) {
            return SensorsRankReqBO.dauRank(fromDate, toDate, limit, true);
        }

        if (tab == 3) {
            return SensorsRankReqBO.postContentRank(fromDate, toDate, limit, true);
        }
        if (tab == 4) {
            return SensorsRankReqBO.fansIncRank(fromDate, toDate, limit, true);
        }

        return SensorsRankReqBO.browsingRank(fromDate, toDate, limit, true);
    }

}
