package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * Created on 2020-01-13
 */
@Data
@ApiModel(value="MainTabVO", description="标签初始化")
public class MainTabVO {

    @ApiModelProperty(value="tabID", required = true, example = "0", notes = "1：推荐 2：视频 3：FM 4：我的")
    private int type;
    @ApiModelProperty(value="tab名称", required = true, example = "视频", notes = "1：推荐 2：视频 3：FM 4：我的")
    private String tabName;
    @ApiModelProperty(value="非选中状态下icon", required = true, example = "http://xxxx.jpg")
    private String imageNormalUrl;
    @ApiModelProperty(value="选中状态下icon", required = true, example = "http://xxxx.jpg")
    private String imageSelectedUrl;
    @ApiModelProperty(value="是否是活动状态", required = true, example = "true")
    private Boolean isActivity;
    @ApiModelProperty(value="是否需要登录", required = true, example = "false")
    private Boolean needLogin;
    @ApiModelProperty(value="页面地址", required = true, example = "http://xxxx")
    private String actUrl;
    @ApiModelProperty(value="切换类型", required = true, example = "0", notes = "0：正常切换 1：侧边滑出")
    private int modalStyle;

}
