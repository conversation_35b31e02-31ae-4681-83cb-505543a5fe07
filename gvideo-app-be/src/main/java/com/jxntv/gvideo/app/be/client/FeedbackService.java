package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.FeedbackClientFallback;
import com.jxntv.gvideo.interact.client.FeedbackClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> Created on 2021/7/27.
 */
@FeignClient(name = "interact-service", contextId = "feedback", fallbackFactory = FeedbackClientFallback.class)
public interface FeedbackService extends FeedbackClient {
}
