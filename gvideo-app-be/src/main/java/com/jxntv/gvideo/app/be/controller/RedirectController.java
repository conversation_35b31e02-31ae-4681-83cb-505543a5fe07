package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 301重定向接口
 * @date 2021/11/03 17:10
 */
@Slf4j
@Controller
@RequestMapping("/api/resource")
@Api(value = "301重定向接口", tags = {"301重定向接口"})
public class RedirectController {
    @Resource
    private OssClient ossClient;

    @GetMapping("/{ossId}")
    public void test(@PathVariable String ossId, HttpServletResponse response) throws IOException {
        log.info("开始301跳转, ossId = {}", ossId);
        OssDTO ossDTO = ossClient.getOriFile(ossId).getResult();
        if (Objects.isNull(ossDTO)) {
            return;
        }
        response.sendRedirect(ossDTO.getUrl());
    }
}
