package com.jxntv.gvideo.app.be.track;

import com.jxntv.gvideo.app.be.client.ConsumerUserChannelClient;
import com.jxntv.gvideo.app.be.common.config.AppConfig;
import com.jxntv.gvideo.app.be.manager.LocationManager;
import com.jxntv.gvideo.app.be.model.vo.UserTokenVO;
import com.jxntv.gvideo.app.be.model.vo.UserVO;
import com.jxntv.gvideo.app.be.model.vo.post.PhoneSmsVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.track.sdk.report.AppTrackReportBO;
import com.jxntv.gvideo.track.sdk.report.AppTrackReportHandler;
import com.jxntv.gvideo.track.sdk.report.AppTrackType;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.UgcVestClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.jxntv.gvideo.user.client.enums.ConsumerUserRegisterOrSignInSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 自建埋点-短信登录上报
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@Slf4j
@Component
public class AppTrackReportSmsLoginHandler implements AppTrackReportHandler {
    @Resource
    private AppTrackAnalyticsService appTrackAnalyticsService;
    @Resource
    private AppConfig appConfig;
    @Resource
    private ConsumerUserClient consumerUserClient;
    @Resource
    private UgcVestClient ugcVestClient;
    @Resource
    private LocationManager locationManager;
    @Resource
    private ConsumerUserChannelClient consumerChannelService;

    @Override
    public boolean support(AppTrackReportBO trackReportBO) {
        return AppTrackType.USER_SMS_LOGIN.equals(trackReportBO.getType());
    }

    @Override
    public void handler(AppTrackReportBO trackReportBO) {
        Map<String, Object> params = trackReportBO.getParams();
        PhoneSmsVO phoneSmsVO = (PhoneSmsVO) params.get("phoneSmsVO");
        UserTokenVO userToken = getUserToken(trackReportBO.getRetVal());
        if (Objects.isNull(userToken)) {
            return;
        }
        UserVO user = userToken.getUser();

        log.info("AppTrackReport USER_SMS_LOGIN  phoneSmsVO.source:{} is",phoneSmsVO.getSource());

        if (user.isNew()) {

            //  提交注册事件
            trackRegisterResult(phoneSmsVO,user, trackReportBO);

            //  上报登录事件
            trackSubmitRegistration(phoneSmsVO, user, true, trackReportBO);

        } else {
            //  上报登录事件
            this.trackSubmitRegistration(phoneSmsVO, user, false, trackReportBO);

        }
    }

    /**
     * 上报用户注册事件
     *
     * @param phoneSmsVO
     * @param user
     * @param reportBO
     */
    private void trackRegisterResult(PhoneSmsVO phoneSmsVO, UserVO user, AppTrackReportBO reportBO) {

        //  账号
        String account = String.valueOf(user.getUserId());
        //  引流渠道
        String yl_channel = getYlChannel(reportBO.getUserInfo().getDeviceId(), user.getPhone());
        //  注册时间
        Long register_time = getRegisterTime(user.getUserId());
        //  昵称
        String nickname = user.getNickname();

        String fail_reason = "";

        String islogin_id = reportBO.getUserInfo().getDeviceId();

        String platform_type = reportBO.getUserInfo().getOs();

        Map<String,Object> eventProp = new HashMap<>();
        eventProp.put("fail_reason", fail_reason);
        eventProp.put("jslogin_id", StringUtils.isEmpty(islogin_id) ? "" : islogin_id);
        eventProp.put("nick_name", StringUtils.isEmpty(nickname)  ? "" : nickname);
        eventProp.put("account", account);
        eventProp.put("platform_type", StringUtils.isEmpty(platform_type) ?  "" : platform_type);
        eventProp.put("is_success", true);
        eventProp.put("is_login", true);
        eventProp.put("register_time", register_time);
        eventProp.put("login_type", StringUtils.isEmpty(user.getLoginType()) ? "" : user.getLoginType());
        eventProp.put("YL_channel", StringUtils.isEmpty(yl_channel) ? "" : yl_channel);
        eventProp.put("app_version", StringUtils.isEmpty(reportBO.getUserInfo().getAppVersion()) ? "" : reportBO.getUserInfo().getAppVersion());
        eventProp.put("jslogin_id", StringUtils.isEmpty(reportBO.getUserInfo().getDeviceId()) ? "" : reportBO.getUserInfo().getDeviceId());
        eventProp.put("register_source", ConsumerUserRegisterOrSignInSource.toStr(phoneSmsVO.getSource()));

        appTrackAnalyticsService.report("register_result",eventProp,reportBO);

    }

    private void trackSubmitRegistration(PhoneSmsVO phoneSmsVO, UserVO userVo, boolean isNew, AppTrackReportBO reportBO) {
        //  账号
        String account = String.valueOf(userVo.getUserId());

        //  是否使用固定验证码登陆
        boolean isSubmitCode = false;
        if (Objects.nonNull(phoneSmsVO) && !StringUtils.isEmpty(phoneSmsVO.getSmsCode())) {
            isSubmitCode = Objects.equals(appConfig.getAdminSmsCode(), phoneSmsVO.getSmsCode());
        }

        Map<String,Object> eventProp = new HashMap<>();
        eventProp.put("account", userVo.getUserId());
        eventProp.put("submit_code", isSubmitCode);
        eventProp.put("submitcode_time", new Date());
        eventProp.put("is_newregister", isNew);
        eventProp.put("login_type", StringUtils.isEmpty(userVo.getLoginType()) ? "" : userVo.getLoginType());
        eventProp.put("is_success", true);
        eventProp.put("jslogin_id", StringUtils.isEmpty(reportBO.getUserInfo().getDeviceId()) ? "" : reportBO.getUserInfo().getDeviceId());
        eventProp.put("register_source", ConsumerUserRegisterOrSignInSource.toStr(phoneSmsVO.getSource()));

        appTrackAnalyticsService.report("submit_registration",eventProp,reportBO);

    }



    private String getYlChannel(String deviceId, String mobile) {
        return consumerUserClient.queryChannelNameByDeviceAndMobile(deviceId, mobile).orElse("");
    }

    /**
     * 获取用户创建时间或者当前时间
     *
     * @param jid
     * @return
     */
    private Long getRegisterTime(Long jid) {
        return consumerUserClient.getBaseUserByJid(jid).map(ConsumerUserDTO::getCreateDate).map(Date::getTime).orElse(System.currentTimeMillis());
    }


    private UserTokenVO getUserToken(Object retVal) {
        if (retVal instanceof Result) {
            Object result = ((Result<?>) retVal).getResult();
            if (result instanceof UserTokenVO) {
                return (UserTokenVO) result;
            }
        }
        return null;
    }

    private boolean isSockpuppet(Long jid) {
        return Objects.nonNull(jid) && ugcVestClient.queryByJid(jid).orElse(false);
    }

}
