package com.jxntv.gvideo.app.be.model.vo;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.dto.GroupUserDTO;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerAuthenticationDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Created on 2021/5/18.
 */
@Data
@ApiModel("圈子成员对象")
public class SimpleGroupUserVO {
    @ApiModelProperty("jid")
    private Long jid;
    @ApiModelProperty("头像")
    private ImageVO avatar;
    @ApiModelProperty("昵称")
    private String nickname;
    @ApiModelProperty("角色类型 1 普通用户 2 管理员 3 圈主 4 超级管理员")
    private Integer roleType;
    @ApiModelProperty(value = "是否认证")
    private Boolean isAuthentication;
    @ApiModelProperty(value = "认证信息")
    private String authenticationIntro;


    public static List<SimpleGroupUserVO> buildList(List<GroupUserDTO> dtoList, OssClient ossService, ConsumerUserClient consumerUserClient) {
        List<SimpleGroupUserVO> results = new ArrayList<>();

        //  聚合所有的用户信息
        List<Long> jidList = dtoList.stream().map(GroupUserDTO::getUserId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ConsumerUserDTO> consumerUserList = consumerUserClient.listUserByJids(jidList).orElse(Collections.emptyList());
        Map<Long, ConsumerUserDTO> userMap = consumerUserList.stream().collect(Collectors.toMap(ConsumerUserDTO::getJid, e -> e, (u1, u2) -> u1));

        //  聚合所有的图片信息
        List<String> imageIds = consumerUserList.stream().map(ConsumerUserDTO::getAvatar).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ImageVO> imageList = ImageVO.convertVO(imageIds, ossService);
        Map<String, ImageVO> imageMap = imageList.stream().collect(Collectors.toMap(ImageVO::getOssId, e -> e, (e1, e2) -> e1));

        for (GroupUserDTO dto : dtoList) {
            SimpleGroupUserVO vo = new SimpleGroupUserVO();
            Long jid = dto.getUserId();
            vo.setJid(jid);
            ConsumerUserDTO consumerUser = userMap.get(jid);
            if (Objects.nonNull(consumerUser)) {
                vo.setNickname(consumerUser.getNickname());
                vo.setAvatar(imageMap.get(consumerUser.getAvatar()));
            }

            vo.setRoleType(dto.getRoleType().getCode());
            //构建用户认证信息
            buildAuth(vo, consumerUserClient);

            results.add(vo);
        }
        return results;
    }


    private static void buildAuth(SimpleGroupUserVO vo, ConsumerUserClient consumerUserClient) {
        Result<ConsumerAuthenticationDTO> dtoResult = consumerUserClient.queryAuthenticationInfo(vo.getJid());
        if (dtoResult.callSuccess()) {
            vo.setIsAuthentication(true);
            vo.setAuthenticationIntro(dtoResult.getResult().getIntroduction());
        } else {
            vo.setIsAuthentication(false);
            vo.setAuthenticationIntro("");
        }
    }
}
