package com.jxntv.gvideo.app.be.model.vo.blind.date.accost;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value="GroupBlindDateAccostPopFlagVO", description="都市放心爱-相亲-招呼弹窗状态")
public class GroupBlindDateAccostPopFlagVO {

    @ApiModelProperty(value="弹窗类型 1-接收 2-回应")
    @NotNull(message = "弹窗类型不能为空")
    private Integer type;

}
