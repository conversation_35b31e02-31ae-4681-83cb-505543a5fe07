package com.jxntv.gvideo.app.be.login.security.handler;

import com.jxntv.gvideo.app.be.login.security.jwt.JwtUtils;
import com.jxntv.gvideo.app.be.utils.IpUtils;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.om.client.LoginLogClient;
import com.jxntv.gvideo.om.dto.LoginLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;

/**
 * Created on 2020-01-08
 */
@Component
@Slf4j
public class MyAuthenticationSuccessHandler implements AuthenticationSuccessHandler {

    @Resource
    private LoginLogClient loginLogClient;
    @Resource
    private JwtUtils jwtUtils;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {

        String username = request.getParameter("username");
        log.info("登录成功");
        log.info("username=>" + username);

        Object principal = authentication.getPrincipal();
        if (principal instanceof User) {
            User userDetail = (User) authentication.getPrincipal();
            //存储认证信息
            SecurityContextHolder.getContext().setAuthentication(authentication);
            //生成token
            String token = jwtUtils.generateToken(userDetail);
            Result<String> result = Result.ok(token);

            String loginResult = JsonUtils.toJson(result);
            String ipAddress = IpUtils.getIpAddress();

            LoginLogDTO dto = new LoginLogDTO();
            dto.setType(1);
            dto.setIp(ipAddress);
            dto.setUsername(username);
            dto.setLoginSuccess(true);
            dto.setLoginResult(loginResult);
            dto.setLoginDate(LocalDateTime.now());
            //  插入日志
            loginLogClient.addLog(dto);

            response.setContentType("application/json;charset=UTF-8");
            response.getWriter().write(loginResult);
        }
    }
}
