package com.jxntv.gvideo.app.be.model.enums;

public enum ViewBlindDateUserPermissionEnum {


    interested_person(1,"你可能感兴趣的人"),

    fans_list(2,"粉丝列表"),

    follow_list(3,"关注列表"),

    my_follow(4,"我关注的人"),

    group_member_out_list(5,"社区成员-外部列表"),

    group_member_all(6,"社区成员-全部"),

    search_user(7,"搜索中的用户"),

    im_p2p_avatar(8,"私聊头像"),

    im_group_avatar(9,"群聊头像");


    private Integer code;
    private String name;

    ViewBlindDateUserPermissionEnum(Integer id, String name) {
        this.code = id;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
