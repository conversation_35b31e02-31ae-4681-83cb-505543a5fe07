package com.jxntv.gvideo.app.be.converter.feed.step10;

import com.jxntv.gvideo.app.be.converter.feed.CommonConverter;
import com.jxntv.gvideo.app.be.converter.feed.FeedHandler;
import com.jxntv.gvideo.app.be.manager.CommentStatisticManager;
import com.jxntv.gvideo.app.be.manager.InteractManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.ImageVO;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.utils.MediaUtils;
import com.jxntv.gvideo.app.be.utils.ShareUrlUtils;
import com.jxntv.gvideo.media.client.dto.MediaResourceDTO;
import com.jxntv.gvideo.media.client.dto.MediaResourceImageDTO;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.MediaResourceImageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 图文动态转换器
 */
@Slf4j
@Order(10)
@Component
@RefreshScope
public class AudioFMConverter extends CommonConverter implements FeedHandler {

    @Resource
    private OssManager ossManager;
    @Resource
    private CommentStatisticManager commentStatisticManager;
    @Resource
    private InteractManager interactManager;

    private boolean support(MediaResourceDTO dto) {
        return Objects.equals(ContentType.AUDIO_FM.getCode(), dto.getContentType());
    }

    @Override
    public boolean handle(FeedContext context) {

        //  跳过不处理的类型
        if (!support(context.getMediaResource())) {
            return true;
        }


        Long jid = context.getJid();
        MediaResourceDTO dto = context.getMediaResource();
        Integer mediaType = Objects.equals(dto.getPlayStyle(), 1) ? 3 : 4;

        FeedVO vo =context.getFeed();
        vo.setId(dto.getId());
        vo.setContentType(dto.getContentType());
        vo.setDataType(dto.getDataType());
        vo.setMediaType(mediaType);
        vo.setMediaStatus(dto.getStatus());
        vo.setAnswerSquareType(1);
        vo.setAnswerSquareId(dto.getAnswerId());
        vo.setLabels(dto.getContentTypeLabelStr());
        vo.setLocation(getLocation(dto));
        vo.setPlatform(dto.getPlatform());
        vo.setIsAnonymous(dto.getIsAnonymous());
        if (Boolean.TRUE.equals(dto.getIsAnonymous())) {
            Date effectEndDate = dto.getEffectEndDate();
            vo.setRemainderEffectTime(effectEndDate.getTime() - System.currentTimeMillis());
        }
        vo.setContent(dto.getContent());

        //  设置封面
        List<MediaResourceImageDTO> images = dto.getImages();
        if (!CollectionUtils.isEmpty(images)) {
            Map<Integer, MediaResourceImageDTO> imageMap = images.stream().collect(Collectors.toMap(MediaResourceImageDTO::getType, e -> e, (e1, e2) -> e2));
            MediaResourceImageDTO coverDTO = imageMap.getOrDefault(MediaResourceImageType.RECOMMENDED_FLOW.getCode(), imageMap.get(MediaResourceImageType.COMMON.getCode()));
            if (Objects.nonNull(coverDTO)) {
                ossManager.getOssFile(coverDTO.getOssId()).ifPresent(ossDTO -> {
                    vo.setCoverUrl(ossDTO.getUrl());
                    vo.setCoverImage(ImageVO.buildDTO(ossDTO));
                });
            }

        }
        vo.setCreateDate(dto.getCreateDate());
        vo.setDescription(dto.getIntroduction());
        //  设置喜欢相关数据
        interactManager.getFavorStatistic(dto.getId(), jid).ifPresent(interactFavorStatisticDTO -> {
            vo.setIsFavor(Boolean.TRUE.equals(interactFavorStatisticDTO.getIsFavor()) ? 1 : 0);
            vo.setFavors(interactFavorStatisticDTO.getFavors());
        });

        vo.setIsStick(1);
        //  设置媒体资源
        if (Objects.nonNull(dto.getMediaFile())) {
            ossManager.getAudio(dto.getMediaFile().getOssId())
                    .ifPresent(audioDTO -> vo.setMediaUrls(Collections.singletonList(audioDTO.getUrl())));
        }

        vo.setCanComment(dto.getIsComment());
        vo.setIsPublic(MediaUtils.isPublic(dto));

        vo.setShareUrl(ShareUrlUtils.generateShareUrl("media", dto.getId()));
        vo.setTitle(this.getTitle(dto));
        vo.setContentLabel(ContentType.getContentLabel(dto.getContentType()));

        //  设置关联社区信息
        vo.setGroupInfo(getGroupInfo(dto, jid));

        //  设置评论总数
        vo.setReviews(commentStatisticManager.getTotalByMediaId(dto.getId(), jid));

        //  设置优质评论
        vo.setQualityComment(getQualityComment(dto));

        return true;
    }

}
