package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.app.be.model.vo.command.CommandVO;
import com.jxntv.gvideo.user.client.dto.ConsumerCommandDTO;
import com.jxntv.gvideo.user.client.enums.ConsumerCommandBusinessType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class CommandConverter {

    public CommandVO convert(ConsumerCommandDTO dto) {
        CommandVO vo = new CommandVO();
        vo.setCommandId(dto.getId());
        vo.setBusinessType(ConsumerCommandBusinessType.toStr(dto.getBusinessType()));
        vo.setBody(convertBody(dto));
        return vo;
    }

    private Map<String,Object> convertBody(ConsumerCommandDTO dto){
        Map<String, Object> body = Objects.isNull(dto.getBody()) ? new HashMap<>() : dto.getBody();
        body.put("commandId", dto.getId());
        body.put("businessType", ConsumerCommandBusinessType.toStr(dto.getBusinessType()));
        body.put("startTime", convertTimestamp(dto.getStartTime()));
        body.put("endTime", convertTimestamp(dto.getEndTime()));
        if (Objects.nonNull(dto.getLogStartTime()) && Objects.nonNull(dto.getLogEndTime())) {
            body.put("logStartTime", convertTimestamp(dto.getLogStartTime()));
            body.put("logEndTime", convertTimestamp(dto.getLogEndTime()));
        }
        return body;
    }

    private long convertTimestamp(LocalDateTime time){
        return Objects.isNull(time)?0: time.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
    }

}
