package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.aliyun.sdk.OssClient;
import com.jxntv.gvideo.aliyun.sdk.dto.NlpDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.client.AdvertSearchPlaceholderService;
import com.jxntv.gvideo.app.be.client.ConsumerUserChannelClient;
import com.jxntv.gvideo.app.be.common.config.AppConfig;
import com.jxntv.gvideo.app.be.config.BehaviorConfig;
import com.jxntv.gvideo.app.be.config.BlindDateConfig;
import com.jxntv.gvideo.app.be.config.JspBeanConfig;
import com.jxntv.gvideo.app.be.config.ShopConfig;
import com.jxntv.gvideo.app.be.converter.AdvertLaunchConverter;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.manager.AppAuditingManager;
import com.jxntv.gvideo.app.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.app.be.model.vo.AgreeVO.VersionUpdate;
import com.jxntv.gvideo.app.be.sensors.Sensors;
import com.jxntv.gvideo.app.be.sensors.SensorsType;
import com.jxntv.gvideo.app.be.utils.AppVersionUtils;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.AESUtil;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.media.client.ImSensitiveConfigClient;
import com.jxntv.gvideo.media.client.SpreadClient;
import com.jxntv.gvideo.media.client.dto.AdvertSearchPlaceholderDTO;
import com.jxntv.gvideo.media.client.dto.ImSensitiveConfigDTO;
import com.jxntv.gvideo.media.client.dto.SpreadDTO;
import com.jxntv.gvideo.media.client.dto.search.SpreadSearchDTO;
import com.jxntv.gvideo.media.client.enums.ImSensitiveType;
import com.jxntv.gvideo.media.client.enums.SpreadStatus;
import com.jxntv.gvideo.om.dto.AppAuditingDTO;
import com.jxntv.gvideo.track.sdk.report.AppTrack;
import com.jxntv.gvideo.track.sdk.report.AppTrackType;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.jxntv.gvideo.user.client.param.QueryRecommendSwitchParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created on 2020-01-11
 */
@RestController
@RequestMapping("/api/init")
@Api(value = "初始化接口", tags = {"InitController"})
@Slf4j
@RefreshScope
public class InitController {

    @Resource
    private AppConfig appConfig;
    @Resource
    private ShopConfig shopConfig;
    @Autowired
    private AdvertSearchPlaceholderService searchPlaceholderService;
    @Autowired
    private ConsumerUserChannelClient channelService;
    @Resource
    private ImSensitiveConfigClient imSensitiveConfigClient;
    @Resource
    private AdvertLaunchConverter advertLaunchConverter;
    @Resource
    private SpreadClient spreadClient;
    @Resource
    private OssClient ossClient;
    @Resource
    private ConsumerUserManager consumerUserManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private BlindDateConfig blindDateConfig;
    @Resource
    private AppAuditingManager appAuditingManager;
    @Resource
    private JspBeanConfig jspBeanConfig;
    @Resource
    private ConsumerUserClient consumerUserClient;
    @Resource
    private BehaviorConfig behaviorConfig;

    @GetMapping("/config")
    @Sensors(type = SensorsType.CONFIG)
    @AppTrack(type = AppTrackType.CONFIG)
    @ApiOperation(value = "初始化获取配置接口", notes = "H5链接和版本号等")
    public Result<AgreeVO> getConfig() {
        UserInfo userInfo = ThreadLocalCache.get();
        String os = userInfo.getOs();
        // 目前安卓下载地址会根据该字段区分
        String channelType = userInfo.getChannelType();
        String appVersion = userInfo.getAppVersion();
        AgreeVO vo = new AgreeVO();
        vo.setSelectVideoMaxTime(appConfig.getSelectVideoMaxTime());
        vo.setUserPolicy(appConfig.getH5().getUser());
        vo.setLawPolicy(appConfig.getH5().getLaw());
        vo.setPrivacyPolicy(appConfig.getH5().getPrivacy());
        vo.setSdkPrivacyPolicy(appConfig.getH5().getSdkPrivacy());
        vo.setFacePrivacyPolicy(appConfig.getH5().getFacePrivacy());
        vo.setCancelPolicy(appConfig.getH5().getCancel());
        vo.setFeedback(appConfig.getH5().getFeedback());
        vo.setSettle(appConfig.getH5().getSettle());
        vo.setReport(appConfig.getH5().getReport());
        vo.setShareSwitch(CollectionUtils.isEmpty(appConfig.getShareSwitch()) ? Collections.emptyMap() : appConfig.getShareSwitch());
        vo.setQrcodeUrl(appConfig.getQrcodeUrl());
        vo.setImTimes(appConfig.getImTimes());
        vo.setThemeColorSwitch(appConfig.getTheme().getColorSwitch());
        vo.setThemeColorType(appConfig.getTheme().getColorType());
        vo.setSxPvfactor(appConfig.getSxPvfactor());
        vo.setShowLocalTv(appConfig.getShowLocalTv());
        vo.setShowLocalBroadcast(appConfig.getShowLocalBroadcast());
        // 如果是过年主题，只在5.06.06及以上版本才生效
        if (2 == vo.getThemeColorType() && appVersion.compareTo("5.06.06") < 0) {
            vo.setThemeColorType(0);
        }
        if (AppVersionUtils.isSupportNewLoginSDK(appVersion)) {
            vo.setQuickLoginKey(AESUtil.AESCBCEncode("android".equalsIgnoreCase(os) ? appConfig.getAndroid().getQuickLoginNewKey() : appConfig.getIos().getQuickLoginNewKey()));
        } else {
            vo.setQuickLoginKey(AESUtil.AESCBCEncode("android".equalsIgnoreCase(os) ? appConfig.getAndroid().getQuickLoginKey() : appConfig.getIos().getQuickLoginKey()));
        }
        vo.setMatchMakerQrcodeUrl(blindDateConfig.getMatchMaker().getQrcodeOssUrl());
        vo.setMatchMakerWechatUrl(blindDateConfig.getMatchMaker().getWechatOssUrl());
        vo.setMatchMakerJid(blindDateConfig.getMatchMaker().getJid());
        if (Objects.nonNull(vo.getMatchMakerJid())) {
            ConsumerUserDTO consumerUserDTO = consumerUserManager.getByJid(vo.getMatchMakerJid());
            if (Objects.nonNull(consumerUserDTO) && StringUtils.hasText(consumerUserDTO.getAvatar())) {
                OssDTO ossDTO = ossManager.getAvatar(consumerUserDTO.getAvatar()).orElse(null);
                vo.setMatchMakerAvatar(Objects.isNull(ossDTO) ? "" : ossDTO.getUrl());
            }
        }

        vo.setMedalDetailUrl(appConfig.getH5().getMedalDetail());
        vo.setActivityDetailUrl(appConfig.getH5().getActivityDetail());

        //转发社区白名单
        vo.setRelayGroupIds(behaviorConfig.getRelay().getGroupIds());

        //  设置商城相关配置
        AgreeVO.Shop shop = new AgreeVO.Shop();
        shop.setShopWhiteList(shopConfig.getShopWhiteList());
        shop.setMyOrderListUrl(shopConfig.getMyOrderListUrl());
        shop.setShowOrderEntry(shopConfig.getShowOrderEntry());
        shop.setShowCouponEntry(shopConfig.getShowCouponEntry());
        shop.setMyCouponUrl(shopConfig.getMyCouponUrl());

        vo.setShop(shop);

        //  都市放心爱相关配置
        AgreeVO.BlindDate blindDate = new AgreeVO.BlindDate();
        blindDate.setGroupId(blindDateConfig.getGroupId());
        blindDate.setGroupName(blindDateConfig.getGroupName());
        blindDate.setImGroupOwnerIds(blindDateConfig.getImGroupOwnerIds());
        vo.setBlindDate(blindDate);

        //  设置自建埋点相关配置
        AgreeVO.Track track = new AgreeVO.Track();
        if (AppVersionUtils.isAppTrackErrorVersion(userInfo)) {
            track.setReportEnable(false);
        } else {
            track.setReportEnable(appConfig.getTrack().getReportEnable());
        }
        vo.setTrack(track);

        //  设置验证相关配置
        AgreeVO.Captcha captcha = new AgreeVO.Captcha();
        captcha.setGeeEnable(appConfig.getCaptcha().getGeeEnable());
        vo.setCaptcha(captcha);

        //  AI相关配置
        AgreeVO.Ai ai = new AgreeVO.Ai();
        ai.setEnable(appConfig.getAi().getEnable());
        ai.setUrl(appConfig.getAi().getUrl());
        ai.setNoteEnable(appConfig.getAi().getNoteEnable());
        ai.setNoteUrl(appConfig.getAi().getNoteUrl());
        vo.setAi(ai);


        vo.setWatchTaskEnable(jspBeanConfig.getWatchTaskEnable());
        if (Boolean.TRUE.equals(vo.getWatchTaskEnable())) {
            AgreeVO.WatchTask watchTask = new AgreeVO.WatchTask();
            watchTask.setPopTimes(jspBeanConfig.getWatch().getPopTimes());
            watchTask.setPopInterval(jspBeanConfig.getWatch().getPopInterval());
            watchTask.setReportInterval(jspBeanConfig.getWatch().getReportInterval());
            watchTask.setToastIgnoreColumnIds(jspBeanConfig.getWatch().getToastIgnoreColumnIds());
            watchTask.setIconEnable(jspBeanConfig.getWatch().getIconEnable());
            watchTask.setSoundEnable(jspBeanConfig.getWatch().getSoundEnable());
            vo.setWatchTask(watchTask);
        }

        // 设置搜索占位符
        this.setSearchPlaceholder(vo);
        AgreeVO.InitConfig config = new AgreeVO.InitConfig();
        Result<NlpDTO> tokenRst = ossClient.getToken();
        if (tokenRst.callSuccess()) {
            NlpDTO nlpDTO = tokenRst.getResult();
            config.setAliToken(nlpDTO.getToken());
            config.setNlpAppKey(nlpDTO.getAppKey());
        }
        vo.setInitConfig(config);

        if (StringUtils.isEmpty(os) || StringUtils.isEmpty(appVersion)) {
            return Result.ok(vo);
        }
        vo.setAppStoreAppId(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getAppStoreAppId() : appConfig.getIos().getAppStoreAppId());
        Integer needForceUpdate = needForceUpdate(os, appVersion);
        if (!needForceUpdate.equals(0)) {
            VersionUpdate versionUpdate = new VersionUpdate();
            versionUpdate.setVersion(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getCurrentVersion() : appConfig.getIos().getCurrentVersion());
            versionUpdate.setForceUpdate(needForceUpdate.equals(2));
            versionUpdate.setDate(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getDate() : appConfig.getIos().getDate());
            versionUpdate.setBundleId(appConfig.getBundleId());
            versionUpdate.setReleaseNotes(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getReleaseNotes() : appConfig.getIos().getReleaseNotes());
            versionUpdate.setOfficialSource(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getOfficialSource() : appConfig.getIos().getOfficialSource());
            versionUpdate.setDownloadUrl(os.equalsIgnoreCase("android") ? appConfig.getAndroid().getDownloadUrl() : appConfig.getIos().getAppStoreUrl());

            vo.setVersionUpdate(versionUpdate);
        }

        // 新增配置
        vo.setSignEnable(appConfig.getSignEnable());
        vo.setSignLinkUrl(appConfig.getSignLinkUrl());
        vo.setUniversalLinkEnable(appConfig.getUniversalLinkEnable());
        vo.setUniversalLink(appConfig.getUniversalLink());

        //  当前APP是否正在审核
        AppAuditingDTO auditing = appAuditingManager.getAuditing(userInfo.getOs(), userInfo.getAppVersion());
        if (Objects.nonNull(auditing)) {
            vo.setAppAuditing(Objects.equals(auditing.getStatus(), 1));
        } else {
            vo.setAppAuditing(false);
        }

        //  个性化推荐开关
        QueryRecommendSwitchParam queryRecommendSwitchParam = new QueryRecommendSwitchParam();
        queryRecommendSwitchParam.setDeviceId(userInfo.getDeviceId());
        Integer recommendSwitch = consumerUserClient.queryRecommendSwitch(queryRecommendSwitchParam).orElse(1);
        vo.setRecommendSwitch(recommendSwitch);

        log.info("app init config {}", JsonUtils.toJson(vo));
        return Result.ok(vo);
    }

    /**
     * 判断是否要强制升级，如果要则返回2，可升可不升返回1，已是最新版返回0
     */
    private Integer needForceUpdate(String os, String appVersion) {
        if (os.equalsIgnoreCase("android")) {
            AppConfig.Android android = appConfig.getAndroid();
            if (appVersion.equals(android.getCurrentVersion())) {
                return 0;
            }
            // 比较用户当前版本和最低版本
            if (compareAppVersion(appVersion, android.getLowestVersion()) < 0) {
                // 需要强制升级的
                return 2;
            }
            return 1;
        } else if (os.equalsIgnoreCase("ios")) {
            AppConfig.Ios ios = appConfig.getIos();

            if (appVersion.equals(ios.getCurrentVersion())) {
                return 0;
            }
            // 比较用户当前版本和最低版本
            if (compareAppVersion(appVersion, ios.getLowestVersion()) < 0) {
                // 需要强制升级的
                return 2;
            }
            return 1;
        }
        return 0;
    }

    @GetMapping("/tabs")
    @ApiOperation(value = "初始化APP底部标签", notes = "一期是首页/视频/FM/我的")
    public Result<List<MainTabVO>> getTabs() {
        List<MainTabVO> mainTabVOS = new ArrayList<>();
        return Result.ok(mainTabVOS);
    }

    @GetMapping("/tabs/switch")
    @ApiOperation(value = "APP底部标签开关", notes = "临时方案，只返回开关")
    public Result<MainTabSwitchTempVo> getTabSwitchTemp() {
        MainTabSwitchTempVo vo = new MainTabSwitchTempVo();
        vo.setTabSwitch(appConfig.getTab().getSwitch());
        vo.setTabType(appConfig.getTab().getType());
        UserInfo userInfo = ThreadLocalCache.get();
        if (Objects.nonNull(userInfo)) {
            String os = userInfo.getOs();
            String appVersion = userInfo.getAppVersion();
            // 5.06.06 以下不开节日主题
            if (1 == vo.getTabType() && appVersion.compareTo("5.06.06") < 0) {
                vo.setTabType(0);
            }
        }
        return Result.ok(vo);
    }

    @GetMapping("/advertise/second")
    @ApiOperation(value = "推广位置弹出等待时常", notes = "单位是秒，用户观看视频几秒后加载推广位置")
    public Result<Integer> getAdvertSec() {
        Integer second = 5;
        return Result.ok(second);
    }

    @GetMapping("/advertise/open")
    @ApiOperation(value = "开屏广告", notes = "开屏广告传入屏幕长款参数，返回开屏广告列表")
    public Result<List<AdvertLaunchVO>> getAdvertOpen() {
        SpreadSearchDTO dto = new SpreadSearchDTO();
        dto.setStatus(SpreadStatus.ING.getCode());
        dto.setCurrent(1);
        dto.setSize(10000);
        PageDTO<SpreadDTO> pageDTO = spreadClient.page(dto).getResult();
        if (CollectionUtils.isEmpty(pageDTO.getList())) {
            return Result.ok(Collections.emptyList());
        }
        List<AdvertLaunchVO> collect = pageDTO.getList().stream().map(advertLaunchConverter::convert).collect(Collectors.toList());

        //  如果版本正在审核，移除短剧的tab展示
        UserInfo userInfo = ThreadLocalCache.get();
        AppAuditingDTO auditing = appAuditingManager.getAuditing(userInfo.getOs(), userInfo.getAppVersion());
        if (Objects.nonNull(auditing) && Objects.equals(auditing.getStatus(), 1)) {
            collect.removeIf(e -> Objects.nonNull(e.getActUrl())&&e.getActUrl().contains("hitSeries"));
        }
        return Result.ok(collect);
    }

    @GetMapping("/im/sensitive/config")
    @ApiOperation(value = "敏感词规则配置")
    public Result<SensitiveConfigVO> getSensitiveConfig() {
        List<ImSensitiveConfigDTO> configDTOList = imSensitiveConfigClient.getAll().orElse(Collections.emptyList());
        if (CollectionUtils.isEmpty(configDTOList)) {
            return Result.fail("敏感词配置为空");
        }

        SensitiveConfigVO vo = new SensitiveConfigVO();
        List<ImSensitiveConfigDTO> prologueList = configDTOList.stream().filter(e -> Objects.equals(ImSensitiveType.prologue.getCode(), e.getType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(prologueList)) {
            vo.setWarnings(prologueList.get(0).getWarnings());
        }

        List<ImSensitiveConfigDTO> collect = configDTOList.stream().filter(e -> !Objects.equals(ImSensitiveType.prologue.getCode(), e.getType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            List<SensitiveConfigVO.Config> configs = collect.stream().map(e -> {
                ImSensitiveType sensitiveType = ImSensitiveType.parse(e.getType());
                SensitiveConfigVO.Config config = new SensitiveConfigVO.Config();
                if (Objects.nonNull(sensitiveType)) {
                    config.setType(sensitiveType.getDesc());
                }
                config.setWarnings(e.getWarnings());
                if (Objects.nonNull(e.getContent())) {
                    config.setContent(JsonUtils.fromJsonToList(e.getContent(), String.class));
                }
                return config;

            }).collect(Collectors.toList());

            vo.setConfigs(configs);
        }


        return Result.ok(vo);
    }


    /**
     * 比较APP版本号的大小
     * <p>
     * 1、前者大则返回一个正数
     * 2、后者大返回一个负数
     * 3、相等则返回0
     *
     * @param version1 app版本号
     * @param version2 app版本号
     * @return int
     */
    public static int compareAppVersion(String version1, String version2) {
        if (version1 == null || version2 == null) {
            throw new RuntimeException("版本号不能为空");
        }
        // 注意此处为正则匹配，不能用.
        String[] versionArray1 = version1.split("\\.");
        String[] versionArray2 = version2.split("\\.");
        int idx = 0;
        // 取数组最小长度值
        int minLength = Math.min(versionArray1.length, versionArray2.length);
        int diff = 0;
        // 先比较长度，再比较字符
        while (idx < minLength
                && (diff = versionArray1[idx].length() - versionArray2[idx].length()) == 0
                && (diff = versionArray1[idx].compareTo(versionArray2[idx])) == 0) {
            ++idx;
        }
        // 如果已经分出大小，则直接返回，如果未分出大小，则再比较位数，有子版本的为大
        diff = (diff != 0) ? diff : versionArray1.length - versionArray2.length;
        return diff;
    }

    /**
     * 设置搜索占位符
     *
     * @param vo
     */
    private void setSearchPlaceholder(AgreeVO vo) {
        String deviceId = ThreadLocalCache.get().getDeviceId();
        Long openinstallId = null;
        if (!StringUtils.isEmpty(deviceId)) {
            ConsumerChannelDTO channelDTO = channelService.getNewestChannel(deviceId).getResult();
            if (Objects.nonNull(channelDTO)) {
                openinstallId = Long.valueOf(channelDTO.getChannelId());
            }
        }
        List<AdvertSearchPlaceholderDTO> placeholderList = searchPlaceholderService.listAllByOpeninstallId(openinstallId).orElse(Collections.emptyList());
        vo.setPlaceholderList(placeholderList.stream().map(dto -> {
            AdvertSearchPlaceholderVO placeholderVO = new AdvertSearchPlaceholderVO();
            if (!CollectionUtils.isEmpty(dto.getPlaceholder())) {
                placeholderVO.setNewPlaceholder(dto.getPlaceholder());
                placeholderVO.setPlaceholder(dto.getPlaceholder().get(0).getPlaceholder());
            }
            placeholderVO.setStartDate(Date.from(dto.getStartDate().atZone(ZoneId.systemDefault()).toInstant()));
            placeholderVO.setEndDate(Date.from(dto.getEndDate().atZone(ZoneId.systemDefault()).toInstant()));
            return placeholderVO;
        }).collect(Collectors.toList()));
    }

}
