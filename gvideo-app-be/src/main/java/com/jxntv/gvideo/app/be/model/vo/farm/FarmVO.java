package com.jxntv.gvideo.app.be.model.vo.farm;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("农场对象")
public class FarmVO {

    @ApiModelProperty("农场标题")
    private String title;

    @ApiModelProperty("鸡蛋奖品数量")
    private Integer eggsPrizeQuantity;

    @ApiModelProperty("兑换一次鸡蛋奖励所需鸡蛋数量")
    private Integer eggsCost;

    @ApiModelProperty("农场主信息")
    private FarmerVO farmer;

    @ApiModelProperty("农场小鸡信息")
    private ChickenVO chicken;
}
