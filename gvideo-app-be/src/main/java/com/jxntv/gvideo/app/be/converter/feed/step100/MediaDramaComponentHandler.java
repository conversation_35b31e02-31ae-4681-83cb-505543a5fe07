package com.jxntv.gvideo.app.be.converter.feed.step100;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.app.be.converter.feed.FeedHandler;
import com.jxntv.gvideo.app.be.manager.DramaProgramManager;
import com.jxntv.gvideo.app.be.manager.OssManager;
import com.jxntv.gvideo.app.be.model.enums.FeedComponentType;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedContext;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.feed.component.FeedComponent;
import com.jxntv.gvideo.app.be.model.vo.feed.component.MediaDramaComponentInfo;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.media.client.DramaOrderClient;
import com.jxntv.gvideo.media.client.dto.DramaColumnDTO;
import com.jxntv.gvideo.media.client.dto.DramaOrderDTO;
import com.jxntv.gvideo.media.client.dto.DramaOrderSearchDTO;
import com.jxntv.gvideo.media.client.dto.drama.DramaProgramDTO;
import com.jxntv.gvideo.media.client.enums.DataType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 给剧集添加付费信息
 */
@Slf4j
@Order(100)
@Component
public class MediaDramaComponentHandler implements FeedHandler {

    @Resource
    private DramaProgramManager dramaProgramManager;
    @Resource
    private OssManager ossManager;
    @Resource
    private DramaOrderClient dramaOrderClient;


    @Override
    public boolean handle(FeedContext context) {
        Long mediaId = context.getId();
        Long userId = context.getJid();
        FeedVO feed = context.getFeed();
        if (DataType.DRAMA.getCode().equals(feed.getDataType())) {
            DramaProgramDTO programDTO = dramaProgramManager.getByMediaId(mediaId);

            if (Objects.nonNull(programDTO)) {

                List<DramaProgramDTO> programList = dramaProgramManager.listByColumnId(programDTO.getColumnId());
                int updateNum = programList.stream().mapToInt(DramaProgramDTO::getEpisode).max().orElse(1);

                String coverUrl = ossManager.getOssFile(programDTO.getCoverId()).map(OssDTO::getUrl).orElse("");

                MediaDramaComponentInfo info = new MediaDramaComponentInfo();
                info.setUpdatedNum(updateNum);
                info.setSeason(programDTO.getSeason());
                info.setEpisode(programDTO.getEpisode());
                info.setPrice(programDTO.getPrice());
                info.setJspBean(programDTO.getJspBean());
                info.setName(programDTO.getName());
                info.setCoverUrl(coverUrl);
                info.setColumnId(programDTO.getColumnId());

                DramaColumnDTO columnDTO = dramaProgramManager.getColumnById(programDTO.getColumnId());
                if (Objects.nonNull(columnDTO)) {
                    info.setColumnName(columnDTO.getColumnName());
                    info.setColumnCategory(columnDTO.getCategory());
                    info.setColumnStyle(columnDTO.getStyle());
                    info.setTotalNum(columnDTO.getDramaTotalNum());
                    info.setJspBeanPackage(columnDTO.getJspBean());
                    //  查看是否需要付费
                    if ((Objects.nonNull(info.getPrice()) && info.getPrice().compareTo(BigDecimal.ZERO) > 0) 
                        || (Objects.nonNull(info.getJspBean()) && info.getJspBean() > 0)) {
                        boolean isPay = isPay(columnDTO.getId(), programDTO.getEpisode(), userId);
                        info.setIsPay(isPay);

                        if (isPay(columnDTO.getId(), programDTO.getEpisode(), userId)) {
                            info.setIsPay(true);
                        } else {
                            info.setIsPay(false);
                            //  未支付的情况需要清理视频链接
                            feed.setMediaUrls(Collections.emptyList());
                        }

                    }

                }

                FeedComponent<MediaDramaComponentInfo> component = FeedComponent.of(FeedComponentType.MEDIA_DRAMA, info);

                feed.addComponent(component);
            }

        }

        return true;
    }

    /**
     * 检查用户是否支付该剧集
     *
     * @param columnId 栏目ID
     * @param episode  集数
     * @param userId   当前用户id
     * @return 是否支付该集数
     */
    private boolean isPay(Long columnId, Integer episode, Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }

        DramaOrderSearchDTO dramaOrderSearchDTO = new DramaOrderSearchDTO();
        dramaOrderSearchDTO.setColumnId(columnId);
        dramaOrderSearchDTO.setUserId(userId);
        dramaOrderSearchDTO.setCurrent(1);
        dramaOrderSearchDTO.setSize(100);
        List<DramaOrderDTO> dtoList = dramaOrderClient.page(dramaOrderSearchDTO).map(PageDTO::getList).orElse(Collections.emptyList());

        return dtoList.stream().anyMatch(e -> e.getEpisode().contains(String.valueOf(episode)) && Objects.equals(1, e.getOrderState()));
    }


}
