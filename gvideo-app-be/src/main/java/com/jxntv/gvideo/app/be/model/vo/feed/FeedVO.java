package com.jxntv.gvideo.app.be.model.vo.feed;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jxntv.gvideo.app.be.model.vo.*;
import com.jxntv.gvideo.app.be.model.vo.feed.component.FeedComponent;
import com.jxntv.gvideo.app.be.model.vo.shop.GoodsVO;
import com.jxntv.gvideo.media.client.dto.MediaResourceLocationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * Created on 2020-03-09
 */
@Data
@ApiModel(value = "FeedVO")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeedVO {
    @ApiModelProperty(value = "作者", required = true)
    private AuthorVO author;
    @ApiModelProperty(value = "内容")
    private String content;
    @ApiModelProperty(value = "资源封面", required = true, example = "http://xxx.jpg")
    private String coverUrl;
    private ImageVO coverImage;
    @ApiModelProperty(value = "创建时间", required = true, example = "http://xxx.jpg")
    private Date createDate;
    @ApiModelProperty(value = "资源描述", required = true, example = "贝加尔湖描述")
    private String description;
    @ApiModelProperty(value = "资源ID", required = true, example = "1234567")
    private Long id;
    @ApiModelProperty(value = "栏目ID", example = "1")
    private Long columnId;
    @ApiModelProperty(value = "是否收藏 0否 1是", required = true)
    private Integer isFavor;
    @ApiModelProperty(value = "喜欢总数", required = true)
    private Integer favors;
    @ApiModelProperty(value = "是否影藏点赞", required = true)
    private Boolean hidFavor;
    @ApiModelProperty(value = "置顶 0否 1是")
    private Integer isStick;
    @ApiModelProperty(value = "资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 17 横向滚动块 23 自建频道直播", required = true, example = "1", notes = "1视频2FM")
    private Integer mediaType;
    private Integer showType;
    @ApiModelProperty(value = "媒体资源状态:媒体资源状态 1 待修改 2 待提交 3 启用 4 禁用 5 审核中 6 已废弃")
    private Integer mediaStatus;
    @ApiModelProperty(value = "资源链接集合", required = true)
    private List<String> mediaUrls;
    @ApiModelProperty(value = "挂件")
    private PendantVO pendant;
    @ApiModelProperty(value = "资源是否允许评论")
    private Boolean canComment;
    @ApiModelProperty(value = "资源1公开0")
    private Integer isPublic;
    @ApiModelProperty(value = "资源评论数量", required = true, example = "100000")
    private Integer reviews;
    @ApiModelProperty(value = "分享链接", required = true)
    private String shareUrl;
    @ApiModelProperty(value = "原始数据运营配置标识", required = true, example = "0", notes = "0原始1运营位")
    private Integer sourceType;
    @ApiModelProperty(value = "资源标题", required = true, example = "贝加尔湖")
    private String title;
    //    @ApiModelProperty(value = "标签类型 1预告 2广播 3直播 4音频")
//    private Integer tagType;
    @ApiModelProperty(value = "展开推荐位时间 0直接展开")
    private Integer showRecTime;
    @ApiModelProperty(value = "是否人工干预")
    private boolean isManual;
    @ApiModelProperty(value = "是否置顶")
    private boolean isTop;
    @ApiModelProperty(value = "PPTV唯一资源标识")
    private String vodId;
    @ApiModelProperty(value = "直播开播人ID")
    private Long liveBroadcastAnchorId;
    @ApiModelProperty(value = "直播状态码 0 未开始 1 预告 2 直播中 3 回放 4 下架")
    private Integer liveBroadcastStatus;
    @ApiModelProperty(value = "直播状态 0 未开始 1 预告 2 直播中 3 回放 4 下架")
    private String liveBroadcastStatusStr;
    @ApiModelProperty(value = "直播样式 1 活动直播 2 互动直播 3 自建频道直播")
    private Integer playType;
    @ApiModelProperty(value = "直播开始时间")
    private Date liveBroadcastStartTime;
    @ApiModelProperty(value = "缩略图片链接")
    private List<String> imageUrls;
    @ApiModelProperty(value = "缩略图片链接")
    private List<ImageVO> imageList;
    @ApiModelProperty(value = "源图片链接")
    private List<String> oriUrls;
    @ApiModelProperty(value = "语音文字")
    private String soundContent;
    @ApiModelProperty(value = "媒体长度")
    private String length;
    @ApiModelProperty(value = "来源")
    private String source;
    @ApiModelProperty(value = "PV")
    private Integer pv;
    @ApiModelProperty(value = "专题ID：当specialId = mediaId时，当前资源为专题列表，需要进入专题列表页，否则资源详情页")
    private Long specialId;
    @ApiModelProperty(value = "专题头图")
    private String headPic;
    @ApiModelProperty(value = "详情页大图")
    private String detailBigPic;
    @ApiModelProperty(value = "详情页小图")
    private String detailSmallPic;
    @ApiModelProperty(value = "专题栏目")
    private List<SpecialTagVO> specialTagList;
    @ApiModelProperty(value = "内容标签")
    private String contentLabel;
    @ApiModelProperty("圈子关联信息对象")
    private GroupRelatedVO groupInfo;
    @ApiModelProperty("最后审核通过时间")
    private Long lastPublish;
    @ApiModelProperty("认证用户回复")
    private AuthUgcReplyVo authUgcReply;
    @ApiModelProperty("优质回复内容")
    private CommentVO qualityComment;
    @ApiModelProperty("问题导师Jid")
    private Long mentorJid;
    @ApiModelProperty("评论主键id")
    private Long commentId;
    @ApiModelProperty("问答id")
    private Long answerSquareId;
    @ApiModelProperty("1问2答")
    private Integer answerSquareType;
    @ApiModelProperty("提问对象")
    private FeedQuestionVO questionVO;
    @ApiModelProperty("增加返回标签")
    private List<String> labels;
    @ApiModelProperty(value = "是否点赞")
    private Boolean isPraise;
    @ApiModelProperty(value = "点赞数量")
    private Integer praiseTotal;
    @ApiModelProperty(value = "外部分享链接标题")
    private String outShareTitle;
    @ApiModelProperty(value = "外部分享链接")
    private String outShareUrl;
    @ApiModelProperty("商品信息")
    private GoodsVO goods;
    @ApiModelProperty("播出时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime playTime;
    @ApiModelProperty("H5页面标题")
    private String linkTitle;
    @ApiModelProperty("跳转H5链接")
    private String linkUrl;
    @ApiModelProperty("定位信息")
    private MediaResourceLocationDTO location;
    @ApiModelProperty("爆料信息")
    private BrokeTheNewsVO brokeTheNews;
    @ApiModelProperty(value = "资源类型：0-默认资源、1-提问，2-回答，3-爆料")
    private Integer dataType;
    @ApiModelProperty(value = "内容类型：1-视频自制内容、2-视频电影、3-视频节目、4-音频fm、5-视频剧集、6-活动直播、7-互动直播、9-图文、10-音频、11-赣云节目、12-新闻、13-专题、14-赣云视频")
    private Integer contentType;

    @ApiModelProperty(value = "数据来源")
    private Integer platform;

    @ApiModelProperty(value = "发布时间展示标识 true-展示 false-不展示")
    private boolean showReleaseTimeFlag;

    @ApiModelProperty("是否需要显示红名")
    private Boolean isNeedShowRedName;

    @ApiModelProperty("勋章列表")
    private List<GroupMedalVO> medals;

    @ApiModelProperty("是否匿名发帖")
    private Boolean isAnonymous;

    @ApiModelProperty("剩余有效时间")
    private long remainderEffectTime;
    @ApiModelProperty("发布IP")
    private String ip;
    @ApiModelProperty("IP归属地")
    private String ipLocation;



    @ApiModelProperty("今日聊信息")
    private TodayTalkVO todayTalk;

    @ApiModelProperty("直播回顾播放开始时间：秒")
    private Integer liveBroadcastPlayTime;


    @ApiModelProperty("活动直播/视频-站外观看限制 0-不限制 1-限制")
    private Integer outSideRestriction;

    @ApiModelProperty("活动直播/视频-站外观看限制秒数")
    private Integer outSideSeconds;

    @ApiModelProperty("信息流组件列表")
    private List<FeedComponent<?>> components;

    @ApiModelProperty("被转发资源信息")
    private FeedVO relayMedia;

    @ApiModelProperty(value = "皮肤挂件")
    private FeedPendantVO skinPendant;

    public void addComponent(FeedComponent<?> component) {
        if (CollectionUtils.isEmpty(components)) {
            components = new ArrayList<>();
        }
        components.add(component);
    }


}
