package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.ConsumerUserClientFallback;
import com.jxntv.gvideo.user.client.ConsumerUserClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "user-service", contextId = "consumer-user", fallbackFactory = ConsumerUserClientFallback.class)
public interface ConsumerUserService extends ConsumerUserClient {
}
