package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.user.client.ChannelClient;
import com.jxntv.gvideo.user.client.dto.ChannelSaveDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelTabDTO;
import com.jxntv.gvideo.user.client.dto.ConsumerChannelTabSearchDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ConsumerChannelClientFallback implements FallbackFactory<ChannelClient> {
    @Override
    public ChannelClient create(Throwable throwable) {
        return new ChannelClient() {
            @Override
            public Result<Void> saveChannel(ChannelSaveDTO saveDTO) {
                log.error("ConsumerChannelClient.saveChannel() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelDTO> getCurrentChannel(String deviceId) {
                log.error("ConsumerChannelClient.getCurrentChannel() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelDTO> getNewestChannel(String deviceId) {
                log.error("ConsumerChannelClient.getNewestChannel() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelDTO> getChannelUserNew(String deviceId) {
                log.error("ConsumerChannelClient.getNewestChannel() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> batchSync(List<ConsumerChannelTabDTO> all) {
                log.error("ConsumerChannelClient.batchSync() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addConsumerChannelTab(ConsumerChannelTabDTO channelTabDTO) {
                log.error("ConsumerChannelClient.addConsumerChannelTab() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> updateConsumerChannelTab(Long id, ConsumerChannelTabDTO channelTabDTO) {
                log.error("ConsumerChannelClient.updateConsumerChannelTab() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Void> deleteConsumerChannelTab(Long id) {
                log.error("ConsumerChannelClient.deleteConsumerChannelTab() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelTabDTO> getConsumerChannelTabById(Long id) {
                log.error("ConsumerChannelClient.getConsumerChannelTabById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelTabDTO> getConsumerChannelTabByCode(String code) {
                log.error("ConsumerChannelClient.getConsumerChannelTabByCode() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelTabDTO> getConsumerChannelTabByName(String name) {
                log.error("ConsumerChannelClient.getConsumerChannelTabByName() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<ConsumerChannelTabDTO>> getConsumerChannelTabPage(ConsumerChannelTabSearchDTO searchDTO) {
                log.error("ConsumerChannelClient.getConsumerChannelTabPage() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<ConsumerChannelTabDTO> getConsumerChannelTabByDeviceOrMobile(String deviceId, String mobile) {
                log.error("ConsumerChannelClient.getConsumerChannelTabByDeviceOrMobile() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<ConsumerChannelTabDTO>> listAll() {
                log.error("ConsumerChannelClient.listAll() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
