package com.jxntv.gvideo.app.be.model.vo.drama;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: niedamin
 * @Date: 2023/06/30 15:00
 */
@Data
@ApiModel(value = "DramaListVO", description = "短剧列表")
public class DramaListVO implements Serializable {

    @ApiModelProperty("columnId")
    private Long columnId;

    @ApiModelProperty("节目名称")
    private String columnName;

    @ApiModelProperty("封面图")
    private String coverUrl;

    @ApiModelProperty("总集数")
    private Integer totalNum;

    @ApiModelProperty("当前集数")
    private Integer currentNum;

    @ApiModelProperty("分类")
    private String category;

    @ApiModelProperty("分类图片")
    private String categoryIcon;

    @ApiModelProperty("亮点")
    private String point;

    @ApiModelProperty("今豆一口价")
    private Integer jspBean;
}
