package com.jxntv.gvideo.app.be.controller;

import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.login.thread.ThreadLocalCache;
import com.jxntv.gvideo.app.be.login.thread.UserInfo;
import com.jxntv.gvideo.app.be.model.vo.notification.NotificationDetailVO;
import com.jxntv.gvideo.app.be.model.vo.notification.NotificationUnreadVO;
import com.jxntv.gvideo.app.be.model.vo.notification.NotificationVO;
import com.jxntv.gvideo.app.be.service.NotificationService;
import com.jxntv.gvideo.common.model.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Created on 2020-04-15
 */
@Slf4j
@RestController
@RequestMapping("/api/notification")
@Api(value = "通知接口", tags = {"NotificationController"})
public class NotificationController {

    @Resource
    private NotificationService notificationService;

    @GetMapping("/unread")
    @ApiOperation(value = "获取站内信的小红点", notes = "未读通知")
    public Result<NotificationUnreadVO> unread() {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        NotificationUnreadVO unreadVO = notificationService.getUnread(jid);
        return Result.ok(unreadVO);
    }

    @GetMapping("/list")
    @ApiOperation(value = "通知汇总列表", notes = "通知汇总列表")
    public Result<Page<NotificationVO>> pageOfSummary(
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "每页记录") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        Page<NotificationVO> result = notificationService.pageOfSummary(jid, pageNum, pageSize);
        return Result.ok(result);
    }

    @GetMapping("/list/{msgType}/detail")
    @ApiOperation(value = "通知详情列表", notes = "通知详情列表")
    public Result<Page<NotificationDetailVO>> pageOfDetails(
            @PathVariable(value = "msgType") Integer msgType,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "每页记录") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        Page<NotificationDetailVO> page = notificationService.pageOfDetails(msgType, jid, pageNum, pageSize);
        return Result.ok(page);
    }

    @GetMapping("clear")
    @ApiOperation(value = "清空所有未读消息(或者清空某个消息类型下的未读消息)", notes = "清空所有未读消息(或者清空某个消息类型下的未读消息)")
    Result<Void> clearUnread( @RequestParam(value = "msgType",required = false) Integer msgType) {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.nonNull(jid)) {
            notificationService.clearUnread(jid,msgType);
        }
        return Result.ok();
    }

    @GetMapping("/list/v2")
    @ApiOperation(value = "通知汇总列表(v2)", notes = "通知汇总列表(v2)")
    public Result<Page<NotificationVO>> pageOfSummaryV2(
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "每页记录") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @ApiParam(name = "groupId", value = "社区id") @RequestParam(value = "groupId", required = false) Long groupId) {
        Long jid = ThreadLocalCache.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        Page<NotificationVO> result = notificationService.pageOfSummaryV2(jid, groupId,pageNum, pageSize);
        return Result.ok(result);
    }

    @GetMapping("/list/{msgType}/detail/v2")
    @ApiOperation(value = "通知详情列表", notes = "通知详情列表")
    public Result<Page<NotificationDetailVO>> pageOfDetailsV2(
            @PathVariable(value = "msgType") Integer msgType,
            @ApiParam(name = "pageNum", value = "页码") @RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
            @ApiParam(name = "pageSize", value = "每页记录") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @ApiParam(name = "groupId", value = "社区id") @RequestParam(value = "groupId", required = false) Long groupId) {
        UserInfo userInfo = ThreadLocalCache.get();
        Long jid = userInfo.getJid();
        if (Objects.isNull(jid)) {
            return Result.fail("用户未登录");
        }
        Page<NotificationDetailVO> page = notificationService.pageOfDetailsV2(msgType, jid,groupId, pageNum, pageSize);
        return Result.ok(page);
    }
}
