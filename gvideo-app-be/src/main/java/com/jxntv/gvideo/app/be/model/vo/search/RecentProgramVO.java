package com.jxntv.gvideo.app.be.model.vo.search;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("最近播放节目")
public class RecentProgramVO {

    @ApiModelProperty("节目id")
    private Long id;

    @ApiModelProperty("栏目id")
    private Long columnId;

    @ApiModelProperty("节目名称")
    private String programName;

    @ApiModelProperty("节目播放日期")
    private String playDate;
}
