package com.jxntv.gvideo.app.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.om.client.FloatWidgetColorClient;
import com.jxntv.gvideo.om.dto.widget.FloatWidgetColorDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> Created on 2021/5/12.
 */
@Slf4j
@Component
public class FloatWidgetColorClientFallback implements FallbackFactory<FloatWidgetColorClient> {
    @Override
    public FloatWidgetColorClient create(Throwable throwable) {
        return new FloatWidgetColorClient() {


            @Override
            public Result<Long> create(FloatWidgetColorDTO dto) {
                log.error("FloatWidgetColorClient.create() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateById(Long id, FloatWidgetColorDTO dto) {
                log.error("FloatWidgetColorClient.updateById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteById(Long id) {
                log.error("FloatWidgetColorClient.deleteById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<FloatWidgetColorDTO>> all() {
                log.error("FloatWidgetColorClient.all() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
