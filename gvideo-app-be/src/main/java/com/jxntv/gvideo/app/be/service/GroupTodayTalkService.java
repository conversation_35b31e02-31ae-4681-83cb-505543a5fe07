package com.jxntv.gvideo.app.be.service;

import com.jxntv.gvideo.app.be.common.vo.Page;
import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import com.jxntv.gvideo.app.be.model.vo.today.talk.GroupTodayTalkBulletScreenVO;
import com.jxntv.gvideo.app.be.model.vo.today.talk.GroupTodayTalkCreateVO;
import com.jxntv.gvideo.app.be.model.vo.today.talk.GroupTodayTalkVO;
import com.jxntv.gvideo.app.be.model.vo.today.talk.GroupTodayTalkVoteVO;

/**
 * 圈子-今日聊话题
 *
 * <AUTHOR>
 * @date 2022-10-18 14:56:01
 */
public interface GroupTodayTalkService {


    /**
     * 通过圈子ID查询今日聊列表
     *
     * @param groupId
     * @return
     */
    Page<FeedVO> getListByGroupId(Long groupId, Integer pageNum, Integer pageSize);


    /**
     * 查询今日聊投稿列表
     *
     * @param groupId  社区id
     * @param pageNum  分页页号
     * @param pageSize 分页大小
     * @return
     */
    Page<GroupTodayTalkVO> getVoteListByGroupId(Long groupId, Integer pageNum, Integer pageSize);

    /**
     * 投稿今日聊
     *
     * @param vo
     * @return
     */
    Boolean contribute(GroupTodayTalkCreateVO vo);

    /**
     * 查询今日聊弹幕
     *
     * @param mediaId
     * @return
     */
    GroupTodayTalkBulletScreenVO queryBulletScreenByMediaId(Long mediaId);


    Long vote(GroupTodayTalkVoteVO voteVO);
}
