package com.jxntv.gvideo.app.be.model.vo.group.icon;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class GroupIconVO {

    @ApiModelProperty("主键id")
    private Long id;
    @ApiModelProperty("圈子id")
    private Long groupId;
    @ApiModelProperty("组件ID")
    private Long componentId;
    @ApiModelProperty("名称")
    private String name;
    @ApiModelProperty("补充说明")
    private String note;
    @ApiModelProperty("0-原生菜单，1-原生内容，2-H5链接，3-微信小程序")
    private Integer type;
    @ApiModelProperty("跳转URL：type=0，原生菜单，type=1，原生内容，type=2，H5链接，type=3，微信小程序")
    private String linkUrl;
    @ApiModelProperty("icon图片id")
    private String iconId;
    @ApiModelProperty("icon图片id")
    private String iconUrl;
    @ApiModelProperty("气泡文案展示类型，1-动态展示，2-静态展示")
    private Integer buttonWordsShowType;
    @ApiModelProperty("气泡文案 ")
    private String buttonWords;
    @ApiModelProperty("是否需要登录")
    private Boolean loginRequired;

}
