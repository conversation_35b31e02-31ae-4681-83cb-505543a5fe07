package com.jxntv.gvideo.app.be.model.vo.media.collection;

import com.jxntv.gvideo.app.be.model.vo.feed.FeedVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("合辑对象")
public class LiveCollectionListVO {

    @ApiModelProperty("合辑ID")
    private Long collectionId;

    @ApiModelProperty("合辑标题")
    private String collectionTitle;

    @ApiModelProperty("合辑展示样式:1-大图横滑 2-竖版小图横滑 3-横版小图，4图展示")
    private Integer collectionStyle;

    @ApiModelProperty("合辑内容列表")
    private List<FeedVO> contents;

    @ApiModelProperty("合集投放ID")
    private Long positionId;
}
