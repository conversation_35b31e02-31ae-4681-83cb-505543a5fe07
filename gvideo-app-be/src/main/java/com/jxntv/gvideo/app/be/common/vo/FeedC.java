package com.jxntv.gvideo.app.be.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created on 2020-01-11
 */
@Data
public class FeedC<T> {
    private Long total;
    private Boolean hasMore;
    private List<T> list;
    private String cursor;

    public static <T> FeedC<T> empty(String cursor) {
        FeedC<T> feed = new FeedC<>();
        feed.setHasMore(false);
        feed.setTotal(0L);
        feed.setList(new ArrayList<>());
        feed.setCursor(cursor);
        return feed;
    }


    @Data
    public static class SlideC<T> {
        @ApiModelProperty(value = "置顶 0否 1是")
        private Integer isStick;
        @ApiModelProperty(value = "资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 17 横向滚动块 23 自建频道直播", required = true, example = "1", notes = "1视频2FM")
        private Integer mediaType;
        @ApiModelProperty(value = "切换间隔时间，单位：毫秒")
        private Float sliderTime;
        @ApiModelProperty(value = "展示块上边内容")
        private List<T> headTop;
        @ApiModelProperty(value = "展示块下边内容")
        private List<T> headBottom;
        @ApiModelProperty(value = "热点新闻")
        private ListItemC hotNews;
    }

    @Data
    public static class ListItemC<T> {
        @ApiModelProperty(value = "置顶 0否 1是")
        private Integer isStick;
        @ApiModelProperty(value = "内容列表ID")
        private Long id;
        @ApiModelProperty(value = "布局块名称")
        private String blockName;
        @ApiModelProperty(value = "资源大类 1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 17 横向滚动块 23 自建频道直播  36新闻金刚位", required = true, example = "1", notes = "1视频2FM")
        private Integer mediaType;
        @ApiModelProperty(value = "切换间隔时间，单位：毫秒")
        private Float sliderTime;
        @ApiModelProperty(value = "子列表")
        private List<T> items;
        @ApiModelProperty("跳转按钮名称")
        private String linkBtnName;
        @ApiModelProperty("H5页面标题")
        private String linkTitle;
        @ApiModelProperty("跳转H5链接")
        private String linkUrl;
        @ApiModelProperty(value = "金刚信息流列表")
        private List<T> kingKongNews;
    }

}
