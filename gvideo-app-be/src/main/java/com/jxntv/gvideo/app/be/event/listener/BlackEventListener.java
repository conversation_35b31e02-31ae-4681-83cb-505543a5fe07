package com.jxntv.gvideo.app.be.event.listener;

import com.jxntv.gvideo.aliyun.sdk.ImClient;
import com.jxntv.gvideo.aliyun.sdk.dto.im.MessageBodyDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.im.SendC2CMessageDTO;
import com.jxntv.gvideo.app.be.event.BlackEvent;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.interact.client.InteractBlacklistClient;
import com.jxntv.gvideo.interact.client.InteractClient;
import com.jxntv.gvideo.interact.client.InteractImRecordClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 关注操作事件
 * @date 2022/03/08 17:21
 */
@Slf4j
@Component
public class BlackEventListener {

    @Resource
    private ImClient imClient;
    @Resource
    private InteractBlacklistClient interactBlacklistClient;
    @Resource
    private InteractClient interactClient;
    @Resource
    private InteractImRecordClient interactImRecordClient;

    @Async
    @EventListener
    public void onEvent(BlackEvent event) {
        log.info("【拉黑事件】event is {}", JsonUtils.toJson(event));

        Boolean isBlack = interactBlacklistClient.isBlack(event.getUserId(), event.getToUserId()).orElse(false);
        if (isBlack) {
            //  拉黑动作删除互相关注
            interactClient.deleteFollow(event.getUserId(), 1, event.getToUserId());

            interactClient.deleteFollow(event.getToUserId(), 1, event.getUserId());

            //  清理两人对话记录数
            interactImRecordClient.imRecordReset(event.getUserId(), event.getToUserId());
        }

        //  发送消息
        Map<String, Object> data = new HashMap<>(2);
        data.put("cmd", "13");
        data.put("jid", event.getUserId());
        data.put("isBlackByReceiver", isBlack);

        Map<String, Object> customCmdMsg = new HashMap<>();
        customCmdMsg.put("cmd", "CustomCmdMsg");
        customCmdMsg.put("data", data);

        MessageBodyDTO.CustomContentDTO customContentDTO = new MessageBodyDTO.CustomContentDTO();
        customContentDTO.setData(customCmdMsg);

        MessageBodyDTO msgBody = MessageBodyDTO.build(customContentDTO);

        SendC2CMessageDTO sendC2CMessageDTO = new SendC2CMessageDTO();
        sendC2CMessageDTO.setToAccount(String.valueOf(event.getToUserId()));
        sendC2CMessageDTO.setMessages(Collections.singletonList(msgBody));

        imClient.sendC2CMessage(sendC2CMessageDTO);
    }

}
