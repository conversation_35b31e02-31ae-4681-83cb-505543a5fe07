package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.app.be.client.MediaFileService;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.dto.MediaFileDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
/**
 * <AUTHOR>
 * @date 2023/7/6 10:04
 */
@Slf4j
@Component
public class MediaFileManager {

    @Resource
    private MediaFileService mediaFileService;

    @Cached(name = "app::media_file::getById", cacheType = CacheType.BOTH, expire = 60, timeUnit = TimeUnit.MINUTES, cacheNullValue = true)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 10 * 60, refreshLockTimeout = 60)
    public Result<MediaFileDTO> getById(Long id) {
        return Objects.isNull(id) ? Result.ok(null) : mediaFileService.get(id);
    }
}
