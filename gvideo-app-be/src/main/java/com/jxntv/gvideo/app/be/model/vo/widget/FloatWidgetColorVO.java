package com.jxntv.gvideo.app.be.model.vo.widget;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/6/16
 * Email: <EMAIL>
 */
@Data
@ApiModel("浮动组件底色")
public class FloatWidgetColorVO {
    @ApiModelProperty(value = "主键")
    private Long id;
    @ApiModelProperty(value = "起始渐变色")
    private String colorStart;
    @ApiModelProperty(value = "结束渐变色")
    private String colorEnd;
}
