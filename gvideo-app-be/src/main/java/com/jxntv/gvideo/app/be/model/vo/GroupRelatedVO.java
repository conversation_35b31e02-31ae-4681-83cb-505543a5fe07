package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> Created on 2021/5/28.
 */
@Data
@ApiModel("动态关联圈子信息")
public class GroupRelatedVO {
    @ApiModelProperty("圈子id")
    private Long groupId;
    @ApiModelProperty("圈子名称")
    private String groupName;
    @ApiModelProperty("社区图标URL")
    private String coverUrl;
    @ApiModelProperty("是否加入社区")
    private Boolean join;
    @ApiModelProperty("话题id")
    private Long topicId;
    @ApiModelProperty("话题名称")
    private String topicName;
    @ApiModelProperty("动态状态（圈子内部状态）内容状态 1 启用 2 禁用")
    private Integer status;
    @ApiModelProperty("精华")
    private Boolean essence;
    @ApiModelProperty("置顶")
    private Boolean top;
    @ApiModelProperty("简介")
    private String introduction;
    @ApiModelProperty("所属MCN")
    private Long tenantId;
    @ApiModelProperty("所属MCN名称")
    private String tenantName;
    private List<String> labels;

}
