package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.media.client.ChannelClient;
import com.jxntv.gvideo.media.client.dto.ChannelTabDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MediaChannelManager {

    @Resource
    private ChannelClient channelClient;

    @Cached(name = "app::media::channel::listChannelTab", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public Result<List<ChannelTabDTO>> listChannelTab(String type, String os, String appVersion) {
        return channelClient.listChannelTabByType(type, os, appVersion);
    }


    @Cached(name = "app::media::channel::getChannelTabById", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public Result<ChannelTabDTO> getChannelTabById(Long tabId) {
        return channelClient.getChannelTabById(tabId);
    }

}
