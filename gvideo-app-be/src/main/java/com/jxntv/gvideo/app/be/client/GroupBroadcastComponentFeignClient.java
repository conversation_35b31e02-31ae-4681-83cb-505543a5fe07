package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.GroupBroadcastComponentClientFallback;
import com.jxntv.gvideo.group.sdk.client.GroupBroadcastComponentClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "group-service", contextId = "group-broadcast-component", fallbackFactory = GroupBroadcastComponentClientFallback.class)
public interface GroupBroadcastComponentFeignClient extends GroupBroadcastComponentClient {
}
