package com.jxntv.gvideo.app.be.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.jxntv.gvideo.app.be.model.vo.VideoTagsVO;
import com.jxntv.gvideo.common.model.Result;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/api/voice")
@Api(value = "视频打标", tags = {"VoiceTagsController"})
public class VoiceTagsController {

    // 本地：http://10.72.231.100:10050/tags
    // 线上：http://111.75.202.209:10050/tags
    private static final String voiceUrl = "http://111.75.202.209:10050/tags";
    @GetMapping(path = "/tags",produces = "text/event-stream;charset=UTF-8")
    public SseEmitter streamTags(VideoTagsVO videoTagsVO){
        SseEmitterUTF8 emitter = new SseEmitterUTF8(Long.MAX_VALUE);
        if(!videoTagsVO.getSource().contains("http")) {
            videoTagsVO.setSource("https://hdjbm.jxgdw.com/" + videoTagsVO.getSource());
        }
        try {
            // 订阅另一个SSE接口
            OkHttpClient client = new OkHttpClient.Builder()
                    .readTimeout(0, TimeUnit.MILLISECONDS) // 设置读取超时为无限
                    .build();
            RequestBody body = RequestBody.create(JSON.toJSONString(videoTagsVO).getBytes(StandardCharsets.UTF_8));
            Request request =new Request.Builder().addHeader("Content-Type", "application/json;charset=utf-8").addHeader("Accept", "text/event-stream").url(voiceUrl).post(body).build();
            EventSourceListener listener = new EventSourceListener() {
                @Override
                public void onFailure(@NotNull EventSource eventSource, @Nullable Throwable t, @Nullable Response response) {
                    log.error("对话请求连接失败:", t);
                    super.onFailure(eventSource, t, response);
                    emitter.completeWithError(t);
                }

                @Override
                public void onOpen(@NotNull EventSource eventSource, @NotNull Response response) {
                    log.error("对话请求连接打开");
                }

                @Override
                public void onEvent(@NotNull EventSource eventSource, @Nullable String id, @Nullable String type, @NotNull String data) {

                    log.info("sse接收数据:{}",data);
                    JSONObject object = JSON.parseObject(new String(data.getBytes(StandardCharsets.UTF_8)));
                    try {
                        emitter.send(JSON.toJSONString(object));
                    } catch (IOException e) {
                        log.info("数据出错");
                        emitter.completeWithError(e); // 出错则结束
                    }
                }
                @Override
                public void onClosed(@NotNull EventSource eventSource) {
                    log.info("关闭连接");
                    emitter.complete();
                }
            };

            EventSource.Factory factory = EventSources.createFactory(client);
            factory.newEventSource(request, listener);
        } catch (Exception e) {
            emitter.completeWithError(e); // 出错则结束
        }

        return emitter; // 返回SseEmitter给客户端
    }

} class SseEmitterUTF8 extends SseEmitter {

    public SseEmitterUTF8(Long timeout) {
        super(timeout);
    }

    @Override
    protected void extendResponse(ServerHttpResponse outputMessage) {
        super.extendResponse(outputMessage);

        HttpHeaders headers = outputMessage.getHeaders();
        headers.setContentType( new MediaType("text", "event-stream", Charset.forName("UTF-8")));
    }
}