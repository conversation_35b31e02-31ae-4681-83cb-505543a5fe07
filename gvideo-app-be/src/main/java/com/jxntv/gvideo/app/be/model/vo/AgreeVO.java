package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created on 2020-03-08
 */
@Data
@ApiModel(value = "AgreeVO", description = "初始配置对象")
public class AgreeVO {

    @ApiModelProperty(value = "用户协议h5链接")
    private String userPolicy;
    @ApiModelProperty(value = "法律政策h5链接")
    private String lawPolicy;
    @ApiModelProperty(value = "隐私条款h5链接")
    private String privacyPolicy;
    @ApiModelProperty(value = "sdk条款h5链接")
    private String sdkPrivacyPolicy;
    @ApiModelProperty(value = "实人认证隐私条款h5链接")
    private String facePrivacyPolicy;
    @ApiModelProperty(value = "注销协议h5链接")
    private String cancelPolicy;
    @ApiModelProperty(value = "反馈页h5链接")
    private String feedback;
    @ApiModelProperty(value = "入驻页h5链接")
    private String settle;
    @ApiModelProperty(value = "举报h5链接")
    private String report;
    @ApiModelProperty(value = "苹果商店appId(用于去评分)")
    private String appStoreAppId;
    @ApiModelProperty(value = "视频最大时长")
    private Integer selectVideoMaxTime;
    @ApiModelProperty(value = "版本升级对象")
    private VersionUpdate versionUpdate;
    @ApiModelProperty(value = "内部配置信息")
    private InitConfig initConfig;
    @ApiModelProperty(value = "分享按钮组")
    private Map<String, Boolean> shareSwitch;
    @ApiModelProperty(value = "APP二维码地址")
    private String qrcodeUrl;
    @ApiModelProperty(value = "主题颜色开关：false：关闭 true:开启")
    private Boolean themeColorSwitch;
    @ApiModelProperty(value = "主题颜色类型：0-无、1-两会、2-过年")
    private Integer themeColorType;
    @ApiModelProperty(value = "搜索框占位符列表")
    private List<AdvertSearchPlaceholderVO> placeholderList;
    @ApiModelProperty(value = "一键登录密钥")
    private String quickLoginKey;
    @ApiModelProperty(value = "私信限制次数")
    private Integer imTimes;

    @ApiModelProperty(value = "小红娘二维码照片url")
    private String matchMakerQrcodeUrl;

    @ApiModelProperty(value = "小红娘企业微信照片url")
    private String matchMakerWechatUrl;

    @ApiModelProperty(value = "小红娘jid")
    private Long matchMakerJid;

    @ApiModelProperty(value = "小红娘头像")
    private String matchMakerAvatar;

    @ApiModelProperty(value = "勋章详情url")
    private String medalDetailUrl;

    @ApiModelProperty(value = "活动详情H5链接")
    private String activityDetailUrl;

    @ApiModelProperty(value = "商城相关配置")
    private Shop shop;

    @ApiModelProperty(value = "都市放心爱相关配置")
    private BlindDate blindDate;

    @ApiModelProperty(value = "自建埋点相关配置")
    private Track track;

    @ApiModelProperty(value = "省宣埋点上报系数")
    private Integer sxPvfactor;

    @ApiModelProperty(value = "签到是否启用")
    private Boolean signEnable;

    @ApiModelProperty(value = "签到跳转地址")
    private String signLinkUrl;

    @ApiModelProperty(value = "是否启用动态注册微信")
    private Boolean universalLinkEnable;

    @ApiModelProperty(value = "跳转地址")
    private String universalLink;

    @ApiModelProperty(value = "App是否正在审核")
    private Boolean appAuditing;

    @ApiModelProperty(value = "验证码相关配置")
    private Captcha captcha;

    @ApiModelProperty(value = "AI相关配置")
    private Ai ai;

    @ApiModelProperty(value = "观看任务")
    private WatchTask watchTask;

    @ApiModelProperty(value = "是否开启观看任务")
    private Boolean watchTaskEnable;

    @ApiModelProperty(value = "个性化推荐")
    private Integer recommendSwitch;

    @ApiModelProperty(value = "转发社区白名单")
    private List<Long> relayGroupIds;

    @ApiModelProperty(value = "是否展示地方电视台")
    private Boolean showLocalTv;

    @ApiModelProperty(value = "是否展示地方广播")
    private Boolean showLocalBroadcast;

    @Data
    public static class VersionUpdate {
        @ApiModelProperty(value = "程序版本")
        private String version;
        @ApiModelProperty(value = "是否强制更新")
        private Boolean forceUpdate;
        @ApiModelProperty(value = "更新日期")
        private Long date;
        @ApiModelProperty(value = "更新内容")
        private String releaseNotes;
        @ApiModelProperty(value = "包名")
        private String bundleId;
        @ApiModelProperty(value = "是否从官网如苹果获取更新内容")
        private Boolean officialSource;
        @ApiModelProperty(value = "下载地址")
        private String downloadUrl;
    }

    @Data
    public static class InitConfig {
        @ApiModelProperty(value = "语音token", required = true)
        private String aliToken;
        @ApiModelProperty(value = "语音appkey", required = true)
        private String nlpAppKey;
    }

    @Data
    public static class Shop {
        @ApiModelProperty(value = "商城域名白名单", required = true)
        private List<String> shopWhiteList;
        @ApiModelProperty(value = "是否显示我的订单列表", required = true)
        private Boolean showOrderEntry;
        @ApiModelProperty(value = "我的订单URL", required = true)
        private String myOrderListUrl;
        @ApiModelProperty(value = "是否显示我的优惠券入口", required = true)
        private Boolean showCouponEntry;
        @ApiModelProperty(value = "我的优惠券页面地址", required = true)
        private String myCouponUrl;
    }

    @Data
    public static class BlindDate {
        @ApiModelProperty(value = "社区ID", required = true)
        private Long groupId;
        @ApiModelProperty(value = "社区名称", required = true)
        private String groupName;

        @ApiModelProperty(value = "IM群聊群主ID列表", required = true)
        private List<String> imGroupOwnerIds;

    }

    @Data
    public static class Track {
        @ApiModelProperty(value = "App自建埋点上报状态  true-上报 false-不上报")
        private Boolean reportEnable;
    }

    @Data
    public static class Captcha {
        @ApiModelProperty(value = "极验验证开启状态  true-开启 false-关闭")
        private Boolean geeEnable;
    }

    /**
     * 智能体
     */
    @Data
    public static class Ai {

        @ApiModelProperty(value = "智能体开启状态  true-开启 false-关闭")
        private Boolean enable;

        @ApiModelProperty(value = "智能体URL")
        private String url;

        @ApiModelProperty(value = "智能笔记开启状态  true-开启 false-关闭")
        private Boolean noteEnable;

        @ApiModelProperty(value = "智能笔记URL")
        private String noteUrl;
    }


    @Data
    public static class WatchTask {
        /**
         * 每日弹窗次数
         */
        @ApiModelProperty(value = "每日弹窗次数")
        private Integer popTimes;

        @ApiModelProperty(value = "弹窗间隔时间（秒）")
        private Integer popInterval;

        @ApiModelProperty(value = "上报时长（秒）")
        private Integer reportInterval;

        @ApiModelProperty(value = "toast忽略栏目ID列表")
        private List<Long> toastIgnoreColumnIds;

        @ApiModelProperty(value = "icon开启状态  true-开启 false-关闭")
        private Boolean iconEnable;

        @ApiModelProperty(value = "声效开启状态  true-开启 false-关闭")
        private Boolean soundEnable;
    }
}
