package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> Created on 2021/5/27.
 */
@Data
@ApiModel("圈子编辑对象")
public class GroupModifyVO {
    @ApiModelProperty(value = "圈子id", required = true)
    @NotNull(message = "社区ID不能为空")
    private Long groupId;
    @ApiModelProperty("圈子名称")
    private String name;
    @ApiModelProperty("圈子简介")
    private String introduction;
    @ApiModelProperty("圈子封面")
    private String cover;
}
