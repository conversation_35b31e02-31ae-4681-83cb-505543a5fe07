package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.GroupQaComponentClientFallback;
import com.jxntv.gvideo.group.sdk.client.GroupQaComponentClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "group-service", contextId = "group-qa-component", fallbackFactory = GroupQaComponentClientFallback.class)
public interface GroupQaComponentFeignClient extends GroupQaComponentClient {
}
