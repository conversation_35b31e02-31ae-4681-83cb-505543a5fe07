package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.GroupAnswerReminderPopUpFallback;
import com.jxntv.gvideo.group.sdk.client.GroupAnswerReminderPopUpClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "group-service", fallbackFactory = GroupAnswerReminderPopUpFallback.class)
public interface GroupAnswerReminderPopUpFeignClient extends GroupAnswerReminderPopUpClient {
}
