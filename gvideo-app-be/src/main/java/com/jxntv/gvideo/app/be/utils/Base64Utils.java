package com.jxntv.gvideo.app.be.utils;

import java.io.UnsupportedEncodingException;
import java.util.Base64;

public class Base64Utils {

    static final Base64.Decoder decoder = Base64.getDecoder();
    static final Base64.Encoder encoder = Base64.getEncoder();
    static final String charset = "UTF-8";

    public static String encode(String s) {
        try {
            return encoder.encodeToString(s.getBytes(charset));
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }

    public static String decode(String s) {
        try {
            return new String(decoder.decode(s.getBytes(charset)), charset);
        } catch (UnsupportedEncodingException e) {
            return null;
        }
    }
}
