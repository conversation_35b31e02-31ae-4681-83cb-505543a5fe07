package com.jxntv.gvideo.app.be.model.vo.blind.date;

import com.jxntv.gvideo.app.be.model.vo.blind.date.accost.GroupBlindDateAccostVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "GroupBlindDateIntroductionVO", description = "相亲-个人介绍信息")
public class GroupBlindDateIntroductionVO {

    @ApiModelProperty(value = "jid")
    private Long jid;

    @ApiModelProperty(value = "昵称")
    private String nickname;

    @ApiModelProperty(value = "真实姓名")
    private String name;

    @ApiModelProperty(value = "用户头像")
    private String avatar;

    @ApiModelProperty(value = "实人认证状态 0、未认证 1、已认证")
    private Integer verifyFlag;

    @ApiModelProperty(value = "性别 1、男 2、女")
    private Integer gender;

    @ApiModelProperty(value = "年龄")
    private Integer age;

    @ApiModelProperty(value = "内心独白")
    private String monologue;

    @ApiModelProperty(value = "兴趣爱好")
    private String hobby;

    @ApiModelProperty(value = "单位认证状态 0未认证 1认证通过")
    private Integer companyVerifyStatus;

    @ApiModelProperty(value = "基本信息")
    private List<Info> basicInfos;

    @ApiModelProperty(value = "交友要求")
    private List<Info> needs;

    @ApiModelProperty(value = "个人生活照张数")
    private Integer dailyPhotosSize;

    @ApiModelProperty(value = "个人生活照")
    private List<String> dailyPhotos;

    @ApiModelProperty(value = "查看人的资料完善状态 0需要完善 1已完善")
    private Integer viewerInfoStatus;

    @ApiModelProperty(value = "查看人的照片完善状态 0需要完善 1已完善")
    private Integer viewerPhotoStatus;

    @ApiModelProperty(value = "查看人的实名状态 0未认证 1已认证")
    private Integer viewerVerifyFlag;

    @ApiModelProperty(value = "查看人的Jid")
    private Long viewerJid;

    @ApiModelProperty(value = "查看人的单位认证状态 0未认证 1认证通过")
    private Integer viewerCompanyVerify;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Info {

        @ApiModelProperty(value = "表头")
        private String title;
        @ApiModelProperty(value = "值")
        private String value;
    }

    @ApiModelProperty(value = "相亲活动配对状态 true-配对成功  false-配对失败")
    private Boolean lovePaired;

    @ApiModelProperty(value = "私聊权限 true-有权限  false-无权限")
    private Boolean imPower;

    @ApiModelProperty(value = "我发送的招呼信息")
    private GroupBlindDateAccostVO mySendAccost;

    @ApiModelProperty(value = "我接收的招呼信息")
    private GroupBlindDateAccostVO myReceiveAccost;

}
