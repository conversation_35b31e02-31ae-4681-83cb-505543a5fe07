package com.jxntv.gvideo.app.be.converter;

import com.jxntv.gvideo.app.be.model.vo.settings.UserNotificationSettingsVO;
import com.jxntv.gvideo.user.client.dto.ConsumerNotificationSettingsDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserSettingsConverter {

    public UserNotificationSettingsVO convert(ConsumerNotificationSettingsDTO dto) {
        UserNotificationSettingsVO vo = new UserNotificationSettingsVO();
        vo.setJid(dto.getJid());
        vo.setImNotificationOpen(dto.getImNotificationOpen());
        return vo;
    }

    public ConsumerNotificationSettingsDTO convert(UserNotificationSettingsVO vo) {
        ConsumerNotificationSettingsDTO dto = new ConsumerNotificationSettingsDTO();
        dto.setJid(vo.getJid());
        dto.setImNotificationOpen(vo.getImNotificationOpen());
        return dto;
    }
}
