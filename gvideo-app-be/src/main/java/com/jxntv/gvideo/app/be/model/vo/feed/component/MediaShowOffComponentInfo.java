package com.jxntv.gvideo.app.be.model.vo.feed.component;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("炫一下动态信息")
public class MediaShowOffComponentInfo extends FeedComponentInfo {

    @ApiModelProperty("炫一下封面图")
    private String coverUrl;

    @ApiModelProperty("炫一下跳转URL")
    private String linkUrl;

    @ApiModelProperty("活动ID")
    private String huodongId;

    @ApiModelProperty("活动内容")
    private String huodong;

    @ApiModelProperty("活动名称")
    private String huodongName;

    @ApiModelProperty("内容ID")
    private String contentId;

}
