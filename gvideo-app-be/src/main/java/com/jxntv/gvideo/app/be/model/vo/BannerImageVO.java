package com.jxntv.gvideo.app.be.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("banner轮播图片对象")
public class BannerImageVO implements Serializable {

    @ApiModelProperty("banner id")
    private Long bannerId;

    @ApiModelProperty("banner名称")
    private String bannerName;

    @ApiModelProperty("是否为弹窗:0-否，1-是")
    private Integer isPop;

    @ApiModelProperty("弹窗次数")
    private Integer popTime;


    @ApiModelProperty("轮播图片URL")
    private String url;

    @ApiModelProperty("轮播图对象")
    private ImageVO image;

    @ApiModelProperty("媒体资源id，（内链包含）")
    private Long mediaId;

    @ApiModelProperty("跳转链接媒体类型，1 长视频 2 短视频 3 长音频 4 短音频 5 活动直播横屏 6 活动直播竖屏 7 互动直播横屏 8 互动直播竖屏 9 图文 10 语音 " +
            "11 文章大图/三图 12 文章左文右图 13 外链 14 特殊展示块 15 滚动文章块 " +
            "20 圈子详情 21 话题详情 " + "22 放心爱详情" + "23 自建频道看电视详情" + "24 看电视详情" + "25 整期详情" +
            "27 问答广场 28 放心爱完善资料 30 新闻首页 31 推荐首页 32 我的圈子 33 发现圈子")
    private Integer mediaType;

    @ApiModelProperty("跳转链接标题")
    private String linkTitle;

    @ApiModelProperty("跳转链接URL")
    private String linkUrl;

    @ApiModelProperty("跳转内容类型")
    private String jumpMediaType;

    @ApiModelProperty("跳转地址")
    private String jumpAddress;

    @ApiModelProperty("是否加入圈子")
    private Boolean isJoined;

    @ApiModelProperty("是否为直播，展示直播图标")
    private Boolean isLive;

    @ApiModelProperty("节目ID")
    private Long programId;

    @ApiModelProperty(value = "页面路由地址")
    private String actUrl;

}
