package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.media.client.RadioBroadcastClient;
import com.jxntv.gvideo.media.client.dto.radio.RadioBroadcastDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RadioBroadcastManager {

    @Resource
    private RadioBroadcastClient radioBroadcastClient;

    @Cached(name = "app::radio::getRadioList", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public List<RadioBroadcastDTO> getRadioList(Integer source) {
        return radioBroadcastClient.getList(source).orElse(Collections.emptyList());
    }

    @Cached(name = "app::radio::getRadioById", cacheType = CacheType.BOTH, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 10, stopRefreshAfterLastAccess = 5 * 60, refreshLockTimeout = 60)
    public RadioBroadcastDTO getRadioById(Long id) {
        return radioBroadcastClient.getRadioById(id).orElse(null);
    }
}
