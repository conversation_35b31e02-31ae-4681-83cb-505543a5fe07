package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.app.be.client.MediaResourceRewardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/3/23
 */
@Slf4j
@Component
public class MediaResourceRewardManager {
    @Resource
    private MediaResourceRewardService mediaResourceRewardService;

    @Cached(name = "app::media:reward::amount", cacheType = CacheType.BOTH, expire = 60)
    public BigDecimal rewardAmount(Long mediaId) {
        return Objects.isNull(mediaId) ? BigDecimal.ZERO : mediaResourceRewardService.rewardAmount(mediaId).orElse(BigDecimal.ZERO);
    }
}
