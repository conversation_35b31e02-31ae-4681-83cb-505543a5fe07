package com.jxntv.gvideo.app.be.manager;

import com.alicp.jetcache.anno.CacheRefresh;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.om.client.AdvertClient;
import com.jxntv.gvideo.om.dto.advert.AdvertContentDTO;
import com.jxntv.gvideo.om.dto.advert.AdvertContentSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class AdvertManager {

    @Resource
    private AdvertClient advertClient;

    @Cached(name = "app::advert::content::page", cacheType = CacheType.LOCAL, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    public PageDTO<AdvertContentDTO> advertContentPage(AdvertContentSearchDTO searchDTO) {
        return advertClient.advertContentPage(searchDTO).orElse(null);
    }
    @Cached(name = "app::advert::content::list", cacheType = CacheType.LOCAL, expire = 10, timeUnit = TimeUnit.MINUTES)
    @CacheRefresh(refresh = 1, stopRefreshAfterLastAccess = 5, timeUnit = TimeUnit.MINUTES)
    public List<AdvertContentDTO> getAdvertList(Integer showIndex, Integer locationType, Long locationId) {
        AdvertContentSearchDTO searchDTO = new AdvertContentSearchDTO();
        searchDTO.setShowIndex(showIndex);
        searchDTO.setLocationType(locationType);
        searchDTO.setLocationId(locationId);
        searchDTO.setCurrent(1);
        searchDTO.setSize(50);
        return advertClient.advertContentPage(searchDTO).map(PageDTO::getList).orElse(Collections.emptyList());
    }
}
