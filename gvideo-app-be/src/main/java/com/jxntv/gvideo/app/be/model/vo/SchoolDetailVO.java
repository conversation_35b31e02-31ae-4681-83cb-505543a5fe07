package com.jxntv.gvideo.app.be.model.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2022-06-14 8:45
 * @description
 */
@Data
public class SchoolDetailVO {

    private Long id;

    /**
     * 学校唯一ID
     */
    private Long schoolCode;

    /**
     * 学校名字
     */
    private String schoolName;

    /**
     * 校区
     */
    private String schoolArea;

    /**
     * 年级
     */
    private Integer schoolGrade;

    /**
     * 班级
     */
    private Integer schoolClass;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区县
     */
    private String county;

    /**
     * 逻辑删除，0-没删除，1-删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 校区，年级，班级，数据处理
     */
    private Map<String, Map<Integer, List<Integer>>> schoolAreaGradeClassList;


}
