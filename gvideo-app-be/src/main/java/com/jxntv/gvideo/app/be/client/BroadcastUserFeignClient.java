package com.jxntv.gvideo.app.be.client;

import com.jxntv.gvideo.app.be.client.fallback.BroadcastUserClientFallback;
import com.jxntv.gvideo.group.sdk.client.CityLoveBroadcastUserClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2021/5/12.
 */
@FeignClient(name = "group-service", contextId = "broadcast-user", fallbackFactory = BroadcastUserClientFallback.class)
public interface BroadcastUserFeignClient extends CityLoveBroadcastUserClient {
}
