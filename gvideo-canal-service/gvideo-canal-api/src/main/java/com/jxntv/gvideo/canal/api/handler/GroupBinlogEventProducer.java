package com.jxntv.gvideo.canal.api.handler;

import com.jxntv.gvideo.canal.api.disruptor.MetaEventHandler;
import com.jxntv.gvideo.canal.api.model.MetaEvent;
import com.jxntv.gvideo.canal.api.utils.JsonUtils;
import com.jxntv.gvideo.canal.client.constants.Topics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class GroupBinlogEventProducer implements MetaEventHandler {

    @Resource
    private KafkaTemplate<String, Object> kafkaTemplate;


    @Override
    public void onEvent(MetaEvent event, long l, boolean b) throws Exception {
        if ("group_info".equals(event.getMeta().getTable())) {
            event.getValues().forEach(value -> {
                try {
                    String json = JsonUtils.toJson(value.getValue());
                    kafkaTemplate.send(Topics.GROUP_TOPIC, json);
                    log.info("send group_info event {}", json);
                } catch (Exception e) {
                    log.error("发送异常", e);
                }
            });
        }
    }

}
