package com.jxntv.gvideo.canal.api.disruptor;

import com.jxntv.gvideo.canal.api.core.BinlogParser;
import com.jxntv.gvideo.canal.api.model.MetaEvent;
import com.jxntv.gvideo.canal.api.model.BinlogEvent;
import com.lmax.disruptor.WorkHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * 解析工作处理器
 */
@Slf4j
public class ParseWorkHandler implements WorkHandler<MetaEvent> {

    private final BinlogParser binlogParser;

    public ParseWorkHandler(BinlogParser binlogParser) {
        this.binlogParser = binlogParser;
    }

    @Override
    public void onEvent(MetaEvent event) throws Exception {

        //  解析binlog事件
        List<BinlogEvent> binlogEvents = binlogParser.parse(event);
        //  包装values
        if (CollectionUtils.isEmpty(binlogEvents)) {
            event.setValues(Collections.emptyList());
        } else {
            event.setValues(binlogEvents);
        }

    }
}
