package com.jxntv.gvideo.canal.api.core;

import com.github.shyiko.mysql.binlog.event.*;
import com.jxntv.gvideo.canal.api.model.BinlogEvent;
import com.jxntv.gvideo.canal.api.model.Column;
import com.jxntv.gvideo.canal.api.model.MetaEvent;
import com.jxntv.gvideo.canal.api.model.Table;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.*;

@Slf4j
public class MySQLBinlogParser implements BinlogParser {

    private final BinlogDataStructureManager dataStructureManager;

    public MySQLBinlogParser(BinlogDataStructureManager dataStructureManager) {
        this.dataStructureManager = dataStructureManager;
    }

    @Override
    public List<BinlogEvent> parse(MetaEvent event) {
        TableMapEventData meta = event.getMeta();
        EventData data = event.getData();
        String database = meta.getDatabase();
        String table = meta.getTable();
        long tableId = meta.getTableId();

        Table structure = dataStructureManager.getStructure(database, table, tableId);
        if (Objects.nonNull(structure)) {
            if (data instanceof WriteRowsEventData) {
                WriteRowsEventData eventData = (WriteRowsEventData) data;
                return this.parse(eventData, structure.getDatabase(), structure.getTable(), structure.getColumns());
            }

            if (data instanceof UpdateRowsEventData) {
                UpdateRowsEventData eventData = (UpdateRowsEventData) data;
                return this.parse(eventData, structure.getDatabase(), structure.getTable(), structure.getColumns());
            }

            if (data instanceof DeleteRowsEventData) {
                DeleteRowsEventData eventData = (DeleteRowsEventData) data;
                return this.parse(eventData, structure.getDatabase(), structure.getTable(), structure.getColumns());
            }

        }

        return Collections.emptyList();
    }


    public List<BinlogEvent> parse(WriteRowsEventData writeRowsEventData, String database, String table, List<Column> columns) {

        List<BinlogEvent> result = new ArrayList<>();

        List<Serializable[]> rows = writeRowsEventData.getRows();

        try {
            rows.forEach(row -> {
                Map<String, Object> afterMap = new HashMap<>();
                columns.forEach(col -> afterMap.put(underlineToCamel(col.getName()), row[col.getPosition() - 1]));

                BinlogEvent event = new BinlogEvent();
                event.setEventType(BinlogEventType.INSERT);
                event.setDatabase(database);
                event.setTable(table);
                event.setAfter(afterMap);

                result.add(event);
            });
        } catch (Exception e) {
            log.error("解析binlog event 错误：", e);
        }


        return result;
    }


    public List<BinlogEvent> parse(UpdateRowsEventData updateRowsEventData, String database, String table, List<Column> columns) {
        List<BinlogEvent> result = new ArrayList<>();

        List<Map.Entry<Serializable[], Serializable[]>> rows = updateRowsEventData.getRows();
        try {
            rows.forEach(row -> {
                Map<String, Object> afterMap = new HashMap<>();
                Map<String, Object> beforeMap = new HashMap<>();

                Serializable[] beforeRow = row.getKey();
                Serializable[] afterRow = row.getValue();

                columns.forEach(col -> beforeMap.put(underlineToCamel(col.getName()), beforeRow[col.getPosition() - 1]));

                columns.forEach(col -> afterMap.put(underlineToCamel(col.getName()), afterRow[col.getPosition() - 1]));

                BinlogEvent event = new BinlogEvent();
                event.setEventType(BinlogEventType.UPDATE);
                event.setDatabase(database);
                event.setTable(table);
                event.setBefore(beforeMap);
                event.setAfter(afterMap);

                result.add(event);
            });
        } catch (Exception e) {
            log.error("解析binlog event 错误：", e);
        }

        return result;
    }


    public List<BinlogEvent> parse(DeleteRowsEventData deleteRowsEventData, String database, String table, List<Column> columns) {
        List<BinlogEvent> result = new ArrayList<>();

        List<Serializable[]> rows = deleteRowsEventData.getRows();
        try {
            rows.forEach(row -> {
                Map<String, Object> beforeMap = new HashMap<>();
                columns.forEach(col -> beforeMap.put(underlineToCamel(col.getName()), row[col.getPosition() - 1]));

                BinlogEvent event = new BinlogEvent();
                event.setEventType(BinlogEventType.DELETE);
                event.setDatabase(database);
                event.setTable(table);
                event.setBefore(beforeMap);
                result.add(event);
            });
        } catch (Exception e) {
            log.error("解析binlog event 错误：", e);
        }

        return result;
    }


    /**
     * 下划线，转驼峰命名
     *
     * @param columnName 列名
     * @return 驼峰命名
     */
    private String underlineToCamel(String columnName) {

        // 快速检查
        if (columnName == null || columnName.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!columnName.contains("_")) {
            // 不含下划线，仅将首字母小写
            return columnName.substring(0, 1).toLowerCase() + columnName.substring(1);
        }

        StringBuilder result = new StringBuilder();
        // 用下划线将原始字符串分割
        String[] camels = columnName.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 处理真正的驼峰片段
            if (result.length() == 0) {
                // 第一个驼峰片段，全部字母都小写
                result.append(camel.toLowerCase());
            } else {
                // 其他的驼峰片段，首字母大写
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

}
