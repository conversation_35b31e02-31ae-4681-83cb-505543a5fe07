package com.jxntv.gvideo.canal.client.dto;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;

import java.io.Serializable;

public interface IBinlogEvent<T> extends Serializable {

    /**
     * binlog日志类型：增、删、改
     */
    BinlogEventType getEventType();

    /**
     * @return 操作前的值
     */
    T getBefore();

    /**
     * @return 操作后的值
     */

    T getAfter();

    /**
     * @return 返回当前的value
     */
    default T getValue() {
        return BinlogEventType.DELETE.equals(getEventType()) ? getBefore() : getAfter();
    }


}
