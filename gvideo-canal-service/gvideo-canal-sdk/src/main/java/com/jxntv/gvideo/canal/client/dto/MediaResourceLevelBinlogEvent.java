package com.jxntv.gvideo.canal.client.dto;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;

import java.util.Date;


public class MediaResourceLevelBinlogEvent implements IBinlogEvent<MediaResourceLevelBinlogEvent.EventBody> {

    private BinlogEventType eventType;
    private EventBody before;
    private EventBody after;


    public static class EventBody {
        private Long resourceId;
        private String level;
        private Date updateTime;

        public Long getResourceId() {
            return resourceId;
        }

        public void setResourceId(Long resourceId) {
            this.resourceId = resourceId;
        }

        public String getLevel() {
            return level;
        }

        public void setLevel(String level) {
            this.level = level;
        }

        public Date getUpdateTime() {
            return updateTime;
        }

        public void setUpdateTime(Date updateTime) {
            this.updateTime = updateTime;
        }
    }


    @Override
    public BinlogEventType getEventType() {
        return eventType;
    }

    public void setEventType(BinlogEventType eventType) {
        this.eventType = eventType;
    }

    @Override
    public EventBody getBefore() {
        return before;
    }

    public void setBefore(EventBody before) {
        this.before = before;
    }

    @Override
    public EventBody getAfter() {
        return after;
    }

    public void setAfter(EventBody after) {
        this.after = after;
    }
}
