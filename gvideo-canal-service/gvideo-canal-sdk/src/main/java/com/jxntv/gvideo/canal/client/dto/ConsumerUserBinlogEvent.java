package com.jxntv.gvideo.canal.client.dto;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;

/**
 * UGC用户binlog事件
 */
public class ConsumerUserBinlogEvent implements IBinlogEvent<ConsumerUserBinlogEvent.EventValue> {
    /**
     * 变更类型
     */
    private BinlogEventType eventType;
    /**
     * 变更前
     */
    private EventValue after;
    /**
     * 变更后
     */
    private EventValue before;


    @Override
    public BinlogEventType getEventType() {
        return eventType;
    }

    public void setEventType(BinlogEventType eventType) {
        this.eventType = eventType;
    }

    @Override
    public EventValue getAfter() {
        return after;
    }

    public void setAfter(EventValue after) {
        this.after = after;
    }

    @Override
    public EventValue getBefore() {
        return before;
    }

    public void setBefore(EventValue before) {
        this.before = before;
    }

    public static class EventValue {

        /**
         * 媒体资源id
         */
        private Long jid;
        /**
         * 是否启用
         */
        private Integer status;
        /**
         * 是否删除
         */
        private Integer delFlag;


        public Long getJid() {
            return jid;
        }

        public void setJid(Long jid) {
            this.jid = jid;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getDelFlag() {
            return delFlag;
        }

        public void setDelFlag(Integer delFlag) {
            this.delFlag = delFlag;
        }
    }

}
