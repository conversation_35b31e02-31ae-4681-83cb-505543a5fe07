package com.jxntv.gvideo.canal.client.dto;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;

/**
 * 社区binlog事件
 */
public class GroupBinlogEvent {
    /**
     * 变更类型
     */
    private BinlogEventType eventType;

    /**
     * 媒体资源id
     */
    private Long id;

    /**
     * 圈子状态 1启用0禁用
     */
    private Integer status;

    public BinlogEventType getEventType() {
        return eventType;
    }

    public void setEventType(BinlogEventType eventType) {
        this.eventType = eventType;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "GroupBinlogEvent{" +
                "eventType=" + eventType +
                ", id=" + id +
                ", status=" + status +
                '}';
    }
}
