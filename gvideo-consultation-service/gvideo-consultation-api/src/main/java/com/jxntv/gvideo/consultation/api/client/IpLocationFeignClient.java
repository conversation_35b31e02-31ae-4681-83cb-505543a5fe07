package com.jxntv.gvideo.consultation.api.client;

import com.jxntv.gvideo.aliyun.sdk.IpLocationClient;
import com.jxntv.gvideo.consultation.api.client.fallback.IpLocationClientFallback;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "aliyun-service", contextId = "ip-location", fallbackFactory = IpLocationClientFallback.class)
public interface IpLocationFeignClient extends IpLocationClient {
}
