package com.jxntv.gvideo.consultation.api.vo.file;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 *
 * Created on 2020-02-25
 */
@Data
@ApiModel(value="OssFileUploadVO", description="oss文件上传对象")
public class OssFileUploadVO {

    @ApiModelProperty(value = "文件类型", required = true)
    @NotNull(message = "文件类型不能为空")
    private Integer bizType;
    @ApiModelProperty(value = "base64Str", required = true)
    @NotEmpty(message ="上传图片不能为空")
    private String base64Str;

}
