package com.jxntv.gvideo.consultation.api.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.consultation.api.client.ConsumerUserService;
import com.jxntv.gvideo.consultation.api.entity.SysUser;
import com.jxntv.gvideo.consultation.api.service.wx.WxUserService;
import com.jxntv.gvideo.consultation.api.utils.LiveSdk;
import com.jxntv.gvideo.media.client.enums.PlatformEnum;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 小程序用户
 * <AUTHOR>
 * @date 2022/12/1 16:03
 */
@Slf4j
@Component
public class SysUserJob {

    @Resource
    private WxUserService wxUserService;

    @Resource
    private ConsumerUserService consumerUserClient;
    /**
     * 同步今视频jid
     */
    @XxlJob("processSysUserJidJob")
    public ReturnT<String> processSysUserJidJob() {
        log.info("【小程序用户】 同步今视频jid");
        LambdaQueryWrapper<SysUser> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.isNotNull(SysUser::getMobile);
        lambdaQuery.isNull(SysUser::getJid);
        lambdaQuery.orderByAsc(SysUser::getId);
        IPage<SysUser> page = this.wxUserService.page(new Page<>(1, 10), lambdaQuery);
        if (!CollectionUtils.isEmpty(page.getRecords())){
            page.getRecords().forEach(e ->{
                // 创建今视频用户
                ConsumerUserDTO consumerUserDTO = consumerUserClient.getUserByMobile(e.getMobile()).orElse(null);
                log.info("同步今视频jid,mobile:{},consumerUserDTO:{}",e.getMobile(), JsonUtils.toJson(consumerUserDTO));
                if (Objects.isNull(consumerUserDTO)) {
                    // 注册用户
                    Result<ConsumerUserDTO> newUserResp = consumerUserClient.addUser(e.getMobile(), "+86", e.getOpenId(), PlatformEnum.APPLET.getCode());
                    ConsumerUserDTO newUser = newUserResp.getResult();
                    if (Objects.isNull(newUser)) {
                        return;
                    }
                    //  注册IM账号
                    LiveSdk.registerIM(String.valueOf(newUser.getJid()));
                    e.setJid(newUser.getJid());
                }else{
                    e.setJid(consumerUserDTO.getJid());
                }
                log.info("同步今视频jid,mobile:{},jid:{}",e.getMobile(),e.getJid());
                e.setUpdateTime(new Date());
                this.wxUserService.updateById(e);
            });
        }

        return ReturnT.SUCCESS;
    }

}
