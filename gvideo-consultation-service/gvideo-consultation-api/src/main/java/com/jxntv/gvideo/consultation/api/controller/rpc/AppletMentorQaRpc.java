package com.jxntv.gvideo.consultation.api.controller.rpc;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.consulatation.api.client.AppletMentorQaClient;
import com.jxntv.gvideo.consulatation.api.dto.AppletMentorQaAnswerTimesDTO;
import com.jxntv.gvideo.consulatation.api.dto.MentorQaDTO;
import com.jxntv.gvideo.consulatation.api.param.QueryMentorQaParam;
import com.jxntv.gvideo.consultation.api.service.mgr.MentorQaService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 导师问答
 *
 * <AUTHOR>
 * @date 2022/11/29 15:12
 */
@RestController
@Api(value = "管理后台课程接口")
public class AppletMentorQaRpc implements AppletMentorQaClient {

    @Resource
    private MentorQaService mentorQaService;


    @Override
    public Result<Boolean> rollbackAnswerTimes(AppletMentorQaAnswerTimesDTO dto) {
        return Result.ok(mentorQaService.rollbackAnswerTimes(dto));
    }

    @Override
    public Result<PageDTO<MentorQaDTO>> page(QueryMentorQaParam param) {
        return mentorQaService.page(param);
    }

    @Override
    public Result<MentorQaDTO> getMentorById(Long id) {
        return mentorQaService.getMentorById(id);
    }

    @Override
    public Result<Long> add(MentorQaDTO mentorQaDTO) {
        return mentorQaService.add(mentorQaDTO);
    }

    @Override
    public Result<Long> delete(Long id) {
        return mentorQaService.delete(id);
    }

    @Override
    public Result<Long> edit(Long id, MentorQaDTO mentorQaDTO) {
        return mentorQaService.edit(id, mentorQaDTO);
    }

}
