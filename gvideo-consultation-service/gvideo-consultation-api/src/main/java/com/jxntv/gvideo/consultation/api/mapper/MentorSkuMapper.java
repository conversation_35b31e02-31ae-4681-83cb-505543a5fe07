package com.jxntv.gvideo.consultation.api.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jxntv.gvideo.consultation.api.entity.MentorSku;
import com.jxntv.gvideo.consultation.api.param.QueryMentorParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MentorSkuMapper extends BaseMapper<MentorSku> {
    int updateBatch(List<MentorSku> list);

    int updateBatchSelective(List<MentorSku> list);

    int batchInsert(@Param("list") List<MentorSku> list);

    int insertOrUpdate(MentorSku record);

    int insertOrUpdateSelective(MentorSku record);

    /**
     * 查询导师信息
     * @param param
     * @return
     */
    List<MentorSku> queryMentors(QueryMentorParam param);

    /**
     * 查询导师信息
     * @param param
     * @return
     */
    List<MentorSku> queryConsultMentors(QueryMentorParam param);
}
