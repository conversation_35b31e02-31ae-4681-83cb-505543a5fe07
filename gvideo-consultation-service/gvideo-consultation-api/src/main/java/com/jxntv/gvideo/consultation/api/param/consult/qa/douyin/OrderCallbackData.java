package com.jxntv.gvideo.consultation.api.param.consult.qa.douyin;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: niedamin
 * @Date: 2022/11/02 11:08
 */
@Data
public class OrderCallbackData {

    @ApiModelProperty("当前交易发起的小程序id")
    @JSONField(name = "appid")
    private String appid;
    @ApiModelProperty("开发者侧的订单号")
    @JSONField(name = "cp_orderno")
    private String cpOrderno;
    @ApiModelProperty("预下单时开发者传入字段")
    @JSONField(name = "cp_extra")
    private String cpExtra;
    @ApiModelProperty("way 字段中标识了支付渠道： 1-微信支付，2-支付宝支付，10-抖音支付")
    @JSONField(name = "way")
    private String way;
    @ApiModelProperty("支付渠道侧单号")
    @JSONField(name = "channel_no")
    private String channelNo;
    @ApiModelProperty("支付渠道侧PC单号，支付页面可见")
    @JSONField(name = "payment_order_no")
    private String paymentOrderNo;
    @ApiModelProperty("支付金额，单位为分")
    @JSONField(name = "total_amount")
    private Long totalAmount;
    @ApiModelProperty("固定SUCCESS")
    @JSONField(name = "status")
    private String status;
    @ApiModelProperty("订单来源视频对应视频 id")
    @JSONField(name = "item_id")
    private String itemId;
    @ApiModelProperty("该笔交易卖家商户号")
    @JSONField(name = "seller_uid")
    private String sellerUid;
    @ApiModelProperty("支付时间，Unix 时间戳，10 位，整型数")
    @JSONField(name = "paid_at")
    private Long paidAt;
    @ApiModelProperty("抖音侧订单号")
    @JSONField(name = "order_id")
    private String orderId;

}
