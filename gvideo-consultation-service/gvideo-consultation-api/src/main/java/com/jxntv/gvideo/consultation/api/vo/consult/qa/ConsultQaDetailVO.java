package com.jxntv.gvideo.consultation.api.vo.consult.qa;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ConsultDetailVO", description = "咨询详细信息")
public class ConsultQaDetailVO {

    @ApiModelProperty(value = "咨询资源ID")
    private Long id;

    @ApiModelProperty(value = "资源标题")
    private String title;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "资源封面", required = true, example = "http://xxx.jpg")
    private String coverUrl;
    @ApiModelProperty(value = "资源封面", required = true, example = "http://xxx.jpg")
    private ImageVO coverImage;

    @ApiModelProperty(value = "缩略图片链接")
    private List<String> imageUrls;

    @ApiModelProperty(value = "缩略图片链接")
    private List<ImageVO> imageList;

    @ApiModelProperty(value = "源图片链接")
    private List<String> oriUrls;

    @ApiModelProperty(value = "资源链接集合", required = true)
    private List<String> mediaUrls;

    @ApiModelProperty(value = "语音文字")
    private String soundContent;

    @ApiModelProperty(value = "媒体长度")
    private String length;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @ApiModelProperty(value = "导师回答列表")
    private List<CommentVO> mentorCommentList;

    @ApiModelProperty(value = "是否追问： 0-否 1-是")
    private Integer isQuestionClosely;

    @ApiModelProperty(value = "追问信息")
    private ReplyVO questionClosely;

    @ApiModelProperty(value = "追问回复信息")
    private ReplyVO questionCloselyAnswer;

    @ApiModelProperty(value = "导师信息")
    private MentorInfo mentorInfo;

    @ApiModelProperty(value = "订单是否退款 0-未退款 1-已退款")
    private Integer isRefund;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MentorInfo {

        @ApiModelProperty("导师jid")
        private Long jid;

        @ApiModelProperty("导师名称")
        private String nickname;

        @ApiModelProperty("导师头像")
        private String avatar;
    }
}
