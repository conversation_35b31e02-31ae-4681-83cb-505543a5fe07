package com.jxntv.gvideo.consultation.api.param.user.douyin;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: niedamin
 * @Date: 2022/11/01 10:40
 */
@Data
@ApiModel("Code2SessionParam")
public class Code2SessionParam {

    @ApiModelProperty("小程序ID")
    @JSONField(name = "appid")
    private String appId;

    @ApiModelProperty("小程序的 APP Secret")
    @JSONField(name = "secret")
    private String secret;

    @ApiModelProperty("login 接口返回的登录凭证")
    @JSONField(name = "code")
    private String code;

    @ApiModelProperty("login 接口返回的匿名登录凭证")
    @JSONField(name = "anonymous_code")
    private String anonymousCode;

}
