package com.jxntv.gvideo.consultation.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: niedamin
 * @Date: 2022/11/29 11:09
 */
@Data
@TableName(value = "mentor")
public class Mentor {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 导师id
     */
    @TableField(value = "mentor_jid")
    @ApiModelProperty(value="导师id")
    private Long mentorJid;

    /**
     * 导师名字
     */
    @TableField(value = "mentor_name")
    @ApiModelProperty(value="导师名字")
    private String mentorName;

    /**
     * 导师头像
     */
    @TableField(value = "mentor_avatar")
    @ApiModelProperty(value="导师头像")
    private String mentorAvatar;

    /**
     * 简介描述
     */
    @TableField(value = "intro")
    @ApiModelProperty(value="简介描述")
    private String intro;

    /**
     * 擅长领域,逗号分隔
     */
    @TableField(value = "expertise")
    @ApiModelProperty(value="擅长领域,逗号分隔")
    private String expertise;

    /**
     * 标签,逗号分隔
     */
    @TableField(value = "tags")
    @ApiModelProperty(value="标签,逗号分隔")
    private String tags;

    /**
     * 级别
     */
    @TableField(value = "`level`")
    @ApiModelProperty(value="级别")
    private Integer level;

    /**
     * 手机号
     */
    @TableField(value = "`mobile`")
    @ApiModelProperty(value="手机号")
    private String mobile;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    @ApiModelProperty(value="更新时间")
    private LocalDateTime updateTime;
}
