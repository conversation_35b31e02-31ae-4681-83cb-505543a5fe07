package com.jxntv.gvideo.consultation.api.convert;

import com.jxntv.gvideo.consulatation.api.dto.MentorQaDTO;
import com.jxntv.gvideo.consultation.api.param.ConsultQaCostDTO;
import com.jxntv.gvideo.consultation.api.vo.MentorInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MentorInfoConverter {

    public MentorInfoVO convert(MentorQaDTO dto, ConsultQaCostDTO consultQaCostDTO) {
        if (Objects.isNull(dto)){
            return null;
        }
        MentorInfoVO  vo = new MentorInfoVO();
        vo.setJid(dto.getMentorJid());
        vo.setNickname(dto.getMentorName());
        vo.setAvatar(dto.getMentorAvatar());
        vo.setRemainder(dto.getAnswerTimes() - dto.getUsedAnswerTimes());
        if (StringUtils.hasText(dto.getExpertise())){
            vo.setAreasOfExpertise(Arrays.asList(dto.getExpertise().split(",")));
        }
        vo.setIntroduction(dto.getIntro());
        if (Objects.nonNull(consultQaCostDTO)){
            vo.setIsNewUser(consultQaCostDTO.getIsNewUser());
            vo.setOriginalCost(consultQaCostDTO.getOriginalCost());
            if (Boolean.TRUE.equals(consultQaCostDTO.getIsNewUser())){
                vo.setDiscountCost(consultQaCostDTO.getNewUserCost());
                vo.setDiscount(consultQaCostDTO.getNewUserCost().divide(consultQaCostDTO.getOriginalCost(),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.TEN).setScale(1,BigDecimal.ROUND_HALF_UP));
            }
            if (Boolean.TRUE.equals(consultQaCostDTO.getIsOldUserDiscount())){
                vo.setDiscountCost(consultQaCostDTO.getDiscountCost());
                vo.setDiscount(consultQaCostDTO.getDiscountCost().divide(consultQaCostDTO.getOriginalCost(),2,BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.TEN).setScale(1,BigDecimal.ROUND_HALF_UP));
            }

        }

        return vo;
    }
}
