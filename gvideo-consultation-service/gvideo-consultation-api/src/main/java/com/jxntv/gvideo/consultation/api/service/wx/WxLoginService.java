package com.jxntv.gvideo.consultation.api.service.wx;

import com.jxntv.gvideo.consultation.api.param.LowVersionBindMobileParam;
import com.jxntv.gvideo.consultation.api.param.UpdateUserParam;
import com.jxntv.gvideo.consultation.api.supports.R;

import javax.servlet.http.HttpServletRequest;

/**
 *  微信登录接口
 *  <AUTHOR>
 *  @date 2022/6/23 16:27
 */
public interface WxLoginService {

    /**
     * 小程序登录接口
     * @param code
     * @return
     */
    R login(HttpServletRequest request,String code);

    /**
     * 更新微信用户信息，返回调用接口的token
     * @param param
     * @return
     */
    R upUserInfo(HttpServletRequest request, UpdateUserParam param);

    /**
     * 绑定用户手机号
     * @param token
     * @return
     */
    R bindMobile(String token);

    /**
     * 低版本微信客户端绑定手机号
     * @param param
     * @return
     */
    R lowVersionBindMobile(LowVersionBindMobileParam param);

    /**
     * 绑定用户手机号
     * @param token
     * @return
     */
    R bindMobileV2(String token);

    /**
     * 低版本微信客户端绑定手机号
     * @param param
     * @return
     */
    R lowVersionBindMobileV2(LowVersionBindMobileParam param);
}
