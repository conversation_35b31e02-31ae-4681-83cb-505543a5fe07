package com.jxntv.gvideo.consultation.api.param.consult.qa.douyin;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: niedamin
 * @Date: 2022/11/01 14:48
 */
@Data
public class OrderCallBackParam {

    @ApiModelProperty("Unix 时间戳，10 位，整型数")
    @JSONField(name = "timestamp")
    private Long timestamp;

    @ApiModelProperty("随机数")
    @JSONField(name = "nonce")
    private String nonce;

    @ApiModelProperty("订单信息的 json 字符串")
    @JSONField(name = "msg")
    private String msg;

    @ApiModelProperty("回调类型标记，支付成功回调为payment")
    @JSONField(name = "type")
    private String type;

    @ApiModelProperty("签名")
    @JSONField(name = "msg_signature")
    private String msg_signature;
}
