package com.jxntv.gvideo.consultation.api.service.qa;


import com.jxntv.gvideo.consultation.api.param.ConsultQaCostDTO;
import com.jxntv.gvideo.consultation.api.supports.Page;
import com.jxntv.gvideo.consultation.api.supports.R;
import com.jxntv.gvideo.consultation.api.vo.consult.qa.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ConsultQaService {

    /**
     * 查询是否为新用户
     *
     * @return
     */
    Boolean isNewUser();

    /**
     * 查询咨询费用配置
     *
     * @return
     */
    ConsultQaCostDTO cost();

    /**
     * 查询导师咨询费用（返回分）
     *
     * @return
     */
    BigDecimal consultCost(Long mentorJid);

    /**
     * 查询banner
     *
     * @return
     */
    R<String> banner();

    /**
     * 创建咨询信息
     *
     * @param vo
     * @return
     */
    R<Long> create(ConsultQaCreateVO vo);

    /**
     * 查询资源详情
     * @param mediaId
     * @return
     */
    R<ConsultQaDetailVO> get(Long mediaId);

    /**
     * 查询我的提问列表
     * @param pageNum
     * @param pageSize
     * @return
     */
    R<Page<ConsultQaInfoVO>> list(int pageNum, int pageSize);

    /**
     * 追问
     *
     * @param vo
     * @return
     */
    R<Long> questionClosely(QuestionQaCloselyVO vo);


    R<List<NoConsultInfoVO>> getNoConsultInfoList();

    /**
     * 查询未使用（提问）的订单ID（已支付完成）
     *
     * @return
     */
    R<ToBeUsedOrderVO> queryToBeUsedOrderId();

    /**
     * 查询未读回答
     * @return
     */
    R<AnswerNotReadPopVO> queryNoReadAnswer();
}
