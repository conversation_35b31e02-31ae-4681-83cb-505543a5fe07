package com.jxntv.gvideo.consultation.api.job;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jxntv.gvideo.consultation.api.entity.MentorQa;
import com.jxntv.gvideo.consultation.api.service.mgr.MentorQaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 导师提问次数重置任务
 * <AUTHOR>
 * @date 2022/11/29 16:03
 */
@Slf4j
@Component
public class MentorQaTimesJob {

    @Resource
    private MentorQaService mentorQaService;

    /**
     * 导师回答次数刷新任务
     * 每天0点刷新
     * 刷新流程，将所有导师的已回答次数重置为0
     */
    @XxlJob("resetMentorAnswerTimesJob")
    public ReturnT<String> resetMentorAnswerTimesJob() {
        log.info("【导师回答次数】 刷新开始");
        for (int i = 1; ; i++) {
            Page<MentorQa> page = mentorQaService.page(Page.of(i, 100));
            List<MentorQa> mentors = page.getRecords();
            if (CollectionUtils.isEmpty(mentors)) {
                break;
            }
            //  重置回答次数为0
            mentors.forEach(e -> e.setUsedAnswerTimes(0));
            //  保存重置结果
            mentorQaService.updateBatchById(mentors);
        }

        return ReturnT.SUCCESS;
    }

}
