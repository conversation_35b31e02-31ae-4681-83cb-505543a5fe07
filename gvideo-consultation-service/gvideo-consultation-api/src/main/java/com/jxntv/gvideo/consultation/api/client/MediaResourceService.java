package com.jxntv.gvideo.consultation.api.client;

import com.jxntv.gvideo.consultation.api.client.fallback.MediaResourceClientFallback;
import com.jxntv.gvideo.media.client.MediaResourceClient;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = "media-service", contextId = "media-resource", fallbackFactory = MediaResourceClientFallback.class)
public interface MediaResourceService extends MediaResourceClient {
}
