package com.jxntv.gvideo.consultation.api.param;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Data
public class ConsultQaCostDTO {

    /**
     * 是否新用户
     */
    private Boolean isNewUser;

    /**
     * 新用户提问价格
     */
    private BigDecimal newUserCost;

    /**
     * 提问原价
     */
    private BigDecimal originalCost;

    /**
     * 是否有老用户限时活动
     */
    private Boolean isOldUserDiscount;

    /**
     * 老用户活动价格
     */
    private BigDecimal discountCost;

    /**
     * 新用户优惠活动banner图
     */
    private String newUserBanner;

    /**
     * 老用户优惠活动banner图
     */
    private String discountBanner;


}