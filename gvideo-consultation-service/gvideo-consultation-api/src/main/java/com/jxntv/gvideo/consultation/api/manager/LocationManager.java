package com.jxntv.gvideo.consultation.api.manager;

import com.jxntv.gvideo.aliyun.sdk.IpLocationClient;
import com.jxntv.gvideo.aliyun.sdk.dto.IpLocationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

@Slf4j
@Component
public class LocationManager {

    @Resource
    private IpLocationClient ipLocationClient;

    /**
     * 通过IP查询地理位置
     *
     * @param ip 访问IP地址
     * @return IP所属位置
     */
    public String getAddress(String ip) {
        IpLocationDTO dto = ipLocationClient.getLocation(ip).orElse(null);
        return Objects.isNull(dto) ? "" : dto.getCountry() + "-" + dto.getRegion() + "-" + dto.getCity();
    }

    /**
     * 通过ip查询所处城市
     *
     * @param ip 访问IP地址
     * @return IP所属城市
     */
    public String getCityFromIp(String ip) {
        IpLocationDTO dto = ipLocationClient.getLocation(ip).orElse(null);
        return Objects.isNull(dto) ? "" : StringUtils.hasText(dto.getCity()) ? dto.getCity() : dto.getCountry();
    }

}
