package com.jxntv.gvideo.consultation.api.controller.api.qa;

import com.jxntv.gvideo.consultation.api.param.consult.qa.douyin.*;
import com.jxntv.gvideo.consultation.api.service.douyin.DouyinQaPayService;
import com.jxntv.gvideo.consultation.api.supports.R;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * @Author: niedamin
 * @Date: 2022/11/01 15:14
 */
@RestController
@RequestMapping("/api/consult/qa/douyin")
@Api(value = "小程序-问答咨询-抖音", tags = {"小程序-问答咨询-抖音"})
@Slf4j
public class DouyinQaPayController {

    @Resource
    private DouyinQaPayService douyinQaPayService;

    @PostMapping("/order")
    public R createOrder(@RequestBody CreateDouyinOrderParam param) {
        return douyinQaPayService.createOrder(param);
    }

    @PostMapping("/pay/callback")
    public Map<String, Object> payCallBack(@RequestBody OrderCallBackParam param) {
        return douyinQaPayService.payCallBack(param);
    }

    @PostMapping("/refund/callback")
    public Map<String, Object> refundCallBack(@RequestBody RefundCallBackParam param) {
        return douyinQaPayService.refundCallBack(param);
    }

    @PostMapping("/settle/callback")
    public Map<String, Object> settleCallBack(@RequestBody SettleCallBackParam param) {
        return douyinQaPayService.settleCallBack(param);
    }

    @PostMapping("/order/push")
    public R pushOrder(@RequestBody PushCancelOrderParam param) {
        return douyinQaPayService.pushOrder(param);
    }

}
