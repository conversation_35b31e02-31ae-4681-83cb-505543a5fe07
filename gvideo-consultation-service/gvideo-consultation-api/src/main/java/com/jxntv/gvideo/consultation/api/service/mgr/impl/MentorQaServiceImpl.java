package com.jxntv.gvideo.consultation.api.service.mgr.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.common.model.SearchDTO;
import com.jxntv.gvideo.common.utils.AssertUtil;
import com.jxntv.gvideo.consulatation.api.dto.AppletMentorDTO;
import com.jxntv.gvideo.consulatation.api.dto.AppletMentorQaAnswerTimesDTO;
import com.jxntv.gvideo.consulatation.api.dto.MentorQaDTO;
import com.jxntv.gvideo.consulatation.api.param.QueryMentorQaParam;
import com.jxntv.gvideo.consultation.api.entity.MentorQa;
import com.jxntv.gvideo.consultation.api.mapper.MentorQaMapper;
import com.jxntv.gvideo.consultation.api.service.mgr.AppletMentorService;
import com.jxntv.gvideo.consultation.api.service.mgr.MentorQaService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 导师-1:1图文咨询业务实现类
 *
 * <AUTHOR>
 * @date 2022-11-29 14:50:23
 */
@Service("mentorQaService")
public class MentorQaServiceImpl extends ServiceImpl<MentorQaMapper, MentorQa> implements MentorQaService {

    @Resource
    private AppletMentorService appletMentorService;


    @Override
    public Boolean rollbackAnswerTimes(AppletMentorQaAnswerTimesDTO dto) {
        AssertUtil.notNull(dto.getMentorJid(), CodeMessage.BAD_REQUEST.getCode(), "导师jid不能为空");
        MentorQa mentorQa = this.getOne(Wrappers.<MentorQa>lambdaQuery().eq(MentorQa::getMentorJid, dto.getMentorJid()));
        if (Objects.nonNull(mentorQa)) {
            //  还原导师提问次数
            AssertUtil.assertOrThrow(mentorQa.getUsedAnswerTimes() >= 1, CodeMessage.BAD_REQUEST.getCode(), "导师已经提问次数异常");
            mentorQa.setUsedAnswerTimes(mentorQa.getUsedAnswerTimes() - 1);
            return this.updateById(mentorQa);
        }
        return true;
    }

    @Override
    public Boolean userAnswerTimes(AppletMentorQaAnswerTimesDTO dto) {
        AssertUtil.notNull(dto.getMentorJid(), CodeMessage.BAD_REQUEST.getCode(), "导师jid不能为空");
        MentorQa mentorQa = this.getOne(Wrappers.<MentorQa>lambdaQuery().eq(MentorQa::getMentorJid, dto.getMentorJid()));
        if (Objects.nonNull(mentorQa)) {
            //  扣除导师提问次数
            mentorQa.setUsedAnswerTimes(mentorQa.getUsedAnswerTimes() + 1);
            return this.updateById(mentorQa);
        }
        return true;
    }

    @Override
    public Integer getAnswerTimes(Long mentorJid) {
        AssertUtil.notNull(mentorJid, CodeMessage.BAD_REQUEST.getCode(), "导师jid不能为空");
        MentorQa mentorQa = this.getOne(Wrappers.<MentorQa>lambdaQuery().eq(MentorQa::getMentorJid, mentorJid).eq(MentorQa::getState, 1));
        AssertUtil.notNull(mentorQa, CodeMessage.BAD_REQUEST.getCode(), "导师服务配置为空不能为空");
        return mentorQa.getAnswerTimes() - mentorQa.getUsedAnswerTimes();
    }

    @Override
    public IPage<MentorQaDTO> pageMentor(SearchDTO dto) {
        return this.baseMapper.pageMentor(new Page<>(dto.getCurrent(), dto.getSize()));
    }

    @Override
    public MentorQaDTO queryByMentorJid(Long mentorJid) {
        return this.baseMapper.queryByMentorJid(mentorJid);

    }

    @Override
    public Result<PageDTO<MentorQaDTO>> page(QueryMentorQaParam param) {
        if (Objects.isNull(param.getPageNum()) || Objects.isNull(param.getPageSize())) {
            return Result.fail("分页参数不能为空");
        }
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        LambdaQueryWrapper<MentorQa> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotBlank(param.getMentorName()), MentorQa::getMentorName, param.getMentorName());
        queryWrapper.eq(Objects.nonNull(param.getPrice()), MentorQa::getPrice, param.getPrice());
        List<MentorQa> list = this.list(queryWrapper);
        PageInfo<MentorQa> pageInfo = PageInfo.of(list);
        PageDTO<MentorQaDTO> dto = new PageDTO<>();
        dto.setPageSize(pageInfo.getPageSize());
        dto.setPageNum(pageInfo.getPageNum());
        List<MentorQaDTO> resultList = list.stream().map(mentorQa -> {
            MentorQaDTO dto1 = new MentorQaDTO();
            BeanUtil.copyProperties(mentorQa, dto1);
            return dto1;
        }).collect(Collectors.toList());
        dto.setList(resultList);
        dto.setTotal((int) pageInfo.getTotal());
        return Result.ok(dto);
    }

    @Override
    public Result<Long> add(MentorQaDTO mentorQaDTO) {
        LambdaQueryWrapper<MentorQa> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(MentorQa::getMentorJid, mentorQaDTO.getMentorJid());
        if (Objects.nonNull(this.getOne(queryWrapper))) {
            return Result.fail("该导师已存在，请重新选择导师。");
        }
        MentorQa mentorQa = new MentorQa();
        BeanUtil.copyProperties(mentorQaDTO, mentorQa);
        mentorQa.setCreateTime(LocalDateTime.now());
        mentorQa.setUpdateTime(LocalDateTime.now());
        AppletMentorDTO mentor = appletMentorService.getMentorByJid(mentorQaDTO.getMentorJid());
        if (Objects.nonNull(mentor)) {
            mentorQa.setMentorName(mentor.getMentorName());
        }
        this.save(mentorQa);
        return Result.ok(mentorQa.getId());
    }

    @Override
    public Result<Long> delete(Long id) {
        this.removeById(id);
        return Result.ok(id);
    }

    @Override
    public Result<Long> edit(Long id, MentorQaDTO dto) {
        MentorQa mentorQa = this.getById(id);
        BeanUtil.copyProperties(dto, mentorQa);
        mentorQa.setId(id);
        this.saveOrUpdate(mentorQa);
        return Result.ok(id);
    }

    @Override
    public Result<MentorQaDTO> getMentorById(Long id) {
        MentorQa mentorQa = this.getById(id);
        if (Objects.isNull(mentorQa)) {
            return Result.fail("导师信息不存在");
        }
        MentorQaDTO dto = new MentorQaDTO();
        BeanUtil.copyProperties(mentorQa, dto);
        return Result.ok(dto);
    }
}