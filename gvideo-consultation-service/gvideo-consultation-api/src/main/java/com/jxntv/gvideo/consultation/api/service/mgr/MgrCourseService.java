package com.jxntv.gvideo.consultation.api.service.mgr;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.consulatation.api.dto.AppletCourseDTO;
import com.jxntv.gvideo.consulatation.api.param.QueryCourseParam;

/**
 * @Author: niedamin
 * @Date: 2022/11/21 15:49
 */
public interface MgrCourseService {

    Result<Long> createCourse(AppletCourseDTO dto);

    Result<Long> modifyCourse(Long id, AppletCourseDTO dto);

    Result<Long> deleteCourseById(Long id);

    Result<PageDTO<AppletCourseDTO>> queryCourse(QueryCourseParam param);

    Result<AppletCourseDTO> getCourseById(Long id);
}
