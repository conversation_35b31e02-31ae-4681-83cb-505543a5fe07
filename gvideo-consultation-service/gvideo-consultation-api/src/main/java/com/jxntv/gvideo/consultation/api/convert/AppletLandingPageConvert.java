package com.jxntv.gvideo.consultation.api.convert;

import com.jxntv.gvideo.consulatation.api.dto.AppletLandingPageDTO;
import com.jxntv.gvideo.consultation.api.entity.AppletLandingPage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * @Author: niedamin
 * @Date: 2022/12/15 11:08
 */
@Component
public class AppletLandingPageConvert {

    public AppletLandingPage convert(AppletLandingPageDTO dto) {
        AppletLandingPage page = new AppletLandingPage();
        page.setId(dto.getId());
        page.setLandingPageName(dto.getLandingPageName());
        page.setLandingPageImage(StringUtils.join(dto.getLandingPageImage(), ","));
        page.setJumpType(dto.getJumpType());
        page.setMentorJid(dto.getMentorJid());
        page.setDouyinShareId(dto.getDouyinShareId());
        page.setWeixinShareImage(dto.getWeixinShareImage());
        page.setMentorJid(dto.getMentorJid());
        page.setCreateTime(dto.getCreateTime());
        page.setUpdateTime(dto.getUpdateTime());
        return page;
    }

    public AppletLandingPageDTO convert(AppletLandingPage appletLandingPage) {
        AppletLandingPageDTO dto = new AppletLandingPageDTO();
        dto.setId(appletLandingPage.getId());
        dto.setLandingPageName(appletLandingPage.getLandingPageName());
        dto.setLandingPageImage(Arrays.asList(appletLandingPage.getLandingPageImage().split(",")));
        dto.setJumpType(appletLandingPage.getJumpType());
        dto.setMentorJid(appletLandingPage.getMentorJid());
        dto.setDouyinShareId(appletLandingPage.getDouyinShareId());
        dto.setWeixinShareImage(appletLandingPage.getWeixinShareImage());
        dto.setCreateTime(appletLandingPage.getCreateTime());
        dto.setUpdateTime(appletLandingPage.getUpdateTime());
        return dto;
    }

}
