package com.jxntv.gvideo.consultation.api.client;

import com.jxntv.gvideo.consultation.api.client.fallback.GroupContentClientFallback;
import com.jxntv.gvideo.group.sdk.client.GroupContentClient;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR> on 2021/4/30.
 */
@FeignClient(name = "group-service", contextId = "group-content", fallbackFactory = GroupContentClientFallback.class)
public interface GroupContentService extends GroupContentClient {
}
