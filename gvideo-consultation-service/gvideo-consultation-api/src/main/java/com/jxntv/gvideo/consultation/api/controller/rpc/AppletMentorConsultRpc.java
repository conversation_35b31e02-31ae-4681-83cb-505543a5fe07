package com.jxntv.gvideo.consultation.api.controller.rpc;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.consulatation.api.client.AppletMentorConsultClient;
import com.jxntv.gvideo.consulatation.api.dto.MentorConsultDTO;
import com.jxntv.gvideo.consulatation.api.param.QueryMentorQaParam;
import com.jxntv.gvideo.consultation.api.service.mgr.MentorConsultService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Author: niedamin
 * @Date: 2022/11/29 16:53
 */
@RestController
public class AppletMentorConsultRpc implements AppletMentorConsultClient {

    @Resource
    private MentorConsultService mentorConsultService;


    @Override
    public Result<PageDTO<MentorConsultDTO>> page(QueryMentorQaParam param) {
        return mentorConsultService.page(param);
    }

    @Override
    public Result<MentorConsultDTO> getMentorById(Long id) {
        return mentorConsultService.getMentorById(id);
    }

    @Override
    public Result<Long> add(MentorConsultDTO mentorConsultDTO) {
        return mentorConsultService.add(mentorConsultDTO);
    }

    @Override
    public Result<Long> delete(Long id) {
        return mentorConsultService.delete(id);
    }

    @Override
    public Result<Long> edit(Long id, MentorConsultDTO mentorConsultDTO) {
        return mentorConsultService.edit(id, mentorConsultDTO);
    }
}

