package com.jxntv.gvideo.consulatation.api.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 *  咨询订单
 *  <AUTHOR>
 *  @date 2022/6/27 10:56
 */
@Data
@EqualsAndHashCode
public class ConsultOrderDTO implements Serializable {

    /**
     * 主键
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 昵称
     */
    private String userNickName;

    /**
     * 头像地址
     */
    private String userAvatarUrl;

    /**
     * 用户手机号
     */
    private String userMobile;

    /**
     * 订单状态:10:待支付、20:待咨询、30:咨询中,40:已完成、50:已取消
     */
    private Integer state;

    /**
     * 订单金额
     */
    private String amount;

    /**
     * 咨询师id
     */
    private Long counselorId;

    /**
     * 咨询师名称
     */
    private String counselorName;

    /**
     * 咨询师头像
     */
    private String counselorAvatar;

    /**
     * 支付状态:1:未支付,2:一支付
     */
    private Integer payState;

    /**
     * 支付金额
     */
    private String payMoney;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 订单类型
     */
    private Integer orderType;

    private Integer platform;
}
