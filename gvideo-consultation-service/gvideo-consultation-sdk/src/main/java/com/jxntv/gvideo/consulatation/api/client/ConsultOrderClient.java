package com.jxntv.gvideo.consulatation.api.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.consulatation.api.constants.enums.FeignHeader;
import com.jxntv.gvideo.consulatation.api.dto.ConsultOrderDTO;
import com.jxntv.gvideo.consulatation.api.dto.OrderStatusDTO;
import com.jxntv.gvideo.consulatation.api.param.ChangeStatusParam;
import com.jxntv.gvideo.consulatation.api.param.QueryOrderCountParam;
import com.jxntv.gvideo.consulatation.api.param.QueryOrderParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 *  咨询订单接口
 *  <AUTHOR>
 *  @date 2022/6/27 11:05
 */
public interface ConsultOrderClient {

    String PREFIX = "/api/consult/order/";

    /**
     * 随机选题
     * @return
     */
    @PostMapping(value =PREFIX + "query",headers = {FeignHeader.FULL_HEADER})
    Result<PageDTO<ConsultOrderDTO>> queryOrder(@RequestBody QueryOrderParam param);


    /**
     * 改变订单状态
     * @param param
     * @return
     */
    @PostMapping(value=PREFIX + "start",headers = {FeignHeader.FULL_HEADER})
    Result changStatus(@RequestBody ChangeStatusParam param);


    /**
     * 订单状态枚举
     * @return
     */
    @PostMapping(value=PREFIX + "status",headers = {FeignHeader.FULL_HEADER})
    Result<List<OrderStatusDTO>> orderStatus();

    /**
     * 统计订单金额
     * @return
     */
    @PostMapping(value=PREFIX + "sumOrder",headers = {FeignHeader.FULL_HEADER})
    Result<String> sumOrderMoney();



    /**
     * 获取订单数
     * @return
     */
    @PostMapping(value =PREFIX + "count",headers = {FeignHeader.FULL_HEADER})
    Result<Integer> queryOrderCount(@RequestBody QueryOrderCountParam param);
}
