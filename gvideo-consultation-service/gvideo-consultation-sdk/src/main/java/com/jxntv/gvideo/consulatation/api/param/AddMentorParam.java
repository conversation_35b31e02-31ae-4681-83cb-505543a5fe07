package com.jxntv.gvideo.consulatation.api.param;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  增加导师入参
 *  <AUTHOR>
 *  @date 2022/7/12 11:07
 */
@Data
@EqualsAndHashCode
public class AddMentorParam implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 导师id
     */
    private Long mentorJid;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 简介描述
     */
    private String intro;

    /**
     * 擅长领域,逗号分隔
     */
    private String expertise;

    /**
     * 标签,逗号分隔
     */
    private String tags;

    /**
     * 定价
     */
    private BigDecimal price;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 服务描述
     */
    private String serviceDesc;

    /**
     * 级别
     */
    private Integer level;

}
