package com.jxntv.gvideo.search.api.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.api.service.PostIndexService;
import com.jxntv.gvideo.search.api.utils.PageUtils;
import com.jxntv.gvideo.search.client.PostSearchClient;
import com.jxntv.gvideo.search.client.dto.post.PostIndexDTO;
import com.jxntv.gvideo.search.client.dto.post.PostIndexSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class PostSearchController implements PostSearchClient {

    @Resource
    private PostIndexService postIndexService;

    @Override
    public Result<PageDTO<PostIndexDTO>> search(PostIndexSearchDTO searchDTO) {
        Page<PostIndexDTO> page = postIndexService.search(searchDTO);
        return Result.ok(PageUtils.pageOf(page, e -> e));
    }
}
