package com.jxntv.gvideo.search.api.event.binlog.listener;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.search.api.domain.MultiContent;
import com.jxntv.gvideo.search.api.event.binlog.MediaResourceEvent;
import com.jxntv.gvideo.search.api.repository.MultiContentRepository;
import com.jxntv.gvideo.search.client.enums.MultiContentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class NewsEventListener {

    private static final List<Integer> CONTENT_TYPES = Arrays.asList(ContentType.NEWS.getCode(), ContentType.GAN_YUN_VIDEO.getCode());

    @Resource
    private MultiContentRepository multiContentRepository;

    @Async
    @EventListener
    public void onEvent(MediaResourceEvent event) {
        //  处理新闻类型
        if (CONTENT_TYPES.contains(event.getContentType())) {
            //  删除事件
            if (BinlogEventType.DELETE.equals(event.getEventType())) {
                String id = MultiContentType.NEWS.name() + "-" + event.getId();
                multiContentRepository.deleteById(id);
                log.debug("删除新闻资源事件, resourceId:{}", event.getId());
            }
            //  保存事件
            else {
                String id = MultiContentType.NEWS.name() + "-" + event.getId();
                MultiContent entity = multiContentRepository.findById(id).orElse(new MultiContent());
                entity.setId(id);
                entity.setType(MultiContentType.NEWS);
                entity.setContentId(event.getId());
                entity.setTitle(event.getShowName());
                entity.setSingleTitle(event.getShowName());
                entity.setDescription(event.getIntroduction());
                entity.setPublishTime(event.getCreateDate());
                entity.setEnable(Objects.equals(event.getStatus(), MediaResourceStatus.ENABLE.getCode()));
                multiContentRepository.save(entity);
            }

        }
    }
}
