package com.jxntv.gvideo.search.api.event.listener;

import com.jxntv.gvideo.canal.client.dto.MediaResourceBinlogEvent;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.media.client.enums.ContentType;
import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.search.api.converter.NewsIndexConverter;
import com.jxntv.gvideo.search.api.domain.NewsIndex;
import com.jxntv.gvideo.search.api.event.MediaResourceBatchEvent;
import com.jxntv.gvideo.search.api.service.NewsIndexService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NewsIndexListener {

    @Resource
    private NewsIndexService newsIndexService;
    @Resource
    private NewsIndexConverter newsIndexConverter;

    @Async
    @EventListener
    public void onEvent(MediaResourceBatchEvent batchEvent) {
        Set<Integer> newsContentType = new HashSet<>(Arrays.asList(ContentType.NEWS.getCode(), ContentType.SPECIAL.getCode(), ContentType.GAN_YUN_VIDEO.getCode()));
        List<MediaResourceBinlogEvent> events = batchEvent.getEvents().stream().filter(e -> newsContentType.contains(e.getContentType())).collect(Collectors.toList());
        //  插入事件
        List<MediaResourceBinlogEvent> insertList = events.stream().filter(e ->Objects.equals(e.getStatus(), MediaResourceStatus.ENABLE.getCode()) && BinlogEventType.INSERT.equals(e.getEventType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertList)) {
            List<NewsIndex> collect = insertList.stream().map(newsIndexConverter::convert).filter(Objects::nonNull).collect(Collectors.toList());
            newsIndexService.save(collect);
        }

        //  更新事件
        List<MediaResourceBinlogEvent> updateList = events.stream().filter(e ->Objects.equals(e.getStatus(), MediaResourceStatus.ENABLE.getCode()) && BinlogEventType.UPDATE.equals(e.getEventType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateList)) {
            List<NewsIndex> collect = updateList.stream().map(newsIndexConverter::convert).filter(Objects::nonNull).collect(Collectors.toList());
            newsIndexService.save(collect);
        }

        //  删除事件
        List<MediaResourceBinlogEvent> deleteList = events.stream().filter(e -> !Objects.equals(MediaResourceStatus.ENABLE.getCode(), e.getStatus()) || BinlogEventType.DELETE.equals(e.getEventType())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteList)) {
            List<String> removeIds = deleteList.stream().map(e -> String.valueOf(e.getId())).collect(Collectors.toList());
            newsIndexService.removeByIds(removeIds);
        }
    }


}
