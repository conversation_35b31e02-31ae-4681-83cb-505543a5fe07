package com.jxntv.gvideo.search.api.common;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.exception.CodeMessageException;
import com.jxntv.gvideo.common.model.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * Created on 2020-02-04
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(CodeMessageException.class)
    public Result<?> service(CodeMessageException e) {
        return Result.fail(e.getCode(), e.getMessage());
    }


    @ExceptionHandler(Throwable.class)
    public Result<?> throwable(Throwable throwable) {
        log.error("error:", throwable);
        return Result.fail(CodeMessage.ERROR.getCode(), CodeMessage.ERROR.getMessage());
    }

    /**
     * 处理所有不可知的异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    Result<Void> handleException(MethodArgumentNotValidException e) {
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder s = new StringBuilder();
        for (FieldError fieldError : e.getBindingResult().getFieldErrors()) {
            stringBuilder.append(fieldError.getField()).append(":").append(fieldError.getDefaultMessage()).append("\n");
            s.append(fieldError.getDefaultMessage()).append(" ");
        }
        log.error(stringBuilder.toString());
        return Result.fail(s.substring(0, s.length() - 1));
    }
}
