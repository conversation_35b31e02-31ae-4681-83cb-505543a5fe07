package com.jxntv.gvideo.search.api.event.binlog;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TvProgramEvent implements Serializable {

    private BinlogEventType eventType;
    /**
     * 自增ID
     */
    private Long id;
    /**
     * 节目所属栏目id
     */
    private Long columnId;
    /**
     * 节目封面id(如果封面id不为空取id对应的阿里云资源，否则取coverUrl)
     */
    private String coverId;
    /**
     * 节目封面URL，兼容赣云数据
     */
    private String coverUrl;
    /**
     * 视频文件id（如果视频文件id不为空,取视频文件id对应的阿里云资源，否则取videoUrl）
     */
    private String videoId;
    /**
     * 电视节目播放URL，兼容赣云
     */
    private String videoUrl;
    /**
     * 数据来源类型，1-赣云新增数据，2-赣云修改数据
     */
    private Integer sourceType;
    /**
     * 数据来源id
     */
    private Long sourceId;


    /**
     * 播放时间
     */
    private LocalDateTime playTime;
    /**
     * 播放日期
     */
    private String playDate;
    /**
     * 第几季
     */
    private Integer season;
    /**
     * 第几集
     */
    private Integer episode;

    /**
     * 播放时长
     */
    private Integer duration;

    /**
     * 节目对应内容id
     */
    private Long mediaId;
    /**
     * 节目名称
     */
    private String programName;

    /**
     * 节目简介
     */
    private String introduction;

    /**
     * 节目状态，0-禁用，1-启用
     */
    private Integer status;

    /**
     * 入库时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;

}
