package com.jxntv.gvideo.search.api.converter;

import com.jxntv.gvideo.canal.client.dto.TvProgramBinlogEvent;
import com.jxntv.gvideo.media.client.TvProgramClient;
import com.jxntv.gvideo.media.client.dto.tv.TvProgramDTO;
import com.jxntv.gvideo.search.api.client.BigDataSearchClient;
import com.jxntv.gvideo.search.api.client.dto.CutWordReqDTO;
import com.jxntv.gvideo.search.api.client.dto.CutWordRespDTO;
import com.jxntv.gvideo.search.api.domain.ProgramIndex;
import com.jxntv.gvideo.search.client.dto.program.ProgramIndexDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ProgramIndexConverter {
    @Resource
    private BigDataSearchClient bigDataSearchClient;
    @Resource
    private TvProgramClient tvProgramClient;


    public ProgramIndexDTO convert(ProgramIndex entity) {
        ProgramIndexDTO dto = new ProgramIndexDTO();
        dto.setProgramId(Long.valueOf(entity.getId()));
        dto.setColumnId(entity.getSid());
        dto.setTokens(entity.getTokens());
        return dto;
    }

    public ProgramIndex convert(TvProgramBinlogEvent event) {
        TvProgramDTO dto = tvProgramClient.getById(event.getId()).orElse(null);
        if (Objects.nonNull(dto)) {
            String sentence = dto.getProgramName();
            List<String> tokens = bigDataSearchClient.cutWord(new CutWordReqDTO(sentence)).map(CutWordRespDTO::getTokens).orElse(Collections.singletonList(sentence));

            ProgramIndex index = new ProgramIndex();
            index.setId(String.valueOf(dto.getId()));
            index.setTokens(tokens);
            index.setSentence(sentence);
            index.setSid(dto.getColumnId());
            index.setTime(dto.getPlayTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            return index;
        }

        return null;
    }
}
