package com.jxntv.gvideo.search.api.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.api.service.LiveBroadcastIndexService;
import com.jxntv.gvideo.search.api.utils.PageUtils;
import com.jxntv.gvideo.search.client.LiveBroadcastSearchClient;
import com.jxntv.gvideo.search.client.dto.live.LiveBroadcastIndexDTO;
import com.jxntv.gvideo.search.client.dto.live.LiveBroadcastIndexSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class LiveBroadcastSearchController implements LiveBroadcastSearchClient {

    @Resource
    private LiveBroadcastIndexService liveBroadcastIndexService;


    @Override
    public Result<PageDTO<LiveBroadcastIndexDTO>> search(LiveBroadcastIndexSearchDTO searchDTO) {
        Page<LiveBroadcastIndexDTO> page = liveBroadcastIndexService.search(searchDTO);
        return Result.ok(PageUtils.pageOf(page, e -> e));
    }
}
