package com.jxntv.gvideo.search.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableFeignClients(basePackages = "com.jxntv.gvideo.**")
@SpringBootApplication(scanBasePackages = "com.jxntv.gvideo.**")
public class GvideoSearchServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(GvideoSearchServiceApplication.class, args);
    }

}
