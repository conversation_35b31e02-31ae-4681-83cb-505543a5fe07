package com.jxntv.gvideo.search.api.event.listener;

import com.jxntv.gvideo.canal.client.dto.VideoSimilarRecommendBackupBinlogEvent;
import com.jxntv.gvideo.canal.client.dto.VideoSimilarRecommendBinlogEvent;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.search.api.domain.VideoSimilarRecommendDoc;
import com.jxntv.gvideo.search.api.event.VideoSimilarRecommendBackupBatchEvent;
import com.jxntv.gvideo.search.api.event.VideoSimilarRecommendBatchEvent;
import com.jxntv.gvideo.search.api.service.VideoSimilarRecommendService;
import com.jxntv.gvideo.search.client.enums.VideoSimilarRecommendType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 视频推荐数据监听
 *
 * <AUTHOR>
 * @date 2022/2/7
 * Email: <EMAIL>
 */
@Slf4j
@Component
public class VideoSimilarRecommendListener {

    @Resource
    private VideoSimilarRecommendService videoSimilarRecommendService;


    /**
     * 接收相似视频推荐数据
     */
    @EventListener
    public void onEvent(VideoSimilarRecommendBatchEvent batchEvent) {

        List<VideoSimilarRecommendBinlogEvent> events = batchEvent.getEvents();
        //  删除
        List<String> removeIds = events.stream().filter(e -> BinlogEventType.DELETE.equals(e.getEventType())).map(this::createId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removeIds)) {
            videoSimilarRecommendService.removeByIds(removeIds);
        }
        //  插入或更新
        List<VideoSimilarRecommendDoc> entityList = events.stream()
                .filter(e -> !BinlogEventType.DELETE.equals(e.getEventType()))
                .map(this::convert).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(entityList)) {
            videoSimilarRecommendService.save(entityList);
        }

    }

    /**
     * 接收相似视频兜底推荐数据
     * 兜底类型当前内容id固定为-1
     */
    @EventListener
    public void onEvent(VideoSimilarRecommendBackupBatchEvent batchEvent) {

        List<VideoSimilarRecommendBackupBinlogEvent> events = batchEvent.getEvents();
        //  删除事件
        List<String> removeIds = events.stream().filter(e -> BinlogEventType.DELETE.equals(e.getEventType())).map(this::createId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(removeIds)) {
            videoSimilarRecommendService.removeByIds(removeIds);
        }

        //  插入或者更新
        List<VideoSimilarRecommendDoc> entityList = events.stream()
                .filter(e -> !BinlogEventType.DELETE.equals(e.getEventType()))
                .map(this::convert)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(entityList)) {
            videoSimilarRecommendService.save(entityList);
        }

    }


    private VideoSimilarRecommendDoc convert(VideoSimilarRecommendBinlogEvent e) {
        Integer type = VideoSimilarRecommendType.SIMILAR.getCode();

        VideoSimilarRecommendDoc entity = new VideoSimilarRecommendDoc();
        entity.setId(createId(e));
        entity.setType(type);
        entity.setContentId(e.getContentId());
        entity.setRecommendId(e.getSimilarContentId());
        entity.setRankDate(e.getRankDate());
        entity.setRankNum(e.getRankNum());
        return entity;
    }

    private VideoSimilarRecommendDoc convert(VideoSimilarRecommendBackupBinlogEvent event) {
        Integer type = VideoSimilarRecommendType.BACKUP.getCode();
        VideoSimilarRecommendDoc entity = new VideoSimilarRecommendDoc();
        entity.setId(createId(event));
        entity.setType(type);
        entity.setContentId(-1L);
        entity.setRecommendId(event.getContentId());
        entity.setRankDate(event.getRankDate());
        entity.setRankNum(event.getRankNum());
        return entity;
    }

    private String createId(VideoSimilarRecommendBinlogEvent event) {
        return createId(VideoSimilarRecommendType.SIMILAR.getCode(), event.getId());
    }

    private String createId(VideoSimilarRecommendBackupBinlogEvent event) {
        return createId(VideoSimilarRecommendType.BACKUP.getCode(), event.getId());
    }

    private String createId(Integer type, Long sourceId) {
        return type + "_" + sourceId;
    }


}
