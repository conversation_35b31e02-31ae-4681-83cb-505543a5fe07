package com.jxntv.gvideo.search.api.job;

import com.jxntv.gvideo.search.api.domain.initializer.Initializer;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class IndexInitJob {

    @Resource
    private Map<String, Initializer<?>> initializerMap;

    @XxlJob("indexInitJob")
    public ReturnT<String> execute() {
        String params = XxlJobHelper.getJobParam();
        XxlJobHelper.log("获取到参数为:{}", params);
        if (StringUtils.isNotEmpty(params)) {
            List<String> names = Arrays.asList(params.split(",")) ;
            initializerMap.forEach((k, v) -> {
                //  如果执行器的名称和参数匹配，执行初始化
                if (names.stream().anyMatch(k::startsWith)) {
                    v.indexAll();
                }
            });

        } else {
            //  默认初始化全部索引
            initializerMap.forEach((k, v) -> v.indexAll());
        }

        return ReturnT.SUCCESS;
    }
}
