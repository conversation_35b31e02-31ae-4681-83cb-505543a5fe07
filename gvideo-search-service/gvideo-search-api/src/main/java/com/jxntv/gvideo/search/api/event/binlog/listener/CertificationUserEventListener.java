package com.jxntv.gvideo.search.api.event.binlog.listener;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.search.api.domain.MultiContent;
import com.jxntv.gvideo.search.api.event.binlog.CertificationEvent;
import com.jxntv.gvideo.search.api.repository.MultiContentRepository;
import com.jxntv.gvideo.search.client.enums.MultiContentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class CertificationUserEventListener {

    @Resource
    private MultiContentRepository multiContentRepository;


    /**
     * 认证用户事件处理
     * 1. 处理用户删除事件
     * 2. 处理用户更新事件
     *
     * @param event 事件
     */

    @Async
    @EventListener
    public void onEvent(CertificationEvent event) {
        try {
            String id = MultiContentType.CERTIFICATION_USER.name() + "-" + event.getId();
            if (BinlogEventType.DELETE.equals(event.getEventType())) {
                multiContentRepository.deleteById(id);
                log.debug("删除平台用户事件, userId:{}", event.getId());
                return;
            }

            // 获取用户详情并转换
            MultiContent entity = multiContentRepository.findById(id).orElse(new MultiContent());
            entity.setId(id);
            entity.setType(MultiContentType.CERTIFICATION_USER);
            entity.setContentId(event.getId());
            entity.setTitle(event.getName());
            entity.setSingleTitle(event.getName());
            entity.setDescription(event.getIntroduce());
            entity.setPublishTime(event.getCreateDate());
            entity.setEnable(Boolean.TRUE.equals(event.getStatus()));
            multiContentRepository.save(entity);
            log.debug("成功处理平台用户事件, userId:{}", event.getId());
        } catch (Exception e) {
            log.error("处理平台用户事件异常, userId:{}", event.getId(), e);
        }
    }


}
