package com.jxntv.gvideo.search.api.controller;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.api.converter.GroupContentConverter;
import com.jxntv.gvideo.search.api.domain.GroupContent;
import com.jxntv.gvideo.search.api.service.group.GroupContentService;
import com.jxntv.gvideo.search.api.utils.PageUtils;
import com.jxntv.gvideo.search.client.GroupContentSearchClient;
import com.jxntv.gvideo.search.client.dto.group.GroupContentDTO;
import com.jxntv.gvideo.search.client.dto.group.GroupContentSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
public class GroupContentSearchController implements GroupContentSearchClient {

    @Resource
    private GroupContentService groupContentService;
    @Resource
    private GroupContentConverter groupContentConverter;

    @Override
    public Result<PageDTO<GroupContentDTO>> search(GroupContentSearchDTO searchDTO) {
        Page<GroupContent> page = groupContentService.search(searchDTO);
        return Result.ok(PageUtils.pageOf(page, groupContentConverter::convert));
    }

}
