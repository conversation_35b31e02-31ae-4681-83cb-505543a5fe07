package com.jxntv.gvideo.search.api.service;

import com.jxntv.gvideo.search.api.domain.UserIndex;
import com.jxntv.gvideo.search.client.dto.user.UserIndexDTO;
import com.jxntv.gvideo.search.client.dto.user.UserIndexSearchDTO;
import org.springframework.data.domain.Page;

public interface UserIndexService extends ElasticsearchService<UserIndex> {
    Page<UserIndexDTO> search(UserIndexSearchDTO searchDTO);
}
