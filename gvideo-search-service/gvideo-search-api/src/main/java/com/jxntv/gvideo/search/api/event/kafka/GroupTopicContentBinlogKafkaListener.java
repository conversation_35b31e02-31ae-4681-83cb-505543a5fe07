package com.jxntv.gvideo.search.api.event.kafka;

import com.jxntv.gvideo.canal.client.constants.Topics;
import com.jxntv.gvideo.canal.client.dto.GroupTopicContentBinlogEvent;
import com.jxntv.gvideo.common.utils.JsonUtils;
import com.jxntv.gvideo.search.api.event.GroupTopicContentBatchEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: niedamin
 * @Date: 2022/08/29 9:48
 */
@Slf4j
@Component
public class GroupTopicContentBinlogKafkaListener {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 接收kafka事件源，转换成内部事件
     */
    @KafkaListener(topics = {Topics.GROUP_TOPIC_CONTENT})
    public void onMessage(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        try {
            List<GroupTopicContentBinlogEvent> events = records.stream().map(e -> JsonUtils.fromJson(e.value(), GroupTopicContentBinlogEvent.class)).collect(Collectors.toList());

            log.info("接收 group_topic_content 消息:{}", JsonUtils.toJson(events));

            if (!CollectionUtils.isEmpty(events)) {
                GroupTopicContentBatchEvent batchEvent = new GroupTopicContentBatchEvent();
                batchEvent.setEvents(events);

                applicationEventPublisher.publishEvent(batchEvent);
            }

            //  手动提交ack
            ack.acknowledge();

        } catch (Exception e) {
            log.error("接收日志消费失败", e);
        }

    }
}
