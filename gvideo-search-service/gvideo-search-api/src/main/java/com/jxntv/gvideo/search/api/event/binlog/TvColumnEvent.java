package com.jxntv.gvideo.search.api.event.binlog;

import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TvColumnEvent implements Serializable {



    private BinlogEventType eventType;

    private Long id;
    /**
     * 栏目简介
     */
    private String introduction;
    /**
     * 栏目封面
     */
    private String coverId;
    /**
     * 栏目封面URL，兼容赣云数据
     */
    private String coverUrl;
    /**
     * 栏目名称
     */
    private String columnName;
    /**
     * 栏目权重
     */
    private Integer weight;

    /**
     * 栏目关联社区id
     */
    private Long groupId;

    /**
     * 栏目状态，0-禁用，1-启用
     */
    private Integer status;

    /**
     * 入库时间
     */
    private LocalDateTime createDate;
    /**
     * 更新时间
     */
    private LocalDateTime updateDate;







}
