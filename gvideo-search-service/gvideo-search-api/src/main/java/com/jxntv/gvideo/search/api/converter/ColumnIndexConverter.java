package com.jxntv.gvideo.search.api.converter;

import com.jxntv.gvideo.canal.client.dto.TvColumnBinlogEvent;
import com.jxntv.gvideo.media.client.TvColumnClient;
import com.jxntv.gvideo.media.client.dto.tv.TvColumnDTO;
import com.jxntv.gvideo.search.api.client.BigDataSearchClient;
import com.jxntv.gvideo.search.api.client.dto.CutWordReqDTO;
import com.jxntv.gvideo.search.api.client.dto.CutWordRespDTO;
import com.jxntv.gvideo.search.api.domain.ColumnIndex;
import com.jxntv.gvideo.search.client.dto.column.ColumnIndexDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class ColumnIndexConverter {
    @Resource
    private BigDataSearchClient bigDataSearchClient;
    @Resource
    private TvColumnClient tvColumnClient;


    public ColumnIndexDTO convert(ColumnIndex index) {
        ColumnIndexDTO columnIndexDTO = new ColumnIndexDTO();
        columnIndexDTO.setColumnId(Long.valueOf(index.getId()));
        columnIndexDTO.setTokens(index.getTokens());
        return columnIndexDTO;
    }

    public ColumnIndex convert(TvColumnBinlogEvent event) {
        TvColumnDTO dto = tvColumnClient.getById(event.getId()).orElse(null);
        if (Objects.nonNull(dto)) {
            String sentence = dto.getColumnName();
            List<String> tokens = bigDataSearchClient.cutWord(new CutWordReqDTO(sentence)).map(CutWordRespDTO::getTokens).orElse(Collections.singletonList(sentence));

            ColumnIndex index = new ColumnIndex();
            index.setId(String.valueOf(dto.getId()));
            index.setTokens(tokens);
            index.setSentence(sentence);
            return index;
        }

        return null;
    }
}
