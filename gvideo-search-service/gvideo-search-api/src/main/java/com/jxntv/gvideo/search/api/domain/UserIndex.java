package com.jxntv.gvideo.search.api.domain;

import com.jxntv.gvideo.search.api.common.IndexEntity;
import lombok.Data;
import nonapi.io.github.classgraph.json.Id;
import org.springframework.data.elasticsearch.annotations.Document;

import java.io.Serializable;
import java.util.List;

@Data
@Document(indexName = "uid_infos")
public class UserIndex implements IndexEntity, Serializable {
    @Id
    private String id;

    /**
     * pgc/ugc 分类
     */
    private String type;
    /**
     * 匹配分词
     */
    private List<String> tokens;
    /**
     * 原语句
     */
    private String sentence;
}
