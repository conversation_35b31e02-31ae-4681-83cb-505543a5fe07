package com.jxntv.gvideo.search.api.event.kafka;

import com.jxntv.gvideo.canal.client.constants.Topics;
import com.jxntv.gvideo.canal.client.dto.CertificationBinlogEvent;
import com.jxntv.gvideo.search.api.event.CertificationBatchEvent;
import com.jxntv.gvideo.search.api.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CertificationBinlogKafkaListener {

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    /**
     * 接收kafka事件源，转换成内部事件
     */
    @KafkaListener(topics = {Topics.CERTIFICATION_TOPIC})
    public void onMessage(List<ConsumerRecord<String, String>> records, Acknowledgment ack) {
        try {
            List<CertificationBinlogEvent> events = records.stream().map(e -> JsonUtils.fromJson(e.value(), CertificationBinlogEvent.class)).collect(Collectors.toList());

            log.info("接收certification消息:{}", JsonUtils.toJson(events));

            applicationEventPublisher.publishEvent(new CertificationBatchEvent(events));

            //  手动提交ack
            ack.acknowledge();

        } catch (Exception e) {
            log.error("接收日志消费失败", e);
        }

    }


}
