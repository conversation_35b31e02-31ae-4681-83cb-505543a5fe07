package com.jxntv.gvideo.search.api.service.group;

import com.jxntv.gvideo.search.api.domain.GroupContent;
import com.jxntv.gvideo.search.client.dto.group.GroupContentSearchDTO;
import org.springframework.data.domain.Page;

import java.util.List;

public interface GroupContentService {

    Page<GroupContent> search(GroupContentSearchDTO searchDTO);

    boolean save(List<GroupContent> entityList);

    boolean remove(List<GroupContent> entityList);


}
