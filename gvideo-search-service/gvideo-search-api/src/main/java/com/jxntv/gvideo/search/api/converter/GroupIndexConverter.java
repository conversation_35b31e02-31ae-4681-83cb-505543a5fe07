package com.jxntv.gvideo.search.api.converter;

import com.jxntv.gvideo.canal.client.dto.GroupBinlogEvent;
import com.jxntv.gvideo.group.sdk.client.GroupTopicClient;
import com.jxntv.gvideo.group.sdk.dto.GroupInfoDTO;
import com.jxntv.gvideo.search.api.client.BigDataSearchClient;
import com.jxntv.gvideo.search.api.client.dto.CutWordReqDTO;
import com.jxntv.gvideo.search.api.client.dto.CutWordRespDTO;
import com.jxntv.gvideo.search.api.domain.GroupIndex;
import com.jxntv.gvideo.search.client.dto.group.GroupIndexDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class GroupIndexConverter {

    @Resource
    private GroupTopicClient groupTopicClient;
    @Resource
    private BigDataSearchClient bigDataSearchClient;

    public GroupIndex convert(GroupBinlogEvent event) {
        GroupInfoDTO dto = groupTopicClient.getSimpleById(event.getId()).orElse(null);
        if (Objects.nonNull(dto)) {
            String name = dto.getName();
            List<String> tokens = bigDataSearchClient.cutWord(new CutWordReqDTO(name)).map(CutWordRespDTO::getTokens).orElse(Collections.singletonList(name));

            GroupIndex index = new GroupIndex();
            index.setId(String.valueOf(event.getId()));
            index.setTokens(tokens);
            index.setSentence(name);
            return index;
        }

        return null;
    }


    public GroupIndexDTO convert(List<String> tokens,double medianScore ,SearchHit<GroupIndex> searchHit) {
        GroupIndex content = searchHit.getContent();
        double score = searchHit.getScore();

        GroupIndexDTO dto = new GroupIndexDTO();
        dto.setGroupId(Long.valueOf(content.getId()));
        dto.setTokens(tokens);
        dto.setMedianScore(medianScore);
        dto.setScore(score);
        return dto;
    }

}
