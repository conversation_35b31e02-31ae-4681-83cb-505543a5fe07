package com.jxntv.gvideo.search.api.event.binlog;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.jxntv.gvideo.canal.client.enums.BinlogEventType;
import com.jxntv.gvideo.media.client.enums.BroadcastStatusEnum;
import com.jxntv.gvideo.media.client.enums.RecommendEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class LiveBroadcastEvent implements Serializable {

    private BinlogEventType eventType;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 直播状态 0 未开始 1 预告 2 直播中 3 回放 4 下架
     */
    private BroadcastStatusEnum status;
    /**
     * 直播类型 1 横屏 2 竖屏 3音频直播
     */
    private Integer type;
    /**
     * 是否外链直播：0-不是，1-是
     */
    private Integer liveMode;
    /**
     * 流类型：0-推流、1-拉流
     */
    private Integer streamType;
    /**
     * 是否可录制：0-不支持、1-支持
     */
    private Integer recordType;
    /**
     * APP名称
     */
    private String appName;
    /**
     * 流名称
     */
    private String streamName;
    /**
     * 封面图
     */
    private String thumb;
    /**
     * 推荐状态 0 默认 1 推荐 2 置顶
     */
    private RecommendEnum recommendStatus;
    /**
     * 公告
     */
    private String notice;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 直播流地址
     */
    private String liveUrl;
    /**
     * 回放状态视频UUID
     */
    private String uuid;
    /**
     * 直播流备用地址
     */
    private String liveUrlBak;
    /**
     * 0 启用直播流地址 1 启用备用地址
     */
    private Integer liveUrlStatus;
    /**
     * 创建时间
     */
    private LocalDateTime createdDate;
    /**
     * 更新时间
     */
    private LocalDateTime updatedDate;
    /**
     * 创建人
     */
    private Long createdBy;
    /**
     * 创建人名称
     */
    private String createdName;

    /**
     * 修改人
     */
    private Long updatedBy;
    @TableLogic
    private Integer delFlag;
    /**
     * 直播描述
     */
    private String description;
    private String updatedName;
    private Long certificationId;
    private Long mediaId;
    /**
     * 点击
     */
    private Integer click;
    /**
     * 转发
     */
    private Integer share;
    /**
     * 评论
     */
    private String comment;
    private Integer likeSum;


    /**
     * 预告画面
     */
    private Integer previewScreen;


    /**
     * 预告为播流地址时存在
     */
    private String preStream;

    /**
     * 回放流
     */
    private String feedbackStream;

    /**
     * 直播回顾播放时间帧
     */
    private Integer liveBroadcastPlayTime;

    /**
     * 直播间是否允许点赞
     */
    private Integer allowLikes;

    /**
     * 是否播放点赞动画
     */
    private Integer showEffects;
    /**
     * 点击数是否展示
     */
    private Integer viewsSwitch;
    /**
     * 是否录制：1-是、2-否
     */
    private Integer record;

    /**
     * 录制状态 0-未录制 1-录制中 2-录制结束 3-录制失败
     */
    private Integer recordStatus;

    /**
     * 奔流系统videoId
     */
    private String blVideoId;

    /**
     * 站外观看限制 0-不限制 1-限制
     */
    private Integer outSideRestriction;

    /**
     * 站外观看限制秒数
     */
    private Integer outSideSeconds;

}
