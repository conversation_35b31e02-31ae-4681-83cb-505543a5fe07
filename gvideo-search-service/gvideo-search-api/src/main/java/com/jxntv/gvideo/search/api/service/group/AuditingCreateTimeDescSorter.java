package com.jxntv.gvideo.search.api.service.group;

import com.jxntv.gvideo.media.client.enums.MediaResourceStatus;
import com.jxntv.gvideo.search.api.domain.GroupContent;
import com.jxntv.gvideo.search.client.dto.group.GroupContentSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AuditingCreateTimeDescSorter implements KeySorter {
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    private static final String key_prefix = "search::group::content::status_5_create_time_desc_sorted_set::group_";
    private static final double scale = -1_000_000_000;

    @Override
    public Set<ZSetOperations.TypedTuple<String>> search(GroupContentSearchDTO searchDTO) {
        Set<ZSetOperations.TypedTuple<String>> result = new HashSet<>();
        if (!CollectionUtils.isEmpty(searchDTO.getGroupIds()) && isSupport(searchDTO)) {
            List<Long> groupIds = searchDTO.getGroupIds();
            long limit = (long) searchDTO.getCurrent() * searchDTO.getSize() - 1;

            for (Long groupId : groupIds) {
                String groupKey = this.groupKey(groupId);
                Set<ZSetOperations.TypedTuple<String>> rangeWithScores = stringRedisTemplate.opsForZSet().rangeWithScores(groupKey, 0, limit);
                if (!CollectionUtils.isEmpty(rangeWithScores)) {
                    result.addAll(rangeWithScores);
                }

            }

        }

        return result;
    }

    @Override
    public boolean save(List<GroupContent> entityList) {

        Map<Boolean, List<GroupContent>> booleanListMap = entityList.stream().collect(Collectors.partitioningBy(this::isValid));

        List<GroupContent> validList = booleanListMap.get(true);

        if (!CollectionUtils.isEmpty(validList)) {
            //  先按照社区id分组
            Map<Long, List<GroupContent>> listMap = validList.stream().collect(Collectors.groupingBy(GroupContent::getGroupId));

            listMap.forEach((groupId, list) -> {

                String groupKey = this.groupKey(groupId);
                Set<ZSetOperations.TypedTuple<String>> tupleSet = list.stream().map(e -> {
                    String value = e.uniqueKey();
                    double score = this.score(e);
                    return new DefaultTypedTuple<>(value, score);
                }).collect(Collectors.toSet());

                this.stringRedisTemplate.opsForZSet().add(groupKey, tupleSet);

            });
        }


        List<GroupContent> invalidList = booleanListMap.get(false);
        if (!CollectionUtils.isEmpty(invalidList)) {
            this.remove(invalidList);
        }

        return true;
    }

    @Override
    public boolean remove(List<GroupContent> entityList) {
        //  先按照社区id分组
        Map<Long, List<GroupContent>> listMap = entityList.stream().collect(Collectors.groupingBy(GroupContent::getGroupId));

        listMap.forEach((groupId, list) -> {

            String groupKey = this.groupKey(groupId);
            Object[] values = list.stream().map(GroupContent::uniqueKey).distinct().toArray();

            stringRedisTemplate.opsForZSet().remove(groupKey, values);

        });

        return true;
    }


    private boolean isSupport(GroupContentSearchDTO searchDTO) {
        return Objects.nonNull(searchDTO.getJid()) && (Objects.isNull(searchDTO.getSortType()) || Objects.equals(searchDTO.getSortType(), 0));
    }


    private boolean isValid(GroupContent entity) {
        return Objects.equals(MediaResourceStatus.AUDIT.getCode(), entity.getMediaStatus());
    }

    protected String groupKey(Long groupId) {
        return key_prefix + groupId;
    }


    /**
     * 内容创建时间排序
     * mysql自增，使用mediaId当做时间排序即可
     *
     * @param entity
     * @return
     */
    protected double score(GroupContent entity) {
        //  mysql自增，使用id当做时间排序即可
        Long mediaId = entity.getMediaId();
        //  id缩放，避免long溢出的问题
        return mediaId / scale;
    }

}
