package com.jxntv.gvideo.search.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.client.dto.hint.SearchHintDTO;
import com.jxntv.gvideo.search.client.dto.hint.SearchHintQueryParam;
import com.jxntv.gvideo.search.client.fallback.SearchHintClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "search-service", contextId = "search-hint", fallbackFactory = SearchHintClientFallback.class)
public interface SearchHintClient {


    @PostMapping("/api/search/hint")
    Result<String> addHint(@RequestBody SearchHintDTO dto);

    @PutMapping("/api/search/hint/{id}")
    Result<Boolean> updateHint(@PathVariable String id, @RequestBody SearchHintDTO dto);

    @DeleteMapping("/api/search/hint/{id}")
    Result<Boolean> deleteHint(@PathVariable String id);

    @GetMapping("/api/search/hint/check")
    Result<Boolean> isHint(@RequestParam String keyword);

    @PostMapping("/api/search/hint/page")
    Result<PageDTO<SearchHintDTO>> page(@RequestBody SearchHintQueryParam queryParam);


}
