package com.jxntv.gvideo.search.client;

import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.search.client.dto.group.GroupContentDTO;
import com.jxntv.gvideo.search.client.dto.group.GroupContentSearchDTO;
import com.jxntv.gvideo.search.client.fallback.GroupContentSearchClientFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "search-service", contextId = "group-content",fallbackFactory = GroupContentSearchClientFallback.class)
public interface GroupContentSearchClient {

    @PostMapping("/api/group/content/search")
    Result<PageDTO<GroupContentDTO>> search(@RequestBody GroupContentSearchDTO searchDTO);
}
