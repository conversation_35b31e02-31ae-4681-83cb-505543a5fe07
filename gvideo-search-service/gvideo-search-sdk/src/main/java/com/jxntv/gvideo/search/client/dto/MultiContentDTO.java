package com.jxntv.gvideo.search.client.dto;

import com.jxntv.gvideo.search.client.enums.MultiContentType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class MultiContentDTO implements Serializable {
    /**
     * 主键ID
     */
    private String id;
    /**
     * 数据类型
     */
    private MultiContentType type;
    /**
     * 内容ID
     */
    private Long contentId;
    /**
     * 查询令牌
     */
    private List<String> tokens;

    /**
     * 得分
     */
    private Float score;

    /**
     * 剧集
     */
    private List<Episode> episodes;


    @Data
    public static class Episode {

        /**
         * 剧集ID
         */
        private Long episodeId;

        /**
         * 剧集标题
         */
        private String episodeTitle;
        /**
         * 播出时间
         */
        private Date airDate;
        /**
         * 序列号
         */
        private String episodeNo;
    }


}
