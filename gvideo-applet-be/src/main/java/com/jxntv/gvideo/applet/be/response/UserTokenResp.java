package com.jxntv.gvideo.applet.be.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2020-02-25
 */
@Data
@ApiModel(value = "UserTokenResp", description = "用户对象及token")
public class UserTokenResp {

    @ApiModelProperty(value = "用户信息", required = true)
    private UserResp user;
    @ApiModelProperty(value = "token", required = true)
    private String token;
}
