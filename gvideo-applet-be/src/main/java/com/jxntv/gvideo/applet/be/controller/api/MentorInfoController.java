package com.jxntv.gvideo.applet.be.controller.api;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.applet.be.client.MentorInfoService;
import com.jxntv.gvideo.applet.be.config.ConsultProperties;
import com.jxntv.gvideo.applet.be.convert.MentorInfoConverter;
import com.jxntv.gvideo.applet.be.param.ConsultCostDTO;
import com.jxntv.gvideo.applet.be.service.applet.ConsultService;
import com.jxntv.gvideo.applet.be.supports.Page;
import com.jxntv.gvideo.applet.be.utils.DateUtils;
import com.jxntv.gvideo.applet.be.vo.consult.ConsultDetailVO;
import com.jxntv.gvideo.applet.be.vo.mentor.MentorInfoVO;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.MentorInfoClient;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorSearchDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupMentorConsultCostDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 *  导师信息
 *  <AUTHOR>
 */
@Slf4j
@Api(value = "小程序导师信息", tags = {"小程序导师信息"})
@RestController
@RequestMapping("/api/mentor")
public class MentorInfoController {


    @Resource
    private ConsultProperties mentorConsultProperties;

    @Resource
    private MentorInfoService mentorInfoService;

    @Resource
    private MentorInfoConverter mentorInfoConverter;

    @Resource
    private ConsultService consultService;
    /**
     * 查询导师列表
     * @return
     */
    @ApiOperation("查询导师列表")
    @GetMapping("/list")
    public Result<Page<MentorInfoVO>> list(@ApiParam(value = "页码") @RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                                           @ApiParam(value = "分页大小") @RequestParam(value = "pageSize", defaultValue = "10") int pageSize){


        ConsultCostDTO consultCostDTO = consultService.cost();

        GroupFreeMentorSearchDTO searchDTO = new GroupFreeMentorSearchDTO();
        searchDTO.setComponentId(mentorConsultProperties.getGatherId());
        searchDTO.setCurrent(pageNum);
        searchDTO.setSize(pageSize);
        searchDTO.setOrderBy("answerTimes");
        Result<PageDTO<GroupFreeMentorDTO>> result = mentorInfoService.searchFreeMentor(searchDTO);
        ConsultCostDTO finalConsultCostDTO = consultCostDTO;
        return result.map(page -> Page.Of(page, e -> mentorInfoConverter.convert(e, finalConsultCostDTO)));
    }

    /**
     * 查询导师详情
     * @return
     */
    @ApiOperation("获取导师详情")
    @GetMapping("/{jid}")
    public Result<MentorInfoVO> get(@ApiParam(value = "jid") @PathVariable Long jid){
        Result<GroupFreeMentorDTO> result = mentorInfoService.getByJid(jid, mentorConsultProperties.getGroupId());
        if (!result.callSuccess()){
            return  Result.fail("获取导师信息失败");
        }
        ConsultCostDTO consultCostDTO = consultService.cost();
        return  Result.ok(mentorInfoConverter.convert(result.getResult(), consultCostDTO));
    }

}
