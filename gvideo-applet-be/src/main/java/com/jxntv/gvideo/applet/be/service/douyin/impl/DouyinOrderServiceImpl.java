package com.jxntv.gvideo.applet.be.service.douyin.impl;


import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.applet.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.applet.be.manager.OssManager;
import com.jxntv.gvideo.applet.be.security.thread.ThreadLocalCache;
import com.jxntv.gvideo.applet.be.service.douyin.DouyinOrderService;
import com.jxntv.gvideo.applet.be.vo.consult.*;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.consulatation.api.constants.enums.ConsultPlatformEnum;
import com.jxntv.gvideo.media.client.ConsultQaOrderClient;
import com.jxntv.gvideo.media.client.dto.consult.qa.ConsultQaOrderDTO;
import com.jxntv.gvideo.user.client.DouyinUserClient;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import com.jxntv.gvideo.user.client.dto.DouyinUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>

 */
@Slf4j
@Component
public class DouyinOrderServiceImpl implements DouyinOrderService {
    @Resource
    private ConsultQaOrderClient consultQaOrderClient;

    @Resource
    private DouyinUserClient douyinUserClient;

    @Autowired
    private OssManager ossManager;

    @Autowired
    private ConsumerUserManager consumerUserManager;

    @Override
    public Result<ToBeUsedOrderVO> queryToBeUsedOrderId() {
        DouyinUserDTO douyinUserDTO = douyinUserClient.queryUserByOpenId(ThreadLocalCache.getOpenId()).orElse(null);
        if (Objects.isNull(douyinUserDTO) || Objects.isNull(douyinUserDTO.getJid())) {
            return Result.fail("用户信息不存在");
        }
        Result<ConsultQaOrderDTO> douyinOrderDTOResult = consultQaOrderClient.queryToBeUsedOrder(douyinUserDTO.getJid(), ConsultPlatformEnum.DOUYIN.getCode());
        if (!douyinOrderDTOResult.callSuccess()){
            return Result.fail("获取待咨询的订单信息失败");
        }

        ConsultQaOrderDTO consultQaOrderDTO = douyinOrderDTOResult.getResult();
        if (Objects.isNull(consultQaOrderDTO)){
            return Result.ok();
        }
        ToBeUsedOrderVO vo =  new ToBeUsedOrderVO();
        vo.setOrderNo(consultQaOrderDTO.getThirdOrderNo());
        ConsumerUserDTO consumerDto = consumerUserManager.getByJid(consultQaOrderDTO.getMentorId());
        if (Objects.nonNull(consumerDto)) {
            ToBeUsedOrderVO.MentorInfo mentorInfo = new ToBeUsedOrderVO.MentorInfo();
            mentorInfo.setJid(consumerDto.getJid());
            mentorInfo.setNickname(consumerDto.getNickname());
            if (StringUtils.isNotEmpty(consumerDto.getAvatar())) {
                mentorInfo.setAvatar(ossManager.getAvatar(consumerDto.getAvatar()).map(OssDTO::getUrl).orElse(""));
            }
            vo.setMentorInfo(mentorInfo);
        }

        return Result.ok(vo);
    }
}
