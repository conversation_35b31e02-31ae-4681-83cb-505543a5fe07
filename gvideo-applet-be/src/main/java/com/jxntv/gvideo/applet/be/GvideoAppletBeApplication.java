package com.jxntv.gvideo.applet.be;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableAsync
@EnableFeignClients(basePackages = "com.jxntv.gvideo.**")
@SpringBootApplication(scanBasePackages = "com.jxntv.gvideo.**", exclude = {DataSourceAutoConfiguration.class})
@EnableCreateCacheAnnotation
public class GvideoAppletBeApplication {

    public static void main(String[] args) {
        SpringApplication.run(GvideoAppletBeApplication.class, args);
    }

}
