package com.jxntv.gvideo.applet.be.convert;

import com.jxntv.gvideo.aliyun.sdk.dto.OssDTO;
import com.jxntv.gvideo.aliyun.sdk.dto.VodAudioPlayDTO;
import com.jxntv.gvideo.applet.be.manager.ConsumerUserManager;
import com.jxntv.gvideo.applet.be.manager.OssManager;
import com.jxntv.gvideo.applet.be.vo.consult.CommentVO;
import com.jxntv.gvideo.applet.be.vo.consult.ReplyVO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.interact.client.dto.CommentDTO;
import com.jxntv.gvideo.interact.client.dto.CommentReplyDTO;
import com.jxntv.gvideo.interact.client.dto.CommentTreeReplyDTO;
import com.jxntv.gvideo.media.client.enums.DataType;
import com.jxntv.gvideo.user.client.dto.ConsumerUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Component
public class CommentConverter  {


    @Resource
    private OssManager ossManager;

    @Resource
    private ConsumerUserManager consumerUserManager;

    public Map<String, String> getResizeImageMap(String image) {
        Map<String, String> imageMap = new LinkedHashMap<>();
        //  查询返回评论图片
        if (StringUtils.isNotEmpty(image)) {
            for (String item : image.split(":")) {
                Result<OssDTO> ossRst = ossManager.getResize(item);
                ossRst.ifPresent(ossDTO -> imageMap.put(ossDTO.getUuid(), ossDTO.getUrl()));

            }
        }
        return imageMap;
    }

    public Map<String, String> getImageMap(String image) {
        Map<String, String> imageMap = new TreeMap<>();
        //查询返回评论图片
        if (StringUtils.isNotEmpty(image)) {
            for (String item : image.split(":")) {
                Result<OssDTO> ossRst = ossManager.getOssFile(item);
                if (ossRst.callSuccess()) {
                    ossRst.ifPresent(ossDTO -> imageMap.put(ossDTO.getUuid(), ossDTO.getUrl()));
                }
            }
        }
        return imageMap;
    }

    public CommentVO toVo(CommentDTO dto) {
        CommentVO vo = new CommentVO();
        vo.setPrimaryId(dto.getId());
        vo.setContent(dto.getContent());
        vo.setCommentDate(dto.getCreateDate());
        vo.setCommentDate(dto.getCreateDate());
        String image = dto.getImage();
        vo.setImageList(getResizeImageList(image));
        vo.setOriImageList(getImageList(image));
        //查询返回评论语音
        vo.setSoundUrl(ossManager.getAudio(dto.getSoundOssId()).map(VodAudioPlayDTO::getUrl).orElse(""));
        ossManager.getContentBySoundId(dto.getSoundOssId()).ifPresent(soundDTO -> {
            vo.setSoundContent(soundDTO.getContent());
            vo.setLength(soundDTO.getLength());
        });

        return vo;
    }

    public List<String> getImageList(String image) {
        // OssService ossService = SpringUtils.getBean(OssService.class);
        List<String> imageList = new ArrayList<>();
        //查询返回评论图片
        if (StringUtils.isNotEmpty(image)) {
            for (String item : image.split(":")) {
                String url = ossManager.getOssFile(item).map(OssDTO::getUrl).orElse("");
                imageList.add(url);
            }
        }
        return imageList;
    }

    public List<String> getResizeImageList(String image) {
        List<String> imageList = new ArrayList<>();
        //查询返回评论图片
        if (StringUtils.isNotEmpty(image)) {
            for (String item : image.split(":")) {
                String url = ossManager.getResize(item).map(OssDTO::getUrl).orElse("");
                imageList.add(url);
            }
        }
        return imageList;
    }

    public ReplyVO toReplyVO(CommentReplyDTO dto) {
        ReplyVO vo = new ReplyVO();
        vo.setReplyId(dto.getId());
        vo.setContent(dto.getContent());
        vo.setReplyDate(dto.getCreateDate());
        String image = dto.getImage();
        vo.setImageList(getResizeImageList(image));
        vo.setOriImageList(getImageList(image));
        //查询返回评论语音
        String sound = dto.getSoundOssId();
        vo.setSoundUrl(ossManager.getAudio(sound).map(VodAudioPlayDTO::getUrl).orElse(""));

        ossManager.getContentBySoundId(sound).ifPresent(soundDTO -> {
            vo.setSoundContent(soundDTO.getContent());
            vo.setLength(soundDTO.getLength());
        });
        ConsumerUserDTO consumerDto = consumerUserManager.getByJid(dto.getFromJid());
        if (Objects.nonNull(consumerDto)) {
            vo.setJid(dto.getFromJid());
            vo.setNickname(consumerDto.getNickname());
            if (StringUtils.isNotEmpty(consumerDto.getAvatar())) {
                vo.setAvatar(ossManager.getAvatar(consumerDto.getAvatar()).map(OssDTO::getUrl).orElse(""));
            }
        }
        return vo;
    }
}
