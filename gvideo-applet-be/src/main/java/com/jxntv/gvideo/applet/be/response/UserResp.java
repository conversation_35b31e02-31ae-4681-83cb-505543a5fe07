package com.jxntv.gvideo.applet.be.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created on 2020-02-25
 */
@Data
@ApiModel(value = "UserResp", description = "用户对象")
public class UserResp {
    @ApiModelProperty(value = "用户ID", required = true)
    private Long userId;
    @ApiModelProperty(value = "昵称", required = true)
    private String nickname;
    @ApiModelProperty(value = "头像")
    private String avatar;
    @ApiModelProperty(value = "国家代码")
    private String countryCode;
    @ApiModelProperty(value = "手机号")
    private String phone;
    @ApiModelProperty(value = "1 后台用户 0 普通用户")
    private Integer platformUser;
    @ApiModelProperty(value = "省份id")
    private Integer provinceId;
    @ApiModelProperty(value = "城市id")
    private Integer cityId;
    @ApiModelProperty(value = "省份")
    private String province;
    @ApiModelProperty(value = "城市")
    private String city;
    @JsonIgnore
    private boolean isNew;
    @JsonIgnore
    private String loginType;
}
