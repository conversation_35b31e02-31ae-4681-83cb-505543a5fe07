package com.jxntv.gvideo.applet.be.client.fallback;

import com.jxntv.gvideo.common.exception.CodeMessage;
import com.jxntv.gvideo.common.model.PageDTO;
import com.jxntv.gvideo.common.model.Result;
import com.jxntv.gvideo.group.sdk.client.MentorInfoClient;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorEditDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.GroupFreeMentorSearchDTO;
import com.jxntv.gvideo.group.sdk.dto.mentor.MentorDTO;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MentorInfoClientFallback implements FallbackFactory<MentorInfoClient> {
    @Override
    public MentorInfoClient create(Throwable throwable) {
        return new MentorInfoClient() {
            @Override
            public Result<GroupFreeMentorDTO> addFreeMentor(GroupFreeMentorEditDTO mentorInfoReqDTO) {
                log.error("MentorInfoClient.createMentorInfo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<PageDTO<GroupFreeMentorDTO>> searchFreeMentor(GroupFreeMentorSearchDTO mentorInfoSearchDTO) {
                log.error("MentorInfoClient.queryMentorInfos() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupFreeMentorDTO> updateFreeMentor(GroupFreeMentorEditDTO mentorInfoReqDTO) {
                log.error("MentorInfoClient.updateMentorInfo() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }


            @Override
            public Result<List<GroupFreeMentorDTO>> listMentorByComponentId(Long componentId) {
                log.error("MentorInfoClient.listMentorByComponentId() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteFreeMentorByJid(Long jid) {
                log.error("MentorInfoClient.deleteFreeMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> queryIsMentorByJid(Long jid) {
                log.error("MentorInfoClient.queryIsMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Long> addMentor(MentorDTO dto) {
                log.error("MentorInfoClient.queryIsMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> updateMentorByJid(Long jid, MentorDTO dto) {
                log.error("MentorInfoClient.queryIsMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteMentorByJid(Long jid) {
                log.error("MentorInfoClient.queryIsMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<List<Long>> searchFreeMentorJidList() {
                log.error("MentorInfoClient.searchFreeMentorJidList() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<GroupFreeMentorDTO> getById(Long mentorId) {
                log.error("MentorInfoClient.getById() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
            @Override
            public Result<GroupFreeMentorDTO> getByJid(Long mentorId,Long groupId) {
                log.error("MentorInfoClient.getByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }

            @Override
            public Result<Boolean> deleteFreeMentor(Long componentId, Long jid) {
                log.error("MentorInfoClient.queryIsMentorByJid() fallback", throwable);
                return Result.fail(CodeMessage.SERVICE_UNAVAILABLE.getCode(), CodeMessage.SERVICE_UNAVAILABLE.getMessage());
            }
        };
    }
}
